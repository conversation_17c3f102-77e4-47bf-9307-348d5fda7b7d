"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorInputMapping = exports.AxisInputMapping = exports.ButtonInputMapping = exports.BaseInputMapping = exports.InputMappingType = void 0;
/**
 * 输入映射类型
 */
var InputMappingType;
(function (InputMappingType) {
    /** 按钮映射 */
    InputMappingType["BUTTON"] = "button";
    /** 轴映射 */
    InputMappingType["AXIS"] = "axis";
    /** 向量映射 */
    InputMappingType["VECTOR"] = "vector";
    /** 组合映射 */
    InputMappingType["COMPOSITE"] = "composite";
})(InputMappingType || (exports.InputMappingType = InputMappingType = {}));
/**
 * 输入映射基类
 */
var BaseInputMapping = /** @class */ (function () {
    /**
     * 创建输入映射
     * @param name 映射名称
     * @param type 映射类型
     * @param deviceName 设备名称
     */
    function BaseInputMapping(name, type, deviceName) {
        this.name = name;
        this.type = type;
        this.deviceName = deviceName;
    }
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    BaseInputMapping.prototype.getName = function () {
        return this.name;
    };
    /**
     * 获取映射类型
     * @returns 映射类型
     */
    BaseInputMapping.prototype.getType = function () {
        return this.type;
    };
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    BaseInputMapping.prototype.getDeviceName = function () {
        return this.deviceName;
    };
    return BaseInputMapping;
}());
exports.BaseInputMapping = BaseInputMapping;
/**
 * 按钮映射
 */
var ButtonInputMapping = /** @class */ (function (_super) {
    __extends(ButtonInputMapping, _super);
    /**
     * 创建按钮映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param buttonKey 按钮键名
     */
    function ButtonInputMapping(name, deviceName, buttonKey) {
        var _this = _super.call(this, name, InputMappingType.BUTTON, deviceName) || this;
        _this.buttonKey = buttonKey;
        return _this;
    }
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 按钮状态
     */
    ButtonInputMapping.prototype.evaluate = function (device) {
        return !!device.getValue(this.buttonKey);
    };
    return ButtonInputMapping;
}(BaseInputMapping));
exports.ButtonInputMapping = ButtonInputMapping;
/**
 * 轴映射
 */
var AxisInputMapping = /** @class */ (function (_super) {
    __extends(AxisInputMapping, _super);
    /**
     * 创建轴映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param axisKey 轴键名
     * @param scale 轴缩放
     * @param offset 轴偏移
     * @param deadZone 轴死区
     */
    function AxisInputMapping(name, deviceName, axisKey, scale, offset, deadZone) {
        if (scale === void 0) { scale = 1; }
        if (offset === void 0) { offset = 0; }
        if (deadZone === void 0) { deadZone = 0.1; }
        var _this = _super.call(this, name, InputMappingType.AXIS, deviceName) || this;
        _this.axisKey = axisKey;
        _this.scale = scale;
        _this.offset = offset;
        _this.deadZone = deadZone;
        return _this;
    }
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 轴值
     */
    AxisInputMapping.prototype.evaluate = function (device) {
        var value = device.getValue(this.axisKey) || 0;
        // 应用死区
        var absValue = Math.abs(value);
        if (absValue < this.deadZone) {
            return 0;
        }
        // 应用缩放和偏移
        var normalizedValue = (absValue - this.deadZone) / (1 - this.deadZone);
        return (value > 0 ? normalizedValue : -normalizedValue) * this.scale + this.offset;
    };
    return AxisInputMapping;
}(BaseInputMapping));
exports.AxisInputMapping = AxisInputMapping;
/**
 * 向量映射
 */
var VectorInputMapping = /** @class */ (function (_super) {
    __extends(VectorInputMapping, _super);
    /**
     * 创建向量映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param xAxisKey X轴键名
     * @param yAxisKey Y轴键名
     * @param scale 轴缩放
     * @param deadZone 轴死区
     */
    function VectorInputMapping(name, deviceName, xAxisKey, yAxisKey, scale, deadZone) {
        if (scale === void 0) { scale = 1; }
        if (deadZone === void 0) { deadZone = 0.1; }
        var _this = _super.call(this, name, InputMappingType.VECTOR, deviceName) || this;
        _this.xAxisKey = xAxisKey;
        _this.yAxisKey = yAxisKey;
        _this.scale = scale;
        _this.deadZone = deadZone;
        return _this;
    }
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 向量值 [x, y]
     */
    VectorInputMapping.prototype.evaluate = function (device) {
        var x = device.getValue(this.xAxisKey) || 0;
        var y = device.getValue(this.yAxisKey) || 0;
        // 计算向量长度
        var length = Math.sqrt(x * x + y * y);
        // 应用死区
        if (length < this.deadZone) {
            return [0, 0];
        }
        // 应用缩放
        var normalizedLength = (length - this.deadZone) / (1 - this.deadZone);
        var scale = normalizedLength / length * this.scale;
        return [x * scale, y * scale];
    };
    return VectorInputMapping;
}(BaseInputMapping));
exports.VectorInputMapping = VectorInputMapping;
