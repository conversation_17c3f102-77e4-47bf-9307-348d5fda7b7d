"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionContext = void 0;
/**
 * 视觉脚本执行上下文
 * 提供视觉脚本执行时的上下文环境
 */
var ExecutionContext = /** @class */ (function () {
    /**
     * 创建执行上下文
     * @param options 上下文选项
     */
    function ExecutionContext(options) {
        /** 上下文变量 */
        this.variables = new Map();
        /** 上下文函数 */
        this.functions = new Map();
        this.engine = options.engine;
        this.entity = options.entity;
        this.world = options.world;
        // 初始化上下文
        this.initialize();
    }
    /**
     * 初始化上下文
     */
    ExecutionContext.prototype.initialize = function () {
        // 注册内置函数
        this.registerBuiltinFunctions();
    };
    /**
     * 注册内置函数
     */
    ExecutionContext.prototype.registerBuiltinFunctions = function () {
        var _this = this;
        // 日志函数
        this.registerFunction('log', function (message) {
            console.log("[VisualScript] ".concat(message));
        });
        this.registerFunction('warn', function (message) {
            console.warn("[VisualScript] ".concat(message));
        });
        this.registerFunction('error', function (message) {
            console.error("[VisualScript] ".concat(message));
        });
        // 时间函数
        this.registerFunction('delay', function (ms) {
            return new Promise(function (resolve) { return setTimeout(resolve, ms); });
        });
        this.registerFunction('now', function () {
            return Date.now();
        });
        // 实体函数
        this.registerFunction('getEntity', function (id) {
            return _this.world.getEntity(id);
        });
        this.registerFunction('createEntity', function (name) {
            return _this.world.createEntity(name);
        });
        this.registerFunction('destroyEntity', function (entity) {
            _this.world.removeEntity(entity);
        });
        // 组件函数
        this.registerFunction('getComponent', function (entity, type) {
            return entity.getComponent(type);
        });
        this.registerFunction('addComponent', function (entity, type, options) {
            return entity.addComponent(type, options);
        });
        this.registerFunction('removeComponent', function (entity, type) {
            entity.removeComponent(type);
        });
        // 数学函数
        this.registerFunction('random', function (min, max) {
            return Math.random() * (max - min) + min;
        });
        this.registerFunction('randomInt', function (min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        });
        this.registerFunction('clamp', function (value, min, max) {
            return Math.min(Math.max(value, min), max);
        });
        this.registerFunction('lerp', function (a, b, t) {
            return a + (b - a) * t;
        });
        // 字符串函数
        this.registerFunction('concat', function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            return args.join('');
        });
        this.registerFunction('format', function (template) {
            var args = [];
            for (var _i = 1; _i < arguments.length; _i++) {
                args[_i - 1] = arguments[_i];
            }
            return template.replace(/{(\d+)}/g, function (match, index) {
                return args[index] !== undefined ? args[index] : match;
            });
        });
        // 数组函数
        this.registerFunction('createArray', function () {
            var items = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                items[_i] = arguments[_i];
            }
            return __spreadArray([], items, true);
        });
        this.registerFunction('arrayLength', function (array) {
            return array.length;
        });
        this.registerFunction('arrayGet', function (array, index) {
            return array[index];
        });
        this.registerFunction('arraySet', function (array, index, value) {
            array[index] = value;
            return array;
        });
        this.registerFunction('arrayPush', function (array, value) {
            array.push(value);
            return array;
        });
        this.registerFunction('arrayPop', function (array) {
            return array.pop();
        });
        // 对象函数
        this.registerFunction('createObject', function (properties) {
            return properties || {};
        });
        this.registerFunction('objectGet', function (object, key) {
            return object[key];
        });
        this.registerFunction('objectSet', function (object, key, value) {
            object[key] = value;
            return object;
        });
        this.registerFunction('objectKeys', function (object) {
            return Object.keys(object);
        });
        this.registerFunction('objectValues', function (object) {
            return Object.values(object);
        });
    };
    /**
     * 设置变量
     * @param name 变量名称
     * @param value 变量值
     */
    ExecutionContext.prototype.setVariable = function (name, value) {
        this.variables.set(name, value);
    };
    /**
     * 获取变量
     * @param name 变量名称
     * @returns 变量值
     */
    ExecutionContext.prototype.getVariable = function (name) {
        return this.variables.get(name);
    };
    /**
     * 删除变量
     * @param name 变量名称
     * @returns 是否删除成功
     */
    ExecutionContext.prototype.deleteVariable = function (name) {
        return this.variables.delete(name);
    };
    /**
     * 注册函数
     * @param name 函数名称
     * @param func 函数实现
     */
    ExecutionContext.prototype.registerFunction = function (name, func) {
        this.functions.set(name, func);
    };
    /**
     * 获取函数
     * @param name 函数名称
     * @returns 函数实现
     */
    ExecutionContext.prototype.getFunction = function (name) {
        return this.functions.get(name);
    };
    /**
     * 调用函数
     * @param name 函数名称
     * @param args 函数参数
     * @returns 函数返回值
     */
    ExecutionContext.prototype.callFunction = function (name) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        var func = this.getFunction(name);
        if (!func) {
            throw new Error("\u672A\u627E\u5230\u51FD\u6570: ".concat(name));
        }
        return func.apply(void 0, args);
    };
    /**
     * 清空上下文
     */
    ExecutionContext.prototype.clear = function () {
        // 清空变量
        this.variables.clear();
        // 清空函数（保留内置函数）
        var builtinFunctions = new Map(this.functions);
        this.functions.clear();
        // 重新注册内置函数
        this.registerBuiltinFunctions();
    };
    return ExecutionContext;
}());
exports.ExecutionContext = ExecutionContext;
