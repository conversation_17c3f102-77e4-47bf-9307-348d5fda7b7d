"use strict";
/**
 * InteractionSystem.ts
 *
 * 交互系统，用于处理3D场景中的对象交互
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionSystem = void 0;
var System_1 = require("../core/System");
var three_1 = require("three");
var InputSystem_1 = require("../input/InputSystem");
var Debug_1 = require("../utils/Debug");
/**
 * 交互系统
 * 用于处理3D场景中的对象交互
 */
var InteractionSystem = exports.InteractionSystem = /** @class */ (function (_super) {
    __extends(InteractionSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 交互系统配置
     */
    function InteractionSystem(world, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级（默认为0）
        _super.call(this, 0) || this;
        /** 可交互组件列表 */
        _this.interactableComponents = new Map();
        /** 交互事件组件列表 */
        _this.interactionEventComponents = new Map();
        /** 当前可用的交互对象列表（按距离排序） */
        _this.availableInteractables = [];
        /** 射线投射器 */
        _this.raycaster = new three_1.Raycaster();
        /** 交互检测计时器 */
        _this.interactionCheckTimer = 0;
        /** 交互检测间隔（秒） */
        _this.interactionCheckInterval = 0.1;
        // 保存世界引用
        _this.world = world;
        // 初始化配置
        _this.config = {
            debug: config.debug || false,
            maxInteractionDistance: config.maxInteractionDistance || 5,
            enableFrustumCheck: config.enableFrustumCheck !== undefined ? config.enableFrustumCheck : true,
            enableHighlight: config.enableHighlight !== undefined ? config.enableHighlight : true,
            enablePrompt: config.enablePrompt !== undefined ? config.enablePrompt : true,
            enableSound: config.enableSound !== undefined ? config.enableSound : true
        };
        // 获取输入系统引用
        _this.inputSystem = world.getSystem(InputSystem_1.InputSystem.NAME);
        if (!_this.inputSystem) {
            Debug_1.Debug.warn('InteractionSystem', 'InputSystem not found, some features may not work properly');
        }
        return _this;
    }
    /**
     * 注册可交互组件
     * @param entity 实体
     * @param component 可交互组件
     */
    InteractionSystem.prototype.registerInteractableComponent = function (entity, component) {
        this.interactableComponents.set(entity, component);
        if (this.config.debug) {
            Debug_1.Debug.log('InteractionSystem', "Registered interactable component for entity ".concat(entity.id));
        }
    };
    /**
     * 注销可交互组件
     * @param entity 实体
     */
    InteractionSystem.prototype.unregisterInteractableComponent = function (entity) {
        // 从高亮和最近的交互对象中移除
        if (this.highlightedInteractable === entity) {
            this.highlightedInteractable = undefined;
        }
        if (this.closestInteractable === entity) {
            this.closestInteractable = undefined;
        }
        // 从可用交互对象列表中移除
        var index = this.availableInteractables.indexOf(entity);
        if (index !== -1) {
            this.availableInteractables.splice(index, 1);
        }
        // 从组件列表中移除
        this.interactableComponents.delete(entity);
        if (this.config.debug) {
            Debug_1.Debug.log('InteractionSystem', "Unregistered interactable component for entity ".concat(entity.id));
        }
    };
    /**
     * 注册交互事件组件
     * @param entity 实体
     * @param component 交互事件组件
     */
    InteractionSystem.prototype.registerInteractionEventComponent = function (entity, component) {
        this.interactionEventComponents.set(entity, component);
        if (this.config.debug) {
            Debug_1.Debug.log('InteractionSystem', "Registered interaction event component for entity ".concat(entity.id));
        }
    };
    /**
     * 注销交互事件组件
     * @param entity 实体
     */
    InteractionSystem.prototype.unregisterInteractionEventComponent = function (entity) {
        this.interactionEventComponents.delete(entity);
        if (this.config.debug) {
            Debug_1.Debug.log('InteractionSystem', "Unregistered interaction event component for entity ".concat(entity.id));
        }
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    InteractionSystem.prototype.update = function (deltaTime) {
        // 更新交互检测计时器
        this.interactionCheckTimer += deltaTime;
        // 每隔一段时间检测一次可交互对象
        if (this.interactionCheckTimer >= this.interactionCheckInterval) {
            this.interactionCheckTimer = 0;
            this.gatherAvailableInteractables();
        }
        // 处理输入
        this.handleInput();
        // 更新高亮效果
        this.updateHighlight();
    };
    /**
     * 收集可用的交互对象
     */
    InteractionSystem.prototype.gatherAvailableInteractables = function () {
        // 清空可用交互对象列表
        this.availableInteractables = [];
        // 获取主相机
        var camera = this.world.getMainCamera();
        if (!camera)
            return;
        // 获取相机位置
        var cameraPosition = camera.position.clone();
        // 遍历所有可交互组件
        for (var _i = 0, _a = this.interactableComponents; _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], component = _b[1];
            // 检查是否可见和可交互
            if (!component.visible || !component.interactive)
                continue;
            // 获取对象位置
            var position = component.getWorldPosition();
            // 计算距离
            var distance = position.distanceTo(cameraPosition);
            // 检查是否在最大交互距离内
            if (distance > this.config.maxInteractionDistance)
                continue;
            // 如果启用视锥体检测，检查是否在视锥体内
            if (this.config.enableFrustumCheck && !this.isInFrustum(entity, camera))
                continue;
            // 添加到可用交互对象列表
            this.availableInteractables.push(entity);
        }
        // 按距离排序
        this.sortInteractablesByDistance(cameraPosition);
        // 更新最近的交互对象
        this.closestInteractable = this.availableInteractables.length > 0 ? this.availableInteractables[0] : undefined;
    };
    /**
     * 检查对象是否在视锥体内
     * @param entity 实体
     * @param camera 相机
     * @returns 是否在视锥体内
     */
    InteractionSystem.prototype.isInFrustum = function (entity, camera) {
        // 获取对象的3D表示
        var object = this.getObject3D(entity);
        if (!object)
            return false;
        // 更新相机的视锥体
        camera.updateMatrixWorld();
        // 检查对象是否在视锥体内
        var frustum = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);
        return object.visible;
    };
    /**
     * 按距离排序交互对象
     * @param cameraPosition 相机位置
     */
    InteractionSystem.prototype.sortInteractablesByDistance = function (cameraPosition) {
        var _this = this;
        this.availableInteractables.sort(function (a, b) {
            var componentA = _this.interactableComponents.get(a);
            var componentB = _this.interactableComponents.get(b);
            if (!componentA || !componentB)
                return 0;
            var positionA = componentA.getWorldPosition();
            var positionB = componentB.getWorldPosition();
            var distanceA = positionA.distanceTo(cameraPosition);
            var distanceB = positionB.distanceTo(cameraPosition);
            return distanceA - distanceB;
        });
    };
    /**
     * 处理输入
     */
    InteractionSystem.prototype.handleInput = function () {
        // 如果没有输入系统，则返回
        if (!this.inputSystem)
            return;
        // 检查交互键是否按下
        if (this.inputSystem.isKeyDown('e') || this.inputSystem.isKeyDown('E')) {
            this.interactWithClosestInteractable();
        }
    };
    /**
     * 与最近的交互对象交互
     */
    InteractionSystem.prototype.interactWithClosestInteractable = function () {
        if (!this.closestInteractable)
            return;
        var component = this.interactableComponents.get(this.closestInteractable);
        if (!component)
            return;
        // 调用交互回调
        component.interact();
        // 如果启用声音，播放交互声音
        if (this.config.enableSound && component.interactionSound) {
            // 尝试获取音频系统并播放声音
            var audioSystem = this.world.getSystem('AudioSystem');
            if (audioSystem && typeof audioSystem.playSound === 'function') {
                audioSystem.playSound(component.interactionSound);
            }
            else if (this.config.debug) {
                Debug_1.Debug.warn('InteractionSystem', 'AudioSystem not found or playSound method not available');
            }
        }
    };
    /**
     * 更新高亮效果
     */
    InteractionSystem.prototype.updateHighlight = function () {
        // 如果未启用高亮，则返回
        if (!this.config.enableHighlight)
            return;
        // 如果最近的交互对象改变，更新高亮
        if (this.closestInteractable !== this.highlightedInteractable) {
            // 移除旧的高亮
            if (this.highlightedInteractable) {
                var oldComponent = this.interactableComponents.get(this.highlightedInteractable);
                if (oldComponent) {
                    oldComponent.setHighlighted(false);
                }
            }
            // 添加新的高亮
            if (this.closestInteractable) {
                var newComponent = this.interactableComponents.get(this.closestInteractable);
                if (newComponent) {
                    newComponent.setHighlighted(true);
                }
            }
            // 更新高亮对象
            this.highlightedInteractable = this.closestInteractable;
        }
    };
    /**
     * 获取对象的3D表示
     * @param entity 实体
     * @returns 3D对象
     */
    InteractionSystem.prototype.getObject3D = function (entity) {
        // 从实体中获取Transform组件
        var transform = entity.getComponent('Transform');
        if (transform) {
            // 使用Transform组件的getObject3D方法获取3D对象
            return transform.getObject3D();
        }
        // 如果没有Transform组件，尝试从实体中获取mesh属性（用于示例代码兼容）
        if (entity.mesh instanceof three_1.Object3D) {
            return entity.mesh;
        }
        return null;
    };
    /**
     * 销毁系统
     */
    InteractionSystem.prototype.dispose = function () {
        // 清空所有列表
        this.interactableComponents.clear();
        this.interactionEventComponents.clear();
        this.availableInteractables = [];
        this.closestInteractable = undefined;
        this.highlightedInteractable = undefined;
        if (this.config.debug) {
            Debug_1.Debug.log('InteractionSystem', 'Disposed');
        }
    };
    /** 系统名称 */
    InteractionSystem.NAME = 'InteractionSystem';
    return InteractionSystem;
}(System_1.System));
