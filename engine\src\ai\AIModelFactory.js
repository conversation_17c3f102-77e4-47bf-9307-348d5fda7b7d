"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelFactory = void 0;
/**
 * AI模型工厂
 * 用于创建和管理AI模型实例
 */
var AIModelType_1 = require("./AIModelType");
var GPTModel_1 = require("./models/GPTModel");
var StableDiffusionModel_1 = require("./models/StableDiffusionModel");
var BERTModel_1 = require("./models/BERTModel");
var RoBERTaModel_1 = require("./models/RoBERTaModel");
var DistilBERTModel_1 = require("./models/DistilBERTModel");
var ALBERTModel_1 = require("./models/ALBERTModel");
var XLNetModel_1 = require("./models/XLNetModel");
var BARTModel_1 = require("./models/BARTModel");
var T5Model_1 = require("./models/T5Model");
/**
 * AI模型工厂
 */
var AIModelFactory = exports.AIModelFactory = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 配置
     */
    function AIModelFactory(config) {
        if (config === void 0) { config = {}; }
        /** 模型缓存 */
        this.modelCache = new Map();
        this.config = __assign(__assign({}, AIModelFactory.DEFAULT_CONFIG), config);
    }
    /**
     * 创建模型
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型实例
     */
    AIModelFactory.prototype.createModel = function (modelType, config) {
        if (config === void 0) { config = {}; }
        try {
            // 生成模型ID
            var modelId = this.generateModelId(modelType, config);
            // 检查缓存
            if (this.modelCache.has(modelId)) {
                return this.modelCache.get(modelId) || null;
            }
            // 创建模型实例
            var model = null;
            // 根据模型类型创建不同的模型实例
            switch (modelType) {
                case AIModelType_1.AIModelType.GPT:
                    model = new GPTModel_1.GPTModel(config, this.config);
                    break;
                case AIModelType_1.AIModelType.STABLE_DIFFUSION:
                    model = new StableDiffusionModel_1.StableDiffusionModel(config, this.config);
                    break;
                case AIModelType_1.AIModelType.BERT:
                    model = new BERTModel_1.BERTModel(config, this.config);
                    break;
                case AIModelType_1.AIModelType.ROBERTA:
                    // 转换为RoBERTa特定配置
                    model = new RoBERTaModel_1.RoBERTaModel(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                case AIModelType_1.AIModelType.DISTILBERT:
                    // 转换为DistilBERT特定配置
                    model = new DistilBERTModel_1.DistilBERTModel(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                case AIModelType_1.AIModelType.ALBERT:
                    // 转换为ALBERT特定配置
                    model = new ALBERTModel_1.ALBERTModel(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                case AIModelType_1.AIModelType.XLNET:
                    // 转换为XLNet特定配置
                    model = new XLNetModel_1.XLNetModel(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                case AIModelType_1.AIModelType.BART:
                    // 转换为BART特定配置
                    model = new BARTModel_1.BARTModel(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                case AIModelType_1.AIModelType.T5:
                    // 转换为T5特定配置
                    model = new T5Model_1.T5Model(__assign(__assign({}, config), { variant: config.variant }), this.config);
                    break;
                default:
                    console.error("\u4E0D\u652F\u6301\u7684\u6A21\u578B\u7C7B\u578B: ".concat(modelType));
                    return null;
            }
            // 添加到缓存
            if (model) {
                this.modelCache.set(modelId, model);
            }
            return model;
        }
        catch (error) {
            console.error("\u521B\u5EFA\u6A21\u578B\u5B9E\u4F8B\u5931\u8D25: ".concat(error));
            return null;
        }
    };
    /**
     * 生成模型ID
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型ID
     */
    AIModelFactory.prototype.generateModelId = function (modelType, config) {
        // 基本ID
        var id = "".concat(modelType);
        // 添加版本信息
        if (config.version) {
            id += "-".concat(config.version);
        }
        else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
            id += "-".concat(this.config.modelVersions[modelType]);
        }
        // 添加其他配置信息
        if (config.variant) {
            id += "-".concat(config.variant);
        }
        return id;
    };
    /**
     * 获取模型
     * @param modelId 模型ID
     * @returns 模型实例
     */
    AIModelFactory.prototype.getModel = function (modelId) {
        return this.modelCache.get(modelId) || null;
    };
    /**
     * 获取所有模型
     * @returns 模型实例映射
     */
    AIModelFactory.prototype.getAllModels = function () {
        return new Map(this.modelCache);
    };
    /**
     * 释放模型
     * @param modelId 模型ID
     * @returns 是否成功
     */
    AIModelFactory.prototype.releaseModel = function (modelId) {
        // 检查模型是否存在
        if (!this.modelCache.has(modelId)) {
            return false;
        }
        // 获取模型
        var model = this.modelCache.get(modelId);
        // 释放模型
        if (model) {
            model.dispose();
        }
        // 从缓存中移除
        this.modelCache.delete(modelId);
        return true;
    };
    /**
     * 释放所有模型
     */
    AIModelFactory.prototype.releaseAllModels = function () {
        // 释放所有模型
        for (var _i = 0, _a = this.modelCache.values(); _i < _a.length; _i++) {
            var model = _a[_i];
            model.dispose();
        }
        // 清空缓存
        this.modelCache.clear();
    };
    /**
     * 销毁
     */
    AIModelFactory.prototype.dispose = function () {
        this.releaseAllModels();
    };
    /** 默认配置 */
    AIModelFactory.DEFAULT_CONFIG = {
        debug: false,
        useLocalModels: false,
        apiKeys: {},
        baseUrls: {},
        modelVersions: {}
    };
    return AIModelFactory;
}());
