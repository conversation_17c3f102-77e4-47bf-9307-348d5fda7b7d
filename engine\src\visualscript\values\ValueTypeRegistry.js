"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValueTypeRegistry = void 0;
/**
 * 视觉脚本值类型注册表
 * 用于注册和管理值类型
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 值类型注册表
 * 用于注册和管理值类型
 */
var ValueTypeRegistry = /** @class */ (function (_super) {
    __extends(ValueTypeRegistry, _super);
    /**
     * 创建值类型注册表
     */
    function ValueTypeRegistry() {
        var _this = _super.call(this) || this;
        /** 值类型映射 */
        _this.valueTypes = new Map();
        /** 值类型标签映射 */
        _this.tags = new Map();
        // 注册内置值类型
        _this.registerBuiltinValueTypes();
        return _this;
    }
    /**
     * 注册内置值类型
     */
    ValueTypeRegistry.prototype.registerBuiltinValueTypes = function () {
        // 注册布尔类型
        this.registerValueType({
            type: 'boolean',
            label: '布尔值',
            description: '布尔值类型，表示真或假',
            icon: 'boolean',
            color: '#4CAF50',
            creator: function () { return false; },
            validator: function (value) { return typeof value === 'boolean'; },
            converter: function (value) { return Boolean(value); },
            primitive: true,
            tags: ['primitive', 'logic']
        });
        // 注册数字类型
        this.registerValueType({
            type: 'number',
            label: '数字',
            description: '数字类型，表示整数或浮点数',
            icon: 'number',
            color: '#2196F3',
            creator: function () { return 0; },
            validator: function (value) { return typeof value === 'number' && !isNaN(value); },
            converter: function (value) { return Number(value); },
            primitive: true,
            tags: ['primitive', 'math']
        });
        // 注册整数类型
        this.registerValueType({
            type: 'integer',
            label: '整数',
            description: '整数类型，表示整数',
            icon: 'integer',
            color: '#1976D2',
            creator: function () { return 0; },
            validator: function (value) { return typeof value === 'number' && !isNaN(value) && Number.isInteger(value); },
            converter: function (value) { return Math.floor(Number(value)); },
            primitive: true,
            tags: ['primitive', 'math']
        });
        // 注册字符串类型
        this.registerValueType({
            type: 'string',
            label: '字符串',
            description: '字符串类型，表示文本',
            icon: 'string',
            color: '#FF9800',
            creator: function () { return ''; },
            validator: function (value) { return typeof value === 'string'; },
            converter: function (value) { return String(value); },
            primitive: true,
            tags: ['primitive', 'text']
        });
        // 注册数组类型
        this.registerValueType({
            type: 'array',
            label: '数组',
            description: '数组类型，表示一组值',
            icon: 'array',
            color: '#9C27B0',
            creator: function () { return []; },
            validator: function (value) { return Array.isArray(value); },
            converter: function (value) { return Array.isArray(value) ? value : [value]; },
            composite: true,
            tags: ['composite', 'collection']
        });
        // 注册对象类型
        this.registerValueType({
            type: 'object',
            label: '对象',
            description: '对象类型，表示键值对集合',
            icon: 'object',
            color: '#F44336',
            creator: function () { return ({}); },
            validator: function (value) { return typeof value === 'object' && value !== null && !Array.isArray(value); },
            converter: function (value) { return typeof value === 'object' && value !== null ? value : { value: value }; },
            composite: true,
            tags: ['composite', 'collection']
        });
        // 注册向量2类型
        this.registerValueType({
            type: 'vector2',
            label: '向量2',
            description: '二维向量类型，表示二维空间中的点或向量',
            icon: 'vector2',
            color: '#00BCD4',
            creator: function () { return ({ x: 0, y: 0 }); },
            validator: function (value) {
                return typeof value === 'object' &&
                    value !== null &&
                    'x' in value &&
                    'y' in value &&
                    typeof value.x === 'number' &&
                    typeof value.y === 'number';
            },
            converter: function (value) {
                if (typeof value === 'object' && value !== null) {
                    return {
                        x: typeof value.x === 'number' ? value.x : 0,
                        y: typeof value.y === 'number' ? value.y : 0
                    };
                }
                return { x: 0, y: 0 };
            },
            composite: true,
            tags: ['composite', 'math', 'vector']
        });
        // 注册向量3类型
        this.registerValueType({
            type: 'vector3',
            label: '向量3',
            description: '三维向量类型，表示三维空间中的点或向量',
            icon: 'vector3',
            color: '#009688',
            creator: function () { return ({ x: 0, y: 0, z: 0 }); },
            validator: function (value) {
                return typeof value === 'object' &&
                    value !== null &&
                    'x' in value &&
                    'y' in value &&
                    'z' in value &&
                    typeof value.x === 'number' &&
                    typeof value.y === 'number' &&
                    typeof value.z === 'number';
            },
            converter: function (value) {
                if (typeof value === 'object' && value !== null) {
                    return {
                        x: typeof value.x === 'number' ? value.x : 0,
                        y: typeof value.y === 'number' ? value.y : 0,
                        z: typeof value.z === 'number' ? value.z : 0
                    };
                }
                return { x: 0, y: 0, z: 0 };
            },
            composite: true,
            tags: ['composite', 'math', 'vector']
        });
        // 注册颜色类型
        this.registerValueType({
            type: 'color',
            label: '颜色',
            description: '颜色类型，表示RGBA颜色',
            icon: 'color',
            color: '#E91E63',
            creator: function () { return ({ r: 1, g: 1, b: 1, a: 1 }); },
            validator: function (value) {
                return typeof value === 'object' &&
                    value !== null &&
                    'r' in value &&
                    'g' in value &&
                    'b' in value &&
                    typeof value.r === 'number' &&
                    typeof value.g === 'number' &&
                    typeof value.b === 'number';
            },
            converter: function (value) {
                if (typeof value === 'object' && value !== null) {
                    return {
                        r: typeof value.r === 'number' ? value.r : 1,
                        g: typeof value.g === 'number' ? value.g : 1,
                        b: typeof value.b === 'number' ? value.b : 1,
                        a: typeof value.a === 'number' ? value.a : 1
                    };
                }
                return { r: 1, g: 1, b: 1, a: 1 };
            },
            composite: true,
            tags: ['composite', 'visual']
        });
    };
    /**
     * 注册值类型
     * @param info 值类型信息
     * @returns 是否注册成功
     */
    ValueTypeRegistry.prototype.registerValueType = function (info) {
        // 检查是否已存在
        if (this.valueTypes.has(info.type)) {
            console.warn("\u503C\u7C7B\u578B\u5DF2\u5B58\u5728: ".concat(info.type));
            return false;
        }
        // 注册值类型
        this.valueTypes.set(info.type, info);
        // 添加到标签映射
        if (info.tags) {
            for (var _i = 0, _a = info.tags; _i < _a.length; _i++) {
                var tag = _a[_i];
                var tagSet = this.tags.get(tag) || new Set();
                tagSet.add(info.type);
                this.tags.set(tag, tagSet);
            }
        }
        // 触发注册事件
        this.emit('valueTypeRegistered', info);
        return true;
    };
    /**
     * 注销值类型
     * @param type 值类型名称
     * @returns 是否注销成功
     */
    ValueTypeRegistry.prototype.unregisterValueType = function (type) {
        // 检查是否存在
        var info = this.valueTypes.get(type);
        if (!info) {
            return false;
        }
        // 从标签映射中移除
        if (info.tags) {
            for (var _i = 0, _a = info.tags; _i < _a.length; _i++) {
                var tag = _a[_i];
                var tagSet = this.tags.get(tag);
                if (tagSet) {
                    tagSet.delete(type);
                    // 如果标签集合为空，移除标签
                    if (tagSet.size === 0) {
                        this.tags.delete(tag);
                    }
                }
            }
        }
        // 从值类型映射中移除
        this.valueTypes.delete(type);
        // 触发注销事件
        this.emit('valueTypeUnregistered', info);
        return true;
    };
    /**
     * 获取值类型信息
     * @param type 值类型名称
     * @returns 值类型信息
     */
    ValueTypeRegistry.prototype.getValueTypeInfo = function (type) {
        return this.valueTypes.get(type);
    };
    /**
     * 获取所有值类型
     * @returns 值类型信息列表
     */
    ValueTypeRegistry.prototype.getAllValueTypes = function () {
        return Array.from(this.valueTypes.values());
    };
    /**
     * 获取指定标签的值类型
     * @param tag 标签
     * @returns 值类型信息列表
     */
    ValueTypeRegistry.prototype.getValueTypesByTag = function (tag) {
        var _this = this;
        var tagSet = this.tags.get(tag);
        if (!tagSet) {
            return [];
        }
        return Array.from(tagSet).map(function (type) { return _this.valueTypes.get(type); });
    };
    /**
     * 获取所有标签
     * @returns 标签列表
     */
    ValueTypeRegistry.prototype.getAllTags = function () {
        return Array.from(this.tags.keys());
    };
    /**
     * 创建值
     * @param type 值类型名称
     * @returns 创建的值
     */
    ValueTypeRegistry.prototype.createValue = function (type) {
        var info = this.valueTypes.get(type);
        if (!info) {
            throw new Error("\u672A\u77E5\u503C\u7C7B\u578B: ".concat(type));
        }
        return info.creator();
    };
    /**
     * 验证值
     * @param type 值类型名称
     * @param value 要验证的值
     * @returns 是否有效
     */
    ValueTypeRegistry.prototype.validateValue = function (type, value) {
        var info = this.valueTypes.get(type);
        if (!info) {
            throw new Error("\u672A\u77E5\u503C\u7C7B\u578B: ".concat(type));
        }
        return info.validator(value);
    };
    /**
     * 转换值
     * @param type 值类型名称
     * @param value 要转换的值
     * @returns 转换后的值
     */
    ValueTypeRegistry.prototype.convertValue = function (type, value) {
        var info = this.valueTypes.get(type);
        if (!info) {
            throw new Error("\u672A\u77E5\u503C\u7C7B\u578B: ".concat(type));
        }
        if (info.converter) {
            return info.converter(value);
        }
        return value;
    };
    /**
     * 清空注册表
     */
    ValueTypeRegistry.prototype.clear = function () {
        // 清空值类型映射
        this.valueTypes.clear();
        // 清空标签映射
        this.tags.clear();
        // 重新注册内置值类型
        this.registerBuiltinValueTypes();
        // 触发清空事件
        this.emit('cleared');
    };
    return ValueTypeRegistry;
}(EventEmitter_1.EventEmitter));
exports.ValueTypeRegistry = ValueTypeRegistry;
