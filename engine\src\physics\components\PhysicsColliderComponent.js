"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsColliderComponent = exports.ColliderType = void 0;
/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
var CANNON = require("cannon-es");
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * 碰撞器类型
 */
var ColliderType;
(function (ColliderType) {
    /** 盒体 */
    ColliderType["BOX"] = "box";
    /** 球体 */
    ColliderType["SPHERE"] = "sphere";
    /** 圆柱体 */
    ColliderType["CYLINDER"] = "cylinder";
    /** 胶囊体 */
    ColliderType["CAPSULE"] = "capsule";
    /** 凸包 */
    ColliderType["CONVEX"] = "convex";
    /** 三角网格 */
    ColliderType["TRIMESH"] = "trimesh";
    /** 平面 */
    ColliderType["PLANE"] = "plane";
    /** 复合 */
    ColliderType["COMPOUND"] = "compound";
    /** 高度场 */
    ColliderType["HEIGHTFIELD"] = "heightfield";
})(ColliderType || (exports.ColliderType = ColliderType = {}));
/**
 * 物理碰撞器组件
 */
var PhysicsColliderComponent = exports.PhysicsColliderComponent = /** @class */ (function (_super) {
    __extends(PhysicsColliderComponent, _super);
    /**
     * 创建物理碰撞器组件
     * @param options 碰撞器选项
     */
    function PhysicsColliderComponent(options) {
        var _this = _super.call(this, PhysicsColliderComponent.type) || this;
        /** 碰撞形状列表 */
        _this.shapes = [];
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        _this.colliderType = options.type;
        _this.params = options.params || {};
        _this.offset = options.offset ? options.offset.clone() : new THREE.Vector3();
        _this.orientation = options.orientation ? options.orientation.clone() : new THREE.Quaternion();
        _this.isTrigger = options.isTrigger || false;
        return _this;
    }
    /**
     * 初始化碰撞器
     * @param _world 物理世界（未使用）
     */
    PhysicsColliderComponent.prototype.initialize = function (_world) {
        if (this.initialized || !this.getEntity() || this.destroyed)
            return;
        // 创建碰撞形状
        var shape = this.createShape();
        if (shape) {
            // 设置是否触发器
            if (this.isTrigger) {
                shape.collisionResponse = false;
            }
            // 添加到形状列表
            this.shapes.push({
                shape: shape,
                offset: new CANNON.Vec3(this.offset.x, this.offset.y, this.offset.z),
                orientation: new CANNON.Quaternion(this.orientation.x, this.orientation.y, this.orientation.z, this.orientation.w)
            });
        }
        this.initialized = true;
    };
    /**
     * 创建碰撞形状
     * @returns 碰撞形状
     */
    PhysicsColliderComponent.prototype.createShape = function () {
        // 从实体的变换组件获取缩放
        var entity = this.getEntity();
        var transform = entity === null || entity === void 0 ? void 0 : entity.getTransform();
        var scale = transform ? transform.getScale() : new THREE.Vector3(1, 1, 1);
        switch (this.colliderType) {
            case ColliderType.BOX:
                return this.createBoxShape(scale);
            case ColliderType.SPHERE:
                return this.createSphereShape(scale);
            case ColliderType.CYLINDER:
                return this.createCylinderShape(scale);
            case ColliderType.CAPSULE:
                return this.createCapsuleShape(scale);
            case ColliderType.CONVEX:
                return this.createConvexShape();
            case ColliderType.TRIMESH:
                return this.createTrimeshShape();
            case ColliderType.PLANE:
                return this.createPlaneShape();
            case ColliderType.HEIGHTFIELD:
                return this.createHeightfieldShape();
            default:
                console.warn("\u4E0D\u652F\u6301\u7684\u78B0\u649E\u5668\u7C7B\u578B: ".concat(this.colliderType));
                return null;
        }
    };
    /**
     * 创建盒体形状
     * @param scale 缩放
     * @returns 盒体形状
     */
    PhysicsColliderComponent.prototype.createBoxShape = function (scale) {
        var halfExtents = this.params.halfExtents || { x: 0.5, y: 0.5, z: 0.5 };
        return new CANNON.Box(new CANNON.Vec3(halfExtents.x * scale.x, halfExtents.y * scale.y, halfExtents.z * scale.z));
    };
    /**
     * 创建球体形状
     * @param scale 缩放
     * @returns 球体形状
     */
    PhysicsColliderComponent.prototype.createSphereShape = function (scale) {
        var radius = this.params.radius || 0.5;
        // 使用最大缩放值作为球体半径的缩放
        var maxScale = Math.max(scale.x, scale.y, scale.z);
        return new CANNON.Sphere(radius * maxScale);
    };
    /**
     * 创建圆柱体形状
     * @param scale 缩放
     * @returns 圆柱体形状
     */
    PhysicsColliderComponent.prototype.createCylinderShape = function (scale) {
        var radiusTop = (this.params.radiusTop || 0.5) * Math.max(scale.x, scale.z);
        var radiusBottom = (this.params.radiusBottom || 0.5) * Math.max(scale.x, scale.z);
        var height = (this.params.height || 1) * scale.y;
        var numSegments = this.params.numSegments || 8;
        return new CANNON.Cylinder(radiusTop, radiusBottom, height, numSegments);
    };
    /**
     * 创建胶囊体形状
     * @param scale 缩放
     * @returns 胶囊体形状
     */
    PhysicsColliderComponent.prototype.createCapsuleShape = function (scale) {
        // CANNON.js没有内置的胶囊体，使用圆柱体代替
        var radius = (this.params.radius || 0.5) * Math.max(scale.x, scale.z);
        var height = (this.params.height || 1) * scale.y;
        var numSegments = this.params.numSegments || 8;
        // 创建圆柱体
        return new CANNON.Cylinder(radius, radius, height, numSegments);
    };
    /**
     * 创建凸包形状
     * @returns 凸包形状
     */
    PhysicsColliderComponent.prototype.createConvexShape = function () {
        // 需要顶点和面
        var vertices = this.params.vertices;
        var faces = this.params.faces;
        if (!vertices || !faces) {
            console.warn('创建凸包形状需要顶点和面数据');
            return null;
        }
        // 转换顶点为CANNON.Vec3
        var cannonVertices = vertices.map(function (v) { return new CANNON.Vec3(v.x, v.y, v.z); });
        return new CANNON.ConvexPolyhedron({
            vertices: cannonVertices,
            faces: faces
        });
    };
    /**
     * 创建三角网格形状
     * @returns 三角网格形状
     */
    PhysicsColliderComponent.prototype.createTrimeshShape = function () {
        // 需要顶点和索引
        var vertices = this.params.vertices;
        var indices = this.params.indices;
        if (!vertices || !indices) {
            // 尝试从网格组件获取几何体
            var entity = this.getEntity();
            var meshComponent = entity === null || entity === void 0 ? void 0 : entity.getComponent('MeshComponent');
            if (meshComponent) {
                // 使用类型断言访问 mesh 属性
                var mesh = meshComponent.mesh;
                if (mesh && mesh.geometry) {
                    return this.createTrimeshFromGeometry(mesh.geometry);
                }
            }
            console.warn('创建三角网格形状需要顶点和索引数据');
            return null;
        }
        // 创建三角网格
        return new CANNON.Trimesh(vertices, indices);
    };
    /**
     * 从Three.js几何体创建三角网格形状
     * @param geometry Three.js几何体
     * @returns 三角网格形状
     */
    PhysicsColliderComponent.prototype.createTrimeshFromGeometry = function (geometry) {
        // 确保几何体已经索引化
        if (!geometry.index) {
            geometry = geometry.toNonIndexed();
        }
        // 获取顶点和索引
        var position = geometry.getAttribute('position');
        var index = geometry.index;
        if (!position || !index) {
            console.warn('几何体缺少位置或索引属性');
            return null;
        }
        // 创建顶点数组
        var vertices = new Float32Array(position.count * 3);
        for (var i = 0; i < position.count; i++) {
            vertices[i * 3] = position.getX(i);
            vertices[i * 3 + 1] = position.getY(i);
            vertices[i * 3 + 2] = position.getZ(i);
        }
        // 创建索引数组
        var indices = new Uint32Array(index.count);
        for (var i = 0; i < index.count; i++) {
            indices[i] = index.getX(i);
        }
        // 创建三角网格
        // 将 TypedArray 转换为普通数组
        var verticesArray = Array.from(vertices);
        var indicesArray = Array.from(indices);
        return new CANNON.Trimesh(verticesArray, indicesArray);
    };
    /**
     * 创建平面形状
     * @returns 平面形状
     */
    PhysicsColliderComponent.prototype.createPlaneShape = function () {
        return new CANNON.Plane();
    };
    /**
     * 创建高度场形状
     * @returns 高度场形状
     */
    PhysicsColliderComponent.prototype.createHeightfieldShape = function () {
        var data = this.params.data;
        var elementSize = this.params.elementSize || 1;
        if (!data) {
            console.warn('创建高度场形状需要高度数据');
            return null;
        }
        return new CANNON.Heightfield(data, {
            elementSize: elementSize
        });
    };
    /**
     * 获取碰撞形状列表
     * @returns 碰撞形状列表
     */
    PhysicsColliderComponent.prototype.getShapes = function () {
        return this.shapes;
    };
    /**
     * 设置是否触发器
     * @param isTrigger 是否触发器
     */
    PhysicsColliderComponent.prototype.setTrigger = function (isTrigger) {
        this.isTrigger = isTrigger;
        // 更新所有形状
        for (var _i = 0, _a = this.shapes; _i < _a.length; _i++) {
            var shape = _a[_i];
            shape.shape.collisionResponse = !isTrigger;
        }
    };
    /**
     * 是否触发器
     * @returns 是否触发器
     */
    PhysicsColliderComponent.prototype.isTriggerCollider = function () {
        return this.isTrigger;
    };
    /**
     * 销毁碰撞器
     */
    PhysicsColliderComponent.prototype.dispose = function () {
        if (this.destroyed)
            return;
        // 清空形状列表
        this.shapes = [];
        this.initialized = false;
        this.destroyed = true;
        // 调用基类的 dispose 方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsColliderComponent.type = 'PhysicsColliderComponent';
    return PhysicsColliderComponent;
}(Component_1.Component));
