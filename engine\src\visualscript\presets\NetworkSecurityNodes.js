"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerNetworkSecurityNodes = exports.ValidateSessionNode = exports.CreateSessionNode = exports.VerifySignatureNode = exports.GenerateSignatureNode = exports.ComputeHashNode = exports.UserAuthenticationNode = exports.DecryptDataNode = exports.EncryptDataNode = void 0;
var FunctionNode_1 = require("../nodes/FunctionNode");
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var NetworkSecuritySystem_1 = require("../../network/NetworkSecuritySystem");
/**
 * 数据加密节点
 * 加密数据
 */
var EncryptDataNode = /** @class */ (function (_super) {
    __extends(EncryptDataNode, _super);
    function EncryptDataNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    EncryptDataNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '要加密的数据'
        });
        this.addInput({
            name: 'algorithm',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '加密算法',
            defaultValue: 'AES'
        });
        this.addInput({
            name: 'key',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '加密密钥'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'encryptedData',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '加密后的数据'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    EncryptDataNode.prototype.execute = function () {
        // 获取输入值
        var data = this.getInputValue('data');
        var algorithm = this.getInputValue('algorithm');
        var key = this.getInputValue('key');
        // 检查输入值是否有效
        if (data === undefined || !algorithm || !key) {
            this.setOutputValue('encryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('encryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
        try {
            // 加密数据
            var encryptedData = securitySystem.encryptData(data, algorithm, key);
            // 设置输出值
            this.setOutputValue('encryptedData', encryptedData);
            // 触发输出流程
            this.triggerFlow('flow');
            return encryptedData;
        }
        catch (error) {
            console.error('加密数据失败:', error);
            this.setOutputValue('encryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
    };
    return EncryptDataNode;
}(FunctionNode_1.FunctionNode));
exports.EncryptDataNode = EncryptDataNode;
/**
 * 数据解密节点
 * 解密数据
 */
var DecryptDataNode = /** @class */ (function (_super) {
    __extends(DecryptDataNode, _super);
    function DecryptDataNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    DecryptDataNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'encryptedData',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要解密的数据'
        });
        this.addInput({
            name: 'algorithm',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '解密算法',
            defaultValue: 'AES'
        });
        this.addInput({
            name: 'key',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '解密密钥'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'decryptedData',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '解密后的数据'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    DecryptDataNode.prototype.execute = function () {
        // 获取输入值
        var encryptedData = this.getInputValue('encryptedData');
        var algorithm = this.getInputValue('algorithm');
        var key = this.getInputValue('key');
        // 检查输入值是否有效
        if (!encryptedData || !algorithm || !key) {
            this.setOutputValue('decryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('decryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
        try {
            // 解密数据
            var decryptedData = securitySystem.decryptData(encryptedData, algorithm, key);
            // 设置输出值
            this.setOutputValue('decryptedData', decryptedData);
            // 触发输出流程
            this.triggerFlow('flow');
            return decryptedData;
        }
        catch (error) {
            console.error('解密数据失败:', error);
            this.setOutputValue('decryptedData', null);
            this.triggerFlow('flow');
            return null;
        }
    };
    return DecryptDataNode;
}(FunctionNode_1.FunctionNode));
exports.DecryptDataNode = DecryptDataNode;
/**
 * 用户认证节点
 * 验证用户身份
 */
var UserAuthenticationNode = /** @class */ (function (_super) {
    __extends(UserAuthenticationNode, _super);
    function UserAuthenticationNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    UserAuthenticationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'userId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '用户ID'
        });
        this.addInput({
            name: 'credentials',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '认证凭据'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '认证成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '认证失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'user',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '用户信息'
        });
        this.addOutput({
            name: 'token',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '认证令牌'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    UserAuthenticationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var userId, credentials, securitySystem, result, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        userId = this.getInputValue('userId');
                        credentials = this.getInputValue('credentials');
                        // 检查输入值是否有效
                        if (!userId || !credentials) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
                        if (!securitySystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, securitySystem.authenticateUser(userId, credentials)];
                    case 2:
                        result = _a.sent();
                        if (result && result.success) {
                            // 设置输出值
                            this.setOutputValue('user', result.user);
                            this.setOutputValue('token', result.token);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('用户认证失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return UserAuthenticationNode;
}(AsyncNode_1.AsyncNode));
exports.UserAuthenticationNode = UserAuthenticationNode;
/**
 * 计算哈希节点
 * 计算数据的哈希值
 */
var ComputeHashNode = /** @class */ (function (_super) {
    __extends(ComputeHashNode, _super);
    function ComputeHashNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ComputeHashNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '要计算哈希的数据'
        });
        this.addInput({
            name: 'algorithm',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '哈希算法',
            defaultValue: NetworkSecuritySystem_1.HashAlgorithm.SHA256
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'hash',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '哈希值'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ComputeHashNode.prototype.execute = function () {
        // 获取输入值
        var data = this.getInputValue('data');
        var algorithm = this.getInputValue('algorithm');
        // 检查输入值是否有效
        if (data === undefined || !algorithm) {
            this.setOutputValue('hash', null);
            this.triggerFlow('flow');
            return null;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('hash', null);
            this.triggerFlow('flow');
            return null;
        }
        try {
            // 计算哈希
            var hash = securitySystem.computeHash(data, algorithm);
            // 设置输出值
            this.setOutputValue('hash', hash);
            // 触发输出流程
            this.triggerFlow('flow');
            return hash;
        }
        catch (error) {
            console.error('计算哈希失败:', error);
            this.setOutputValue('hash', null);
            this.triggerFlow('flow');
            return null;
        }
    };
    return ComputeHashNode;
}(FunctionNode_1.FunctionNode));
exports.ComputeHashNode = ComputeHashNode;
/**
 * 生成签名节点
 * 生成数据签名
 */
var GenerateSignatureNode = /** @class */ (function (_super) {
    __extends(GenerateSignatureNode, _super);
    function GenerateSignatureNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    GenerateSignatureNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '要签名的数据'
        });
        this.addInput({
            name: 'privateKey',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '私钥'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'signature',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '签名'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    GenerateSignatureNode.prototype.execute = function () {
        // 获取输入值
        var data = this.getInputValue('data');
        var privateKey = this.getInputValue('privateKey');
        // 检查输入值是否有效
        if (data === undefined || !privateKey) {
            this.setOutputValue('signature', null);
            this.triggerFlow('flow');
            return null;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('signature', null);
            this.triggerFlow('flow');
            return null;
        }
        try {
            // 生成签名
            var signature = securitySystem.generateSignature(data, privateKey);
            // 设置输出值
            this.setOutputValue('signature', signature);
            // 触发输出流程
            this.triggerFlow('flow');
            return signature;
        }
        catch (error) {
            console.error('生成签名失败:', error);
            this.setOutputValue('signature', null);
            this.triggerFlow('flow');
            return null;
        }
    };
    return GenerateSignatureNode;
}(FunctionNode_1.FunctionNode));
exports.GenerateSignatureNode = GenerateSignatureNode;
/**
 * 验证签名节点
 * 验证数据签名
 */
var VerifySignatureNode = /** @class */ (function (_super) {
    __extends(VerifySignatureNode, _super);
    function VerifySignatureNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    VerifySignatureNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '签名的数据'
        });
        this.addInput({
            name: 'signature',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '签名'
        });
        this.addInput({
            name: 'publicKey',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '公钥'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'valid',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '签名有效'
        });
        this.addOutput({
            name: 'invalid',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '签名无效'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'isValid',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否有效'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    VerifySignatureNode.prototype.execute = function () {
        // 获取输入值
        var data = this.getInputValue('data');
        var signature = this.getInputValue('signature');
        var publicKey = this.getInputValue('publicKey');
        // 检查输入值是否有效
        if (data === undefined || !signature || !publicKey) {
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
        try {
            // 验证签名
            var isValid = securitySystem.verifySignature(data, signature, publicKey);
            // 设置输出值
            this.setOutputValue('isValid', isValid);
            // 触发输出流程
            if (isValid) {
                this.triggerFlow('valid');
            }
            else {
                this.triggerFlow('invalid');
            }
            return isValid;
        }
        catch (error) {
            console.error('验证签名失败:', error);
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
    };
    return VerifySignatureNode;
}(FunctionNode_1.FunctionNode));
exports.VerifySignatureNode = VerifySignatureNode;
/**
 * 创建会话节点
 * 创建用户会话
 */
var CreateSessionNode = /** @class */ (function (_super) {
    __extends(CreateSessionNode, _super);
    function CreateSessionNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateSessionNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'userId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '用户ID'
        });
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '会话数据',
            defaultValue: {}
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'sessionId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '会话ID'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateSessionNode.prototype.execute = function () {
        // 获取输入值
        var userId = this.getInputValue('userId');
        var data = this.getInputValue('data');
        // 检查输入值是否有效
        if (!userId) {
            this.setOutputValue('sessionId', null);
            this.triggerFlow('flow');
            return null;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('sessionId', null);
            this.triggerFlow('flow');
            return null;
        }
        try {
            // 创建会话
            var sessionId = securitySystem.createSession(userId, data);
            // 设置输出值
            this.setOutputValue('sessionId', sessionId);
            // 触发输出流程
            this.triggerFlow('flow');
            return sessionId;
        }
        catch (error) {
            console.error('创建会话失败:', error);
            this.setOutputValue('sessionId', null);
            this.triggerFlow('flow');
            return null;
        }
    };
    return CreateSessionNode;
}(FunctionNode_1.FunctionNode));
exports.CreateSessionNode = CreateSessionNode;
/**
 * 验证会话节点
 * 验证用户会话
 */
var ValidateSessionNode = /** @class */ (function (_super) {
    __extends(ValidateSessionNode, _super);
    function ValidateSessionNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ValidateSessionNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'sessionId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '会话ID'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'valid',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '会话有效'
        });
        this.addOutput({
            name: 'invalid',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '会话无效'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'isValid',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否有效'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ValidateSessionNode.prototype.execute = function () {
        // 获取输入值
        var sessionId = this.getInputValue('sessionId');
        // 检查输入值是否有效
        if (!sessionId) {
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
        // 获取网络安全系统
        var securitySystem = this.graph.getWorld().getSystem(NetworkSecuritySystem_1.NetworkSecuritySystem);
        if (!securitySystem) {
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
        try {
            // 验证会话
            var isValid = securitySystem.validateSession(sessionId);
            // 设置输出值
            this.setOutputValue('isValid', isValid);
            // 触发输出流程
            if (isValid) {
                this.triggerFlow('valid');
            }
            else {
                this.triggerFlow('invalid');
            }
            return isValid;
        }
        catch (error) {
            console.error('验证会话失败:', error);
            this.setOutputValue('isValid', false);
            this.triggerFlow('invalid');
            return false;
        }
    };
    return ValidateSessionNode;
}(FunctionNode_1.FunctionNode));
exports.ValidateSessionNode = ValidateSessionNode;
/**
 * 注册网络安全节点
 * @param registry 节点注册表
 */
function registerNetworkSecurityNodes(registry) {
    // 注册数据加密节点
    registry.registerNodeType({
        type: 'network/security/encryptData',
        category: Node_1.NodeCategory.NETWORK,
        constructor: EncryptDataNode,
        label: '数据加密',
        description: '加密数据',
        icon: 'encrypt',
        color: '#00BCD4',
        tags: ['network', 'security', 'encrypt']
    });
    // 注册数据解密节点
    registry.registerNodeType({
        type: 'network/security/decryptData',
        category: Node_1.NodeCategory.NETWORK,
        constructor: DecryptDataNode,
        label: '数据解密',
        description: '解密数据',
        icon: 'decrypt',
        color: '#00BCD4',
        tags: ['network', 'security', 'decrypt']
    });
    // 注册计算哈希节点
    registry.registerNodeType({
        type: 'network/security/computeHash',
        category: Node_1.NodeCategory.NETWORK,
        constructor: ComputeHashNode,
        label: '计算哈希',
        description: '计算数据的哈希值',
        icon: 'hash',
        color: '#00BCD4',
        tags: ['network', 'security', 'hash']
    });
    // 注册生成签名节点
    registry.registerNodeType({
        type: 'network/security/generateSignature',
        category: Node_1.NodeCategory.NETWORK,
        constructor: GenerateSignatureNode,
        label: '生成签名',
        description: '生成数据签名',
        icon: 'signature',
        color: '#00BCD4',
        tags: ['network', 'security', 'signature', 'sign']
    });
    // 注册验证签名节点
    registry.registerNodeType({
        type: 'network/security/verifySignature',
        category: Node_1.NodeCategory.NETWORK,
        constructor: VerifySignatureNode,
        label: '验证签名',
        description: '验证数据签名',
        icon: 'verify',
        color: '#00BCD4',
        tags: ['network', 'security', 'signature', 'verify']
    });
    // 注册创建会话节点
    registry.registerNodeType({
        type: 'network/security/createSession',
        category: Node_1.NodeCategory.NETWORK,
        constructor: CreateSessionNode,
        label: '创建会话',
        description: '创建用户会话',
        icon: 'session',
        color: '#00BCD4',
        tags: ['network', 'security', 'session', 'create']
    });
    // 注册验证会话节点
    registry.registerNodeType({
        type: 'network/security/validateSession',
        category: Node_1.NodeCategory.NETWORK,
        constructor: ValidateSessionNode,
        label: '验证会话',
        description: '验证用户会话',
        icon: 'validate',
        color: '#00BCD4',
        tags: ['network', 'security', 'session', 'validate']
    });
    // 注册用户认证节点
    registry.registerNodeType({
        type: 'network/security/authenticateUser',
        category: Node_1.NodeCategory.NETWORK,
        constructor: UserAuthenticationNode,
        label: '用户认证',
        description: '验证用户身份',
        icon: 'authenticate',
        color: '#00BCD4',
        tags: ['network', 'security', 'authentication', 'user']
    });
}
exports.registerNetworkSecurityNodes = registerNetworkSecurityNodes;
