"use strict";
/**
 * DL（Digital Learning）引擎入口文件
 * 导出所有公共API
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimationStateMachine = exports.BlendSpace2D = exports.BlendSpace1D = exports.AnimationSystem = exports.Animator = exports.PhysicsColliderComponent = exports.PhysicsBodyComponent = exports.PhysicsCollider = void 0;
// 核心模块
__exportStar(require("./core/Engine"), exports);
__exportStar(require("./core/World"), exports);
__exportStar(require("./core/Entity"), exports);
__exportStar(require("./core/Component"), exports);
__exportStar(require("./core/System"), exports);
// 渲染模块
__exportStar(require("./rendering/Renderer"), exports);
__exportStar(require("./rendering/Camera"), exports);
__exportStar(require("./rendering/RenderSystem"), exports);
__exportStar(require("./rendering/materials/index"), exports);
__exportStar(require("./rendering/Light"), exports);
// 场景模块
__exportStar(require("./scene/Scene"), exports);
__exportStar(require("./scene/SceneManager"), exports);
__exportStar(require("./scene/Transform"), exports);
__exportStar(require("./scene/Skybox"), exports);
// 物理模块
__exportStar(require("./physics/PhysicsSystem"), exports);
__exportStar(require("./physics/PhysicsRaycastResult"), exports);
__exportStar(require("./physics/character/CharacterController"), exports);
__exportStar(require("./physics/ccd/ContinuousCollisionDetection"), exports);
__exportStar(require("./physics/debug/PhysicsDebugger"), exports);
__exportStar(require("./physics/debug/EnhancedPhysicsDebugger"), exports);
// 软体物理模块
__exportStar(require("./physics/softbody/SoftBodySystem"), exports);
__exportStar(require("./physics/softbody/SoftBodyComponent"), exports);
__exportStar(require("./physics/constraints/SliderConstraint"), exports);
__exportStar(require("./physics/constraints/FixedConstraint"), exports);
__exportStar(require("./physics/constraints/WheelConstraint"), exports);
// 导出物理碰撞体相关，解决命名冲突
var PhysicsCollider_1 = require("./physics/PhysicsCollider");
Object.defineProperty(exports, "PhysicsCollider", { enumerable: true, get: function () { return PhysicsCollider_1.PhysicsCollider; } });
// 导出物理组件，解决命名冲突
__exportStar(require("./physics/components/CharacterControllerComponent"), exports);
__exportStar(require("./physics/components/PhysicsConstraintComponent"), exports);
__exportStar(require("./physics/components/PhysicsWorldComponent"), exports);
// 单独导出物理体组件和碰撞体组件，避免命名冲突
var PhysicsBodyComponent_1 = require("./physics/components/PhysicsBodyComponent");
Object.defineProperty(exports, "PhysicsBodyComponent", { enumerable: true, get: function () { return PhysicsBodyComponent_1.PhysicsBodyComponent; } });
var PhysicsColliderComponent_1 = require("./physics/components/PhysicsColliderComponent");
Object.defineProperty(exports, "PhysicsColliderComponent", { enumerable: true, get: function () { return PhysicsColliderComponent_1.PhysicsColliderComponent; } });
// 粒子模块
__exportStar(require("./particles/ParticleSystem"), exports);
__exportStar(require("./particles/ParticleEmitter"), exports);
__exportStar(require("./particles/Particle"), exports);
// 资产模块
__exportStar(require("./assets/AssetManager"), exports);
__exportStar(require("./assets/AssetLoader"), exports);
__exportStar(require("./assets/ResourceManager"), exports);
// GLTF模块
__exportStar(require("./gltf"), exports);
var Animator_1 = require("./animation/Animator");
Object.defineProperty(exports, "Animator", { enumerable: true, get: function () { return Animator_1.Animator; } });
var AnimationSystem_1 = require("./animation/AnimationSystem");
Object.defineProperty(exports, "AnimationSystem", { enumerable: true, get: function () { return AnimationSystem_1.AnimationSystem; } });
var BlendSpace1D_1 = require("./animation/BlendSpace1D");
Object.defineProperty(exports, "BlendSpace1D", { enumerable: true, get: function () { return BlendSpace1D_1.BlendSpace1D; } });
var BlendSpace2D_1 = require("./animation/BlendSpace2D");
Object.defineProperty(exports, "BlendSpace2D", { enumerable: true, get: function () { return BlendSpace2D_1.BlendSpace2D; } });
var AnimationStateMachine_1 = require("./animation/AnimationStateMachine");
Object.defineProperty(exports, "AnimationStateMachine", { enumerable: true, get: function () { return AnimationStateMachine_1.AnimationStateMachine; } });
// UI模块
__exportStar(require("./ui/UIModule"), exports);
// 输入模块
__exportStar(require("./input/InputSystem"), exports);
__exportStar(require("./input/InputManager"), exports);
__exportStar(require("./input/InputDevice"), exports);
__exportStar(require("./input/InputAction"), exports);
__exportStar(require("./input/InputBinding"), exports);
__exportStar(require("./input/InputMapping"), exports);
__exportStar(require("./input/InputRecorder"), exports);
__exportStar(require("./input/InputVisualizer"), exports);
__exportStar(require("./input/components/InputComponent"), exports);
__exportStar(require("./input/devices/KeyboardDevice"), exports);
__exportStar(require("./input/devices/MouseDevice"), exports);
__exportStar(require("./input/devices/GamepadDevice"), exports);
__exportStar(require("./input/devices/TouchDevice"), exports);
__exportStar(require("./input/devices/XRDevice"), exports);
// 交互模块
__exportStar(require("./interaction"), exports);
// 音频模块
__exportStar(require("./audio/AudioSystem"), exports);
__exportStar(require("./audio/AudioSource"), exports);
__exportStar(require("./audio/AudioListener"), exports);
// 网络模块
__exportStar(require("./network/NetworkSystem"), exports);
__exportStar(require("./network/NetworkManager"), exports);
__exportStar(require("./network/NetworkConnection"), exports);
__exportStar(require("./network/WebSocketConnection"), exports);
__exportStar(require("./network/WebRTCConnection"), exports);
__exportStar(require("./network/NetworkMessage"), exports);
__exportStar(require("./network/NetworkEvent"), exports);
__exportStar(require("./network/NetworkEntity"), exports);
__exportStar(require("./network/NetworkUser"), exports);
__exportStar(require("./network/MessageType"), exports);
__exportStar(require("./network/MessageSerializer"), exports);
__exportStar(require("./network/components/NetworkEntityComponent"), exports);
__exportStar(require("./network/components/NetworkTransformComponent"), exports);
__exportStar(require("./network/components/NetworkUserComponent"), exports);
// 工具模块
__exportStar(require("./utils/EventEmitter"), exports);
__exportStar(require("./utils/Time"), exports);
__exportStar(require("./utils/Debug"), exports);
__exportStar(require("./utils/UUID"), exports);
// 角色模块
__exportStar(require("./avatar/controllers"), exports);
// 国际化模块
__exportStar(require("./i18n/I18n"), exports);
// 视觉脚本模块
__exportStar(require("./visualscript"), exports);
