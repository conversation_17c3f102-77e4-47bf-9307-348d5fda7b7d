"use strict";
/**
 * UI3DComponent.ts
 *
 * 3D UI元素组件，用于创建和管理3D空间中的界面元素
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI3DComponent = exports.BillboardMode = void 0;
var three_1 = require("three");
var UIComponent_1 = require("./UIComponent");
/**
 * 广告牌模式枚举
 */
var BillboardMode;
(function (BillboardMode) {
    BillboardMode["NONE"] = "none";
    BillboardMode["FULL"] = "full";
    BillboardMode["Y_AXIS"] = "y-axis";
})(BillboardMode || (exports.BillboardMode = BillboardMode = {}));
/**
 * 3D UI元素组件
 * 用于创建和管理3D空间中的界面元素
 */
var UI3DComponent = /** @class */ (function (_super) {
    __extends(UI3DComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param props 3D UI元素属性
     */
    function UI3DComponent(entity, props) {
        if (props === void 0) { props = {}; }
        var _this = _super.call(this, entity, __assign(__assign({}, props), { is3D: true })) || this;
        _this.billboardMode = BillboardMode.NONE;
        // 材质属性
        _this.transparent = true;
        _this.emissiveIntensity = 1.0;
        // 内容属性
        _this.textContent = '';
        _this.fontSize = 24;
        _this.fontFamily = 'Arial';
        _this.fontColor = '#ffffff';
        _this.textAlign = 'center';
        _this.textBaseline = 'middle';
        // 交互属性
        _this.interactionDistance = 10;
        // 状态
        _this.isHovered = false;
        _this.isActive = false;
        _this.needsUpdate = true;
        // 设置3D对象
        _this.mesh = props.mesh;
        _this.texture = props.texture;
        _this.canvas = props.canvas;
        _this.group = props.group || new three_1.Group();
        // 设置3D变换
        _this.rotation = props.rotation || new three_1.Vector3(0, 0, 0);
        _this.scale = props.scale || new three_1.Vector3(1, 1, 1);
        _this.lookAt = props.lookAt;
        _this.billboardMode = props.billboardMode || BillboardMode.NONE;
        // 设置材质属性
        _this.transparent = props.transparent !== undefined ? props.transparent : true;
        _this.color = props.color;
        _this.emissive = props.emissive;
        _this.emissiveIntensity = props.emissiveIntensity || 1.0;
        // 设置内容属性
        _this.textContent = props.textContent || '';
        _this.fontSize = props.fontSize || 24;
        _this.fontFamily = props.fontFamily || 'Arial';
        _this.fontColor = props.fontColor || '#ffffff';
        _this.textAlign = props.textAlign || 'center';
        _this.textBaseline = props.textBaseline || 'middle';
        // 设置交互属性
        _this.interactionDistance = props.interactionDistance || 10;
        _this.hoverColor = props.hoverColor;
        _this.activeColor = props.activeColor;
        // 创建3D对象（如果没有提供）
        if (!_this.mesh) {
            _this.createMesh();
        }
        // 将网格添加到组
        if (_this.mesh && _this.group && !_this.group.children.includes(_this.mesh)) {
            _this.group.add(_this.mesh);
        }
        // 设置组的位置
        if (_this.group) {
            if (_this.position instanceof three_1.Vector3) {
                _this.group.position.copy(_this.position);
            }
            else {
                _this.group.setPosition(_this.getPosition().x, _this.getPosition().y, 0);
            }
            // 设置组的旋转
            if (_this.rotation instanceof three_1.Euler) {
                _this.group.rotation.copy(_this.rotation);
            }
            else {
                _this.group.setRotationQuaternion(_this.rotation.x, _this.rotation.y, _this.rotation.z);
            }
            // 设置组的缩放
            _this.group.scale.copy(_this.scale);
        }
        // 初始渲染
        _this.render();
        return _this;
    }
    /**
     * 创建网格
     */
    UI3DComponent.prototype.createMesh = function () {
        // 创建画布（如果没有提供）
        if (!this.canvas) {
            this.canvas = document.createElement('canvas');
            this.canvas.width = this.size.x;
            this.canvas.height = this.size.y;
            this.context = this.canvas.getContext('2d');
        }
        // 创建纹理（如果没有提供）
        if (!this.texture && this.canvas) {
            this.texture = new three_1.CanvasTexture(this.canvas);
            this.texture.needsUpdate = true;
        }
        // 创建材质
        var material = new three_1.MeshBasicMaterial({
            map: this.texture,
            transparent: this.transparent,
            opacity: this.opacity
        });
        if (this.color) {
            material.color.set(this.color);
        }
        if (this.emissive) {
            material.emissive = this.emissive;
            material.emissiveIntensity = this.emissiveIntensity;
        }
        // 创建几何体
        var geometry = new three_1.PlaneGeometry(this.size.x / 100, this.size.y / 100);
        // 创建网格
        this.mesh = new three_1.Mesh(geometry, material);
        this.mesh.name = this.id;
    };
    /**
     * 更新画布内容
     */
    UI3DComponent.prototype.updateCanvas = function () {
        if (!this.canvas || !this.context)
            return;
        // 清除画布
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
        // 设置背景
        if (this.backgroundColor) {
            this.context.fillStyle = this.backgroundColor;
            this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
        // 绘制边框
        if (this.borderColor && this.borderWidth > 0) {
            this.context.strokeStyle = this.borderColor;
            this.context.lineWidth = this.borderWidth;
            this.context.strokeRect(this.borderWidth / 2, this.borderWidth / 2, this.canvas.width - this.borderWidth, this.canvas.height - this.borderWidth);
        }
        // 绘制文本
        if (this.textContent) {
            this.context.fillStyle = this.fontColor;
            this.context.font = "".concat(this.fontSize, "px ").concat(this.fontFamily);
            this.context.textAlign = this.textAlign;
            this.context.textBaseline = this.textBaseline;
            var x = this.canvas.width / 2;
            if (this.textAlign === 'left')
                x = this.padding.left;
            else if (this.textAlign === 'right')
                x = this.canvas.width - this.padding.right;
            var y = this.canvas.height / 2;
            if (this.textBaseline === 'top')
                y = this.padding.top;
            else if (this.textBaseline === 'bottom')
                y = this.canvas.height - this.padding.bottom;
            this.context.fillText(this.textContent, x, y);
        }
        // 更新纹理
        if (this.texture) {
            this.texture.needsUpdate = true;
        }
    };
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     * @param camera 相机对象（用于广告牌模式）
     */
    UI3DComponent.prototype.update = function (deltaTime, camera) {
        _super.prototype.update.call(this, deltaTime);
        // 更新广告牌模式
        if (camera && this.billboardMode !== BillboardMode.NONE && this.group) {
            if (this.billboardMode === BillboardMode.FULL) {
                // 完全面向相机
                this.group.quaternion.copy(camera.quaternion);
            }
            else if (this.billboardMode === BillboardMode.Y_AXIS) {
                // 仅在Y轴上面向相机
                var cameraPosition = new three_1.Vector3();
                camera.getWorldPosition(cameraPosition);
                var groupPosition = new three_1.Vector3();
                this.group.getWorldPosition(groupPosition);
                cameraPosition.y = groupPosition.y;
                this.group.lookAt(cameraPosition);
            }
        }
        // 更新lookAt目标
        if (this.lookAt && this.group) {
            this.group.lookAt(this.lookAt);
        }
        // 如果需要更新，则更新画布
        if (this.needsUpdate) {
            this.updateCanvas();
            this.needsUpdate = false;
        }
    };
    /**
     * 渲染UI元素
     */
    UI3DComponent.prototype.render = function () {
        _super.prototype.render.call(this);
        // 更新画布内容
        this.updateCanvas();
    };
    /**
     * 销毁UI元素
     */
    UI3DComponent.prototype.dispose = function () {
        _super.prototype.dispose.call(this);
        // 销毁网格
        if (this.mesh) {
            this.mesh.geometry.dispose();
            this.mesh.material.dispose();
            if (this.mesh.parent) {
                this.mesh.parent.remove(this.mesh);
            }
        }
        // 销毁纹理
        if (this.texture) {
            this.texture.dispose();
        }
        // 销毁组
        if (this.group) {
            while (this.group.children.length > 0) {
                var child = this.group.children[0];
                this.group.remove(child);
            }
        }
        this.mesh = undefined;
        this.texture = undefined;
        this.canvas = undefined;
        this.context = undefined;
        this.group = undefined;
    };
    /**
     * 设置文本内容
     * @param text 文本内容
     */
    UI3DComponent.prototype.setText = function (text) {
        this.textContent = text;
        this.needsUpdate = true;
    };
    /**
     * 设置字体大小
     * @param size 字体大小
     */
    UI3DComponent.prototype.setFontSize = function (size) {
        this.fontSize = size;
        this.needsUpdate = true;
    };
    /**
     * 设置字体颜色
     * @param color 字体颜色
     */
    UI3DComponent.prototype.setFontColor = function (color) {
        this.fontColor = color;
        this.needsUpdate = true;
    };
    /**
     * 设置悬停状态
     * @param hovered 是否悬停
     */
    UI3DComponent.prototype.setHovered = function (hovered) {
        if (this.isHovered === hovered)
            return;
        this.isHovered = hovered;
        // 更新材质颜色
        if (this.mesh && this.hoverColor) {
            var material = this.mesh.material;
            if (hovered && !this.isActive) {
                material.color.set(this.hoverColor);
            }
            else if (!this.isActive) {
                material.color.set(this.color || '#ffffff');
            }
        }
        this.needsUpdate = true;
    };
    /**
     * 设置激活状态
     * @param active 是否激活
     */
    UI3DComponent.prototype.setActive = function (active) {
        if (this.isActive === active)
            return;
        this.isActive = active;
        // 更新材质颜色
        if (this.mesh && this.activeColor) {
            var material = this.mesh.material;
            if (active) {
                material.color.set(this.activeColor);
            }
            else if (this.isHovered && this.hoverColor) {
                material.color.set(this.hoverColor);
            }
            else {
                material.color.set(this.color || '#ffffff');
            }
        }
        this.needsUpdate = true;
    };
    /**
     * 获取3D对象
     * @returns 3D对象（Group）
     */
    UI3DComponent.prototype.getObject3D = function () {
        return this.group;
    };
    /**
     * 设置位置
     * @param position 新位置
     */
    UI3DComponent.prototype.setPosition = function (position) {
        _super.prototype.setPosition.call(this, position);
        if (this.group) {
            if (position instanceof three_1.Vector3) {
                this.group.position.copy(position);
            }
            else {
                this.group.setPosition(position.x, position.y, 0);
            }
        }
    };
    /**
     * 设置旋转
     * @param rotation 新旋转
     */
    UI3DComponent.prototype.setRotation = function (rotation) {
        this.rotation = rotation;
        if (this.group) {
            if (rotation instanceof three_1.Euler) {
                this.group.rotation.copy(rotation);
            }
            else {
                this.group.setRotationQuaternion(rotation.x, rotation.y, rotation.z);
            }
        }
    };
    /**
     * 设置缩放
     * @param scale 新缩放
     */
    UI3DComponent.prototype.setScale = function (scale) {
        this.scale = scale;
        if (this.group) {
            this.group.scale.copy(scale);
        }
    };
    return UI3DComponent;
}(UIComponent_1.UIComponent));
exports.UI3DComponent = UI3DComponent;
