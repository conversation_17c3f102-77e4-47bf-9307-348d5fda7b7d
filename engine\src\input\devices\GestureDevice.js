"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GestureDevice = exports.GestureState = exports.GestureDirection = exports.GestureType = void 0;
/**
 * 手势识别设备
 * 用于识别用户的手势输入
 */
var InputDevice_1 = require("../InputDevice");
/**
 * 手势类型
 */
var GestureType;
(function (GestureType) {
    /** 点击 */
    GestureType["TAP"] = "tap";
    /** 双击 */
    GestureType["DOUBLE_TAP"] = "doubleTap";
    /** 长按 */
    GestureType["LONG_PRESS"] = "longPress";
    /** 滑动 */
    GestureType["SWIPE"] = "swipe";
    /** 捏合 */
    GestureType["PINCH"] = "pinch";
    /** 旋转 */
    GestureType["ROTATE"] = "rotate";
    /** 平移 */
    GestureType["PAN"] = "pan";
})(GestureType || (exports.GestureType = GestureType = {}));
/**
 * 手势方向
 */
var GestureDirection;
(function (GestureDirection) {
    /** 无方向 */
    GestureDirection["NONE"] = "none";
    /** 上 */
    GestureDirection["UP"] = "up";
    /** 下 */
    GestureDirection["DOWN"] = "down";
    /** 左 */
    GestureDirection["LEFT"] = "left";
    /** 右 */
    GestureDirection["RIGHT"] = "right";
})(GestureDirection || (exports.GestureDirection = GestureDirection = {}));
/**
 * 手势状态
 */
var GestureState;
(function (GestureState) {
    /** 开始 */
    GestureState["BEGIN"] = "begin";
    /** 更新 */
    GestureState["UPDATE"] = "update";
    /** 结束 */
    GestureState["END"] = "end";
})(GestureState || (exports.GestureState = GestureState = {}));
/**
 * 手势识别设备
 */
var GestureDevice = /** @class */ (function (_super) {
    __extends(GestureDevice, _super);
    /**
     * 创建手势识别设备
     * @param options 选项
     */
    function GestureDevice(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 'gesture') || this;
        /** 触摸事件处理器 */
        _this.touchEventHandlers = {};
        /** 鼠标事件处理器 */
        _this.mouseEventHandlers = {};
        /** 指针事件处理器 */
        _this.pointerEventHandlers = {};
        /** 当前触摸点 */
        _this.touches = [];
        /** 上一次触摸点 */
        _this.previousTouches = [];
        /** 触摸开始时间 */
        _this.touchStartTime = 0;
        /** 上一次点击时间 */
        _this.lastTapTime = 0;
        /** 长按定时器 */
        _this.longPressTimer = null;
        /** 是否正在进行手势 */
        _this.isGesturing = false;
        /** 当前手势类型 */
        _this.currentGestureType = null;
        /** 手势开始位置 */
        _this.gestureStartPosition = { x: 0, y: 0 };
        /** 手势当前位置 */
        _this.gestureCurrentPosition = { x: 0, y: 0 };
        /** 手势开始时间 */
        _this.gestureStartTime = 0;
        /** 手势缩放初始值 */
        _this.initialScale = 1;
        /** 手势旋转初始值 */
        _this.initialRotation = 0;
        _this.element = options.element || document.body;
        _this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
        _this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;
        _this.longPressThreshold = options.longPressThreshold || 500;
        _this.doubleTapThreshold = options.doubleTapThreshold || 300;
        _this.swipeThreshold = options.swipeThreshold || 50;
        _this.swipeVelocityThreshold = options.swipeVelocityThreshold || 0.3;
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    GestureDevice.prototype.initEventHandlers = function () {
        // 触摸事件处理器
        this.touchEventHandlers = {
            touchstart: this.handleTouchStart.bind(this),
            touchmove: this.handleTouchMove.bind(this),
            touchend: this.handleTouchEnd.bind(this),
            touchcancel: this.handleTouchCancel.bind(this)
        };
        // 鼠标事件处理器
        this.mouseEventHandlers = {
            mousedown: this.handleMouseDown.bind(this),
            mousemove: this.handleMouseMove.bind(this),
            mouseup: this.handleMouseUp.bind(this)
        };
        // 指针事件处理器
        this.pointerEventHandlers = {
            pointerdown: this.handlePointerDown.bind(this),
            pointermove: this.handlePointerMove.bind(this),
            pointerup: this.handlePointerUp.bind(this),
            pointercancel: this.handlePointerCancel.bind(this)
        };
    };
    /**
     * 初始化设备
     */
    GestureDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    GestureDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    };
    /**
     * 移除事件监听器
     */
    GestureDevice.prototype.removeEventListeners = function () {
        // 移除触摸事件监听器
        for (var _i = 0, _a = Object.entries(this.touchEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            this.element.removeEventListener(event_1, handler);
        }
        // 移除鼠标事件监听器
        for (var _c = 0, _d = Object.entries(this.mouseEventHandlers); _c < _d.length; _c++) {
            var _e = _d[_c], event_2 = _e[0], handler = _e[1];
            this.element.removeEventListener(event_2, handler);
        }
        // 移除指针事件监听器
        for (var _f = 0, _g = Object.entries(this.pointerEventHandlers); _f < _g.length; _f++) {
            var _h = _g[_f], event_3 = _h[0], handler = _h[1];
            this.element.removeEventListener(event_3, handler);
        }
    };
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleTouchStart = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 保存触摸点
        this.touches = Array.from(event.touches);
        this.previousTouches = __spreadArray([], this.touches, true);
        // 记录触摸开始时间
        this.touchStartTime = Date.now();
        // 记录手势开始位置和时间
        if (event.touches.length === 1) {
            var touch = event.touches[0];
            this.gestureStartPosition = { x: touch.clientX, y: touch.clientY };
            this.gestureCurrentPosition = { x: touch.clientX, y: touch.clientY };
            this.gestureStartTime = Date.now();
            // 启动长按定时器
            this.startLongPressTimer(touch);
        }
        else if (event.touches.length === 2) {
            // 双指手势
            this.handleMultiTouchStart(event);
        }
        // 设置手势状态
        this.isGesturing = true;
    };
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleTouchMove = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 保存触摸点
        this.previousTouches = __spreadArray([], this.touches, true);
        this.touches = Array.from(event.touches);
        // 更新手势当前位置
        if (event.touches.length === 1) {
            var touch = event.touches[0];
            this.gestureCurrentPosition = { x: touch.clientX, y: touch.clientY };
            // 检测滑动手势
            this.detectSwipeGesture();
        }
        else if (event.touches.length === 2) {
            // 处理双指手势
            this.handleMultiTouchMove(event);
        }
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    };
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleTouchEnd = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        // 检测点击和双击手势
        if (this.previousTouches.length === 1 && event.touches.length === 0) {
            var touch = this.previousTouches[0];
            var touchEndTime = Date.now();
            var touchDuration = touchEndTime - this.touchStartTime;
            // 检测是否为点击手势
            if (touchDuration < this.longPressThreshold) {
                var dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
                var dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
                var distance = Math.sqrt(dx * dx + dy * dy);
                // 如果移动距离小于阈值，则认为是点击
                if (distance < this.swipeThreshold) {
                    // 检测是否为双击
                    var timeSinceLastTap = touchEndTime - this.lastTapTime;
                    if (timeSinceLastTap < this.doubleTapThreshold) {
                        // 双击手势
                        this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
                        this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.END, GestureDirection.NONE, touch.clientX, touch.clientY);
                        this.lastTapTime = 0; // 重置双击时间
                    }
                    else {
                        // 单击手势
                        this.triggerGestureEvent(GestureType.TAP, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
                        this.triggerGestureEvent(GestureType.TAP, GestureState.END, GestureDirection.NONE, touch.clientX, touch.clientY);
                        this.lastTapTime = touchEndTime;
                    }
                }
            }
        }
        else if (this.previousTouches.length === 2 && (event.touches.length === 1 || event.touches.length === 0)) {
            // 结束双指手势
            this.handleMultiTouchEnd(event);
        }
        // 结束当前手势
        if (this.currentGestureType && this.isGesturing) {
            this.triggerGestureEvent(this.currentGestureType, GestureState.END, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y);
        }
        // 重置手势状态
        this.isGesturing = false;
        this.currentGestureType = null;
        this.touches = Array.from(event.touches);
        this.previousTouches = __spreadArray([], this.touches, true);
    };
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleTouchCancel = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        // 结束当前手势
        if (this.currentGestureType && this.isGesturing) {
            this.triggerGestureEvent(this.currentGestureType, GestureState.END, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y);
        }
        // 重置手势状态
        this.isGesturing = false;
        this.currentGestureType = null;
        this.touches = [];
        this.previousTouches = [];
    };
    /**
     * 处理鼠标按下事件
     * @param event 鼠标事件
     */
    GestureDevice.prototype.handleMouseDown = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 记录手势开始位置和时间
        this.gestureStartPosition = { x: event.clientX, y: event.clientY };
        this.gestureCurrentPosition = { x: event.clientX, y: event.clientY };
        this.gestureStartTime = Date.now();
        // 启动长按定时器
        this.startLongPressTimer({ clientX: event.clientX, clientY: event.clientY });
        // 设置手势状态
        this.isGesturing = true;
    };
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    GestureDevice.prototype.handleMouseMove = function (event) {
        // 如果没有按下鼠标，则忽略
        if (!this.isGesturing)
            return;
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新手势当前位置
        this.gestureCurrentPosition = { x: event.clientX, y: event.clientY };
        // 检测滑动手势
        this.detectSwipeGesture();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    };
    /**
     * 处理鼠标释放事件
     * @param event 鼠标事件
     */
    GestureDevice.prototype.handleMouseUp = function (event) {
        // 如果没有按下鼠标，则忽略
        if (!this.isGesturing)
            return;
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        // 检测点击和双击手势
        var mouseUpTime = Date.now();
        var mouseDuration = mouseUpTime - this.gestureStartTime;
        // 检测是否为点击手势
        if (mouseDuration < this.longPressThreshold) {
            var dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
            var dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
            var distance = Math.sqrt(dx * dx + dy * dy);
            // 如果移动距离小于阈值，则认为是点击
            if (distance < this.swipeThreshold) {
                // 检测是否为双击
                var timeSinceLastTap = mouseUpTime - this.lastTapTime;
                if (timeSinceLastTap < this.doubleTapThreshold) {
                    // 双击手势
                    this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.BEGIN, GestureDirection.NONE, event.clientX, event.clientY);
                    this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.END, GestureDirection.NONE, event.clientX, event.clientY);
                    this.lastTapTime = 0; // 重置双击时间
                }
                else {
                    // 单击手势
                    this.triggerGestureEvent(GestureType.TAP, GestureState.BEGIN, GestureDirection.NONE, event.clientX, event.clientY);
                    this.triggerGestureEvent(GestureType.TAP, GestureState.END, GestureDirection.NONE, event.clientX, event.clientY);
                    this.lastTapTime = mouseUpTime;
                }
            }
        }
        // 结束当前手势
        if (this.currentGestureType) {
            this.triggerGestureEvent(this.currentGestureType, GestureState.END, this.getSwipeDirection(), event.clientX, event.clientY);
        }
        // 重置手势状态
        this.isGesturing = false;
        this.currentGestureType = null;
    };
    /**
     * 处理指针按下事件
     * @param event 指针事件
     */
    GestureDevice.prototype.handlePointerDown = function (event) {
        // 模拟鼠标事件处理
        this.handleMouseDown(event);
    };
    /**
     * 处理指针移动事件
     * @param event 指针事件
     */
    GestureDevice.prototype.handlePointerMove = function (event) {
        // 模拟鼠标事件处理
        this.handleMouseMove(event);
    };
    /**
     * 处理指针释放事件
     * @param event 指针事件
     */
    GestureDevice.prototype.handlePointerUp = function (event) {
        // 模拟鼠标事件处理
        this.handleMouseUp(event);
    };
    /**
     * 处理指针取消事件
     * @param event 指针事件
     */
    GestureDevice.prototype.handlePointerCancel = function (event) {
        // 如果没有按下指针，则忽略
        if (!this.isGesturing)
            return;
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 清除长按定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        // 结束当前手势
        if (this.currentGestureType) {
            this.triggerGestureEvent(this.currentGestureType, GestureState.END, this.getSwipeDirection(), event.clientX, event.clientY);
        }
        // 重置手势状态
        this.isGesturing = false;
        this.currentGestureType = null;
    };
    /**
     * 启动长按定时器
     * @param touch 触摸点
     */
    GestureDevice.prototype.startLongPressTimer = function (touch) {
        var _this = this;
        // 清除现有定时器
        if (this.longPressTimer !== null) {
            window.clearTimeout(this.longPressTimer);
        }
        // 创建新定时器
        this.longPressTimer = window.setTimeout(function () {
            // 触发长按手势
            _this.triggerGestureEvent(GestureType.LONG_PRESS, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
            _this.currentGestureType = GestureType.LONG_PRESS;
            _this.longPressTimer = null;
        }, this.longPressThreshold);
    };
    /**
     * 检测滑动手势
     */
    GestureDevice.prototype.detectSwipeGesture = function () {
        var dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
        var dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
        var distance = Math.sqrt(dx * dx + dy * dy);
        var duration = Date.now() - this.gestureStartTime;
        var velocity = distance / duration;
        // 如果移动距离大于阈值，则认为是滑动
        if (distance > this.swipeThreshold) {
            // 如果当前没有手势或手势不是滑动，则开始滑动手势
            if (this.currentGestureType !== GestureType.SWIPE) {
                this.currentGestureType = GestureType.SWIPE;
                this.triggerGestureEvent(GestureType.SWIPE, GestureState.BEGIN, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, undefined, undefined, velocity);
            }
            else {
                // 更新滑动手势
                this.triggerGestureEvent(GestureType.SWIPE, GestureState.UPDATE, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, undefined, undefined, velocity);
            }
        }
    };
    /**
     * 获取滑动方向
     * @returns 滑动方向
     */
    GestureDevice.prototype.getSwipeDirection = function () {
        var dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
        var dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
        var absDx = Math.abs(dx);
        var absDy = Math.abs(dy);
        // 如果水平移动大于垂直移动
        if (absDx > absDy) {
            return dx > 0 ? GestureDirection.RIGHT : GestureDirection.LEFT;
        }
        else if (absDy > absDx) {
            return dy > 0 ? GestureDirection.DOWN : GestureDirection.UP;
        }
        return GestureDirection.NONE;
    };
    /**
     * 处理多点触摸开始
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleMultiTouchStart = function (event) {
        if (event.touches.length === 2) {
            var touch1 = event.touches[0];
            var touch2 = event.touches[1];
            // 计算初始距离和角度
            var dx = touch2.clientX - touch1.clientX;
            var dy = touch2.clientY - touch1.clientY;
            this.initialScale = Math.sqrt(dx * dx + dy * dy);
            this.initialRotation = Math.atan2(dy, dx);
            // 设置手势中心点
            this.gestureStartPosition = {
                x: (touch1.clientX + touch2.clientX) / 2,
                y: (touch1.clientY + touch2.clientY) / 2
            };
            this.gestureCurrentPosition = __assign({}, this.gestureStartPosition);
        }
    };
    /**
     * 处理多点触摸移动
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleMultiTouchMove = function (event) {
        if (event.touches.length === 2) {
            var touch1 = event.touches[0];
            var touch2 = event.touches[1];
            // 计算当前距离和角度
            var dx = touch2.clientX - touch1.clientX;
            var dy = touch2.clientY - touch1.clientY;
            var currentDistance = Math.sqrt(dx * dx + dy * dy);
            var currentRotation = Math.atan2(dy, dx);
            // 计算缩放比例和旋转角度
            var scale = currentDistance / this.initialScale;
            var rotation = currentRotation - this.initialRotation;
            // 更新手势中心点
            this.gestureCurrentPosition = {
                x: (touch1.clientX + touch2.clientX) / 2,
                y: (touch1.clientY + touch2.clientY) / 2
            };
            // 检测捏合手势
            if (Math.abs(scale - 1) > 0.1) {
                if (this.currentGestureType !== GestureType.PINCH) {
                    this.currentGestureType = GestureType.PINCH;
                    this.triggerGestureEvent(GestureType.PINCH, GestureState.BEGIN, GestureDirection.NONE, this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, scale);
                }
                else {
                    this.triggerGestureEvent(GestureType.PINCH, GestureState.UPDATE, GestureDirection.NONE, this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, scale);
                }
            }
            // 检测旋转手势
            if (Math.abs(rotation) > 0.1) {
                if (this.currentGestureType !== GestureType.ROTATE) {
                    this.currentGestureType = GestureType.ROTATE;
                    this.triggerGestureEvent(GestureType.ROTATE, GestureState.BEGIN, GestureDirection.NONE, this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, undefined, rotation);
                }
                else {
                    this.triggerGestureEvent(GestureType.ROTATE, GestureState.UPDATE, GestureDirection.NONE, this.gestureCurrentPosition.x, this.gestureCurrentPosition.y, undefined, rotation);
                }
            }
            // 检测平移手势
            var panDx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
            var panDy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
            var panDistance = Math.sqrt(panDx * panDx + panDy * panDy);
            if (panDistance > this.swipeThreshold) {
                if (this.currentGestureType !== GestureType.PAN) {
                    this.currentGestureType = GestureType.PAN;
                    this.triggerGestureEvent(GestureType.PAN, GestureState.BEGIN, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y);
                }
                else {
                    this.triggerGestureEvent(GestureType.PAN, GestureState.UPDATE, this.getSwipeDirection(), this.gestureCurrentPosition.x, this.gestureCurrentPosition.y);
                }
            }
        }
    };
    /**
     * 处理多点触摸结束
     * @param event 触摸事件
     */
    GestureDevice.prototype.handleMultiTouchEnd = function (event) {
        // 结束当前手势
        if (this.currentGestureType) {
            this.triggerGestureEvent(this.currentGestureType, GestureState.END, GestureDirection.NONE, this.gestureCurrentPosition.x, this.gestureCurrentPosition.y);
        }
    };
    /**
     * 触发手势事件
     * @param type 手势类型
     * @param state 手势状态
     * @param direction 手势方向
     * @param x X坐标
     * @param y Y坐标
     * @param scale 缩放比例
     * @param rotation 旋转角度
     * @param velocity 速度
     * @param originalEvent 原始事件
     */
    GestureDevice.prototype.triggerGestureEvent = function (type, state, direction, x, y, scale, rotation, velocity, originalEvent) {
        // 创建事件数据
        var eventData = {
            type: type,
            state: state,
            direction: direction,
            x: x,
            y: y,
            scale: scale,
            rotation: rotation,
            velocity: velocity,
            duration: Date.now() - this.gestureStartTime,
            originalEvent: originalEvent
        };
        // 更新设备值
        this.setValue('type', type);
        this.setValue('state', state);
        this.setValue('direction', direction);
        this.setValue('position', { x: x, y: y });
        if (scale !== undefined)
            this.setValue('scale', scale);
        if (rotation !== undefined)
            this.setValue('rotation', rotation);
        if (velocity !== undefined)
            this.setValue('velocity', velocity);
        // 触发事件
        this.eventEmitter.emit("".concat(type, ":").concat(state), eventData);
        this.eventEmitter.emit('gesture', eventData);
    };
    /**
     * 添加事件监听器
     */
    GestureDevice.prototype.addEventListeners = function () {
        // 检测设备支持的事件类型
        if ('ontouchstart' in window) {
            // 触摸设备
            for (var _i = 0, _a = Object.entries(this.touchEventHandlers); _i < _a.length; _i++) {
                var _b = _a[_i], event_4 = _b[0], handler = _b[1];
                this.element.addEventListener(event_4, handler, { passive: !this.preventDefault });
            }
        }
        else if ('onpointerdown' in window) {
            // 支持指针事件的设备
            for (var _c = 0, _d = Object.entries(this.pointerEventHandlers); _c < _d.length; _c++) {
                var _e = _d[_c], event_5 = _e[0], handler = _e[1];
                this.element.addEventListener(event_5, handler, { passive: !this.preventDefault });
            }
        }
        else {
            // 鼠标设备
            for (var _f = 0, _g = Object.entries(this.mouseEventHandlers); _f < _g.length; _f++) {
                var _h = _g[_f], event_6 = _h[0], handler = _h[1];
                this.element.addEventListener(event_6, handler, { passive: !this.preventDefault });
            }
        }
    };
    return GestureDevice;
}(InputDevice_1.BaseInputDevice));
exports.GestureDevice = GestureDevice;
