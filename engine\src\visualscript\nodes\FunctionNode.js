"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionNode = void 0;
/**
 * 视觉脚本函数节点
 * 函数节点用于执行纯函数，不影响执行流程
 */
var Node_1 = require("./Node");
/**
 * 函数节点基类
 */
var FunctionNode = /** @class */ (function (_super) {
    __extends(FunctionNode, _super);
    /**
     * 创建函数节点
     * @param options 节点选项
     */
    function FunctionNode(options) {
        var _this = _super.call(this, options) || this;
        /** 节点类型 */
        _this.nodeType = Node_1.NodeType.FUNCTION;
        /** 节点类别 */
        _this.category = Node_1.NodeCategory.FUNCTION;
        /** 是否已执行 */
        _this.executed = false;
        /** 执行结果 */
        _this.result = null;
        _this.functionName = options.functionName || '';
        return _this;
    }
    /**
     * 初始化插槽
     */
    FunctionNode.prototype.initializeSockets = function () {
        // 函数节点没有流程插槽，只有数据输入和输出插槽
        // 子类需要添加具体的数据插槽
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    FunctionNode.prototype.execute = function () {
        // 如果已经执行过，直接返回结果
        if (this.executed) {
            return this.result;
        }
        // 获取所有输入值
        var inputs = {};
        for (var _i = 0, _a = this.inputs.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], name_1 = _b[0], socket = _b[1];
            inputs[name_1] = this.getInputValue(name_1);
        }
        // 执行函数
        this.result = this.compute(inputs);
        this.executed = true;
        // 设置输出值
        if (this.result !== null && this.result !== undefined) {
            // 如果结果是对象，分别设置各个输出
            if (typeof this.result === 'object' && !Array.isArray(this.result)) {
                for (var _c = 0, _d = Object.entries(this.result); _c < _d.length; _c++) {
                    var _e = _d[_c], key = _e[0], value = _e[1];
                    if (this.outputs.has(key)) {
                        this.setOutputValue(key, value);
                    }
                }
            }
            // 如果只有一个输出，直接设置
            else if (this.outputs.size === 1) {
                var outputName = Array.from(this.outputs.keys())[0];
                this.setOutputValue(outputName, this.result);
            }
        }
        return this.result;
    };
    /**
     * 计算函数结果
     * @param inputs 输入值
     * @returns 计算结果
     */
    FunctionNode.prototype.compute = function (inputs) {
        // 子类实现
        return null;
    };
    /**
     * 重置执行状态
     */
    FunctionNode.prototype.reset = function () {
        this.executed = false;
        this.result = null;
    };
    return FunctionNode;
}(Node_1.Node));
exports.FunctionNode = FunctionNode;
