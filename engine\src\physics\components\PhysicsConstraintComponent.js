"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsConstraintComponent = void 0;
/**
 * 物理约束组件
 * 为实体提供物理约束
 */
var CANNON = require("cannon-es");
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * 物理约束组件
 */
var PhysicsConstraintComponent = exports.PhysicsConstraintComponent = /** @class */ (function (_super) {
    __extends(PhysicsConstraintComponent, _super);
    /**
     * 创建物理约束组件
     * @param options 约束选项
     */
    function PhysicsConstraintComponent(options) {
        var _this = _super.call(this, PhysicsConstraintComponent.type) || this;
        /** CANNON.js约束 */
        _this.constraint = null;
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        _this.constraintType = options.type;
        _this.entityA = options.entityA;
        _this.entityB = options.entityB || null;
        _this.pivotA = options.pivotA ? options.pivotA.clone() : new THREE.Vector3();
        _this.pivotB = options.pivotB ? options.pivotB.clone() : new THREE.Vector3();
        _this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(1, 0, 0);
        _this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(1, 0, 0);
        _this.distance = options.distance || 1;
        _this.stiffness = options.stiffness || 100;
        _this.damping = options.damping || 1;
        _this.maxAngle = options.maxAngle !== undefined ? options.maxAngle : null;
        _this.minAngle = options.minAngle !== undefined ? options.minAngle : null;
        _this.enableMotor = options.enableMotor || false;
        _this.motorSpeed = options.motorSpeed || 0;
        _this.motorMaxForce = options.motorMaxForce || 1;
        _this.collideConnected = options.collideConnected || false;
        return _this;
    }
    /**
     * 初始化约束
     * @param world 物理世界
     */
    PhysicsConstraintComponent.prototype.initialize = function (world) {
        if (this.initialized || !this.entity || this.destroyed)
            return;
        this.world = world;
        // 获取物理体
        var bodyA = this.getBody(this.entityA);
        var bodyB = this.entityB ? this.getBody(this.entityB) : null;
        if (!bodyA) {
            console.warn('约束的第一个实体没有物理体组件');
            return;
        }
        // 创建约束
        switch (this.constraintType) {
            case 'point':
                this.createPointConstraint(bodyA, bodyB);
                break;
            case 'distance':
                this.createDistanceConstraint(bodyA, bodyB);
                break;
            case 'hinge':
                this.createHingeConstraint(bodyA, bodyB);
                break;
            case 'lock':
                this.createLockConstraint(bodyA, bodyB);
                break;
            case 'spring':
                this.createSpringConstraint(bodyA, bodyB);
                break;
            default:
                console.warn("\u4E0D\u652F\u6301\u7684\u7EA6\u675F\u7C7B\u578B: ".concat(this.constraintType));
                return;
        }
        // 添加到物理世界
        if (this.constraint) {
            world.addConstraint(this.constraint);
        }
        this.initialized = true;
    };
    /**
     * 获取实体的物理体
     * @param entity 实体
     * @returns 物理体
     */
    PhysicsConstraintComponent.prototype.getBody = function (entity) {
        var physicsBody = entity.getComponent('PhysicsBodyComponent');
        if (!physicsBody)
            return null;
        // 使用类型断言访问 getCannonBody 方法
        return physicsBody.getCannonBody();
    };
    /**
     * 创建点约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    PhysicsConstraintComponent.prototype.createPointConstraint = function (bodyA, bodyB) {
        if (!bodyB) {
            console.warn('点约束需要两个物理体');
            return;
        }
        this.constraint = new CANNON.PointToPointConstraint(bodyA, new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z), bodyB, new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z), this.maxAngle ? this.maxAngle : undefined);
    };
    /**
     * 创建距离约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    PhysicsConstraintComponent.prototype.createDistanceConstraint = function (bodyA, bodyB) {
        if (!bodyB) {
            console.warn('距离约束需要两个物理体');
            return;
        }
        this.constraint = new CANNON.DistanceConstraint(bodyA, bodyB, this.distance, this.maxAngle ? this.maxAngle : undefined);
    };
    /**
     * 创建铰链约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    PhysicsConstraintComponent.prototype.createHingeConstraint = function (bodyA, bodyB) {
        var _a, _b;
        if (!bodyB) {
            console.warn('铰链约束需要两个物理体');
            return;
        }
        this.constraint = new CANNON.HingeConstraint(bodyA, bodyB, {
            pivotA: new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
            pivotB: new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
            axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
            axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z),
            collideConnected: this.collideConnected
        });
        // 设置马达
        if (this.enableMotor) {
            this.constraint.enableMotor();
            this.constraint.setMotorSpeed(this.motorSpeed);
            this.constraint.setMotorMaxForce(this.motorMaxForce);
        }
        // 设置角度限制
        if (this.minAngle !== null && this.maxAngle !== null) {
            // CANNON.js 可能没有直接的 setLimits 方法，使用自定义属性设置
            var hingeConstraint = this.constraint;
            // 使用类型断言设置限制
            (_b = (_a = hingeConstraint).setLimits) === null || _b === void 0 ? void 0 : _b.call(_a, this.minAngle, this.maxAngle);
        }
    };
    /**
     * 创建锁约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    PhysicsConstraintComponent.prototype.createLockConstraint = function (bodyA, bodyB) {
        if (!bodyB) {
            console.warn('锁约束需要两个物理体');
            return;
        }
        this.constraint = new CANNON.LockConstraint(bodyA, bodyB, {
            maxForce: this.motorMaxForce
        });
    };
    /**
     * 创建弹簧约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    PhysicsConstraintComponent.prototype.createSpringConstraint = function (bodyA, bodyB) {
        if (!bodyB) {
            console.warn('弹簧约束需要两个物理体');
            return;
        }
        // 创建一个Spring对象（不是Constraint）
        var spring = new CANNON.Spring(bodyA, bodyB, {
            localAnchorA: new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
            localAnchorB: new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
            restLength: this.distance,
            stiffness: this.stiffness,
            damping: this.damping
        });
        // 弹簧约束需要在每次更新时手动更新
        var world = this.world;
        if (world) {
            // 添加事件监听器来应用弹簧力
            world.addEventListener('postStep', function () {
                spring.applyForce();
            });
            // 存储Spring对象为自定义属性
            this.spring = spring;
            // 创建一个空的约束对象以保持一致的接口
            this.constraint = new CANNON.PointToPointConstraint(bodyA, new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z), bodyB, new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z), 0 // 使用最小力
            );
            // 禁用约束，只使用弹簧力
            // 使用类型断言访问 enabled 属性
            this.constraint.enabled = false;
        }
    };
    /**
     * 获取CANNON.js约束
     * @returns CANNON.js约束
     */
    PhysicsConstraintComponent.prototype.getConstraint = function () {
        return this.constraint;
    };
    /**
     * 启用马达（仅适用于铰链约束）
     */
    PhysicsConstraintComponent.prototype.enableMotorFunc = function () {
        if (this.constraint && this.constraintType === 'hinge') {
            this.constraint.enableMotor();
            this.enableMotor = true;
        }
    };
    /**
     * 禁用马达（仅适用于铰链约束）
     */
    PhysicsConstraintComponent.prototype.disableMotor = function () {
        if (this.constraint && this.constraintType === 'hinge') {
            this.constraint.disableMotor();
            this.enableMotor = false;
        }
    };
    /**
     * 设置马达速度（仅适用于铰链约束）
     * @param speed 马达速度
     */
    PhysicsConstraintComponent.prototype.setMotorSpeed = function (speed) {
        if (this.constraint && this.constraintType === 'hinge') {
            this.constraint.setMotorSpeed(speed);
            this.motorSpeed = speed;
        }
    };
    /**
     * 设置马达最大力（仅适用于铰链约束）
     * @param maxForce 马达最大力
     */
    PhysicsConstraintComponent.prototype.setMotorMaxForce = function (maxForce) {
        if (this.constraint && this.constraintType === 'hinge') {
            this.constraint.setMotorMaxForce(maxForce);
            this.motorMaxForce = maxForce;
        }
    };
    /**
     * 设置角度限制（仅适用于铰链约束）
     * @param min 最小角度
     * @param max 最大角度
     */
    PhysicsConstraintComponent.prototype.setLimits = function (min, max) {
        var _a, _b;
        if (this.constraint && this.constraintType === 'hinge') {
            // 使用类型断言设置限制
            (_b = (_a = this.constraint).setLimits) === null || _b === void 0 ? void 0 : _b.call(_a, min, max);
            this.minAngle = min;
            this.maxAngle = max;
        }
    };
    /**
     * 销毁约束
     */
    PhysicsConstraintComponent.prototype.dispose = function () {
        if (this.destroyed)
            return;
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.world = null;
            this.initialized = false;
        }
        // 清理Spring对象（如果存在）
        if (this.spring) {
            this.spring = null;
        }
        this.destroyed = true;
        // 调用基类的dispose方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsConstraintComponent.type = 'PhysicsConstraintComponent';
    return PhysicsConstraintComponent;
}(Component_1.Component));
