"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerPhysicsNodes = exports.CreatePhysicsMaterialNode = exports.CreateConstraintNode = exports.CollisionDetectionNode = exports.ApplyForceNode = exports.RaycastNode = void 0;
var FunctionNode_1 = require("../nodes/FunctionNode");
var FlowNode_1 = require("../nodes/FlowNode");
var Node_1 = require("../nodes/Node");
var Vector3_1 = require("../../math/Vector3");
var PhysicsSystem_1 = require("../../physics/PhysicsSystem");
var PhysicsBodyComponent_1 = require("../../physics/components/PhysicsBodyComponent");
/**
 * 射线检测节点
 * 执行物理射线检测
 */
var RaycastNode = /** @class */ (function (_super) {
    __extends(RaycastNode, _super);
    function RaycastNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    RaycastNode.prototype.initializeSockets = function () {
        // 添加输入插槽
        this.addInput({
            name: 'origin',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '射线起点'
        });
        this.addInput({
            name: 'direction',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '射线方向'
        });
        this.addInput({
            name: 'maxDistance',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '最大检测距离',
            defaultValue: 100
        });
        this.addInput({
            name: 'collisionGroup',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '碰撞组',
            defaultValue: -1
        });
        this.addInput({
            name: 'collisionMask',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '碰撞掩码',
            defaultValue: -1
        });
        // 添加输出插槽
        this.addOutput({
            name: 'hit',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否命中'
        });
        this.addOutput({
            name: 'hitEntity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '命中的实体'
        });
        this.addOutput({
            name: 'hitPoint',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '命中点'
        });
        this.addOutput({
            name: 'hitNormal',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '命中法线'
        });
        this.addOutput({
            name: 'hitDistance',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '命中距离'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    RaycastNode.prototype.execute = function () {
        // 获取输入值
        var origin = this.getInputValue('origin');
        var direction = this.getInputValue('direction');
        var maxDistance = this.getInputValue('maxDistance');
        var collisionGroup = this.getInputValue('collisionGroup');
        var collisionMask = this.getInputValue('collisionMask');
        // 检查输入值是否有效
        if (!origin || !direction) {
            this.setOutputValue('hit', false);
            return false;
        }
        // 获取物理系统
        var physicsSystem = this.graph.getWorld().getSystem(PhysicsSystem_1.PhysicsSystem);
        if (!physicsSystem) {
            this.setOutputValue('hit', false);
            return false;
        }
        // 执行射线检测
        var result = physicsSystem.raycast(origin, direction, {
            maxDistance: maxDistance,
            collisionGroup: collisionGroup,
            collisionMask: collisionMask
        });
        // 设置输出值
        this.setOutputValue('hit', result.hasHit);
        this.setOutputValue('hitEntity', result.entity);
        this.setOutputValue('hitPoint', result.point);
        this.setOutputValue('hitNormal', result.normal);
        this.setOutputValue('hitDistance', result.distance);
        return result.hasHit;
    };
    return RaycastNode;
}(FunctionNode_1.FunctionNode));
exports.RaycastNode = RaycastNode;
/**
 * 应用力节点
 * 向物理体应用力
 */
var ApplyForceNode = /** @class */ (function (_super) {
    __extends(ApplyForceNode, _super);
    function ApplyForceNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ApplyForceNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标实体'
        });
        this.addInput({
            name: 'force',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '力向量'
        });
        this.addInput({
            name: 'localPoint',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '局部应用点',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ApplyForceNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var force = this.getInputValue('force');
        var localPoint = this.getInputValue('localPoint');
        // 检查输入值是否有效
        if (!entity || !force) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 检查实体是否有物理体组件
        if (!entity.hasComponent(PhysicsBodyComponent_1.PhysicsBodyComponent.TYPE)) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 获取物理体组件
        var physicsBody = entity.getComponent(PhysicsBodyComponent_1.PhysicsBodyComponent.TYPE);
        // 应用力
        var success = physicsBody.applyForce(force, localPoint);
        // 设置输出值
        this.setOutputValue('success', success);
        // 触发输出流程
        this.triggerFlow('flow');
        return success;
    };
    return ApplyForceNode;
}(FlowNode_1.FlowNode));
exports.ApplyForceNode = ApplyForceNode;
/**
 * 碰撞检测节点
 * 检测两个实体之间的碰撞
 */
var CollisionDetectionNode = /** @class */ (function (_super) {
    __extends(CollisionDetectionNode, _super);
    function CollisionDetectionNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CollisionDetectionNode.prototype.initializeSockets = function () {
        // 添加输入插槽
        this.addInput({
            name: 'entityA',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体A'
        });
        this.addInput({
            name: 'entityB',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体B'
        });
        // 添加输出插槽
        this.addOutput({
            name: 'colliding',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否碰撞'
        });
        this.addOutput({
            name: 'contactPoint',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '接触点'
        });
        this.addOutput({
            name: 'contactNormal',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '接触法线'
        });
        this.addOutput({
            name: 'penetrationDepth',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '穿透深度'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CollisionDetectionNode.prototype.execute = function () {
        // 获取输入值
        var entityA = this.getInputValue('entityA');
        var entityB = this.getInputValue('entityB');
        // 检查输入值是否有效
        if (!entityA || !entityB) {
            this.setOutputValue('colliding', false);
            return false;
        }
        // 获取物理系统
        var physicsSystem = this.graph.getWorld().getSystem(PhysicsSystem_1.PhysicsSystem);
        if (!physicsSystem) {
            this.setOutputValue('colliding', false);
            return false;
        }
        // 检测碰撞
        var result = physicsSystem.testCollision(entityA, entityB);
        // 设置输出值
        this.setOutputValue('colliding', result.colliding);
        this.setOutputValue('contactPoint', result.contactPoint);
        this.setOutputValue('contactNormal', result.contactNormal);
        this.setOutputValue('penetrationDepth', result.penetrationDepth);
        return result.colliding;
    };
    return CollisionDetectionNode;
}(FunctionNode_1.FunctionNode));
exports.CollisionDetectionNode = CollisionDetectionNode;
/**
 * 物理约束节点
 * 创建物理约束
 */
var CreateConstraintNode = /** @class */ (function (_super) {
    __extends(CreateConstraintNode, _super);
    function CreateConstraintNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateConstraintNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entityA',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体A'
        });
        this.addInput({
            name: 'entityB',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体B'
        });
        this.addInput({
            name: 'constraintType',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '约束类型',
            defaultValue: 'hinge'
        });
        this.addInput({
            name: 'pivotA',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体A上的枢轴点',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        this.addInput({
            name: 'pivotB',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体B上的枢轴点',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        this.addInput({
            name: 'axisA',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体A上的轴',
            defaultValue: new Vector3_1.Vector3(0, 1, 0)
        });
        this.addInput({
            name: 'axisB',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '实体B上的轴',
            defaultValue: new Vector3_1.Vector3(0, 1, 0)
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'constraintId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '约束ID'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateConstraintNode.prototype.execute = function () {
        // 获取输入值
        var entityA = this.getInputValue('entityA');
        var entityB = this.getInputValue('entityB');
        var constraintType = this.getInputValue('constraintType');
        var pivotA = this.getInputValue('pivotA');
        var pivotB = this.getInputValue('pivotB');
        var axisA = this.getInputValue('axisA');
        var axisB = this.getInputValue('axisB');
        // 检查输入值是否有效
        if (!entityA || !entityB) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 获取物理系统
        var physicsSystem = this.graph.getWorld().getSystem(PhysicsSystem_1.PhysicsSystem);
        if (!physicsSystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 创建约束
        var result = physicsSystem.createConstraint(entityA, entityB, {
            type: constraintType,
            pivotA: pivotA,
            pivotB: pivotB,
            axisA: axisA,
            axisB: axisB
        });
        // 设置输出值
        this.setOutputValue('constraintId', result.id);
        this.setOutputValue('success', result.success);
        // 触发输出流程
        this.triggerFlow('flow');
        return result.success;
    };
    return CreateConstraintNode;
}(FlowNode_1.FlowNode));
exports.CreateConstraintNode = CreateConstraintNode;
/**
 * 物理材质节点
 * 创建物理材质
 */
var CreatePhysicsMaterialNode = /** @class */ (function (_super) {
    __extends(CreatePhysicsMaterialNode, _super);
    function CreatePhysicsMaterialNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreatePhysicsMaterialNode.prototype.initializeSockets = function () {
        // 添加输入插槽
        this.addInput({
            name: 'friction',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '摩擦系数',
            defaultValue: 0.3
        });
        this.addInput({
            name: 'restitution',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '恢复系数',
            defaultValue: 0.3
        });
        // 添加输出插槽
        this.addOutput({
            name: 'material',
            type: Node_1.SocketType.DATA,
            dataType: 'PhysicsMaterial',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '物理材质'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreatePhysicsMaterialNode.prototype.execute = function () {
        // 获取输入值
        var friction = this.getInputValue('friction');
        var restitution = this.getInputValue('restitution');
        // 获取物理系统
        var physicsSystem = this.graph.getWorld().getSystem(PhysicsSystem_1.PhysicsSystem);
        if (!physicsSystem) {
            return null;
        }
        // 创建物理材质
        var material = physicsSystem.createMaterial({
            friction: friction,
            restitution: restitution
        });
        // 设置输出值
        this.setOutputValue('material', material);
        return material;
    };
    return CreatePhysicsMaterialNode;
}(FunctionNode_1.FunctionNode));
exports.CreatePhysicsMaterialNode = CreatePhysicsMaterialNode;
/**
 * 注册物理节点
 * @param registry 节点注册表
 */
function registerPhysicsNodes(registry) {
    // 注册射线检测节点
    registry.registerNodeType({
        type: 'physics/raycast',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: RaycastNode,
        label: '射线检测',
        description: '执行物理射线检测',
        icon: 'ray',
        color: '#E91E63',
        tags: ['physics', 'raycast', 'collision']
    });
    // 注册应用力节点
    registry.registerNodeType({
        type: 'physics/applyForce',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: ApplyForceNode,
        label: '应用力',
        description: '向物理体应用力',
        icon: 'force',
        color: '#E91E63',
        tags: ['physics', 'force', 'dynamics']
    });
    // 注册碰撞检测节点
    registry.registerNodeType({
        type: 'physics/collisionDetection',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CollisionDetectionNode,
        label: '碰撞检测',
        description: '检测两个实体之间的碰撞',
        icon: 'collision',
        color: '#E91E63',
        tags: ['physics', 'collision', 'detection']
    });
    // 注册物理约束节点
    registry.registerNodeType({
        type: 'physics/createConstraint',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreateConstraintNode,
        label: '创建约束',
        description: '创建物理约束',
        icon: 'constraint',
        color: '#E91E63',
        tags: ['physics', 'constraint', 'joint']
    });
    // 注册物理材质节点
    registry.registerNodeType({
        type: 'physics/createMaterial',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreatePhysicsMaterialNode,
        label: '创建物理材质',
        description: '创建物理材质',
        icon: 'material',
        color: '#E91E63',
        tags: ['physics', 'material']
    });
}
exports.registerPhysicsNodes = registerPhysicsNodes;
