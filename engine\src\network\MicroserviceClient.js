"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MicroserviceClient = void 0;
/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
var events_1 = require("events");
var Debug_1 = require("../utils/Debug");
var ServiceDiscoveryClient_1 = require("./ServiceDiscoveryClient");
/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
var MicroserviceClient = /** @class */ (function (_super) {
    __extends(MicroserviceClient, _super);
    /**
     * 创建微服务客户端
     * @param config 配置
     */
    function MicroserviceClient(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 服务发现客户端 */
        _this.serviceDiscoveryClient = null;
        /** 请求缓存 */
        _this.requestCache = new Map();
        /** 服务实例缓存 */
        _this.serviceInstanceCache = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        // 默认配置
        _this.config = __assign({ serviceDiscoveryClient: undefined, apiGatewayUrl: 'http://localhost:3000/api', useApiGateway: true, useServiceDiscovery: true, requestTimeout: 30000, retryCount: 3, retryInterval: 1000, enableRequestCache: true, requestCacheTime: 60000, authToken: '' }, config);
        // 设置服务发现客户端
        if (_this.config.serviceDiscoveryClient) {
            _this.serviceDiscoveryClient = _this.config.serviceDiscoveryClient;
        }
        else if (_this.config.useServiceDiscovery) {
            _this.serviceDiscoveryClient = new ServiceDiscoveryClient_1.ServiceDiscoveryClient();
        }
        return _this;
    }
    /**
     * 初始化客户端
     */
    MicroserviceClient.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 初始化服务发现客户端
        if (this.serviceDiscoveryClient) {
            this.serviceDiscoveryClient.initialize();
        }
        this.initialized = true;
        Debug_1.Debug.log('MicroserviceClient', 'Microservice client initialized');
    };
    /**
     * 设置认证令牌
     * @param token 认证令牌
     */
    MicroserviceClient.prototype.setAuthToken = function (token) {
        this.config.authToken = token;
    };
    /**
     * 发送请求到服务
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 响应数据
     */
    MicroserviceClient.prototype.request = function (serviceName, endpoint, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var mergedOptions, cacheKey, cached, url, instances, instance, protocol, response, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 检查是否已初始化
                        if (!this.initialized) {
                            this.initialize();
                        }
                        mergedOptions = __assign({ method: 'GET', headers: {}, body: undefined, useCache: this.config.enableRequestCache, cacheTime: this.config.requestCacheTime, timeout: this.config.requestTimeout, retryCount: this.config.retryCount, retryInterval: this.config.retryInterval }, options);
                        // 添加认证头
                        if (this.config.authToken) {
                            mergedOptions.headers['Authorization'] = "Bearer ".concat(this.config.authToken);
                        }
                        // 添加内容类型头
                        if (mergedOptions.body && !mergedOptions.headers['Content-Type']) {
                            mergedOptions.headers['Content-Type'] = 'application/json';
                        }
                        cacheKey = this.generateCacheKey(serviceName, endpoint, mergedOptions);
                        // 检查缓存
                        if (mergedOptions.useCache) {
                            cached = this.requestCache.get(cacheKey);
                            if (cached && Date.now() < cached.expireTime) {
                                return [2 /*return*/, cached.data];
                            }
                        }
                        if (!this.config.useApiGateway) return [3 /*break*/, 1];
                        // 通过API网关请求
                        url = "".concat(this.config.apiGatewayUrl, "/").concat(serviceName).concat(endpoint.startsWith('/') ? endpoint : "/".concat(endpoint));
                        return [3 /*break*/, 4];
                    case 1:
                        if (!(this.config.useServiceDiscovery && this.serviceDiscoveryClient)) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.discoverServiceInstances(serviceName)];
                    case 2:
                        instances = _a.sent();
                        if (instances.length === 0) {
                            throw new Error("No instances found for service ".concat(serviceName));
                        }
                        instance = this.selectServiceInstance(instances);
                        protocol = instance.secure ? 'https' : 'http';
                        url = "".concat(protocol, "://").concat(instance.host, ":").concat(instance.port).concat(endpoint.startsWith('/') ? endpoint : "/".concat(endpoint));
                        return [3 /*break*/, 4];
                    case 3: throw new Error('Either API gateway or service discovery must be enabled');
                    case 4:
                        _a.trys.push([4, 6, , 7]);
                        return [4 /*yield*/, this.sendRequest(url, mergedOptions)];
                    case 5:
                        response = _a.sent();
                        // 缓存响应
                        if (mergedOptions.useCache) {
                            this.requestCache.set(cacheKey, {
                                data: response,
                                expireTime: Date.now() + mergedOptions.cacheTime,
                            });
                        }
                        return [2 /*return*/, response];
                    case 6:
                        error_1 = _a.sent();
                        Debug_1.Debug.error('MicroserviceClient', "Request to ".concat(serviceName, "/").concat(endpoint, " failed:"), error_1);
                        throw error_1;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 发送请求
     * @param url URL
     * @param options 请求选项
     * @returns 响应数据
     */
    MicroserviceClient.prototype.sendRequest = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            var retries, lastError, _loop_1, state_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        retries = 0;
                        lastError = null;
                        _loop_1 = function () {
                            var controller_1, timeoutId, fetchOptions, response, data, error_2;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        _b.trys.push([0, 3, , 6]);
                                        controller_1 = new AbortController();
                                        timeoutId = setTimeout(function () { return controller_1.abort(); }, options.timeout);
                                        fetchOptions = {
                                            method: options.method,
                                            headers: options.headers,
                                            signal: controller_1.signal,
                                        };
                                        // 添加请求体
                                        if (options.body !== undefined) {
                                            fetchOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
                                        }
                                        return [4 /*yield*/, fetch(url, fetchOptions)];
                                    case 1:
                                        response = _b.sent();
                                        // 清除超时
                                        clearTimeout(timeoutId);
                                        // 检查响应状态
                                        if (!response.ok) {
                                            throw new Error("HTTP error ".concat(response.status, ": ").concat(response.statusText));
                                        }
                                        return [4 /*yield*/, response.json()];
                                    case 2:
                                        data = _b.sent();
                                        return [2 /*return*/, { value: data }];
                                    case 3:
                                        error_2 = _b.sent();
                                        lastError = error_2;
                                        // 如果是中止错误，则不重试
                                        if (error_2 instanceof DOMException && error_2.name === 'AbortError') {
                                            throw new Error("Request timeout after ".concat(options.timeout, "ms"));
                                        }
                                        // 增加重试次数
                                        retries++;
                                        if (!(retries <= options.retryCount)) return [3 /*break*/, 5];
                                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, options.retryInterval); })];
                                    case 4:
                                        _b.sent();
                                        _b.label = 5;
                                    case 5: return [3 /*break*/, 6];
                                    case 6: return [2 /*return*/];
                                }
                            });
                        };
                        _a.label = 1;
                    case 1:
                        if (!(retries <= options.retryCount)) return [3 /*break*/, 3];
                        return [5 /*yield**/, _loop_1()];
                    case 2:
                        state_1 = _a.sent();
                        if (typeof state_1 === "object")
                            return [2 /*return*/, state_1.value];
                        return [3 /*break*/, 1];
                    case 3: 
                    // 所有重试都失败
                    throw lastError || new Error('Request failed');
                }
            });
        });
    };
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    MicroserviceClient.prototype.discoverServiceInstances = function (serviceName) {
        return __awaiter(this, void 0, void 0, function () {
            var instances, error_3, cachedInstances;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.serviceDiscoveryClient) {
                            throw new Error('Service discovery client is not available');
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.serviceDiscoveryClient.discoverService(serviceName)];
                    case 2:
                        instances = _a.sent();
                        // 更新缓存
                        this.serviceInstanceCache.set(serviceName, instances);
                        return [2 /*return*/, instances];
                    case 3:
                        error_3 = _a.sent();
                        cachedInstances = this.serviceInstanceCache.get(serviceName);
                        if (cachedInstances && cachedInstances.length > 0) {
                            Debug_1.Debug.warn('MicroserviceClient', "Using cached instances for service ".concat(serviceName));
                            return [2 /*return*/, cachedInstances];
                        }
                        throw error_3;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 选择服务实例
     * @param instances 服务实例列表
     * @returns 选择的服务实例
     */
    MicroserviceClient.prototype.selectServiceInstance = function (instances) {
        // 过滤出健康的实例
        var healthyInstances = instances.filter(function (instance) { return instance.status === 'UP'; });
        if (healthyInstances.length === 0) {
            // 如果没有健康的实例，则使用所有实例
            Debug_1.Debug.warn('MicroserviceClient', 'No healthy instances found, using all instances');
            return instances[Math.floor(Math.random() * instances.length)];
        }
        // 随机选择一个健康的实例（简单的负载均衡）
        return healthyInstances[Math.floor(Math.random() * healthyInstances.length)];
    };
    /**
     * 生成缓存键
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 缓存键
     */
    MicroserviceClient.prototype.generateCacheKey = function (serviceName, endpoint, options) {
        var method = options.method, body = options.body;
        var bodyString = body ? JSON.stringify(body) : '';
        return "".concat(method, ":").concat(serviceName, ":").concat(endpoint, ":").concat(bodyString);
    };
    /**
     * 清除请求缓存
     * @param serviceName 服务名称（可选，如果提供则只清除该服务的缓存）
     * @param endpoint 端点（可选，如果提供则只清除该端点的缓存）
     */
    MicroserviceClient.prototype.clearRequestCache = function (serviceName, endpoint) {
        if (!serviceName) {
            // 清除所有缓存
            this.requestCache.clear();
            return;
        }
        var prefix = endpoint
            ? "".concat(serviceName, ":").concat(endpoint)
            : "".concat(serviceName, ":");
        // 清除匹配的缓存
        for (var _i = 0, _a = this.requestCache.keys(); _i < _a.length; _i++) {
            var key = _a[_i];
            if (key.includes(prefix)) {
                this.requestCache.delete(key);
            }
        }
    };
    /**
     * 销毁客户端
     */
    MicroserviceClient.prototype.destroy = function () {
        // 清除缓存
        this.requestCache.clear();
        this.serviceInstanceCache.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
        Debug_1.Debug.log('MicroserviceClient', 'Microservice client destroyed');
    };
    return MicroserviceClient;
}(events_1.EventEmitter));
exports.MicroserviceClient = MicroserviceClient;
