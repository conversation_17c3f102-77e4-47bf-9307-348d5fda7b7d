"use strict";
/**
 * 角色控制器模块
 *
 * 提供角色控制器相关的类和接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmotionBlendController = exports.ControllerPresetType = exports.CharacterControllerPresetManager = exports.ActionPriority = exports.ActionType = exports.ActionControlSystem = exports.CharacterState = exports.CharacterMovementMode = exports.AdvancedCharacterController = void 0;
// 导出高级角色控制器
var AdvancedCharacterController_1 = require("./AdvancedCharacterController");
Object.defineProperty(exports, "AdvancedCharacterController", { enumerable: true, get: function () { return AdvancedCharacterController_1.AdvancedCharacterController; } });
Object.defineProperty(exports, "CharacterMovementMode", { enumerable: true, get: function () { return AdvancedCharacterController_1.CharacterMovementMode; } });
Object.defineProperty(exports, "CharacterState", { enumerable: true, get: function () { return AdvancedCharacterController_1.CharacterState; } });
// 导出动作控制系统
var ActionControlSystem_1 = require("./ActionControlSystem");
Object.defineProperty(exports, "ActionControlSystem", { enumerable: true, get: function () { return ActionControlSystem_1.ActionControlSystem; } });
Object.defineProperty(exports, "ActionType", { enumerable: true, get: function () { return ActionControlSystem_1.ActionType; } });
Object.defineProperty(exports, "ActionPriority", { enumerable: true, get: function () { return ActionControlSystem_1.ActionPriority; } });
// 导出角色控制器预设管理器
var CharacterControllerPresetManager_1 = require("./CharacterControllerPresetManager");
Object.defineProperty(exports, "CharacterControllerPresetManager", { enumerable: true, get: function () { return CharacterControllerPresetManager_1.CharacterControllerPresetManager; } });
Object.defineProperty(exports, "ControllerPresetType", { enumerable: true, get: function () { return CharacterControllerPresetManager_1.ControllerPresetType; } });
// 导出情感混合控制器
var EmotionBlendController_1 = require("./EmotionBlendController");
Object.defineProperty(exports, "EmotionBlendController", { enumerable: true, get: function () { return EmotionBlendController_1.EmotionBlendController; } });
