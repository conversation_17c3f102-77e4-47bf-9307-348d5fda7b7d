"use strict";
/**
 * 动画相关的可视化脚本节点
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAnimationNodes = exports.GetAnimationStateNode = exports.SetAnimationSpeedNode = exports.StopAnimationNode = exports.PlayAnimationNode = void 0;
var VisualScriptNode_1 = require("../VisualScriptNode");
var NodeRegistry_1 = require("../NodeRegistry");
/**
 * 播放动画节点
 */
var PlayAnimationNode = /** @class */ (function (_super) {
    __extends(PlayAnimationNode, _super);
    function PlayAnimationNode() {
        var _this = _super.call(this, 'PlayAnimation', '播放动画') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('entity', 'entity', '实体');
        _this.addInput('clipName', 'string', '动画名称');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    PlayAnimationNode.prototype.execute = function (inputs) {
        if (inputs.trigger && inputs.entity && inputs.clipName) {
            var animationComponent = inputs.entity.getComponent('AnimationComponent');
            if (animationComponent) {
                animationComponent.play(inputs.clipName);
                return { completed: true };
            }
        }
        return {};
    };
    return PlayAnimationNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.PlayAnimationNode = PlayAnimationNode;
/**
 * 停止动画节点
 */
var StopAnimationNode = /** @class */ (function (_super) {
    __extends(StopAnimationNode, _super);
    function StopAnimationNode() {
        var _this = _super.call(this, 'StopAnimation', '停止动画') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('entity', 'entity', '实体');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    StopAnimationNode.prototype.execute = function (inputs) {
        if (inputs.trigger && inputs.entity) {
            var animationComponent = inputs.entity.getComponent('AnimationComponent');
            if (animationComponent) {
                animationComponent.stop();
                return { completed: true };
            }
        }
        return {};
    };
    return StopAnimationNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.StopAnimationNode = StopAnimationNode;
/**
 * 设置动画速度节点
 */
var SetAnimationSpeedNode = /** @class */ (function (_super) {
    __extends(SetAnimationSpeedNode, _super);
    function SetAnimationSpeedNode() {
        var _this = _super.call(this, 'SetAnimationSpeed', '设置动画速度') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('entity', 'entity', '实体');
        _this.addInput('speed', 'number', '速度');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    SetAnimationSpeedNode.prototype.execute = function (inputs) {
        if (inputs.trigger && inputs.entity && typeof inputs.speed === 'number') {
            var animationComponent = inputs.entity.getComponent('AnimationComponent');
            if (animationComponent) {
                var animator = animationComponent.getAnimator();
                if (animator) {
                    animator.setTimeScale(inputs.speed);
                    return { completed: true };
                }
            }
        }
        return {};
    };
    return SetAnimationSpeedNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.SetAnimationSpeedNode = SetAnimationSpeedNode;
/**
 * 获取动画状态节点
 */
var GetAnimationStateNode = /** @class */ (function (_super) {
    __extends(GetAnimationStateNode, _super);
    function GetAnimationStateNode() {
        var _this = _super.call(this, 'GetAnimationState', '获取动画状态') || this;
        _this.addInput('entity', 'entity', '实体');
        _this.addOutput('isPlaying', 'boolean', '是否播放中');
        _this.addOutput('currentClip', 'string', '当前动画');
        _this.addOutput('time', 'number', '播放时间');
        return _this;
    }
    GetAnimationStateNode.prototype.execute = function (inputs) {
        if (inputs.entity) {
            var animationComponent = inputs.entity.getComponent('AnimationComponent');
            if (animationComponent) {
                var animator = animationComponent.getAnimator();
                return {
                    isPlaying: animationComponent.getIsPlaying(),
                    currentClip: animationComponent.getCurrentClip(),
                    time: animator ? animator.getTime() : 0
                };
            }
        }
        return {
            isPlaying: false,
            currentClip: null,
            time: 0
        };
    };
    return GetAnimationStateNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.GetAnimationStateNode = GetAnimationStateNode;
/**
 * 注册动画节点
 */
function registerAnimationNodes() {
    NodeRegistry_1.NodeRegistry.register('PlayAnimation', PlayAnimationNode);
    NodeRegistry_1.NodeRegistry.register('StopAnimation', StopAnimationNode);
    NodeRegistry_1.NodeRegistry.register('SetAnimationSpeed', SetAnimationSpeedNode);
    NodeRegistry_1.NodeRegistry.register('GetAnimationState', GetAnimationStateNode);
}
exports.registerAnimationNodes = registerAnimationNodes;
