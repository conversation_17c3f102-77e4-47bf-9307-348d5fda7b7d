"use strict";
/**
 * UI3DSystem.ts
 *
 * 3D UI系统，管理3D空间中的界面元素
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI3DSystem = void 0;
var System_1 = require("../../core/System");
var three_1 = require("three");
var UI3DComponent_1 = require("../components/UI3DComponent");
var UIComponent_1 = require("../components/UIComponent");
/**
 * 3D UI系统
 * 管理3D空间中的界面元素
 */
var UI3DSystem = /** @class */ (function (_super) {
    __extends(UI3DSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config 3D UI系统配置
     */
    function UI3DSystem(world, uiSystem, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级（默认为0）
        _super.call(this, 0) || this;
        // 保存世界引用
        _this.world = world;
        _this.uiSystem = uiSystem;
        _this.config = {
            debug: config.debug || false,
            defaultFont: config.defaultFont || 'Arial',
            defaultFontSize: config.defaultFontSize || 24,
            defaultFontColor: config.defaultFontColor || '#ffffff',
            defaultBackgroundColor: config.defaultBackgroundColor || 'rgba(0, 0, 0, 0.5)',
            defaultBorderColor: config.defaultBorderColor || '#ffffff',
            defaultBorderWidth: config.defaultBorderWidth || 2,
            defaultBorderRadius: config.defaultBorderRadius || 8,
            defaultPadding: config.defaultPadding || 16,
            defaultInteractionDistance: config.defaultInteractionDistance || 10,
            defaultHoverColor: config.defaultHoverColor || '#aaaaff',
            defaultActiveColor: config.defaultActiveColor || '#5555ff',
            defaultBillboardMode: config.defaultBillboardMode || UI3DComponent_1.BillboardMode.Y_AXIS
        };
        // 创建3D UI根节点
        _this.root = new three_1.Group();
        _this.root.name = 'UI3D-Root';
        // 获取活跃场景
        var activeScene = _this.world.getActiveScene();
        if (activeScene) {
            _this.scene = activeScene.getThreeScene();
            // 将根节点添加到场景
            if (_this.scene) {
                _this.scene.add(_this.root);
            }
        }
        return _this;
    }
    /**
     * 创建3D UI元素
     * @param entity 实体
     * @param type UI元素类型
     * @param props UI元素属性
     * @returns 创建的3D UI组件
     */
    UI3DSystem.prototype.createUIElement = function (entity, type, props) {
        if (props === void 0) { props = {}; }
        // 合并默认属性和提供的属性
        var mergedProps = __assign({ type: type, fontFamily: this.config.defaultFont, fontSize: this.config.defaultFontSize, fontColor: this.config.defaultFontColor, backgroundColor: this.config.defaultBackgroundColor, borderColor: this.config.defaultBorderColor, borderWidth: this.config.defaultBorderWidth, borderRadius: this.config.defaultBorderRadius, padding: this.config.defaultPadding, interactionDistance: this.config.defaultInteractionDistance, hoverColor: this.config.defaultHoverColor, activeColor: this.config.defaultActiveColor, billboardMode: this.config.defaultBillboardMode }, props);
        // 创建3D UI组件
        var component = new UI3DComponent_1.UI3DComponent(entity, mergedProps);
        // 注册到UI系统
        this.uiSystem.registerUIComponent(entity, component);
        // 将3D对象添加到根节点
        if (component.group) {
            this.root.add(component.group);
        }
        return component;
    };
    /**
     * 创建3D按钮
     * @param entity 实体
     * @param text 按钮文本
     * @param props 按钮属性
     * @returns 创建的按钮组件
     */
    UI3DSystem.prototype.createButton = function (entity, text, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.BUTTON, __assign({ textContent: text, backgroundColor: props.backgroundColor || 'rgba(60, 60, 60, 0.8)' }, props));
    };
    /**
     * 创建3D文本
     * @param entity 实体
     * @param text 文本内容
     * @param props 文本属性
     * @returns 创建的文本组件
     */
    UI3DSystem.prototype.createText = function (entity, text, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.TEXT, __assign({ textContent: text, backgroundColor: 'transparent' }, props));
    };
    /**
     * 创建3D图像
     * @param entity 实体
     * @param texture 纹理
     * @param props 图像属性
     * @returns 创建的图像组件
     */
    UI3DSystem.prototype.createImage = function (entity, texture, props) {
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.IMAGE, __assign({ backgroundColor: 'transparent' }, props));
        // 设置纹理
        if (component.mesh) {
            var material = component.mesh.material;
            material.map = texture;
            material.needsUpdate = true;
        }
        return component;
    };
    /**
     * 创建3D面板
     * @param entity 实体
     * @param props 面板属性
     * @returns 创建的面板组件
     */
    UI3DSystem.prototype.createPanel = function (entity, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.PANEL, __assign({ backgroundColor: props.backgroundColor || 'rgba(30, 30, 30, 0.8)' }, props));
    };
    /**
     * 创建3D窗口
     * @param entity 实体
     * @param title 窗口标题
     * @param props 窗口属性
     * @returns 创建的窗口组件
     */
    UI3DSystem.prototype.createWindow = function (entity, title, props) {
        if (title === void 0) { title = ''; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.WINDOW, __assign({ backgroundColor: props.backgroundColor || 'rgba(30, 30, 30, 0.8)' }, props));
        // 如果有标题，更新画布内容
        if (title && component.canvas && component.context) {
            var canvas = component.canvas, context = component.context;
            // 保存当前内容
            var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            // 绘制标题栏
            context.fillStyle = 'rgba(50, 50, 50, 0.9)';
            context.fillRect(0, 0, canvas.width, 40);
            // 绘制标题
            context.fillStyle = component.fontColor;
            context.font = "bold ".concat(component.fontSize, "px ").concat(component.fontFamily);
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText(title, canvas.width / 2, 20);
            // 恢复内容（在标题栏下方）
            context.putImageData(imageData, 0, 40);
            // 更新纹理
            if (component.texture) {
                component.texture.needsUpdate = true;
            }
        }
        return component;
    };
    /**
     * 创建3D UI容器
     * @param entity 实体
     * @param position 位置
     * @param rotation 旋转
     * @param scale 缩放
     * @returns 创建的容器
     */
    UI3DSystem.prototype.createContainer = function (entity, position, rotation, scale) {
        // 创建容器
        var container = new three_1.Group();
        container.name = "UI3D-Container-".concat(entity.id);
        // 设置变换
        if (position)
            container.position.copy(position);
        if (rotation) {
            if (rotation instanceof three_1.Euler) {
                container.rotation.copy(rotation);
            }
            else {
                container.setRotationQuaternion(rotation.x, rotation.y, rotation.z);
            }
        }
        if (scale)
            container.scale.copy(scale);
        // 添加到根节点
        this.root.add(container);
        return container;
    };
    /**
     * 将UI元素添加到容器
     * @param component UI组件
     * @param container 容器
     * @param localPosition 本地位置
     */
    UI3DSystem.prototype.addToContainer = function (component, container, localPosition) {
        if (component.group) {
            // 从当前父节点移除
            if (component.group.parent) {
                component.group.parent.remove(component.group);
            }
            // 设置本地位置
            if (localPosition) {
                component.group.position.copy(localPosition);
            }
            // 添加到容器
            container.add(component.group);
        }
    };
    /**
     * 从容器中移除UI元素
     * @param component UI组件
     */
    UI3DSystem.prototype.removeFromContainer = function (component) {
        if (component.group && component.group.parent) {
            component.group.parent.remove(component.group);
            this.root.add(component.group);
        }
    };
    /**
     * 创建3D UI布局
     * @param _entity 实体 - 未使用，但保留以便将来扩展
     * @param container 容器
     * @param elements UI元素列表
     * @param layout 布局类型
     * @param spacing 元素间距
     */
    UI3DSystem.prototype.createLayout = function (_entity, container, elements, layout, spacing) {
        if (layout === void 0) { layout = 'vertical'; }
        if (spacing === void 0) { spacing = 0.1; }
        if (elements.length === 0)
            return;
        // 计算布局
        switch (layout) {
            case 'horizontal':
                // 水平布局
                var offsetX = 0;
                for (var _i = 0, elements_1 = elements; _i < elements_1.length; _i++) {
                    var element = elements_1[_i];
                    if (element.group) {
                        // 计算元素宽度
                        var width = element.size.x / 100; // 转换为米
                        // 设置位置
                        element.group.setPosition(offsetX + width / 2, 0, 0);
                        // 更新偏移
                        offsetX += width + spacing;
                        // 添加到容器
                        this.addToContainer(element, container);
                    }
                }
                break;
            case 'vertical':
                // 垂直布局
                var offsetY = 0;
                for (var _a = 0, elements_2 = elements; _a < elements_2.length; _a++) {
                    var element = elements_2[_a];
                    if (element.group) {
                        // 计算元素高度
                        var height = element.size.y / 100; // 转换为米
                        // 设置位置
                        element.group.setPosition(0, -offsetY - height / 2, 0);
                        // 更新偏移
                        offsetY += height + spacing;
                        // 添加到容器
                        this.addToContainer(element, container);
                    }
                }
                break;
            case 'grid':
                // 网格布局
                var columns = Math.ceil(Math.sqrt(elements.length));
                var row = 0, col = 0;
                for (var _b = 0, elements_3 = elements; _b < elements_3.length; _b++) {
                    var element = elements_3[_b];
                    if (element.group) {
                        // 计算元素尺寸
                        var width = element.size.x / 100; // 转换为米
                        var height = element.size.y / 100; // 转换为米
                        // 设置位置
                        element.group.setPosition(col * (width + spacing), -row * (height + spacing), 0);
                        // 更新行列
                        col++;
                        if (col >= columns) {
                            col = 0;
                            row++;
                        }
                        // 添加到容器
                        this.addToContainer(element, container);
                    }
                }
                break;
        }
    };
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
     */
    UI3DSystem.prototype.update = function (_deltaTime) {
        // 3D UI系统不需要额外的更新逻辑，因为UI系统会更新所有UI组件
    };
    /**
     * 渲染系统
     */
    UI3DSystem.prototype.render = function () {
        // 3D UI系统不需要额外的渲染逻辑，因为UI系统会渲染所有UI组件
    };
    /**
     * 销毁系统
     */
    UI3DSystem.prototype.dispose = function () {
        // 从场景中移除根节点
        if (this.scene) {
            this.scene.remove(this.root);
        }
        // 清空根节点
        while (this.root.children.length > 0) {
            this.root.remove(this.root.children[0]);
        }
    };
    return UI3DSystem;
}(System_1.System));
exports.UI3DSystem = UI3DSystem;
