"use strict";
/**
 * IUIElement.ts
 *
 * 定义UI元素的基本接口
 * 所有UI元素都应该实现这个接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIAnimationType = exports.UIEventType = exports.UILayoutType = void 0;
/**
 * UI布局类型枚举
 */
var UILayoutType;
(function (UILayoutType) {
    UILayoutType["NONE"] = "none";
    UILayoutType["GRID"] = "grid";
    UILayoutType["FLEX"] = "flex";
    UILayoutType["ABSOLUTE"] = "absolute";
    UILayoutType["RELATIVE"] = "relative";
})(UILayoutType || (exports.UILayoutType = UILayoutType = {}));
/**
 * UI事件类型枚举
 */
var UIEventType;
(function (UIEventType) {
    UIEventType["CLICK"] = "click";
    UIEventType["HOVER"] = "hover";
    UIEventType["DRAG_START"] = "dragstart";
    UIEventType["DRAG"] = "drag";
    UIEventType["DRAG_END"] = "dragend";
    UIEventType["FOCUS"] = "focus";
    UIEventType["BLUR"] = "blur";
    UIEventType["KEY_DOWN"] = "keydown";
    UIEventType["KEY_UP"] = "keyup";
})(UIEventType || (exports.UIEventType = UIEventType = {}));
/**
 * UI动画类型枚举
 */
var UIAnimationType;
(function (UIAnimationType) {
    UIAnimationType["FADE"] = "fade";
    UIAnimationType["SCALE"] = "scale";
    UIAnimationType["MOVE"] = "move";
    UIAnimationType["ROTATE"] = "rotate";
    UIAnimationType["COLOR"] = "color";
})(UIAnimationType || (exports.UIAnimationType = UIAnimationType = {}));
