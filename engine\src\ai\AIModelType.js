"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelType = void 0;
/**
 * AI模型类型
 */
var AIModelType;
(function (AIModelType) {
    /** GPT模型 */
    AIModelType["GPT"] = "gpt";
    /** Stable Diffusion模型 */
    AIModelType["STABLE_DIFFUSION"] = "stable-diffusion";
    /** BERT模型 */
    AIModelType["BERT"] = "bert";
    /** RoBERTa模型 - 用于更高级的情感分析 */
    AIModelType["ROBERTA"] = "roberta";
    /** DistilBERT模型 - 轻量级BERT变体 */
    AIModelType["DISTILBERT"] = "distilbert";
    /** ALBERT模型 - 轻量级BERT变体 */
    AIModelType["ALBERT"] = "albert";
    /** XLNet模型 - 用于高级文本理解 */
    AIModelType["XLNET"] = "xlnet";
    /** BART模型 - 用于文本生成和理解 */
    AIModelType["BART"] = "bart";
    /** T5模型 - 用于文本到文本转换 */
    AIModelType["T5"] = "t5";
    /** 自定义模型 */
    AIModelType["CUSTOM"] = "custom";
})(AIModelType || (exports.AIModelType = AIModelType = {}));
