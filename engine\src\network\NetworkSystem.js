"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkSystem = exports.NetworkState = void 0;
var System_1 = require("../core/System");
var EventEmitter_1 = require("../utils/EventEmitter");
var NetworkManager_1 = require("./NetworkManager");
var NetworkEntityComponent_1 = require("./components/NetworkEntityComponent");
var NetworkTransformComponent_1 = require("./components/NetworkTransformComponent");
var NetworkUserComponent_1 = require("./components/NetworkUserComponent");
var Debug_1 = require("../utils/Debug");
var WebRTCConnectionManager_1 = require("./WebRTCConnectionManager");
var MediaStreamManager_1 = require("./MediaStreamManager");
var EntitySyncManager_1 = require("./EntitySyncManager");
var UserSessionManager_1 = require("./UserSessionManager");
var NetworkEventDispatcher_1 = require("./NetworkEventDispatcher");
var BandwidthController_1 = require("./BandwidthController");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
var DataCompressor_1 = require("./DataCompressor");
var ServiceDiscoveryClient_1 = require("./ServiceDiscoveryClient");
var MicroserviceClient_1 = require("./MicroserviceClient");
/**
 * 网络系统状态
 */
var NetworkState;
(function (NetworkState) {
    /** 已断开连接 */
    NetworkState["DISCONNECTED"] = "disconnected";
    /** 正在连接 */
    NetworkState["CONNECTING"] = "connecting";
    /** 已连接 */
    NetworkState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    NetworkState["DISCONNECTING"] = "disconnecting";
    /** 连接错误 */
    NetworkState["ERROR"] = "error";
})(NetworkState || (exports.NetworkState = NetworkState = {}));
/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
var NetworkSystem = /** @class */ (function (_super) {
    __extends(NetworkSystem, _super);
    /**
     * 创建网络系统
     * @param options 配置选项
     */
    function NetworkSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 网络状态 */
        _this.state = NetworkState.DISCONNECTED;
        /** 重连尝试次数 */
        _this.reconnectAttempts = 0;
        /** 重连定时器ID */
        _this.reconnectTimerId = null;
        /** 同步定时器ID */
        _this.syncTimerId = null;
        /** 网络实体映射表 */
        _this.networkEntities = new Map();
        /** 本地用户ID */
        _this.localUserId = null;
        /** 远程用户映射表 */
        _this.remoteUsers = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** WebRTC连接管理器 */
        _this.webRTCConnectionManager = null;
        /** 媒体流管理器 */
        _this.mediaStreamManager = null;
        /** 实体同步管理器 */
        _this.entitySyncManager = null;
        /** 用户会话管理器 */
        _this.userSessionManager = null;
        /** 网络事件分发器 */
        _this.networkEventDispatcher = null;
        /** 带宽控制器 */
        _this.bandwidthController = null;
        /** 网络质量监控器 */
        _this.networkQualityMonitor = null;
        /** 数据压缩器 */
        _this.dataCompressor = null;
        /** 服务发现客户端 */
        _this.serviceDiscoveryClient = null;
        /** 微服务客户端 */
        _this.microserviceClient = null;
        _this.options = __assign({ autoConnect: false, serverUrl: 'wss://localhost:8080', enableWebRTC: true, iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
            ], maxReconnectAttempts: 5, reconnectInterval: 3000, syncInterval: 100, enableCompression: true, enableMediaStream: true, enableAudio: false, enableVideo: false, enableScreenShare: false, enableNetworkQualityMonitor: true, enableBandwidthControl: true, bandwidthControlStrategy: BandwidthController_1.BandwidthControlStrategy.ADAPTIVE, maxUploadBandwidth: 1024 * 1024, maxDownloadBandwidth: 1024 * 1024, compressionAlgorithm: DataCompressor_1.CompressionAlgorithm.LZ_STRING, compressionLevel: DataCompressor_1.CompressionLevel.MEDIUM, enableEntitySync: true, enableUserSessionManagement: true, defaultUserRole: UserSessionManager_1.UserRole.USER, enablePermissionCheck: true, enableEventBuffer: true, enableEventLogging: false, enableServiceDiscovery: true, serviceRegistryUrl: 'http://localhost:4010/api/registry', enableMicroserviceClient: true, apiGatewayUrl: 'http://localhost:3000/api', useApiGateway: true }, options);
        // 创建数据压缩器
        if (_this.options.enableCompression) {
            _this.dataCompressor = new DataCompressor_1.DataCompressor({
                algorithm: _this.options.compressionAlgorithm,
                level: _this.options.compressionLevel,
                adaptive: true,
            });
        }
        // 创建网络质量监控器
        if (_this.options.enableNetworkQualityMonitor) {
            _this.networkQualityMonitor = new NetworkQualityMonitor_1.NetworkQualityMonitor();
        }
        // 创建带宽控制器
        if (_this.options.enableBandwidthControl) {
            _this.bandwidthController = new BandwidthController_1.BandwidthController({
                maxUploadBandwidth: _this.options.maxUploadBandwidth,
                maxDownloadBandwidth: _this.options.maxDownloadBandwidth,
                strategy: _this.options.bandwidthControlStrategy,
            });
        }
        // 创建服务发现客户端
        if (_this.options.enableServiceDiscovery) {
            _this.serviceDiscoveryClient = new ServiceDiscoveryClient_1.ServiceDiscoveryClient({
                registryUrl: _this.options.serviceRegistryUrl,
            });
        }
        // 创建微服务客户端
        if (_this.options.enableMicroserviceClient) {
            _this.microserviceClient = new MicroserviceClient_1.MicroserviceClient({
                serviceDiscoveryClient: _this.serviceDiscoveryClient,
                apiGatewayUrl: _this.options.apiGatewayUrl,
                useApiGateway: _this.options.useApiGateway,
                useServiceDiscovery: _this.options.enableServiceDiscovery,
            });
        }
        // 创建网络事件分发器
        _this.networkEventDispatcher = new NetworkEventDispatcher_1.NetworkEventDispatcher({
            useEventBuffer: _this.options.enableEventBuffer,
            enableEventLogging: _this.options.enableEventLogging,
        });
        // 创建网络管理器
        _this.networkManager = new NetworkManager_1.NetworkManager(_this.options);
        // 设置网络事件监听器
        _this.setupEventListeners();
        // 如果设置了自动连接，则连接到服务器
        if (_this.options.autoConnect && _this.options.serverUrl) {
            _this.connect(_this.options.serverUrl, _this.options.roomId);
        }
        return _this;
    }
    /**
     * 初始化系统
     */
    NetworkSystem.prototype.initialize = function () {
        var _this = this;
        Debug_1.Debug.log('NetworkSystem', 'Initializing network system');
        // 注册网络组件
        this.engine.registerComponent(NetworkEntityComponent_1.NetworkEntityComponent);
        this.engine.registerComponent(NetworkTransformComponent_1.NetworkTransformComponent);
        this.engine.registerComponent(NetworkUserComponent_1.NetworkUserComponent);
        // 初始化本地用户ID
        if (this.options.userId) {
            this.localUserId = this.options.userId;
        }
        else {
            // 生成随机用户ID
            this.localUserId = "user_".concat(Math.random().toString(36).substring(2, 9));
        }
        // 初始化服务发现客户端
        if (this.serviceDiscoveryClient) {
            this.serviceDiscoveryClient.initialize();
        }
        // 初始化微服务客户端
        if (this.microserviceClient) {
            this.microserviceClient.initialize();
        }
        // 初始化用户会话管理器
        if (this.options.enableUserSessionManagement) {
            this.userSessionManager = new UserSessionManager_1.UserSessionManager({
                defaultRole: this.options.defaultUserRole,
                enablePermissionCheck: this.options.enablePermissionCheck,
            });
            // 创建本地用户会话
            this.userSessionManager.createSession(this.localUserId, this.options.username || "User_".concat(this.localUserId.substring(5)), this.options.defaultUserRole, true);
        }
        // 初始化WebRTC连接管理器
        if (this.options.enableWebRTC) {
            this.webRTCConnectionManager = new WebRTCConnectionManager_1.WebRTCConnectionManager({
                iceServers: this.options.iceServers,
                enableDataChannel: true,
                enableAudio: this.options.enableAudio,
                enableVideo: this.options.enableVideo,
                enableScreenShare: this.options.enableScreenShare,
                useCompression: this.options.enableCompression,
                maxReconnectAttempts: this.options.maxReconnectAttempts,
                reconnectInterval: this.options.reconnectInterval,
                useNetworkQualityMonitor: this.options.enableNetworkQualityMonitor,
                useBandwidthController: this.options.enableBandwidthControl,
            });
            // 初始化WebRTC连接管理器
            this.webRTCConnectionManager.initialize(this.localUserId);
        }
        // 初始化媒体流管理器
        if (this.options.enableMediaStream) {
            this.mediaStreamManager = new MediaStreamManager_1.MediaStreamManager({
                enableDeviceEnumeration: true,
                enableDeviceChangeDetection: true,
                enableAudioProcessing: true,
                enableVideoProcessing: true,
                enableAutoPlay: true,
                enableAudioLevelMonitoring: true,
            });
        }
        // 初始化实体同步管理器
        if (this.options.enableEntitySync) {
            this.entitySyncManager = new EntitySyncManager_1.EntitySyncManager({
                defaultSyncInterval: this.options.syncInterval,
                useSpatialPartitioning: true,
                useInterpolation: true,
                useExtrapolation: true,
                useCompression: this.options.enableCompression,
                useDeltaSync: true,
                usePrioritySync: true,
                useAdaptiveSync: true,
            });
            // 初始化实体同步管理器
            if (this.localUserId && this.bandwidthController) {
                this.entitySyncManager.initialize(this.localUserId, this.bandwidthController);
            }
        }
        // 连接网络质量监控器和带宽控制器
        if (this.networkQualityMonitor && this.bandwidthController) {
            // 监听网络质量更新事件
            this.networkQualityMonitor.on('qualityUpdate', function (quality) {
                var _a;
                // 更新带宽控制器的网络质量数据
                (_a = _this.bandwidthController) === null || _a === void 0 ? void 0 : _a.setNetworkQuality(quality);
            });
        }
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkSystem.prototype.update = function (deltaTime) {
        if (this.state !== NetworkState.CONNECTED) {
            return;
        }
        // 更新网络管理器
        this.networkManager.update(deltaTime);
        // 更新WebRTC连接管理器
        if (this.webRTCConnectionManager) {
            // WebRTC连接管理器没有update方法，但可以在这里添加自定义逻辑
        }
        // 更新实体同步管理器
        if (this.entitySyncManager) {
            // 实体同步管理器通过定时器自动同步，不需要在这里调用
        }
        // 更新网络质量监控器
        if (this.networkQualityMonitor) {
            // 网络质量监控器通过定时器自动采样，不需要在这里调用
        }
        // 更新带宽控制器
        if (this.bandwidthController) {
            // 带宽控制器通过定时器自动调整，不需要在这里调用
        }
    };
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     * @param roomId 房间ID
     */
    NetworkSystem.prototype.connect = function (serverUrl, roomId) {
        var _this = this;
        if (this.state === NetworkState.CONNECTING || this.state === NetworkState.CONNECTED) {
            Debug_1.Debug.warn('NetworkSystem', 'Already connected or connecting to server');
            return;
        }
        this.state = NetworkState.CONNECTING;
        this.eventEmitter.emit('connecting');
        this.networkManager.connect(serverUrl, roomId)
            .then(function () {
            _this.state = NetworkState.CONNECTED;
            _this.reconnectAttempts = 0;
            _this.eventEmitter.emit('connected');
            // 启动同步定时器
            _this.startSyncTimer();
        })
            .catch(function (error) {
            _this.state = NetworkState.ERROR;
            _this.eventEmitter.emit('error', error);
            // 尝试重连
            _this.attemptReconnect();
        });
    };
    /**
     * 断开连接
     */
    NetworkSystem.prototype.disconnect = function () {
        var _this = this;
        if (this.state === NetworkState.DISCONNECTED || this.state === NetworkState.DISCONNECTING) {
            return;
        }
        this.state = NetworkState.DISCONNECTING;
        this.eventEmitter.emit('disconnecting');
        // 停止同步定时器
        this.stopSyncTimer();
        // 停止重连定时器
        this.stopReconnectTimer();
        this.networkManager.disconnect()
            .then(function () {
            _this.state = NetworkState.DISCONNECTED;
            _this.eventEmitter.emit('disconnected');
        })
            .catch(function (error) {
            _this.state = NetworkState.ERROR;
            _this.eventEmitter.emit('error', error);
        });
    };
    /**
     * 获取网络状态
     * @returns 网络状态
     */
    NetworkSystem.prototype.getState = function () {
        return this.state;
    };
    /**
     * 获取本地用户ID
     * @returns 本地用户ID
     */
    NetworkSystem.prototype.getLocalUserId = function () {
        return this.localUserId;
    };
    /**
     * 设置本地用户ID
     * @param userId 用户ID
     */
    NetworkSystem.prototype.setLocalUserId = function (userId) {
        this.localUserId = userId;
    };
    /**
     * 获取远程用户
     * @param userId 用户ID
     * @returns 用户实体
     */
    NetworkSystem.prototype.getRemoteUser = function (userId) {
        return this.remoteUsers.get(userId);
    };
    /**
     * 获取所有远程用户
     * @returns 用户实体映射表
     */
    NetworkSystem.prototype.getRemoteUsers = function () {
        return this.remoteUsers;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    NetworkSystem.prototype.on = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    NetworkSystem.prototype.off = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 发送消息到所有用户
     * @param type 消息类型
     * @param data 消息数据
     */
    NetworkSystem.prototype.sendToAll = function (type, data) {
        // 如果启用了数据压缩，则压缩数据
        if (this.options.enableCompression && this.dataCompressor) {
            var compressedResult = this.dataCompressor.compress(data);
            this.networkManager.sendToAll(type, compressedResult.data);
        }
        else {
            this.networkManager.sendToAll(type, data);
        }
        // 如果启用了WebRTC，则通过WebRTC发送
        if (this.options.enableWebRTC && this.webRTCConnectionManager) {
            var message = { type: type, data: data };
            this.webRTCConnectionManager.broadcastMessage(message);
        }
    };
    /**
     * 发送消息到特定用户
     * @param userId 用户ID
     * @param type 消息类型
     * @param data 消息数据
     */
    NetworkSystem.prototype.sendToUser = function (userId, type, data) {
        // 如果启用了数据压缩，则压缩数据
        if (this.options.enableCompression && this.dataCompressor) {
            var compressedResult = this.dataCompressor.compress(data);
            this.networkManager.sendToUser(userId, type, compressedResult.data);
        }
        else {
            this.networkManager.sendToUser(userId, type, data);
        }
        // 如果启用了WebRTC，则通过WebRTC发送
        if (this.options.enableWebRTC && this.webRTCConnectionManager) {
            var message = { type: type, data: data };
            this.webRTCConnectionManager.sendMessage(userId, message);
        }
    };
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @returns WebRTC连接
     */
    NetworkSystem.prototype.createWebRTCConnection = function (userId) {
        if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'WebRTC is not enabled');
            return null;
        }
        return this.webRTCConnectionManager.createConnection(userId);
    };
    /**
     * 处理WebRTC提议
     * @param userId 远程用户ID
     * @param offer 提议
     */
    NetworkSystem.prototype.handleWebRTCOffer = function (userId, offer) {
        if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'WebRTC is not enabled');
            return;
        }
        this.webRTCConnectionManager.handleOffer(userId, offer);
    };
    /**
     * 处理WebRTC应答
     * @param userId 远程用户ID
     * @param answer 应答
     */
    NetworkSystem.prototype.handleWebRTCAnswer = function (userId, answer) {
        if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'WebRTC is not enabled');
            return;
        }
        this.webRTCConnectionManager.handleAnswer(userId, answer);
    };
    /**
     * 处理WebRTC ICE候选
     * @param userId 远程用户ID
     * @param candidate ICE候选
     */
    NetworkSystem.prototype.handleWebRTCIceCandidate = function (userId, candidate) {
        if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'WebRTC is not enabled');
            return;
        }
        this.webRTCConnectionManager.handleIceCandidate(userId, candidate);
    };
    /**
     * 获取本地媒体流
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    NetworkSystem.prototype.getLocalMediaStream = function (type, config) {
        if (config === void 0) { config = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.options.enableMediaStream || !this.mediaStreamManager) {
                    Debug_1.Debug.warn('NetworkSystem', 'Media stream is not enabled');
                    return [2 /*return*/, null];
                }
                return [2 /*return*/, this.mediaStreamManager.getLocalStream(type, config)];
            });
        });
    };
    /**
     * 停止本地媒体流
     * @param streamId 流ID
     * @returns 是否成功停止
     */
    NetworkSystem.prototype.stopLocalMediaStream = function (streamId) {
        if (!this.options.enableMediaStream || !this.mediaStreamManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Media stream is not enabled');
            return false;
        }
        return this.mediaStreamManager.stopLocalStream(streamId);
    };
    /**
     * 添加远程媒体流
     * @param stream 媒体流
     * @param userId 用户ID
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    NetworkSystem.prototype.addRemoteMediaStream = function (stream, userId, type, config) {
        if (config === void 0) { config = {}; }
        if (!this.options.enableMediaStream || !this.mediaStreamManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Media stream is not enabled');
            return null;
        }
        return this.mediaStreamManager.addRemoteStream(stream, userId, type, config);
    };
    /**
     * 移除远程媒体流
     * @param streamId 流ID
     * @returns 是否成功移除
     */
    NetworkSystem.prototype.removeRemoteMediaStream = function (streamId) {
        if (!this.options.enableMediaStream || !this.mediaStreamManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Media stream is not enabled');
            return false;
        }
        return this.mediaStreamManager.removeRemoteStream(streamId);
    };
    /**
     * 获取网络质量
     * @returns 网络质量数据
     */
    NetworkSystem.prototype.getNetworkQuality = function () {
        if (!this.options.enableNetworkQualityMonitor || !this.networkQualityMonitor) {
            Debug_1.Debug.warn('NetworkSystem', 'Network quality monitor is not enabled');
            return null;
        }
        return this.networkQualityMonitor.getCurrentQuality();
    };
    /**
     * 获取带宽使用情况
     * @returns 带宽使用数据
     */
    NetworkSystem.prototype.getBandwidthUsage = function () {
        if (!this.options.enableBandwidthControl || !this.bandwidthController) {
            Debug_1.Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
            return null;
        }
        return this.bandwidthController.getBandwidthUsage();
    };
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    NetworkSystem.prototype.setBandwidthControlStrategy = function (strategy) {
        if (!this.options.enableBandwidthControl || !this.bandwidthController) {
            Debug_1.Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
            return;
        }
        this.bandwidthController.setStrategy(strategy);
    };
    /**
     * 设置最大上行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    NetworkSystem.prototype.setMaxUploadBandwidth = function (bandwidth) {
        if (!this.options.enableBandwidthControl || !this.bandwidthController) {
            Debug_1.Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
            return;
        }
        this.bandwidthController.setMaxUploadBandwidth(bandwidth);
    };
    /**
     * 设置最大下行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    NetworkSystem.prototype.setMaxDownloadBandwidth = function (bandwidth) {
        if (!this.options.enableBandwidthControl || !this.bandwidthController) {
            Debug_1.Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
            return;
        }
        this.bandwidthController.setMaxDownloadBandwidth(bandwidth);
    };
    /**
     * 添加网络实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    NetworkSystem.prototype.addNetworkEntity = function (entityId, entity) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.addEntity(entityId, entity);
        this.networkEntities.set(entityId, entity);
    };
    /**
     * 移除网络实体
     * @param entityId 实体ID
     */
    NetworkSystem.prototype.removeNetworkEntity = function (entityId) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.removeEntity(entityId);
        this.networkEntities.delete(entityId);
    };
    /**
     * 更新网络实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    NetworkSystem.prototype.updateNetworkEntity = function (entityId, entity) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.updateEntity(entityId, entity);
    };
    /**
     * 标记实体需要同步
     * @param entityId 实体ID
     */
    NetworkSystem.prototype.markEntityForSync = function (entityId) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.markEntityForSync(entityId);
    };
    /**
     * 设置实体同步优先级
     * @param entityId 实体ID
     * @param priority 优先级
     */
    NetworkSystem.prototype.setEntitySyncPriority = function (entityId, priority) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.setEntitySyncPriority(entityId, priority);
    };
    /**
     * 设置实体同步间隔
     * @param entityId 实体ID
     * @param interval 同步间隔（毫秒）
     */
    NetworkSystem.prototype.setEntitySyncInterval = function (entityId, interval) {
        if (!this.options.enableEntitySync || !this.entitySyncManager) {
            Debug_1.Debug.warn('NetworkSystem', 'Entity sync is not enabled');
            return;
        }
        this.entitySyncManager.setEntitySyncInterval(entityId, interval);
    };
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param username 用户名
     * @param role 角色
     * @param isAuthenticated 是否已验证
     * @returns 用户会话
     */
    NetworkSystem.prototype.createUserSession = function (userId, username, role, isAuthenticated) {
        if (role === void 0) { role = UserSessionManager_1.UserRole.USER; }
        if (isAuthenticated === void 0) { isAuthenticated = true; }
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return null;
        }
        return this.userSessionManager.createSession(userId, username, role, isAuthenticated);
    };
    /**
     * 获取用户会话
     * @param userId 用户ID
     * @returns 用户会话
     */
    NetworkSystem.prototype.getUserSession = function (userId) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return null;
        }
        return this.userSessionManager.getSession(userId);
    };
    /**
     * 更新用户会话
     * @param userId 用户ID
     * @param updates 更新数据
     * @returns 更新后的会话
     */
    NetworkSystem.prototype.updateUserSession = function (userId, updates) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return null;
        }
        return this.userSessionManager.updateSession(userId, updates);
    };
    /**
     * 移除用户会话
     * @param userId 用户ID
     * @returns 是否成功移除
     */
    NetworkSystem.prototype.removeUserSession = function (userId) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return false;
        }
        return this.userSessionManager.removeSession(userId);
    };
    /**
     * 检查用户是否有权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否有权限
     */
    NetworkSystem.prototype.hasPermission = function (userId, permission) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return false;
        }
        return this.userSessionManager.hasPermission(userId, permission);
    };
    /**
     * 授予用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    NetworkSystem.prototype.grantPermission = function (userId, permission) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return false;
        }
        return this.userSessionManager.grantPermission(userId, permission);
    };
    /**
     * 撤销用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    NetworkSystem.prototype.revokePermission = function (userId, permission) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return false;
        }
        return this.userSessionManager.revokePermission(userId, permission);
    };
    /**
     * 设置用户角色
     * @param userId 用户ID
     * @param role 角色
     * @returns 是否成功
     */
    NetworkSystem.prototype.setUserRole = function (userId, role) {
        if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
            Debug_1.Debug.warn('NetworkSystem', 'User session management is not enabled');
            return false;
        }
        return this.userSessionManager.setUserRole(userId, role);
    };
    /**
     * 销毁系统
     */
    NetworkSystem.prototype.dispose = function () {
        // 断开连接
        this.disconnect();
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        // 销毁网络管理器
        this.networkManager.dispose();
        // 销毁WebRTC连接管理器
        if (this.webRTCConnectionManager) {
            this.webRTCConnectionManager.dispose();
            this.webRTCConnectionManager = null;
        }
        // 销毁媒体流管理器
        if (this.mediaStreamManager) {
            this.mediaStreamManager.dispose();
            this.mediaStreamManager = null;
        }
        // 销毁实体同步管理器
        if (this.entitySyncManager) {
            this.entitySyncManager.dispose();
            this.entitySyncManager = null;
        }
        // 销毁用户会话管理器
        if (this.userSessionManager) {
            this.userSessionManager.dispose();
            this.userSessionManager = null;
        }
        // 销毁网络事件分发器
        if (this.networkEventDispatcher) {
            this.networkEventDispatcher.dispose();
            this.networkEventDispatcher = null;
        }
        // 销毁带宽控制器
        if (this.bandwidthController) {
            this.bandwidthController.dispose();
            this.bandwidthController = null;
        }
        // 销毁网络质量监控器
        if (this.networkQualityMonitor) {
            this.networkQualityMonitor.dispose();
            this.networkQualityMonitor = null;
        }
        // 清空网络实体和用户
        this.networkEntities.clear();
        this.remoteUsers.clear();
        _super.prototype.dispose.call(this);
    };
    /**
     * 设置网络事件监听器
     */
    NetworkSystem.prototype.setupEventListeners = function () {
        var _this = this;
        // 监听用户加入事件
        this.networkManager.on('userJoined', function (userId, username) {
            _this.handleUserJoined(userId, username);
        });
        // 监听用户离开事件
        this.networkManager.on('userLeft', function (userId) {
            _this.handleUserLeft(userId);
        });
        // 监听实体创建事件
        this.networkManager.on('entityCreated', function (entityId, data) {
            _this.handleEntityCreated(entityId, data);
        });
        // 监听实体更新事件
        this.networkManager.on('entityUpdated', function (entityId, data) {
            _this.handleEntityUpdated(entityId, data);
        });
        // 监听实体删除事件
        this.networkManager.on('entityDeleted', function (entityId) {
            _this.handleEntityDeleted(entityId);
        });
    };
    /**
     * 处理用户加入事件
     * @param userId 用户ID
     * @param username 用户名
     */
    NetworkSystem.prototype.handleUserJoined = function (userId, username) {
        Debug_1.Debug.log('NetworkSystem', "User joined: ".concat(username, " (").concat(userId, ")"));
        // 创建用户实体
        var userEntity = this.engine.createEntity();
        // 添加网络用户组件
        userEntity.addComponent(NetworkUserComponent_1.NetworkUserComponent, {
            userId: userId,
            username: username,
            isLocal: false,
        });
        // 添加到远程用户映射表
        this.remoteUsers.set(userId, userEntity);
        // 触发用户加入事件
        this.eventEmitter.emit('userJoined', userId, username, userEntity);
    };
    /**
     * 处理用户离开事件
     * @param userId 用户ID
     */
    NetworkSystem.prototype.handleUserLeft = function (userId) {
        Debug_1.Debug.log('NetworkSystem', "User left: ".concat(userId));
        // 获取用户实体
        var userEntity = this.remoteUsers.get(userId);
        if (userEntity) {
            // 销毁用户实体
            this.engine.destroyEntity(userEntity);
            // 从远程用户映射表中移除
            this.remoteUsers.delete(userId);
            // 触发用户离开事件
            this.eventEmitter.emit('userLeft', userId);
        }
    };
    /**
     * 处理实体创建事件
     * @param entityId 实体ID
     * @param data 实体数据
     */
    NetworkSystem.prototype.handleEntityCreated = function (entityId, data) {
        // 创建实体
        var entity = this.engine.createEntity();
        // 添加网络实体组件
        entity.addComponent(NetworkEntityComponent_1.NetworkEntityComponent, {
            entityId: entityId,
            ownerId: data.ownerId,
        });
        // 根据数据添加其他组件
        this.applyEntityData(entity, data);
        // 添加到网络实体映射表
        this.networkEntities.set(entityId, entity);
        // 触发实体创建事件
        this.eventEmitter.emit('entityCreated', entityId, entity);
    };
    /**
     * 处理实体更新事件
     * @param entityId 实体ID
     * @param data 实体数据
     */
    NetworkSystem.prototype.handleEntityUpdated = function (entityId, data) {
        // 获取实体
        var entity = this.networkEntities.get(entityId);
        if (entity) {
            // 应用实体数据
            this.applyEntityData(entity, data);
            // 触发实体更新事件
            this.eventEmitter.emit('entityUpdated', entityId, entity);
        }
    };
    /**
     * 处理实体删除事件
     * @param entityId 实体ID
     */
    NetworkSystem.prototype.handleEntityDeleted = function (entityId) {
        // 获取实体
        var entity = this.networkEntities.get(entityId);
        if (entity) {
            // 销毁实体
            this.engine.destroyEntity(entity);
            // 从网络实体映射表中移除
            this.networkEntities.delete(entityId);
            // 触发实体删除事件
            this.eventEmitter.emit('entityDeleted', entityId);
        }
    };
    /**
     * 应用实体数据
     * @param entity 实体
     * @param data 实体数据
     */
    NetworkSystem.prototype.applyEntityData = function (entity, data) {
        // 应用变换数据
        if (data.transform) {
            var transform = entity.getComponent('Transform') || entity.addComponent('Transform');
            if (data.transform.position) {
                transform.setPosition(data.transform.getPosition().x, data.transform.getPosition().y, data.transform.getPosition().z);
            }
            if (data.transform.rotation) {
                transform.setRotationQuaternion(data.transform.rotation.x, data.transform.rotation.y, data.transform.rotation.z, data.transform.rotation.w);
            }
            if (data.transform.scale) {
                transform.setScale(data.transform.scale.x, data.transform.scale.y, data.transform.scale.z);
            }
        }
        // 应用其他组件数据
        // ...
    };
    /**
     * 尝试重连
     */
    NetworkSystem.prototype.attemptReconnect = function () {
        var _this = this;
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            Debug_1.Debug.error('NetworkSystem', 'Max reconnect attempts reached');
            return;
        }
        this.reconnectAttempts++;
        Debug_1.Debug.log('NetworkSystem', "Attempting to reconnect (".concat(this.reconnectAttempts, "/").concat(this.options.maxReconnectAttempts, ")"));
        // 设置重连定时器
        this.reconnectTimerId = window.setTimeout(function () {
            _this.connect(_this.options.serverUrl, _this.options.roomId);
        }, this.options.reconnectInterval);
    };
    /**
     * 停止重连定时器
     */
    NetworkSystem.prototype.stopReconnectTimer = function () {
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
    };
    /**
     * 启动同步定时器
     */
    NetworkSystem.prototype.startSyncTimer = function () {
        var _this = this;
        if (this.syncTimerId !== null) {
            return;
        }
        this.syncTimerId = window.setInterval(function () {
            _this.syncNetworkEntities();
        }, this.options.syncInterval);
    };
    /**
     * 停止同步定时器
     */
    NetworkSystem.prototype.stopSyncTimer = function () {
        if (this.syncTimerId !== null) {
            clearInterval(this.syncTimerId);
            this.syncTimerId = null;
        }
    };
    /**
     * 同步网络实体
     */
    NetworkSystem.prototype.syncNetworkEntities = function () {
        // 同步本地拥有的网络实体
        for (var _i = 0, _a = this.networkEntities.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], entity = _b[1];
            var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
            // 只同步本地拥有的实体
            if (networkEntity && networkEntity.ownerId === this.localUserId) {
                var transform = entity.getComponent('Transform');
                if (transform) {
                    // 发送变换数据
                    this.networkManager.sendEntityUpdate(entityId, {
                        transform: {
                            position: {
                                x: transform.getPosition().x,
                                y: transform.getPosition().y,
                                z: transform.getPosition().z,
                            },
                            rotation: {
                                x: transform.rotation.x,
                                y: transform.rotation.y,
                                z: transform.rotation.z,
                                w: transform.rotation.w,
                            },
                            scale: {
                                x: transform.scale.x,
                                y: transform.scale.y,
                                z: transform.scale.z,
                            },
                        },
                    });
                }
            }
        }
    };
    /**
     * 发送请求到微服务
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 响应数据
     */
    NetworkSystem.prototype.requestService = function (serviceName, endpoint, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.options.enableMicroserviceClient || !this.microserviceClient) {
                    throw new Error('Microservice client is not enabled');
                }
                return [2 /*return*/, this.microserviceClient.request(serviceName, endpoint, options)];
            });
        });
    };
    /**
     * 注册服务实例
     * @param serviceName 服务名称
     * @param host 主机名
     * @param port 端口
     * @param secure 是否安全连接
     * @param metadata 元数据
     * @returns 服务实例
     */
    NetworkSystem.prototype.registerService = function (serviceName, host, port, secure, metadata) {
        if (secure === void 0) { secure = false; }
        if (metadata === void 0) { metadata = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.options.enableServiceDiscovery || !this.serviceDiscoveryClient) {
                    throw new Error('Service discovery is not enabled');
                }
                return [2 /*return*/, this.serviceDiscoveryClient.registerService(serviceName, host, port, secure, metadata)];
            });
        });
    };
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    NetworkSystem.prototype.discoverService = function (serviceName) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.options.enableServiceDiscovery || !this.serviceDiscoveryClient) {
                    throw new Error('Service discovery is not enabled');
                }
                return [2 /*return*/, this.serviceDiscoveryClient.discoverService(serviceName)];
            });
        });
    };
    /**
     * 设置认证令牌
     * @param token 认证令牌
     */
    NetworkSystem.prototype.setAuthToken = function (token) {
        if (this.microserviceClient) {
            this.microserviceClient.setAuthToken(token);
        }
    };
    return NetworkSystem;
}(System_1.System));
exports.NetworkSystem = NetworkSystem;
