"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabbedComponent = void 0;
/**
 * 被抓取组件
 * 用于标记当前被抓取的对象
 */
var Component_1 = require("../../core/Component");
/**
 * 被抓取组件
 * 当一个实体被抓取时，会添加此组件
 */
var GrabbedComponent = exports.GrabbedComponent = /** @class */ (function (_super) {
    __extends(GrabbedComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    function GrabbedComponent(entity, config) {
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, GrabbedComponent.TYPE) || this;
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化属性
        _this._grabber = config.grabber;
        _this._hand = config.hand;
        _this._offset = config.offset || { x: 0, y: 0, z: 0 };
        _this._grabTime = Date.now();
        return _this;
    }
    Object.defineProperty(GrabbedComponent.prototype, "grabber", {
        /**
         * 获取抓取者
         */
        get: function () {
            return this._grabber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbedComponent.prototype, "hand", {
        /**
         * 获取抓取手
         */
        get: function () {
            return this._hand;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbedComponent.prototype, "offset", {
        /**
         * 获取抓取偏移
         */
        get: function () {
            return this._offset;
        },
        /**
         * 设置抓取偏移
         */
        set: function (value) {
            this._offset = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbedComponent.prototype, "grabTime", {
        /**
         * 获取抓取时间戳
         */
        get: function () {
            return this._grabTime;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 获取抓取持续时间（毫秒）
     */
    GrabbedComponent.prototype.getDuration = function () {
        return Date.now() - this._grabTime;
    };
    /** 组件类型 */
    GrabbedComponent.TYPE = 'GrabbedComponent';
    return GrabbedComponent;
}(Component_1.Component));
