"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelManager = void 0;
/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
var System_1 = require("../core/System");
var EventEmitter_1 = require("../utils/EventEmitter");
var AIModelType_1 = require("./AIModelType");
var AIModelFactory_1 = require("./AIModelFactory");
var GPTModel_1 = require("./models/GPTModel");
var StableDiffusionModel_1 = require("./models/StableDiffusionModel");
var BERTModel_1 = require("./models/BERTModel");
var RoBERTaModel_1 = require("./models/RoBERTaModel");
var DistilBERTModel_1 = require("./models/DistilBERTModel");
var ALBERTModel_1 = require("./models/ALBERTModel");
var XLNetModel_1 = require("./models/XLNetModel");
var BARTModel_1 = require("./models/BARTModel");
var T5Model_1 = require("./models/T5Model");
/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
var AIModelManager = exports.AIModelManager = /** @class */ (function (_super) {
    __extends(AIModelManager, _super);
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    function AIModelManager(world, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 使用系统优先级作为参数
        _super.call(this, AIModelManager.PRIORITY) || this;
        /** 已加载的模型 */
        _this.loadedModels = new Map();
        /** 模型加载进度 */
        _this.modelLoadProgress = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 模型加载队列 */
        _this.loadQueue = [];
        /** 是否正在加载模型 */
        _this.isLoading = false;
        // 保存世界引用
        _this.world = world;
        // 合并配置
        _this.config = __assign(__assign({}, AIModelManager.DEFAULT_CONFIG), config);
        // 初始化
        _this.initialize();
        return _this;
    }
    /**
     * 初始化
     */
    AIModelManager.prototype.initialize = function () {
        if (this.config.debug) {
            console.log('初始化AI模型管理器');
            console.log("\u4E16\u754C\u5B9E\u4F8B: ".concat(this.world ? '已设置' : '未设置'));
        }
        // 创建模型工厂
        this.modelFactory = new AIModelFactory_1.AIModelFactory(this.config);
        // 初始化事件发射器
        this.eventEmitter = new EventEmitter_1.EventEmitter();
    };
    /**
     * 加载模型
     * @param modelType 模型类型
     * @param config 模型配置
     * @param options 加载选项
     * @returns 模型实例
     */
    AIModelManager.prototype.loadModel = function (modelType, config, options) {
        if (config === void 0) { config = {}; }
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var modelId;
            var _this = this;
            return __generator(this, function (_a) {
                modelId = this.generateModelId(modelType, config);
                // 检查模型是否已加载
                if (this.loadedModels.has(modelId)) {
                    if (this.config.debug) {
                        console.log("\u6A21\u578B\u5DF2\u52A0\u8F7D: ".concat(modelId));
                    }
                    return [2 /*return*/, this.loadedModels.get(modelId) || null];
                }
                // 创建加载承诺
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        // 添加到加载队列
                        _this.loadQueue.push({
                            modelType: modelType,
                            config: config,
                            options: options,
                            resolve: resolve,
                            reject: reject
                        });
                        // 如果没有正在加载的模型，开始加载
                        if (!_this.isLoading) {
                            _this.processLoadQueue();
                        }
                    })];
            });
        });
    };
    /**
     * 处理加载队列
     */
    AIModelManager.prototype.processLoadQueue = function () {
        return __awaiter(this, void 0, void 0, function () {
            var task, modelId, model, firstEntry, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 如果队列为空，返回
                        if (this.loadQueue.length === 0) {
                            this.isLoading = false;
                            return [2 /*return*/];
                        }
                        // 设置加载状态
                        this.isLoading = true;
                        task = this.loadQueue.shift();
                        if (!task) {
                            this.isLoading = false;
                            return [2 /*return*/];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, 4, 5]);
                        modelId = this.generateModelId(task.modelType, task.config);
                        // 设置加载进度
                        this.modelLoadProgress.set(modelId, 0);
                        this.eventEmitter.emit('modelLoadProgress', {
                            modelId: modelId,
                            progress: 0
                        });
                        return [4 /*yield*/, this.createModelInstance(task.modelType, task.config, task.options)];
                    case 2:
                        model = _a.sent();
                        // 如果模型创建成功，添加到已加载模型列表
                        if (model) {
                            this.loadedModels.set(modelId, model);
                            // 如果超过缓存大小，移除最早加载的模型
                            if (this.loadedModels.size > (this.config.cacheSize || 5)) {
                                firstEntry = this.loadedModels.keys().next();
                                if (!firstEntry.done && firstEntry.value) {
                                    this.loadedModels.delete(firstEntry.value);
                                }
                            }
                            // 设置加载进度为100%
                            this.modelLoadProgress.set(modelId, 1);
                            this.eventEmitter.emit('modelLoadProgress', {
                                modelId: modelId,
                                progress: 1
                            });
                            // 触发模型加载完成事件
                            this.eventEmitter.emit('modelLoaded', {
                                modelId: modelId,
                                model: model
                            });
                            // 解析承诺
                            task.resolve(model);
                        }
                        else {
                            // 如果模型创建失败，触发错误事件
                            this.eventEmitter.emit('modelLoadError', {
                                modelId: modelId,
                                error: new Error("\u65E0\u6CD5\u521B\u5EFA\u6A21\u578B: ".concat(modelId))
                            });
                            // 拒绝承诺
                            task.reject(new Error("\u65E0\u6CD5\u521B\u5EFA\u6A21\u578B: ".concat(modelId)));
                        }
                        return [3 /*break*/, 5];
                    case 3:
                        error_1 = _a.sent();
                        // 触发错误事件
                        this.eventEmitter.emit('modelLoadError', {
                            modelId: this.generateModelId(task.modelType, task.config),
                            error: error_1
                        });
                        // 拒绝承诺
                        task.reject(error_1);
                        return [3 /*break*/, 5];
                    case 4:
                        // 继续处理队列
                        this.processLoadQueue();
                        return [7 /*endfinally*/];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 创建模型实例
     * @param modelType 模型类型
     * @param config 模型配置
     * @param options 加载选项
     * @returns 模型实例
     */
    AIModelManager.prototype.createModelInstance = function (modelType, config, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var model;
            return __generator(this, function (_a) {
                try {
                    // 使用加载选项（如果需要）
                    if (options.useCache !== undefined) {
                        // 这里可以根据需要使用options参数
                    }
                    // 使用模型工厂创建模型实例
                    if (this.modelFactory && options.useFactory !== false) {
                        model = this.modelFactory.createModel(modelType, config);
                        if (model) {
                            return [2 /*return*/, model];
                        }
                        // 如果工厂创建失败，回退到直接创建
                        if (this.config.debug) {
                            console.warn("\u6A21\u578B\u5DE5\u5382\u521B\u5EFA\u6A21\u578B\u5931\u8D25\uFF0C\u56DE\u9000\u5230\u76F4\u63A5\u521B\u5EFA: ".concat(modelType));
                        }
                    }
                    // 根据模型类型创建不同的模型实例
                    switch (modelType) {
                        case AIModelType_1.AIModelType.GPT:
                            return [2 /*return*/, new GPTModel_1.GPTModel(config, this.config)];
                        case AIModelType_1.AIModelType.STABLE_DIFFUSION:
                            return [2 /*return*/, new StableDiffusionModel_1.StableDiffusionModel(config, this.config)];
                        case AIModelType_1.AIModelType.BERT:
                            return [2 /*return*/, new BERTModel_1.BERTModel(config, this.config)];
                        case AIModelType_1.AIModelType.ROBERTA:
                            // 转换为RoBERTa特定配置
                            return [2 /*return*/, new RoBERTaModel_1.RoBERTaModel(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        case AIModelType_1.AIModelType.DISTILBERT:
                            // 转换为DistilBERT特定配置
                            return [2 /*return*/, new DistilBERTModel_1.DistilBERTModel(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        case AIModelType_1.AIModelType.ALBERT:
                            // 转换为ALBERT特定配置
                            return [2 /*return*/, new ALBERTModel_1.ALBERTModel(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        case AIModelType_1.AIModelType.XLNET:
                            // 转换为XLNet特定配置
                            return [2 /*return*/, new XLNetModel_1.XLNetModel(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        case AIModelType_1.AIModelType.BART:
                            // 转换为BART特定配置
                            return [2 /*return*/, new BARTModel_1.BARTModel(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        case AIModelType_1.AIModelType.T5:
                            // 转换为T5特定配置
                            return [2 /*return*/, new T5Model_1.T5Model(__assign(__assign({}, config), { variant: config.variant }), this.config)];
                        default:
                            console.error("\u4E0D\u652F\u6301\u7684\u6A21\u578B\u7C7B\u578B: ".concat(modelType));
                            return [2 /*return*/, null];
                    }
                }
                catch (error) {
                    console.error("\u521B\u5EFA\u6A21\u578B\u5B9E\u4F8B\u5931\u8D25: ".concat(error));
                    return [2 /*return*/, null];
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * 生成模型ID
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型ID
     */
    AIModelManager.prototype.generateModelId = function (modelType, config) {
        // 基本ID
        var id = "".concat(modelType);
        // 添加版本信息
        if (config.version) {
            id += "-".concat(config.version);
        }
        else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
            id += "-".concat(this.config.modelVersions[modelType]);
        }
        // 添加其他配置信息
        if (config.variant) {
            id += "-".concat(config.variant);
        }
        return id;
    };
    /**
     * 获取已加载的模型
     * @param modelId 模型ID
     * @returns 模型实例
     */
    AIModelManager.prototype.getModel = function (modelId) {
        return this.loadedModels.get(modelId) || null;
    };
    /**
     * 获取所有已加载的模型
     * @returns 模型实例映射
     */
    AIModelManager.prototype.getAllModels = function () {
        return new Map(this.loadedModels);
    };
    /**
     * 卸载模型
     * @param modelId 模型ID
     * @returns 是否成功
     */
    AIModelManager.prototype.unloadModel = function (modelId) {
        // 检查模型是否已加载
        if (!this.loadedModels.has(modelId)) {
            return false;
        }
        // 获取模型
        var model = this.loadedModels.get(modelId);
        // 卸载模型
        if (model) {
            model.dispose();
        }
        // 从已加载模型列表中移除
        this.loadedModels.delete(modelId);
        // 触发模型卸载事件
        this.eventEmitter.emit('modelUnloaded', {
            modelId: modelId
        });
        return true;
    };
    /**
     * 卸载所有模型
     */
    AIModelManager.prototype.unloadAllModels = function () {
        // 卸载所有模型
        for (var _i = 0, _a = this.loadedModels.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], modelId = _b[0], model = _b[1];
            model.dispose();
            // 触发模型卸载事件
            this.eventEmitter.emit('modelUnloaded', {
                modelId: modelId
            });
        }
        // 清空已加载模型列表
        this.loadedModels.clear();
    };
    /**
     * 获取模型加载进度
     * @param modelId 模型ID
     * @returns 加载进度 (0-1)
     */
    AIModelManager.prototype.getModelLoadProgress = function (modelId) {
        return this.modelLoadProgress.get(modelId) || 0;
    };
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     * @returns this 实例，用于链式调用
     */
    AIModelManager.prototype.addListener = function (event, listener) {
        this.eventEmitter.on(event, listener);
        return this;
    };
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     * @returns this 实例，用于链式调用
     */
    AIModelManager.prototype.removeListener = function (event, listener) {
        this.eventEmitter.off(event, listener);
        return this;
    };
    /**
     * 监听事件（兼容System类）
     * @param event 事件名称
     * @param callback 回调函数
     * @returns this 实例，用于链式调用
     */
    AIModelManager.prototype.on = function (event, callback) {
        return this.addListener(event, callback);
    };
    /**
     * 取消监听事件（兼容System类）
     * @param event 事件名称
     * @param callback 回调函数
     * @returns this 实例，用于链式调用
     */
    AIModelManager.prototype.off = function (event, callback) {
        if (callback) {
            return this.removeListener(event, callback);
        }
        else {
            // 移除指定事件的所有监听器
            // 注意：EventEmitter 的 removeAllListeners 方法可能不接受参数
            // 这里做一个兼容处理
            try {
                // @ts-ignore - 忽略类型检查
                this.eventEmitter.removeAllListeners(event);
            }
            catch (error) {
                console.warn("\u65E0\u6CD5\u79FB\u9664\u4E8B\u4EF6 ".concat(event, " \u7684\u6240\u6709\u76D1\u542C\u5668:"), error);
            }
            return this;
        }
    };
    /**
     * 销毁
     */
    AIModelManager.prototype.dispose = function () {
        // 卸载所有模型
        this.unloadAllModels();
        // 清空事件监听器
        this.eventEmitter.removeAllListeners();
    };
    /** 系统优先级 */
    AIModelManager.PRIORITY = 50;
    /** 默认配置 */
    AIModelManager.DEFAULT_CONFIG = {
        debug: false,
        cacheSize: 5,
        useLocalModels: false,
        apiKeys: {},
        baseUrls: {},
        modelVersions: {}
    };
    return AIModelManager;
}(System_1.System));
