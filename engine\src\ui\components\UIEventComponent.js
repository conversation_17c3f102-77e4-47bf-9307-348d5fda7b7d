"use strict";
/**
 * UIEventComponent.ts
 *
 * UI事件组件，用于处理UI元素的事件
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIEventComponent = exports.UIEvent = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var IUIElement_1 = require("../interfaces/IUIElement");
var UI3DComponent_1 = require("./UI3DComponent");
/**
 * UI事件
 * 实现IUIEvent接口
 */
var UIEvent = /** @class */ (function () {
    /**
     * 构造函数
     * @param type 事件类型
     * @param target 目标UI元素
     * @param data 事件数据
     */
    function UIEvent(type, target, data) {
        if (data === void 0) { data = {}; }
        this.type = type;
        this.target = target;
        this.data = __assign({ type: type, target: target, stopPropagation: false, preventDefault: false, timestamp: Date.now() }, data);
    }
    /**
     * 阻止事件冒泡
     */
    UIEvent.prototype.stopPropagation = function () {
        this.data.stopPropagation = true;
    };
    /**
     * 阻止事件默认行为
     */
    UIEvent.prototype.preventDefault = function () {
        this.data.preventDefault = true;
    };
    return UIEvent;
}());
exports.UIEvent = UIEvent;
/**
 * UI事件组件
 * 用于处理实体的UI事件
 */
var UIEventComponent = /** @class */ (function (_super) {
    __extends(UIEventComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     */
    function UIEventComponent(entity) {
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'UIEvent') || this;
        // 事件监听器映射
        _this.eventListeners = new Map();
        // 拖拽状态
        _this.isDragging = false;
        // 射线投射器（用于3D UI元素）
        _this.raycaster = new three_1.Raycaster();
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化事件监听器映射
        Object.values(IUIElement_1.UIEventType).forEach(function (type) {
            _this.eventListeners.set(type, []);
        });
        return _this;
    }
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    UIEventComponent.prototype.addEventListener = function (type, listener) {
        var listeners = this.eventListeners.get(type) || [];
        if (!listeners.includes(listener)) {
            listeners.push(listener);
            this.eventListeners.set(type, listeners);
        }
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    UIEventComponent.prototype.removeEventListener = function (type, listener) {
        var listeners = this.eventListeners.get(type) || [];
        var index = listeners.indexOf(listener);
        if (index !== -1) {
            listeners.splice(index, 1);
        }
    };
    /**
     * 分发事件
     * @param event UI事件
     */
    UIEventComponent.prototype.dispatchEvent = function (event) {
        // 获取事件类型的监听器
        var listeners = this.eventListeners.get(event.type) || [];
        // 调用所有监听器
        for (var _i = 0, listeners_1 = listeners; _i < listeners_1.length; _i++) {
            var listener = listeners_1[_i];
            listener(event);
            // 如果事件被阻止冒泡，则停止
            if (event.data.stopPropagation) {
                break;
            }
        }
        // 如果没有阻止冒泡，且目标有父元素，则向上冒泡
        if (!event.data.stopPropagation && event.target.parent) {
            var parentEvent = new UIEvent(event.type, event.target.parent, event.data);
            this.dispatchEvent(parentEvent);
        }
    };
    /**
     * 创建并分发事件
     * @param type 事件类型
     * @param target 目标UI元素
     * @param data 事件数据
     */
    UIEventComponent.prototype.createAndDispatchEvent = function (type, target, data) {
        if (data === void 0) { data = {}; }
        var event = new UIEvent(type, target, data);
        this.dispatchEvent(event);
    };
    /**
     * 处理鼠标移动
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     */
    UIEventComponent.prototype.handleMouseMove = function (x, y, uiElements, camera) {
        // 更新拖拽状态
        if (this.isDragging && this.activeElement && this.dragStartPosition) {
            this.dragCurrentPosition = new three_1.Vector2(x, y);
            var delta = new three_1.Vector2().subVectors(this.dragCurrentPosition, this.dragStartPosition);
            // 分发拖拽事件
            this.createAndDispatchEvent(IUIElement_1.UIEventType.DRAG, this.activeElement, {
                position: new three_1.Vector2(x, y),
                delta: delta,
                originalEvent: { type: 'mousemove', clientX: x, clientY: y }
            });
        }
        // 查找当前悬停的元素
        var element = this.findElementAtPosition(x, y, uiElements, camera);
        // 如果悬停元素改变
        if (element !== this.hoveredElement) {
            // 如果之前有悬停元素，分发鼠标离开事件
            if (this.hoveredElement) {
                this.createAndDispatchEvent(IUIElement_1.UIEventType.HOVER, this.hoveredElement, {
                    position: new three_1.Vector2(x, y),
                    originalEvent: { type: 'mouseleave', clientX: x, clientY: y }
                });
                // 如果是3D UI元素，更新悬停状态
                if (this.hoveredElement instanceof UI3DComponent_1.UI3DComponent) {
                    this.hoveredElement.setHovered(false);
                }
            }
            // 更新悬停元素
            this.hoveredElement = element;
            // 如果有新的悬停元素，分发鼠标进入事件
            if (this.hoveredElement) {
                this.createAndDispatchEvent(IUIElement_1.UIEventType.HOVER, this.hoveredElement, {
                    position: new three_1.Vector2(x, y),
                    originalEvent: { type: 'mouseenter', clientX: x, clientY: y }
                });
                // 如果是3D UI元素，更新悬停状态
                if (this.hoveredElement instanceof UI3DComponent_1.UI3DComponent) {
                    this.hoveredElement.setHovered(true);
                }
            }
        }
    };
    /**
     * 处理鼠标按下
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param button 按下的按钮
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     */
    UIEventComponent.prototype.handleMouseDown = function (x, y, button, uiElements, camera) {
        // 查找当前点击的元素
        var element = this.findElementAtPosition(x, y, uiElements, camera);
        // 如果找到元素
        if (element && element.interactive) {
            // 更新活动元素
            this.activeElement = element;
            // 开始拖拽状态
            this.isDragging = true;
            this.dragStartPosition = new three_1.Vector2(x, y);
            this.dragCurrentPosition = new three_1.Vector2(x, y);
            // 分发鼠标按下事件
            this.createAndDispatchEvent(IUIElement_1.UIEventType.CLICK, element, {
                position: new three_1.Vector2(x, y),
                button: button,
                originalEvent: { type: 'mousedown', clientX: x, clientY: y, button: button }
            });
            // 如果是3D UI元素，更新激活状态
            if (element instanceof UI3DComponent_1.UI3DComponent) {
                element.setActive(true);
            }
            // 更新焦点元素
            if (this.focusedElement !== element) {
                // 如果之前有焦点元素，分发失焦事件
                if (this.focusedElement) {
                    this.createAndDispatchEvent(IUIElement_1.UIEventType.BLUR, this.focusedElement, {
                        originalEvent: { type: 'blur' }
                    });
                }
                // 更新焦点元素
                this.focusedElement = element;
                // 分发获焦事件
                this.createAndDispatchEvent(IUIElement_1.UIEventType.FOCUS, element, {
                    originalEvent: { type: 'focus' }
                });
            }
        }
        else {
            // 如果点击空白处，清除焦点
            if (this.focusedElement) {
                this.createAndDispatchEvent(IUIElement_1.UIEventType.BLUR, this.focusedElement, {
                    originalEvent: { type: 'blur' }
                });
                this.focusedElement = undefined;
            }
        }
    };
    /**
     * 处理鼠标释放
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param button 释放的按钮
     * @param _uiElements UI元素列表 - 未使用，保留以保持API一致性
     * @param _camera 相机（用于3D UI元素） - 未使用，保留以保持API一致性
     */
    UIEventComponent.prototype.handleMouseUp = function (x, y, button, _uiElements, _camera) {
        // 如果有活动元素
        if (this.activeElement) {
            // 如果正在拖拽，分发拖拽结束事件
            if (this.isDragging && this.dragStartPosition && this.dragCurrentPosition) {
                var delta = new three_1.Vector2().subVectors(this.dragCurrentPosition, this.dragStartPosition);
                // 如果拖拽距离足够小，视为点击
                if (delta.length() < 5) {
                    // 分发点击事件
                    this.createAndDispatchEvent(IUIElement_1.UIEventType.CLICK, this.activeElement, {
                        position: new three_1.Vector2(x, y),
                        button: button,
                        originalEvent: { type: 'click', clientX: x, clientY: y, button: button }
                    });
                }
                // 分发拖拽结束事件
                this.createAndDispatchEvent(IUIElement_1.UIEventType.DRAG_END, this.activeElement, {
                    position: new three_1.Vector2(x, y),
                    delta: delta,
                    originalEvent: { type: 'mouseup', clientX: x, clientY: y, button: button }
                });
            }
            // 如果是3D UI元素，更新激活状态
            if (this.activeElement instanceof UI3DComponent_1.UI3DComponent) {
                this.activeElement.setActive(false);
            }
            // 重置拖拽状态
            this.isDragging = false;
            this.dragStartPosition = undefined;
            this.dragCurrentPosition = undefined;
            this.activeElement = undefined;
        }
    };
    /**
     * 处理键盘按下
     * @param key 按下的键
     * @param keyCode 键码
     * @param modifiers 修饰键状态
     */
    UIEventComponent.prototype.handleKeyDown = function (key, keyCode, modifiers) {
        // 如果有焦点元素，分发键盘按下事件
        if (this.focusedElement) {
            this.createAndDispatchEvent(IUIElement_1.UIEventType.KEY_DOWN, this.focusedElement, __assign(__assign({ key: key, keyCode: keyCode }, modifiers), { originalEvent: __assign({ type: 'keydown', key: key, keyCode: keyCode }, modifiers) }));
        }
    };
    /**
     * 处理键盘释放
     * @param key 释放的键
     * @param keyCode 键码
     * @param modifiers 修饰键状态
     */
    UIEventComponent.prototype.handleKeyUp = function (key, keyCode, modifiers) {
        // 如果有焦点元素，分发键盘释放事件
        if (this.focusedElement) {
            this.createAndDispatchEvent(IUIElement_1.UIEventType.KEY_UP, this.focusedElement, __assign(__assign({ key: key, keyCode: keyCode }, modifiers), { originalEvent: __assign({ type: 'keyup', key: key, keyCode: keyCode }, modifiers) }));
        }
    };
    /**
     * 在指定位置查找UI元素
     * @param x X坐标
     * @param y Y坐标
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     * @returns 找到的UI元素，如果没有找到则返回undefined
     */
    UIEventComponent.prototype.findElementAtPosition = function (x, y, uiElements, camera) {
        // 按z-index排序元素（从高到低）
        var sortedElements = __spreadArray([], uiElements, true).sort(function (a, b) { return b.zIndex - a.zIndex; });
        // 首先检查2D UI元素
        for (var _i = 0, sortedElements_1 = sortedElements; _i < sortedElements_1.length; _i++) {
            var element = sortedElements_1[_i];
            if (!element.visible || !element.interactive)
                continue;
            if (!element.is3D) {
                // 2D元素使用屏幕坐标检测
                var position = element.position instanceof three_1.Vector2 ? element.position : new three_1.Vector2(element.position.x, element.position.y);
                var halfSize = element.size.clone().multiplyScalar(0.5);
                // 检查点是否在元素范围内
                if (x >= position.x - halfSize.x && x <= position.x + halfSize.x &&
                    y >= position.y - halfSize.y && y <= position.y + halfSize.y) {
                    return element;
                }
            }
        }
        // 然后检查3D UI元素（如果有相机）
        if (camera) {
            // 将屏幕坐标转换为归一化设备坐标（NDC）
            var ndcX = (x / window.innerWidth) * 2 - 1;
            var ndcY = -(y / window.innerHeight) * 2 + 1;
            // 设置射线投射器
            this.raycaster.setFromCamera(new three_1.Vector2(ndcX, ndcY), camera);
            // 收集所有3D UI元素的网格
            var meshes = [];
            var meshToElement = new Map();
            for (var _a = 0, sortedElements_2 = sortedElements; _a < sortedElements_2.length; _a++) {
                var element = sortedElements_2[_a];
                if (!element.visible || !element.interactive || !element.is3D)
                    continue;
                var ui3d = element;
                if (ui3d.mesh) {
                    meshes.push(ui3d.mesh);
                    meshToElement.set(ui3d.mesh, element);
                }
            }
            // 执行射线投射
            var intersects = this.raycaster.intersectObjects(meshes, false);
            // 返回第一个相交的元素
            if (intersects.length > 0) {
                return meshToElement.get(intersects[0].object);
            }
        }
        return undefined;
    };
    return UIEventComponent;
}(Component_1.Component));
exports.UIEventComponent = UIEventComponent;
