"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacialAnimationComponent = exports.FacialAnimationComponentType = exports.VisemeType = exports.FacialExpressionType = void 0;
/**
 * 面部动画组件
 * 用于控制角色的面部表情和口型同步
 */
var Component_1 = require("../../core/Component");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 面部表情类型
 */
var FacialExpressionType;
(function (FacialExpressionType) {
    FacialExpressionType["NEUTRAL"] = "neutral";
    FacialExpressionType["HAPPY"] = "happy";
    FacialExpressionType["SAD"] = "sad";
    FacialExpressionType["ANGRY"] = "angry";
    FacialExpressionType["SURPRISED"] = "surprised";
    FacialExpressionType["FEAR"] = "fear";
    FacialExpressionType["FEARFUL"] = "fearful";
    FacialExpressionType["DISGUST"] = "disgust";
    FacialExpressionType["DISGUSTED"] = "disgusted";
    FacialExpressionType["CONTEMPT"] = "contempt";
})(FacialExpressionType || (exports.FacialExpressionType = FacialExpressionType = {}));
/**
 * 口型类型
 */
var VisemeType;
(function (VisemeType) {
    VisemeType["NEUTRAL"] = "neutral";
    VisemeType["SILENT"] = "silent";
    VisemeType["AA"] = "aa";
    VisemeType["E"] = "e";
    VisemeType["EE"] = "ee";
    VisemeType["EH"] = "eh";
    VisemeType["IH"] = "ih";
    VisemeType["OH"] = "oh";
    VisemeType["OU"] = "ou";
    VisemeType["PP"] = "pp";
    VisemeType["FF"] = "ff";
    VisemeType["TH"] = "th";
    VisemeType["DD"] = "dd";
    VisemeType["K"] = "k";
    VisemeType["KK"] = "kk";
    VisemeType["CH"] = "ch";
    VisemeType["SS"] = "ss";
    VisemeType["NN"] = "nn";
    VisemeType["R"] = "r";
    VisemeType["RR"] = "rr";
    VisemeType["MM"] = "mm";
    VisemeType["ER"] = "er";
})(VisemeType || (exports.VisemeType = VisemeType = {}));
/**
 * 面部动画组件类型
 */
exports.FacialAnimationComponentType = 'FacialAnimationComponent';
/**
 * 面部动画组件
 */
var FacialAnimationComponent = exports.FacialAnimationComponent = /** @class */ (function (_super) {
    __extends(FacialAnimationComponent, _super);
    /**
     * 构造函数
     * @param entity 实体
     */
    function FacialAnimationComponent(entity) {
        var _this = _super.call(this, FacialAnimationComponent.TYPE) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 当前表情 */
        _this.currentExpression = { expression: FacialExpressionType.NEUTRAL, weight: 1.0 };
        /** 当前口型 */
        _this.currentViseme = { viseme: VisemeType.NEUTRAL, weight: 0.0 };
        /** 表情混合映射 */
        _this.expressionBlendMap = new Map();
        /** 口型混合映射 */
        _this.visemeBlendMap = new Map();
        /** 表情混合速度 */
        _this.expressionBlendSpeed = 5.0;
        /** 口型混合速度 */
        _this.visemeBlendSpeed = 10.0;
        _this.setEntity(entity);
        // 初始化表情混合映射
        for (var _i = 0, _a = Object.values(FacialExpressionType); _i < _a.length; _i++) {
            var expression = _a[_i];
            _this.expressionBlendMap.set(expression, 0);
        }
        _this.expressionBlendMap.set(FacialExpressionType.NEUTRAL, 1.0);
        // 初始化口型混合映射
        for (var _b = 0, _c = Object.values(VisemeType); _b < _c.length; _b++) {
            var viseme = _c[_b];
            _this.visemeBlendMap.set(viseme, 0);
        }
        _this.visemeBlendMap.set(VisemeType.NEUTRAL, 1.0);
        return _this;
    }
    /**
     * 获取组件类型
     */
    FacialAnimationComponent.prototype.getType = function () {
        return FacialAnimationComponent.TYPE;
    };
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    FacialAnimationComponent.prototype.setExpression = function (expression, weight, blendTime) {
        if (weight === void 0) { weight = 1.0; }
        if (!this.isEnabled())
            return;
        this.currentExpression = { expression: expression, weight: weight };
        if (blendTime !== undefined) {
            this.expressionBlendSpeed = 1.0 / Math.max(0.001, blendTime);
        }
        // 更新混合映射
        for (var _i = 0, _a = this.expressionBlendMap.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], _ = _b[1];
            if (key === expression) {
                this.expressionBlendMap.set(key, weight);
            }
            else {
                this.expressionBlendMap.set(key, 0);
            }
        }
        this.eventEmitter.emit('expressionChange', { expression: expression, weight: weight });
    };
    /**
     * 重置表情
     */
    FacialAnimationComponent.prototype.resetExpression = function () {
        this.setExpression(FacialExpressionType.NEUTRAL);
    };
    /**
     * 获取当前表情
     */
    FacialAnimationComponent.prototype.getCurrentExpression = function () {
        return this.currentExpression;
    };
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    FacialAnimationComponent.prototype.setViseme = function (viseme, weight, blendTime) {
        if (weight === void 0) { weight = 1.0; }
        if (!this.isEnabled())
            return;
        this.currentViseme = { viseme: viseme, weight: weight };
        if (blendTime !== undefined) {
            this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
        }
        // 更新混合映射
        for (var _i = 0, _a = this.visemeBlendMap.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], _ = _b[1];
            if (key === viseme) {
                this.visemeBlendMap.set(key, weight);
            }
            else {
                this.visemeBlendMap.set(key, 0);
            }
        }
        this.eventEmitter.emit('visemeChange', { viseme: viseme, weight: weight });
    };
    /**
     * 重置口型
     */
    FacialAnimationComponent.prototype.resetViseme = function () {
        this.setViseme(VisemeType.NEUTRAL);
    };
    /**
     * 获取当前口型
     */
    FacialAnimationComponent.prototype.getCurrentViseme = function () {
        return this.currentViseme;
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationComponent.prototype.update = function (deltaTime) {
        if (!this.isEnabled())
            return;
        // 使用 deltaTime 和混合速度进行平滑过渡
        this.updateBlending(deltaTime);
    };
    /**
     * 更新混合
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationComponent.prototype.updateBlending = function (deltaTime) {
        // 使用 expressionBlendSpeed 和 visemeBlendSpeed 进行平滑过渡
        var expressionBlendFactor = Math.min(1.0, this.expressionBlendSpeed * deltaTime);
        var visemeBlendFactor = Math.min(1.0, this.visemeBlendSpeed * deltaTime);
        // 这里可以添加更复杂的混合逻辑
        if (expressionBlendFactor > 0 || visemeBlendFactor > 0) {
            // 混合逻辑已在 setExpression 和 setViseme 方法中处理
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationComponent.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationComponent.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /** 组件类型 */
    FacialAnimationComponent.TYPE = exports.FacialAnimationComponentType;
    return FacialAnimationComponent;
}(Component_1.Component));
