"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceDiscoveryClient = void 0;
/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
var events_1 = require("events");
var Debug_1 = require("../utils/Debug");
/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
var ServiceDiscoveryClient = /** @class */ (function (_super) {
    __extends(ServiceDiscoveryClient, _super);
    /**
     * 创建服务发现客户端
     * @param config 配置
     */
    function ServiceDiscoveryClient(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 服务实例缓存 */
        _this.serviceCache = new Map();
        /** 已注册的服务实例 */
        _this.registeredInstances = new Map();
        /** 心跳定时器ID */
        _this.heartbeatTimerId = null;
        /** 是否已初始化 */
        _this.initialized = false;
        // 默认配置
        _this.config = __assign({ registryUrl: 'http://localhost:4010/api/registry', heartbeatInterval: 30000, discoveryCache: 60000, enableAutoHeartbeat: true, enableDiscoveryCache: true, retryCount: 3, retryInterval: 1000 }, config);
        return _this;
    }
    /**
     * 初始化客户端
     */
    ServiceDiscoveryClient.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 如果启用自动心跳，则启动心跳定时器
        if (this.config.enableAutoHeartbeat) {
            this.startHeartbeat();
        }
        this.initialized = true;
        Debug_1.Debug.log('ServiceDiscoveryClient', 'Service discovery client initialized');
    };
    /**
     * 注册服务实例
     * @param serviceName 服务名称
     * @param host 主机名
     * @param port 端口
     * @param secure 是否安全连接
     * @param metadata 元数据
     * @returns 服务实例
     */
    ServiceDiscoveryClient.prototype.registerService = function (serviceName, host, port, secure, metadata) {
        if (secure === void 0) { secure = false; }
        if (metadata === void 0) { metadata = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var instanceId, instance, response, registeredInstance, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        instanceId = "".concat(serviceName, "-").concat(host, "-").concat(port, "-").concat(Date.now());
                        instance = {
                            serviceName: serviceName,
                            instanceId: instanceId,
                            host: host,
                            port: port,
                            secure: secure,
                            metadata: metadata,
                            status: 'UP',
                            registrationTime: Date.now(),
                            lastHeartbeatTime: Date.now(),
                        };
                        return [4 /*yield*/, fetch("".concat(this.config.registryUrl, "/register"), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(instance),
                            })];
                    case 1:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to register service: ".concat(response.statusText));
                        }
                        return [4 /*yield*/, response.json()];
                    case 2:
                        registeredInstance = _a.sent();
                        // 添加到已注册实例映射表
                        this.registeredInstances.set(instanceId, registeredInstance);
                        // 触发注册事件
                        this.emit('serviceRegistered', registeredInstance);
                        Debug_1.Debug.log('ServiceDiscoveryClient', "Service ".concat(serviceName, " registered with instance ID ").concat(instanceId));
                        return [2 /*return*/, registeredInstance];
                    case 3:
                        error_1 = _a.sent();
                        Debug_1.Debug.error('ServiceDiscoveryClient', 'Failed to register service:', error_1);
                        throw error_1;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 发送心跳
     * @param instanceId 实例ID
     */
    ServiceDiscoveryClient.prototype.sendHeartbeat = function (instanceId) {
        return __awaiter(this, void 0, void 0, function () {
            var instance, response, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        instance = this.registeredInstances.get(instanceId);
                        if (!instance) {
                            Debug_1.Debug.warn('ServiceDiscoveryClient', "Instance ".concat(instanceId, " not found for heartbeat"));
                            return [2 /*return*/];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, fetch("".concat(this.config.registryUrl, "/heartbeat"), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    serviceName: instance.serviceName,
                                    instanceId: instanceId,
                                }),
                            })];
                    case 2:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to send heartbeat: ".concat(response.statusText));
                        }
                        // 更新最后心跳时间
                        instance.lastHeartbeatTime = Date.now();
                        Debug_1.Debug.log('ServiceDiscoveryClient', "Heartbeat sent for instance ".concat(instanceId));
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        Debug_1.Debug.error('ServiceDiscoveryClient', 'Failed to send heartbeat:', error_2);
                        // 尝试重新注册
                        this.emit('heartbeatFailed', instanceId, error_2);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    ServiceDiscoveryClient.prototype.discoverService = function (serviceName) {
        return __awaiter(this, void 0, void 0, function () {
            var cached, response, instances, error_3, cached;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 检查缓存
                        if (this.config.enableDiscoveryCache) {
                            cached = this.serviceCache.get(serviceName);
                            if (cached && Date.now() - cached.timestamp < this.config.discoveryCache) {
                                return [2 /*return*/, cached.instances];
                            }
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        return [4 /*yield*/, fetch("".concat(this.config.registryUrl, "/discover"), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    serviceName: serviceName,
                                }),
                            })];
                    case 2:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to discover service: ".concat(response.statusText));
                        }
                        return [4 /*yield*/, response.json()];
                    case 3:
                        instances = _a.sent();
                        // 更新缓存
                        if (this.config.enableDiscoveryCache) {
                            this.serviceCache.set(serviceName, {
                                instances: instances,
                                timestamp: Date.now(),
                            });
                        }
                        Debug_1.Debug.log('ServiceDiscoveryClient', "Discovered ".concat(instances.length, " instances of service ").concat(serviceName));
                        return [2 /*return*/, instances];
                    case 4:
                        error_3 = _a.sent();
                        Debug_1.Debug.error('ServiceDiscoveryClient', 'Failed to discover service:', error_3);
                        cached = this.serviceCache.get(serviceName);
                        if (cached) {
                            Debug_1.Debug.warn('ServiceDiscoveryClient', "Using cached instances for service ".concat(serviceName));
                            return [2 /*return*/, cached.instances];
                        }
                        throw error_3;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 注销服务实例
     * @param instanceId 实例ID
     */
    ServiceDiscoveryClient.prototype.deregisterService = function (instanceId) {
        return __awaiter(this, void 0, void 0, function () {
            var instance, response, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        instance = this.registeredInstances.get(instanceId);
                        if (!instance) {
                            Debug_1.Debug.warn('ServiceDiscoveryClient', "Instance ".concat(instanceId, " not found for deregistration"));
                            return [2 /*return*/];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, fetch("".concat(this.config.registryUrl, "/services/").concat(instance.serviceName, "/instances/").concat(instanceId), {
                                method: 'DELETE',
                            })];
                    case 2:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to deregister service: ".concat(response.statusText));
                        }
                        // 从已注册实例映射表中移除
                        this.registeredInstances.delete(instanceId);
                        // 触发注销事件
                        this.emit('serviceDeregistered', instanceId, instance);
                        Debug_1.Debug.log('ServiceDiscoveryClient', "Service instance ".concat(instanceId, " deregistered"));
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        Debug_1.Debug.error('ServiceDiscoveryClient', 'Failed to deregister service:', error_4);
                        throw error_4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 启动心跳
     */
    ServiceDiscoveryClient.prototype.startHeartbeat = function () {
        var _this = this;
        if (this.heartbeatTimerId !== null) {
            return;
        }
        this.heartbeatTimerId = window.setInterval(function () {
            var _loop_1 = function (instanceId) {
                _this.sendHeartbeat(instanceId).catch(function (error) {
                    Debug_1.Debug.error('ServiceDiscoveryClient', "Heartbeat error for instance ".concat(instanceId, ":"), error);
                });
            };
            // 为所有已注册的实例发送心跳
            for (var _i = 0, _a = _this.registeredInstances.keys(); _i < _a.length; _i++) {
                var instanceId = _a[_i];
                _loop_1(instanceId);
            }
        }, this.config.heartbeatInterval);
        Debug_1.Debug.log('ServiceDiscoveryClient', "Heartbeat started with interval ".concat(this.config.heartbeatInterval, "ms"));
    };
    /**
     * 停止心跳
     */
    ServiceDiscoveryClient.prototype.stopHeartbeat = function () {
        if (this.heartbeatTimerId !== null) {
            clearInterval(this.heartbeatTimerId);
            this.heartbeatTimerId = null;
            Debug_1.Debug.log('ServiceDiscoveryClient', 'Heartbeat stopped');
        }
    };
    /**
     * 销毁客户端
     */
    ServiceDiscoveryClient.prototype.destroy = function () {
        // 停止心跳
        this.stopHeartbeat();
        var _loop_2 = function (instanceId) {
            this_1.deregisterService(instanceId).catch(function (error) {
                Debug_1.Debug.error('ServiceDiscoveryClient', "Deregistration error for instance ".concat(instanceId, ":"), error);
            });
        };
        var this_1 = this;
        // 注销所有已注册的实例
        for (var _i = 0, _a = this.registeredInstances.keys(); _i < _a.length; _i++) {
            var instanceId = _a[_i];
            _loop_2(instanceId);
        }
        // 清除缓存
        this.serviceCache.clear();
        this.registeredInstances.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
        Debug_1.Debug.log('ServiceDiscoveryClient', 'Service discovery client destroyed');
    };
    return ServiceDiscoveryClient;
}(events_1.EventEmitter));
exports.ServiceDiscoveryClient = ServiceDiscoveryClient;
