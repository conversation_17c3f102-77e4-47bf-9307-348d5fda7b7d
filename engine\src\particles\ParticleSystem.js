"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticleSystem = void 0;
/**
 * 粒子系统
 * 用于创建和管理粒子效果
 */
var THREE = require("three");
var System_1 = require("../core/System");
var ParticleEmitter_1 = require("./ParticleEmitter");
var Particle_1 = require("./Particle");
var ParticleSystem = exports.ParticleSystem = /** @class */ (function (_super) {
    __extends(ParticleSystem, _super);
    /**
     * 创建粒子系统
     * @param options 粒子系统选项
     */
    function ParticleSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 2) || this;
        /** 粒子发射器列表 */
        _this.emitters = [];
        /** 物理系统引用 */
        _this.physicsSystem = null;
        /** 粒子池 */
        _this.particlePool = [];
        /** 活跃粒子数量 */
        _this.activeParticleCount = 0;
        _this.maxParticles = options.maxParticles || 10000;
        _this.useGPU = options.useGPU !== undefined ? options.useGPU : true;
        _this.enableCollision = options.enableCollision || false;
        _this.enablePhysics = options.enablePhysics || false;
        _this.enableSorting = options.enableSorting || false;
        // 初始化粒子池
        _this.initializeParticlePool();
        return _this;
    }
    /**
     * 初始化粒子池
     */
    ParticleSystem.prototype.initializeParticlePool = function () {
        this.particlePool = [];
        for (var i = 0; i < this.maxParticles; i++) {
            this.particlePool.push(new Particle_1.Particle());
        }
    };
    /**
     * 初始化系统
     */
    ParticleSystem.prototype.initialize = function () {
        if (this.engine) {
            // 获取物理系统
            if (this.enablePhysics) {
                this.physicsSystem = this.engine.getSystem('PhysicsSystem');
                if (!this.physicsSystem) {
                    console.warn('物理系统不可用，粒子物理模拟将被禁用');
                    this.enablePhysics = false;
                }
            }
            // 获取世界中的所有实体
            var world = this.engine.getWorld();
            // 查找具有粒子发射器组件的实体
            var entities = world.getAllEntities();
            for (var _i = 0, entities_1 = entities; _i < entities_1.length; _i++) {
                var entity = entities_1[_i];
                this.setupEntityParticles(entity);
            }
            // 监听实体创建事件
            world.on('entityCreated', this.handleEntityCreated.bind(this));
            // 监听实体移除事件
            world.on('entityRemoved', this.handleEntityRemoved.bind(this));
        }
    };
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    ParticleSystem.prototype.handleEntityCreated = function (entity) {
        this.setupEntityParticles(entity);
    };
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    ParticleSystem.prototype.handleEntityRemoved = function (entity) {
        this.removeEntityParticles(entity);
    };
    /**
     * 设置实体的粒子发射器
     * @param entity 实体
     */
    ParticleSystem.prototype.setupEntityParticles = function (entity) {
        if (!entity)
            return;
        // 检查实体是否有粒子发射器组件
        var emitter = entity.getComponent(ParticleEmitter_1.ParticleEmitter.type);
        if (emitter) {
            // 初始化发射器
            emitter.initialize(this);
            // 添加到发射器列表
            this.emitters.push(emitter);
        }
    };
    /**
     * 移除实体的粒子发射器
     * @param entity 实体
     */
    ParticleSystem.prototype.removeEntityParticles = function (entity) {
        if (!entity)
            return;
        // 查找实体的粒子发射器
        var index = this.emitters.findIndex(function (emitter) { return emitter.getEntity() === entity; });
        if (index !== -1) {
            // 停止发射器
            this.emitters[index].stop();
            // 从列表中移除
            this.emitters.splice(index, 1);
        }
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    ParticleSystem.prototype.update = function (deltaTime) {
        // 更新所有发射器
        for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {
            var emitter = _a[_i];
            // 如果发射器已停止且没有活跃粒子，跳过更新
            if (!emitter.isActive() && emitter.getActiveParticleCount() === 0) {
                continue;
            }
            // 更新发射器
            emitter.update(deltaTime);
        }
        // 更新活跃粒子计数
        this.updateActiveParticleCount();
        // 如果启用排序，对粒子进行排序
        if (this.enableSorting) {
            this.sortParticles();
        }
    };
    /**
     * 更新活跃粒子计数
     */
    ParticleSystem.prototype.updateActiveParticleCount = function () {
        var count = 0;
        for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {
            var emitter = _a[_i];
            count += emitter.getActiveParticleCount();
        }
        this.activeParticleCount = count;
    };
    /**
     * 对粒子进行排序
     */
    ParticleSystem.prototype.sortParticles = function () {
        if (!this.engine)
            return;
        // 获取相机
        var camera = this.engine.getActiveCamera();
        if (!camera)
            return;
        // 获取相机位置
        var cameraPosition = camera.getPosition();
        if (!cameraPosition)
            return;
        var cameraPos = new THREE.Vector3(cameraPosition.x, cameraPosition.y, cameraPosition.z);
        // 对每个发射器的粒子进行排序
        for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {
            var emitter = _a[_i];
            emitter.sortParticles(cameraPos);
        }
    };
    /**
     * 创建粒子
     * @returns 粒子实例，如果粒子池已满则返回null
     */
    ParticleSystem.prototype.createParticle = function () {
        // 检查是否达到最大粒子数量
        if (this.activeParticleCount >= this.maxParticles) {
            return null;
        }
        // 从粒子池中获取一个未使用的粒子
        for (var _i = 0, _a = this.particlePool; _i < _a.length; _i++) {
            var particle = _a[_i];
            if (!particle.active) {
                particle.active = true;
                this.activeParticleCount++;
                return particle;
            }
        }
        return null;
    };
    /**
     * 释放粒子
     * @param particle 要释放的粒子
     */
    ParticleSystem.prototype.releaseParticle = function (particle) {
        if (particle.active) {
            particle.active = false;
            particle.reset();
            this.activeParticleCount--;
        }
    };
    /**
     * 添加粒子发射器
     * @param emitter 粒子发射器
     */
    ParticleSystem.prototype.addEmitter = function (emitter) {
        // 初始化发射器
        emitter.initialize(this);
        // 添加到发射器列表
        this.emitters.push(emitter);
    };
    /**
     * 移除粒子发射器
     * @param emitter 粒子发射器
     */
    ParticleSystem.prototype.removeEmitter = function (emitter) {
        var index = this.emitters.indexOf(emitter);
        if (index !== -1) {
            // 停止发射器
            emitter.stop();
            // 从列表中移除
            this.emitters.splice(index, 1);
        }
    };
    /**
     * 获取所有粒子发射器
     * @returns 粒子发射器数组
     */
    ParticleSystem.prototype.getEmitters = function () {
        return __spreadArray([], this.emitters, true);
    };
    /**
     * 获取活跃粒子数量
     * @returns 活跃粒子数量
     */
    ParticleSystem.prototype.getActiveParticleCount = function () {
        return this.activeParticleCount;
    };
    /**
     * 获取最大粒子数量
     * @returns 最大粒子数量
     */
    ParticleSystem.prototype.getMaxParticles = function () {
        return this.maxParticles;
    };
    /**
     * 是否使用GPU加速
     * @returns 是否使用GPU加速
     */
    ParticleSystem.prototype.isUsingGPU = function () {
        return this.useGPU;
    };
    /**
     * 是否启用碰撞
     * @returns 是否启用碰撞
     */
    ParticleSystem.prototype.isCollisionEnabled = function () {
        return this.enableCollision;
    };
    /**
     * 是否启用物理模拟
     * @returns 是否启用物理模拟
     */
    ParticleSystem.prototype.isPhysicsEnabled = function () {
        return this.enablePhysics;
    };
    /**
     * 获取物理系统
     * @returns 物理系统
     */
    ParticleSystem.prototype.getPhysicsSystem = function () {
        return this.physicsSystem;
    };
    /**
     * 清除所有粒子
     */
    ParticleSystem.prototype.clearAllParticles = function () {
        for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {
            var emitter = _a[_i];
            emitter.clearParticles();
        }
        this.activeParticleCount = 0;
    };
    /**
     * 销毁系统
     */
    ParticleSystem.prototype.dispose = function () {
        // 移除事件监听器
        if (this.engine) {
            var world = this.engine.getWorld();
            world.off('entityCreated', this.handleEntityCreated.bind(this));
            world.off('entityRemoved', this.handleEntityRemoved.bind(this));
        }
        // 停止所有发射器
        for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {
            var emitter = _a[_i];
            emitter.stop();
            emitter.clearParticles();
        }
        this.emitters = [];
        this.particlePool = [];
        this.activeParticleCount = 0;
        _super.prototype.dispose.call(this);
    };
    /** 系统类型 */
    ParticleSystem.type = 'ParticleSystem';
    return ParticleSystem;
}(System_1.System));
