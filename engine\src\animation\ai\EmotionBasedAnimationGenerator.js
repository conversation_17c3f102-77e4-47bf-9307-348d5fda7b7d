"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmotionBasedAnimationGenerator = void 0;
/**
 * 基于情感的动画生成器
 * 根据文本情感分析生成面部动画
 */
var FacialAnimation_1 = require("../FacialAnimation");
/**
 * 情感到表情的映射
 */
var EMOTION_TO_EXPRESSION_MAP = {
    'happy': FacialAnimation_1.FacialExpressionType.HAPPY,
    'sad': FacialAnimation_1.FacialExpressionType.SAD,
    'angry': FacialAnimation_1.FacialExpressionType.ANGRY,
    'surprised': FacialAnimation_1.FacialExpressionType.SURPRISED,
    'fear': FacialAnimation_1.FacialExpressionType.FEAR,
    'disgust': FacialAnimation_1.FacialExpressionType.DISGUST,
    'neutral': FacialAnimation_1.FacialExpressionType.NEUTRAL,
    'joy': FacialAnimation_1.FacialExpressionType.HAPPY,
    'sorrow': FacialAnimation_1.FacialExpressionType.SAD,
    'rage': FacialAnimation_1.FacialExpressionType.ANGRY,
    'shock': FacialAnimation_1.FacialExpressionType.SURPRISED,
    'terror': FacialAnimation_1.FacialExpressionType.FEAR
};
/**
 * 基于情感的动画生成器
 */
var EmotionBasedAnimationGenerator = /** @class */ (function () {
    /**
     * 构造函数
     * @param aiModel AI模型
     * @param debug 是否启用调试
     */
    function EmotionBasedAnimationGenerator(aiModel, debug) {
        if (debug === void 0) { debug = false; }
        this.aiModel = aiModel;
        this.debug = debug;
    }
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    EmotionBasedAnimationGenerator.prototype.generateFacialAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var emotionResult, clip, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.aiModel.analyzeEmotion(request.prompt)];
                    case 1:
                        emotionResult = _a.sent();
                        if (this.debug) {
                            console.log('情感分析结果:', emotionResult);
                        }
                        return [4 /*yield*/, this.createEmotionBasedFacialClip(request, emotionResult)];
                    case 2:
                        clip = _a.sent();
                        return [2 /*return*/, {
                                id: request.id,
                                success: true,
                                clip: clip,
                                generationTime: 0,
                                userData: request.userData
                            }];
                    case 3:
                        error_1 = _a.sent();
                        if (this.debug) {
                            console.error('生成面部动画失败:', error_1);
                        }
                        return [2 /*return*/, {
                                id: request.id,
                                success: false,
                                error: error_1 instanceof Error ? error_1.message : String(error_1),
                                userData: request.userData
                            }];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 创建基于情感的面部动画片段
     * @param request 生成请求
     * @param emotionResult 情感分析结果
     * @returns 面部动画片段
     */
    EmotionBasedAnimationGenerator.prototype.createEmotionBasedFacialClip = function (request, emotionResult) {
        return __awaiter(this, void 0, void 0, function () {
            var primaryExpression, clip, expressionWeight, keyframeCount, middleTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        primaryExpression = this.mapEmotionToExpression(emotionResult.primaryEmotion);
                        clip = {
                            name: request.prompt,
                            duration: request.duration,
                            loop: request.loop,
                            keyframes: []
                        };
                        expressionWeight = Math.min(1.0, emotionResult.intensity);
                        keyframeCount = Math.max(3, Math.ceil(request.duration * 2));
                        // 添加起始关键帧（中性表情）
                        clip.keyframes.push({
                            time: 0,
                            expression: FacialAnimation_1.FacialExpressionType.NEUTRAL,
                            expressionWeight: 1.0
                        });
                        middleTime = request.duration / 2;
                        clip.keyframes.push({
                            time: middleTime,
                            expression: primaryExpression,
                            expressionWeight: expressionWeight
                        });
                        // 添加结束关键帧（回到中性表情）
                        if (request.loop) {
                            clip.keyframes.push({
                                time: request.duration,
                                expression: FacialAnimation_1.FacialExpressionType.NEUTRAL,
                                expressionWeight: 1.0
                            });
                        }
                        else {
                            // 如果不循环，保持最后的表情
                            clip.keyframes.push({
                                time: request.duration,
                                expression: primaryExpression,
                                expressionWeight: expressionWeight * 0.8 // 稍微减弱
                            });
                        }
                        // 添加口型关键帧（如果有）
                        return [4 /*yield*/, this.addVisemeKeyframes(clip, request)];
                    case 1:
                        // 添加口型关键帧（如果有）
                        _a.sent();
                        // 按时间排序
                        clip.keyframes.sort(function (a, b) { return a.time - b.time; });
                        return [2 /*return*/, clip];
                }
            });
        });
    };
    /**
     * 添加口型关键帧
     * @param clip 动画片段
     * @param request 生成请求
     */
    EmotionBasedAnimationGenerator.prototype.addVisemeKeyframes = function (clip, request) {
        return __awaiter(this, void 0, void 0, function () {
            var visemeSequence, visemeDuration, _loop_1, i;
            return __generator(this, function (_a) {
                visemeSequence = [
                    FacialAnimation_1.VisemeType.SILENT,
                    FacialAnimation_1.VisemeType.AA,
                    FacialAnimation_1.VisemeType.EE,
                    FacialAnimation_1.VisemeType.IH,
                    FacialAnimation_1.VisemeType.OH,
                    FacialAnimation_1.VisemeType.OU,
                    FacialAnimation_1.VisemeType.SILENT
                ];
                visemeDuration = request.duration / (visemeSequence.length - 1);
                _loop_1 = function (i) {
                    var time = i * visemeDuration;
                    var viseme = visemeSequence[i];
                    // 查找是否已存在相同时间的关键帧
                    var existingKeyframe = clip.keyframes.find(function (k) { return Math.abs(k.time - time) < 0.01; });
                    if (existingKeyframe) {
                        // 更新现有关键帧
                        existingKeyframe.viseme = viseme;
                        existingKeyframe.visemeWeight = viseme === FacialAnimation_1.VisemeType.SILENT ? 0.0 : 1.0;
                    }
                    else {
                        // 添加新关键帧
                        clip.keyframes.push({
                            time: time,
                            viseme: viseme,
                            visemeWeight: viseme === FacialAnimation_1.VisemeType.SILENT ? 0.0 : 1.0
                        });
                    }
                };
                // 添加口型关键帧
                for (i = 0; i < visemeSequence.length; i++) {
                    _loop_1(i);
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * 将情感映射到表情
     * @param emotion 情感
     * @returns 表情类型
     */
    EmotionBasedAnimationGenerator.prototype.mapEmotionToExpression = function (emotion) {
        var lowerEmotion = emotion.toLowerCase();
        return EMOTION_TO_EXPRESSION_MAP[lowerEmotion] || FacialAnimation_1.FacialExpressionType.NEUTRAL;
    };
    return EmotionBasedAnimationGenerator;
}());
exports.EmotionBasedAnimationGenerator = EmotionBasedAnimationGenerator;
