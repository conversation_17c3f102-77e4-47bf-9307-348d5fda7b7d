"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkSecuritySystem = exports.HashAlgorithm = exports.EncryptionAlgorithm = void 0;
/**
 * 网络安全系统
 * 提供数据加密/解密、用户认证、权限验证等功能
 */
var System_1 = require("../core/System");
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 加密算法类型
 */
var EncryptionAlgorithm;
(function (EncryptionAlgorithm) {
    /** AES加密 */
    EncryptionAlgorithm["AES"] = "aes";
    /** RSA加密 */
    EncryptionAlgorithm["RSA"] = "rsa";
    /** ChaCha20加密 */
    EncryptionAlgorithm["CHACHA20"] = "chacha20";
    /** 自定义加密 */
    EncryptionAlgorithm["CUSTOM"] = "custom";
})(EncryptionAlgorithm || (exports.EncryptionAlgorithm = EncryptionAlgorithm = {}));
/**
 * 哈希算法类型
 */
var HashAlgorithm;
(function (HashAlgorithm) {
    /** MD5哈希 */
    HashAlgorithm["MD5"] = "md5";
    /** SHA-1哈希 */
    HashAlgorithm["SHA1"] = "sha1";
    /** SHA-256哈希 */
    HashAlgorithm["SHA256"] = "sha256";
    /** SHA-512哈希 */
    HashAlgorithm["SHA512"] = "sha512";
    /** 自定义哈希 */
    HashAlgorithm["CUSTOM"] = "custom";
})(HashAlgorithm || (exports.HashAlgorithm = HashAlgorithm = {}));
/**
 * 网络安全系统
 */
var NetworkSecuritySystem = exports.NetworkSecuritySystem = /** @class */ (function (_super) {
    __extends(NetworkSecuritySystem, _super);
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    function NetworkSecuritySystem(world, config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, world) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 加密密钥映射 */
        _this.encryptionKeys = new Map();
        /** 会话映射 */
        _this.sessions = new Map();
        /** 令牌映射 */
        _this.tokens = new Map();
        /** 审计日志 */
        _this.auditLog = [];
        /** 消息计数器（防重放） */
        _this.messageCounters = new Map();
        // 合并配置
        _this.config = __assign(__assign({}, NetworkSecuritySystem.DEFAULT_CONFIG), config);
        // 初始化
        _this.initialize();
        return _this;
    }
    /**
     * 初始化
     */
    NetworkSecuritySystem.prototype.initialize = function () {
        if (this.config.debug) {
            console.log('初始化网络安全系统');
        }
        // 如果启用了证书验证，加载证书
        if (this.config.enableCertificateValidation && this.config.certificatePath) {
            this.loadCertificate(this.config.certificatePath);
        }
    };
    /**
     * 加载证书
     * @param path 证书路径
     */
    NetworkSecuritySystem.prototype.loadCertificate = function (path) {
        if (this.config.debug) {
            console.log("\u52A0\u8F7D\u8BC1\u4E66: ".concat(path));
        }
        // 这里应该实现证书加载逻辑
        // 实际应用中，可能需要使用Node.js的fs模块或浏览器的fetch API
    };
    /**
     * 加密数据
     * @param data 要加密的数据
     * @param algorithm 加密算法
     * @param key 加密密钥
     * @returns 加密后的数据
     */
    NetworkSecuritySystem.prototype.encryptData = function (data, algorithm, key) {
        if (algorithm === void 0) { algorithm = this.config.defaultEncryptionAlgorithm; }
        // 如果数据不是字符串，转换为JSON字符串
        var dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        // 如果没有提供密钥，使用默认密钥
        var encryptionKey = key || this.getDefaultKey(algorithm);
        try {
            // 根据算法选择不同的加密方法
            switch (algorithm) {
                case EncryptionAlgorithm.AES:
                    return this.encryptAES(dataStr, encryptionKey);
                case EncryptionAlgorithm.RSA:
                    return this.encryptRSA(dataStr, encryptionKey);
                case EncryptionAlgorithm.CHACHA20:
                    return this.encryptChaCha20(dataStr, encryptionKey);
                default:
                    throw new Error("\u4E0D\u652F\u6301\u7684\u52A0\u5BC6\u7B97\u6CD5: ".concat(algorithm));
            }
        }
        catch (error) {
            console.error("\u52A0\u5BC6\u6570\u636E\u5931\u8D25: ".concat(error));
            throw error;
        }
    };
    /**
     * 解密数据
     * @param encryptedData 加密的数据
     * @param algorithm 加密算法
     * @param key 加密密钥
     * @returns 解密后的数据
     */
    NetworkSecuritySystem.prototype.decryptData = function (encryptedData, algorithm, key) {
        if (algorithm === void 0) { algorithm = this.config.defaultEncryptionAlgorithm; }
        // 如果没有提供密钥，使用默认密钥
        var encryptionKey = key || this.getDefaultKey(algorithm);
        try {
            // 根据算法选择不同的解密方法
            var decryptedStr = void 0;
            switch (algorithm) {
                case EncryptionAlgorithm.AES:
                    decryptedStr = this.decryptAES(encryptedData, encryptionKey);
                    break;
                case EncryptionAlgorithm.RSA:
                    decryptedStr = this.decryptRSA(encryptedData, encryptionKey);
                    break;
                case EncryptionAlgorithm.CHACHA20:
                    decryptedStr = this.decryptChaCha20(encryptedData, encryptionKey);
                    break;
                default:
                    throw new Error("\u4E0D\u652F\u6301\u7684\u52A0\u5BC6\u7B97\u6CD5: ".concat(algorithm));
            }
            // 尝试解析JSON
            try {
                return JSON.parse(decryptedStr);
            }
            catch (_a) {
                // 如果不是有效的JSON，直接返回字符串
                return decryptedStr;
            }
        }
        catch (error) {
            console.error("\u89E3\u5BC6\u6570\u636E\u5931\u8D25: ".concat(error));
            throw error;
        }
    };
    /**
     * 计算哈希
     * @param data 要哈希的数据
     * @param algorithm 哈希算法
     * @returns 哈希值
     */
    NetworkSecuritySystem.prototype.computeHash = function (data, algorithm) {
        if (algorithm === void 0) { algorithm = this.config.defaultHashAlgorithm; }
        // 如果数据不是字符串，转换为JSON字符串
        var dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        try {
            // 根据算法选择不同的哈希方法
            switch (algorithm) {
                case HashAlgorithm.MD5:
                    return this.hashMD5(dataStr);
                case HashAlgorithm.SHA1:
                    return this.hashSHA1(dataStr);
                case HashAlgorithm.SHA256:
                    return this.hashSHA256(dataStr);
                case HashAlgorithm.SHA512:
                    return this.hashSHA512(dataStr);
                default:
                    throw new Error("\u4E0D\u652F\u6301\u7684\u54C8\u5E0C\u7B97\u6CD5: ".concat(algorithm));
            }
        }
        catch (error) {
            console.error("\u8BA1\u7B97\u54C8\u5E0C\u5931\u8D25: ".concat(error));
            throw error;
        }
    };
    /**
     * 生成签名
     * @param data 要签名的数据
     * @param privateKey 私钥
     * @returns 签名
     */
    NetworkSecuritySystem.prototype.generateSignature = function (data, privateKey) {
        // 如果数据不是字符串，转换为JSON字符串
        var dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        try {
            // 这里应该实现签名逻辑
            // 实际应用中，可能需要使用第三方库
            // 模拟签名
            var hash = this.computeHash(dataStr);
            return this.encryptData(hash, EncryptionAlgorithm.RSA, privateKey);
        }
        catch (error) {
            console.error("\u751F\u6210\u7B7E\u540D\u5931\u8D25: ".concat(error));
            throw error;
        }
    };
    /**
     * 验证签名
     * @param data 签名的数据
     * @param signature 签名
     * @param publicKey 公钥
     * @returns 是否有效
     */
    NetworkSecuritySystem.prototype.verifySignature = function (data, signature, publicKey) {
        // 如果数据不是字符串，转换为JSON字符串
        var dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        try {
            // 这里应该实现签名验证逻辑
            // 实际应用中，可能需要使用第三方库
            // 模拟验证
            var hash = this.computeHash(dataStr);
            var decryptedSignature = this.decryptData(signature, EncryptionAlgorithm.RSA, publicKey);
            return hash === decryptedSignature;
        }
        catch (error) {
            console.error("\u9A8C\u8BC1\u7B7E\u540D\u5931\u8D25: ".concat(error));
            return false;
        }
    };
    /**
     * 创建会话
     * @param userId 用户ID
     * @param data 会话数据
     * @returns 会话ID
     */
    NetworkSecuritySystem.prototype.createSession = function (userId, data) {
        if (data === void 0) { data = {}; }
        // 生成会话ID
        var sessionId = this.generateRandomId();
        // 计算过期时间
        var now = Date.now();
        var expiresAt = now + (this.config.sessionTimeout || 3600000);
        // 创建会话
        this.sessions.set(sessionId, {
            userId: userId,
            createdAt: now,
            expiresAt: expiresAt,
            data: data
        });
        // 记录审计日志
        this.logAudit('createSession', userId, { sessionId: sessionId });
        return sessionId;
    };
    /**
     * 验证会话
     * @param sessionId 会话ID
     * @returns 是否有效
     */
    NetworkSecuritySystem.prototype.validateSession = function (sessionId) {
        // 获取会话
        var session = this.sessions.get(sessionId);
        // 如果会话不存在，返回false
        if (!session) {
            return false;
        }
        // 如果会话已过期，删除会话并返回false
        if (session.expiresAt < Date.now()) {
            this.sessions.delete(sessionId);
            return false;
        }
        return true;
    };
    /**
     * 创建令牌
     * @param userId 用户ID
     * @param scope 令牌范围
     * @param expiresIn 过期时间（毫秒）
     * @returns 令牌
     */
    NetworkSecuritySystem.prototype.createToken = function (userId, scope, expiresIn) {
        if (scope === void 0) { scope = []; }
        if (expiresIn === void 0) { expiresIn = this.config.tokenExpiration || 86400000; }
        // 生成令牌ID
        var tokenId = this.generateRandomId();
        // 计算过期时间
        var now = Date.now();
        var expiresAt = now + expiresIn;
        // 创建令牌
        this.tokens.set(tokenId, {
            userId: userId,
            createdAt: now,
            expiresAt: expiresAt,
            scope: scope
        });
        // 记录审计日志
        this.logAudit('createToken', userId, { tokenId: tokenId, scope: scope });
        return tokenId;
    };
    /**
     * 验证令牌
     * @param tokenId 令牌ID
     * @param requiredScope 所需范围
     * @returns 是否有效
     */
    NetworkSecuritySystem.prototype.validateToken = function (tokenId, requiredScope) {
        // 获取令牌
        var token = this.tokens.get(tokenId);
        // 如果令牌不存在，返回false
        if (!token) {
            return false;
        }
        // 如果令牌已过期，删除令牌并返回false
        if (token.expiresAt < Date.now()) {
            this.tokens.delete(tokenId);
            return false;
        }
        // 如果指定了所需范围，检查令牌是否具有该范围
        if (requiredScope && !token.scope.includes(requiredScope)) {
            return false;
        }
        return true;
    };
    /**
     * 获取默认密钥
     * @param algorithm 算法
     * @returns 密钥
     */
    NetworkSecuritySystem.prototype.getDefaultKey = function (algorithm) {
        // 如果已经有该算法的密钥，直接返回
        if (this.encryptionKeys.has(algorithm)) {
            return this.encryptionKeys.get(algorithm);
        }
        // 生成新密钥
        var key = this.generateRandomKey(algorithm);
        // 保存密钥
        this.encryptionKeys.set(algorithm, key);
        return key;
    };
    /**
     * 生成随机密钥
     * @param algorithm 算法
     * @returns 密钥
     */
    NetworkSecuritySystem.prototype.generateRandomKey = function (algorithm) {
        // 根据算法生成不同长度的密钥
        var length;
        switch (algorithm) {
            case EncryptionAlgorithm.AES:
                length = 32; // 256位
                break;
            case EncryptionAlgorithm.RSA:
                length = 64; // 512位
                break;
            case EncryptionAlgorithm.CHACHA20:
                length = 32; // 256位
                break;
            default:
                length = 32;
        }
        // 生成随机字符串
        return this.generateRandomString(length);
    };
    /**
     * 生成随机字符串
     * @param length 长度
     * @returns 随机字符串
     */
    NetworkSecuritySystem.prototype.generateRandomString = function (length) {
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        var result = '';
        for (var i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    };
    /**
     * 生成随机ID
     * @returns 随机ID
     */
    NetworkSecuritySystem.prototype.generateRandomId = function () {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };
    /**
     * 记录审计日志
     * @param action 操作
     * @param userId 用户ID
     * @param details 详情
     */
    NetworkSecuritySystem.prototype.logAudit = function (action, userId, details) {
        if (details === void 0) { details = {}; }
        // 如果未启用审计日志，直接返回
        if (!this.config.enableAuditLog) {
            return;
        }
        // 添加日志
        this.auditLog.push({
            timestamp: Date.now(),
            action: action,
            userId: userId,
            details: details
        });
        // 如果日志过多，删除旧日志
        if (this.auditLog.length > 1000) {
            this.auditLog.splice(0, 100);
        }
    };
    /**
     * AES加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    NetworkSecuritySystem.prototype.encryptAES = function (data, key) {
        // 这里应该实现AES加密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟加密
        return "AES:".concat(btoa(data), ":").concat(this.generateRandomString(16));
    };
    /**
     * AES解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    NetworkSecuritySystem.prototype.decryptAES = function (encryptedData, key) {
        // 这里应该实现AES解密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟解密
        var parts = encryptedData.split(':');
        if (parts.length !== 3 || parts[0] !== 'AES') {
            throw new Error('无效的AES加密数据');
        }
        return atob(parts[1]);
    };
    /**
     * RSA加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    NetworkSecuritySystem.prototype.encryptRSA = function (data, key) {
        // 这里应该实现RSA加密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟加密
        return "RSA:".concat(btoa(data), ":").concat(this.generateRandomString(16));
    };
    /**
     * RSA解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    NetworkSecuritySystem.prototype.decryptRSA = function (encryptedData, key) {
        // 这里应该实现RSA解密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟解密
        var parts = encryptedData.split(':');
        if (parts.length !== 3 || parts[0] !== 'RSA') {
            throw new Error('无效的RSA加密数据');
        }
        return atob(parts[1]);
    };
    /**
     * ChaCha20加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    NetworkSecuritySystem.prototype.encryptChaCha20 = function (data, key) {
        // 这里应该实现ChaCha20加密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟加密
        return "CHACHA20:".concat(btoa(data), ":").concat(this.generateRandomString(16));
    };
    /**
     * ChaCha20解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    NetworkSecuritySystem.prototype.decryptChaCha20 = function (encryptedData, key) {
        // 这里应该实现ChaCha20解密逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟解密
        var parts = encryptedData.split(':');
        if (parts.length !== 3 || parts[0] !== 'CHACHA20') {
            throw new Error('无效的ChaCha20加密数据');
        }
        return atob(parts[1]);
    };
    /**
     * MD5哈希
     * @param data 数据
     * @returns 哈希值
     */
    NetworkSecuritySystem.prototype.hashMD5 = function (data) {
        // 这里应该实现MD5哈希逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟哈希
        return "md5-".concat(this.generateRandomString(32));
    };
    /**
     * SHA-1哈希
     * @param data 数据
     * @returns 哈希值
     */
    NetworkSecuritySystem.prototype.hashSHA1 = function (data) {
        // 这里应该实现SHA-1哈希逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟哈希
        return "sha1-".concat(this.generateRandomString(40));
    };
    /**
     * SHA-256哈希
     * @param data 数据
     * @returns 哈希值
     */
    NetworkSecuritySystem.prototype.hashSHA256 = function (data) {
        // 这里应该实现SHA-256哈希逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟哈希
        return "sha256-".concat(this.generateRandomString(64));
    };
    /**
     * SHA-512哈希
     * @param data 数据
     * @returns 哈希值
     */
    NetworkSecuritySystem.prototype.hashSHA512 = function (data) {
        // 这里应该实现SHA-512哈希逻辑
        // 实际应用中，可能需要使用第三方库
        // 模拟哈希
        return "sha512-".concat(this.generateRandomString(128));
    };
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    NetworkSecuritySystem.prototype.on = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    NetworkSecuritySystem.prototype.off = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 销毁
     */
    NetworkSecuritySystem.prototype.dispose = function () {
        // 清空映射
        this.encryptionKeys.clear();
        this.sessions.clear();
        this.tokens.clear();
        this.messageCounters.clear();
        // 清空审计日志
        this.auditLog = [];
        // 清空事件监听器
        this.eventEmitter.removeAllListeners();
    };
    /** 系统优先级 */
    NetworkSecuritySystem.PRIORITY = 150;
    /** 默认配置 */
    NetworkSecuritySystem.DEFAULT_CONFIG = {
        debug: false,
        defaultEncryptionAlgorithm: EncryptionAlgorithm.AES,
        defaultHashAlgorithm: HashAlgorithm.SHA256,
        enableEndToEndEncryption: true,
        enableSecureKeyExchange: true,
        enableMessageSigning: true,
        enableSessionManagement: true,
        sessionTimeout: 3600000,
        enableAccessControl: true,
        enableAuditLog: true,
        enableReplayProtection: true,
        enableSecureTokens: true,
        tokenExpiration: 86400000,
        enableCertificateValidation: false
    };
    return NetworkSecuritySystem;
}(System_1.System));
