"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputManager = void 0;
/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var KeyboardDevice_1 = require("./devices/KeyboardDevice");
var MouseDevice_1 = require("./devices/MouseDevice");
var GamepadDevice_1 = require("./devices/GamepadDevice");
var TouchDevice_1 = require("./devices/TouchDevice");
var XRDevice_1 = require("./devices/XRDevice");
var GestureDevice_1 = require("./devices/GestureDevice");
var VoiceDevice_1 = require("./devices/VoiceDevice");
/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
var InputManager = /** @class */ (function () {
    /**
     * 创建输入管理器
     * @param options 输入管理器选项
     */
    function InputManager(options) {
        if (options === void 0) { options = {}; }
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 输入设备列表 */
        this.devices = new Map();
        /** 输入映射列表 */
        this.mappings = new Map();
        /** 输入动作列表 */
        this.actions = new Map();
        /** 输入绑定列表 */
        this.bindings = new Map();
        /** 是否已初始化 */
        this.initialized = false;
        /** 是否已销毁 */
        this.destroyed = false;
        this.element = options.element || document.body;
        this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
        this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;
        // 初始化输入设备
        if (options.enableKeyboard !== false) {
            this.addDevice(new KeyboardDevice_1.KeyboardDevice(this.element, this.preventDefault, this.stopPropagation));
        }
        if (options.enableMouse !== false) {
            this.addDevice(new MouseDevice_1.MouseDevice(this.element, this.preventDefault, this.stopPropagation));
        }
        if (options.enableTouch !== false) {
            this.addDevice(new TouchDevice_1.TouchDevice(this.element, this.preventDefault, this.stopPropagation));
        }
        if (options.enableGamepad !== false) {
            this.addDevice(new GamepadDevice_1.GamepadDevice());
        }
        if (options.enableXR !== false) {
            this.addDevice(new XRDevice_1.XRDevice());
        }
        if (options.enableGesture) {
            this.addDevice(new GestureDevice_1.GestureDevice(options.gestureOptions));
        }
        if (options.enableVoice) {
            this.addDevice(new VoiceDevice_1.VoiceDevice(options.voiceOptions));
        }
        // 设置单例实例
        InputManager.instance = this;
    }
    /**
     * 获取单例实例
     * @returns 输入管理器实例
     */
    InputManager.getInstance = function () {
        if (!InputManager.instance) {
            InputManager.instance = new InputManager();
        }
        return InputManager.instance;
    };
    /**
     * 初始化
     */
    InputManager.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 初始化所有设备
        for (var _i = 0, _a = this.devices.values(); _i < _a.length; _i++) {
            var device = _a[_i];
            device.initialize();
        }
        this.initialized = true;
    };
    /**
     * 更新
     * @param deltaTime 帧间隔时间（秒）
     */
    InputManager.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
        // 更新所有设备
        for (var _i = 0, _a = this.devices.values(); _i < _a.length; _i++) {
            var device = _a[_i];
            device.update(deltaTime);
        }
        // 处理输入动作
        this.processActions();
    };
    /**
     * 销毁
     */
    InputManager.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 销毁所有设备
        for (var _i = 0, _a = this.devices.values(); _i < _a.length; _i++) {
            var device = _a[_i];
            device.destroy();
        }
        // 清空设备列表
        this.devices.clear();
        // 清空映射列表
        this.mappings.clear();
        // 清空动作列表
        this.actions.clear();
        // 清空绑定列表
        this.bindings.clear();
        this.destroyed = true;
    };
    /**
     * 添加设备
     * @param device 输入设备
     */
    InputManager.prototype.addDevice = function (device) {
        this.devices.set(device.getName(), device);
        // 如果已初始化，则初始化设备
        if (this.initialized) {
            device.initialize();
        }
    };
    /**
     * 获取设备
     * @param name 设备名称
     * @returns 输入设备
     */
    InputManager.prototype.getDevice = function (name) {
        return this.devices.get(name);
    };
    /**
     * 添加映射
     * @param mapping 输入映射
     */
    InputManager.prototype.addMapping = function (mapping) {
        this.mappings.set(mapping.getName(), mapping);
    };
    /**
     * 获取映射
     * @param name 映射名称
     * @returns 输入映射
     */
    InputManager.prototype.getMapping = function (name) {
        return this.mappings.get(name);
    };
    /**
     * 添加动作
     * @param action 输入动作
     */
    InputManager.prototype.addAction = function (action) {
        this.actions.set(action.getName(), action);
    };
    /**
     * 获取动作
     * @param name 动作名称
     * @returns 输入动作
     */
    InputManager.prototype.getAction = function (name) {
        return this.actions.get(name);
    };
    /**
     * 添加绑定
     * @param binding 输入绑定
     */
    InputManager.prototype.addBinding = function (binding) {
        this.bindings.set(binding.getName(), binding);
    };
    /**
     * 获取绑定
     * @param name 绑定名称
     * @returns 输入绑定
     */
    InputManager.prototype.getBinding = function (name) {
        return this.bindings.get(name);
    };
    /**
     * 处理输入动作
     */
    InputManager.prototype.processActions = function () {
        // 处理所有动作
        for (var _i = 0, _a = this.actions.values(); _i < _a.length; _i++) {
            var action = _a[_i];
            // 获取动作的绑定
            var binding = this.bindings.get(action.getName());
            if (!binding)
                continue;
            // 获取绑定的映射
            var mapping = this.mappings.get(binding.getMappingName());
            if (!mapping)
                continue;
            // 获取映射的设备
            var device = this.devices.get(mapping.getDeviceName());
            if (!device)
                continue;
            // 检查设备输入是否满足映射条件
            var value = mapping.evaluate(device);
            // 更新动作状态
            action.update(value);
            // 如果动作状态发生变化，触发事件
            if (action.hasChanged()) {
                this.eventEmitter.emit(action.getName(), action);
            }
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    InputManager.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    InputManager.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
        return this;
    };
    return InputManager;
}());
exports.InputManager = InputManager;
