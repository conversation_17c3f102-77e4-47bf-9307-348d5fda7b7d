"use strict";
/**
 * InteractionEventComponent.ts
 *
 * 交互事件组件，用于处理交互事件
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionEventComponent = exports.InteractionEvent = exports.InteractionEventType = void 0;
var Component_1 = require("../../core/Component");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 交互事件类型枚举
 */
var InteractionEventType;
(function (InteractionEventType) {
    /** 交互开始 */
    InteractionEventType["INTERACTION_START"] = "interactionStart";
    /** 交互结束 */
    InteractionEventType["INTERACTION_END"] = "interactionEnd";
    /** 进入交互范围 */
    InteractionEventType["ENTER_RANGE"] = "enterRange";
    /** 离开交互范围 */
    InteractionEventType["EXIT_RANGE"] = "exitRange";
    /** 高亮开始 */
    InteractionEventType["HIGHLIGHT_START"] = "highlightStart";
    /** 高亮结束 */
    InteractionEventType["HIGHLIGHT_END"] = "highlightEnd";
    /** 悬停开始 */
    InteractionEventType["HOVER_START"] = "hoverStart";
    /** 悬停结束 */
    InteractionEventType["HOVER_END"] = "hoverEnd";
})(InteractionEventType || (exports.InteractionEventType = InteractionEventType = {}));
/**
 * 交互事件
 */
var InteractionEvent = /** @class */ (function () {
    /**
     * 构造函数
     * @param type 事件类型
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    function InteractionEvent(type, target, source, data) {
        if (data === void 0) { data = {}; }
        this.type = type;
        this.target = target;
        this.source = source;
        this.timestamp = Date.now();
        this.data = data;
    }
    return InteractionEvent;
}());
exports.InteractionEvent = InteractionEvent;
/**
 * 交互事件组件
 * 用于处理交互事件
 */
var InteractionEventComponent = /** @class */ (function (_super) {
    __extends(InteractionEventComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    function InteractionEventComponent(entity, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'InteractionEvent') || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 事件历史记录 */
        _this.eventHistory = [];
        /** 最大历史记录数量 */
        _this.maxHistorySize = 10;
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化属性
        _this._enabled = config.enabled !== undefined ? config.enabled : true;
        return _this;
    }
    Object.defineProperty(InteractionEventComponent.prototype, "enabled", {
        /**
         * 获取是否启用
         */
        get: function () {
            return this._enabled;
        },
        /**
         * 设置是否启用
         */
        set: function (value) {
            this._enabled = value;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    InteractionEventComponent.prototype.addEventListener = function (type, listener) {
        this.eventEmitter.on(type, listener);
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    InteractionEventComponent.prototype.removeEventListener = function (type, listener) {
        this.eventEmitter.off(type, listener);
    };
    /**
     * 分发事件
     * @param event 交互事件
     */
    InteractionEventComponent.prototype.dispatchEvent = function (event) {
        // 如果未启用，则返回
        if (!this._enabled)
            return;
        // 添加到历史记录
        this.addToHistory(event);
        // 分发事件
        this.eventEmitter.emit(event.type, event);
    };
    /**
     * 创建并分发事件
     * @param type 事件类型
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.createAndDispatchEvent = function (type, target, source, data) {
        if (data === void 0) { data = {}; }
        var event = new InteractionEvent(type, target, source, data);
        this.dispatchEvent(event);
    };
    /**
     * 添加到历史记录
     * @param event 交互事件
     */
    InteractionEventComponent.prototype.addToHistory = function (event) {
        // 添加到历史记录
        this.eventHistory.unshift(event);
        // 如果超过最大数量，则移除最旧的
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.pop();
        }
    };
    /**
     * 获取事件历史记录
     * @returns 事件历史记录
     */
    InteractionEventComponent.prototype.getEventHistory = function () {
        return __spreadArray([], this.eventHistory, true);
    };
    /**
     * 清除事件历史记录
     */
    InteractionEventComponent.prototype.clearEventHistory = function () {
        this.eventHistory = [];
    };
    /**
     * 处理交互开始
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleInteractionStart = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.INTERACTION_START, target, source, data);
    };
    /**
     * 处理交互结束
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleInteractionEnd = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.INTERACTION_END, target, source, data);
    };
    /**
     * 处理进入交互范围
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleEnterRange = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.ENTER_RANGE, target, source, data);
    };
    /**
     * 处理离开交互范围
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleExitRange = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.EXIT_RANGE, target, source, data);
    };
    /**
     * 处理高亮开始
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleHighlightStart = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.HIGHLIGHT_START, target, source, data);
    };
    /**
     * 处理高亮结束
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleHighlightEnd = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.HIGHLIGHT_END, target, source, data);
    };
    /**
     * 处理悬停开始
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleHoverStart = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.HOVER_START, target, source, data);
    };
    /**
     * 处理悬停结束
     * @param target 目标实体
     * @param source 源实体
     * @param data 事件数据
     */
    InteractionEventComponent.prototype.handleHoverEnd = function (target, source, data) {
        if (data === void 0) { data = {}; }
        this.createAndDispatchEvent(InteractionEventType.HOVER_END, target, source, data);
    };
    /**
     * 销毁组件
     */
    InteractionEventComponent.prototype.dispose = function () {
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        // 清空历史记录
        this.clearEventHistory();
        // 调用基类的销毁方法
        _super.prototype.dispose.call(this);
    };
    return InteractionEventComponent;
}(Component_1.Component));
exports.InteractionEventComponent = InteractionEventComponent;
