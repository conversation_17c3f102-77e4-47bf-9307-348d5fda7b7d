"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSessionManager = exports.UserPermission = exports.UserRole = void 0;
/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 用户角色
 */
var UserRole;
(function (UserRole) {
    /** 访客 */
    UserRole["GUEST"] = "guest";
    /** 用户 */
    UserRole["USER"] = "user";
    /** 管理员 */
    UserRole["ADMIN"] = "admin";
    /** 超级管理员 */
    UserRole["SUPER_ADMIN"] = "super_admin";
})(UserRole || (exports.UserRole = UserRole = {}));
/**
 * 用户权限
 */
var UserPermission;
(function (UserPermission) {
    /** 查看 */
    UserPermission["VIEW"] = "view";
    /** 编辑 */
    UserPermission["EDIT"] = "edit";
    /** 创建 */
    UserPermission["CREATE"] = "create";
    /** 删除 */
    UserPermission["DELETE"] = "delete";
    /** 管理用户 */
    UserPermission["MANAGE_USERS"] = "manage_users";
    /** 管理权限 */
    UserPermission["MANAGE_PERMISSIONS"] = "manage_permissions";
    /** 管理系统 */
    UserPermission["MANAGE_SYSTEM"] = "manage_system";
})(UserPermission || (exports.UserPermission = UserPermission = {}));
/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
var UserSessionManager = /** @class */ (function (_super) {
    __extends(UserSessionManager, _super);
    /**
     * 创建用户会话管理器
     * @param config 配置
     */
    function UserSessionManager(config) {
        var _a;
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 用户会话映射表 */
        _this.sessions = new Map();
        /** 角色权限映射表 */
        _this.rolePermissions = new Map();
        /** 会话清理定时器ID */
        _this.cleanupTimerId = null;
        // 默认配置
        _this.config = __assign({ sessionTimeout: 30 * 60 * 1000, enableSessionTimeout: true, enablePermissionCheck: true, defaultRole: UserRole.USER, rolePermissions: (_a = {},
                _a[UserRole.GUEST] = [UserPermission.VIEW],
                _a[UserRole.USER] = [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE],
                _a[UserRole.ADMIN] = [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE, UserPermission.DELETE, UserPermission.MANAGE_USERS],
                _a[UserRole.SUPER_ADMIN] = [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE, UserPermission.DELETE, UserPermission.MANAGE_USERS, UserPermission.MANAGE_PERMISSIONS, UserPermission.MANAGE_SYSTEM],
                _a), allowAnonymous: true, maxUsers: 100 }, config);
        // 初始化角色权限映射
        _this.initRolePermissions();
        // 启动会话清理定时器
        if (_this.config.enableSessionTimeout) {
            _this.startSessionCleanup();
        }
        return _this;
    }
    /**
     * 初始化角色权限映射
     */
    UserSessionManager.prototype.initRolePermissions = function () {
        for (var _i = 0, _a = Object.entries(this.config.rolePermissions); _i < _a.length; _i++) {
            var _b = _a[_i], role = _b[0], permissions = _b[1];
            this.rolePermissions.set(role, new Set(permissions));
        }
    };
    /**
     * 启动会话清理
     */
    UserSessionManager.prototype.startSessionCleanup = function () {
        var _this = this;
        if (this.cleanupTimerId !== null) {
            return;
        }
        this.cleanupTimerId = window.setInterval(function () {
            _this.cleanupSessions();
        }, 60000); // 每分钟检查一次
    };
    /**
     * 停止会话清理
     */
    UserSessionManager.prototype.stopSessionCleanup = function () {
        if (this.cleanupTimerId !== null) {
            clearInterval(this.cleanupTimerId);
            this.cleanupTimerId = null;
        }
    };
    /**
     * 清理过期会话
     */
    UserSessionManager.prototype.cleanupSessions = function () {
        var now = Date.now();
        var timeout = this.config.sessionTimeout;
        for (var _i = 0, _a = this.sessions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], userId = _b[0], session = _b[1];
            if (session.isOnline && now - session.lastActivityTime > timeout) {
                // 会话超时，标记为离线
                session.isOnline = false;
                // 触发用户离线事件
                this.emit('userOffline', userId, session);
            }
        }
    };
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param username 用户名
     * @param role 角色
     * @param isAuthenticated 是否已验证
     * @param sessionToken 会话令牌
     * @param clientInfo 客户端信息
     * @returns 用户会话
     */
    UserSessionManager.prototype.createSession = function (userId, username, role, isAuthenticated, sessionToken, clientInfo) {
        if (role === void 0) { role = this.config.defaultRole; }
        if (isAuthenticated === void 0) { isAuthenticated = true; }
        // 检查是否已存在会话
        if (this.sessions.has(userId)) {
            var existingSession = this.sessions.get(userId);
            // 更新会话
            existingSession.isOnline = true;
            existingSession.lastActivityTime = Date.now();
            existingSession.isAuthenticated = isAuthenticated;
            if (sessionToken) {
                existingSession.sessionToken = sessionToken;
            }
            if (clientInfo) {
                existingSession.clientInfo = __assign(__assign({}, existingSession.clientInfo), clientInfo);
            }
            // 触发用户更新事件
            this.emit('userUpdated', userId, existingSession);
            return existingSession;
        }
        // 检查是否超出最大用户数量
        if (this.sessions.size >= this.config.maxUsers) {
            throw new Error("Maximum number of users (".concat(this.config.maxUsers, ") reached"));
        }
        // 检查是否允许匿名用户
        if (!isAuthenticated && !this.config.allowAnonymous) {
            throw new Error('Anonymous users are not allowed');
        }
        // 创建新会话
        var now = Date.now();
        var session = {
            userId: userId,
            username: username,
            role: role,
            permissions: new Set(this.getPermissionsForRole(role)),
            connectionTime: now,
            lastActivityTime: now,
            isOnline: true,
            isAuthenticated: isAuthenticated,
            sessionToken: sessionToken,
            clientInfo: clientInfo,
        };
        // 添加到会话映射表
        this.sessions.set(userId, session);
        // 触发用户加入事件
        this.emit('userJoined', userId, session);
        return session;
    };
    /**
     * 获取用户会话
     * @param userId 用户ID
     * @returns 用户会话
     */
    UserSessionManager.prototype.getSession = function (userId) {
        return this.sessions.get(userId);
    };
    /**
     * 更新用户会话
     * @param userId 用户ID
     * @param updates 更新数据
     * @returns 更新后的会话
     */
    UserSessionManager.prototype.updateSession = function (userId, updates) {
        var session = this.sessions.get(userId);
        if (!session) {
            return undefined;
        }
        // 更新会话
        Object.assign(session, updates);
        // 更新活动时间
        session.lastActivityTime = Date.now();
        // 如果角色发生变化，则更新权限
        if (updates.role && updates.role !== session.role) {
            session.role = updates.role;
            session.permissions = new Set(this.getPermissionsForRole(updates.role));
        }
        // 触发用户更新事件
        this.emit('userUpdated', userId, session);
        return session;
    };
    /**
     * 移除用户会话
     * @param userId 用户ID
     * @returns 是否成功移除
     */
    UserSessionManager.prototype.removeSession = function (userId) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        // 从会话映射表中移除
        this.sessions.delete(userId);
        // 触发用户离开事件
        this.emit('userLeft', userId, session);
        return true;
    };
    /**
     * 标记用户在线
     * @param userId 用户ID
     * @returns 是否成功
     */
    UserSessionManager.prototype.markUserOnline = function (userId) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        var wasOffline = !session.isOnline;
        // 更新会话
        session.isOnline = true;
        session.lastActivityTime = Date.now();
        // 如果之前是离线状态，则触发用户上线事件
        if (wasOffline) {
            this.emit('userOnline', userId, session);
        }
        return true;
    };
    /**
     * 标记用户离线
     * @param userId 用户ID
     * @returns 是否成功
     */
    UserSessionManager.prototype.markUserOffline = function (userId) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        var wasOnline = session.isOnline;
        // 更新会话
        session.isOnline = false;
        // 如果之前是在线状态，则触发用户离线事件
        if (wasOnline) {
            this.emit('userOffline', userId, session);
        }
        return true;
    };
    /**
     * 更新用户活动时间
     * @param userId 用户ID
     * @returns 是否成功
     */
    UserSessionManager.prototype.updateUserActivity = function (userId) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        // 更新活动时间
        session.lastActivityTime = Date.now();
        return true;
    };
    /**
     * 检查用户是否有权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否有权限
     */
    UserSessionManager.prototype.hasPermission = function (userId, permission) {
        if (!this.config.enablePermissionCheck) {
            return true;
        }
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        return session.permissions.has(permission);
    };
    /**
     * 授予用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    UserSessionManager.prototype.grantPermission = function (userId, permission) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        // 添加权限
        session.permissions.add(permission);
        // 触发权限更新事件
        this.emit('permissionUpdated', userId, session);
        return true;
    };
    /**
     * 撤销用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    UserSessionManager.prototype.revokePermission = function (userId, permission) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        // 移除权限
        var hadPermission = session.permissions.delete(permission);
        // 如果之前有该权限，则触发权限更新事件
        if (hadPermission) {
            this.emit('permissionUpdated', userId, session);
        }
        return hadPermission;
    };
    /**
     * 设置用户角色
     * @param userId 用户ID
     * @param role 角色
     * @returns 是否成功
     */
    UserSessionManager.prototype.setUserRole = function (userId, role) {
        var session = this.sessions.get(userId);
        if (!session) {
            return false;
        }
        // 更新角色
        session.role = role;
        // 更新权限
        session.permissions = new Set(this.getPermissionsForRole(role));
        // 触发角色更新事件
        this.emit('roleUpdated', userId, session);
        return true;
    };
    /**
     * 获取角色的权限
     * @param role 角色
     * @returns 权限列表
     */
    UserSessionManager.prototype.getPermissionsForRole = function (role) {
        var permissions = this.rolePermissions.get(role);
        return permissions ? Array.from(permissions) : [];
    };
    /**
     * 设置角色的权限
     * @param role 角色
     * @param permissions 权限列表
     */
    UserSessionManager.prototype.setRolePermissions = function (role, permissions) {
        this.rolePermissions.set(role, new Set(permissions));
        // 更新所有具有该角色的用户的权限
        for (var _i = 0, _a = this.sessions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], userId = _b[0], session = _b[1];
            if (session.role === role) {
                session.permissions = new Set(permissions);
                // 触发权限更新事件
                this.emit('permissionUpdated', userId, session);
            }
        }
    };
    /**
     * 获取所有在线用户
     * @returns 在线用户列表
     */
    UserSessionManager.prototype.getOnlineUsers = function () {
        return Array.from(this.sessions.values()).filter(function (session) { return session.isOnline; });
    };
    /**
     * 获取所有用户
     * @returns 用户列表
     */
    UserSessionManager.prototype.getAllUsers = function () {
        return Array.from(this.sessions.values());
    };
    /**
     * 获取用户数量
     * @returns 用户数量
     */
    UserSessionManager.prototype.getUserCount = function () {
        return this.sessions.size;
    };
    /**
     * 获取在线用户数量
     * @returns 在线用户数量
     */
    UserSessionManager.prototype.getOnlineUserCount = function () {
        return this.getOnlineUsers().length;
    };
    /**
     * 清空所有会话
     */
    UserSessionManager.prototype.clearSessions = function () {
        this.sessions.clear();
        // 触发会话清空事件
        this.emit('sessionCleared');
    };
    /**
     * 销毁管理器
     */
    UserSessionManager.prototype.dispose = function () {
        this.stopSessionCleanup();
        this.clearSessions();
        this.removeAllListeners();
    };
    return UserSessionManager;
}(EventEmitter_1.EventEmitter));
exports.UserSessionManager = UserSessionManager;
