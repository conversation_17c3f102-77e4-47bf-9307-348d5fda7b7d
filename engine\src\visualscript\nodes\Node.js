"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Node = exports.NodeCategory = exports.NodeType = exports.SocketDirection = exports.SocketType = void 0;
/**
 * 视觉脚本节点基类
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 节点插槽类型
 */
var SocketType;
(function (SocketType) {
    /** 流程插槽 */
    SocketType["FLOW"] = "flow";
    /** 数据插槽 */
    SocketType["DATA"] = "data";
})(SocketType || (exports.SocketType = SocketType = {}));
/**
 * 节点插槽方向
 */
var SocketDirection;
(function (SocketDirection) {
    /** 输入插槽 */
    SocketDirection["INPUT"] = "input";
    /** 输出插槽 */
    SocketDirection["OUTPUT"] = "output";
})(SocketDirection || (exports.SocketDirection = SocketDirection = {}));
/**
 * 节点类型
 */
var NodeType;
(function (NodeType) {
    /** 普通节点 */
    NodeType["NORMAL"] = "normal";
    /** 事件节点 */
    NodeType["EVENT"] = "event";
    /** 函数节点 */
    NodeType["FUNCTION"] = "function";
    /** 异步节点 */
    NodeType["ASYNC"] = "async";
})(NodeType || (exports.NodeType = NodeType = {}));
/**
 * 节点类别
 */
var NodeCategory;
(function (NodeCategory) {
    /** 流程控制 */
    NodeCategory["FLOW"] = "flow";
    /** 数学运算 */
    NodeCategory["MATH"] = "math";
    /** 逻辑运算 */
    NodeCategory["LOGIC"] = "logic";
    /** 字符串操作 */
    NodeCategory["STRING"] = "string";
    /** 数组操作 */
    NodeCategory["ARRAY"] = "array";
    /** 对象操作 */
    NodeCategory["OBJECT"] = "object";
    /** 变量操作 */
    NodeCategory["VARIABLE"] = "variable";
    /** 函数操作 */
    NodeCategory["FUNCTION"] = "function";
    /** 事件操作 */
    NodeCategory["EVENT"] = "event";
    /** 实体操作 */
    NodeCategory["ENTITY"] = "entity";
    /** 组件操作 */
    NodeCategory["COMPONENT"] = "component";
    /** 物理操作 */
    NodeCategory["PHYSICS"] = "physics";
    /** 动画操作 */
    NodeCategory["ANIMATION"] = "animation";
    /** 输入操作 */
    NodeCategory["INPUT"] = "input";
    /** 音频操作 */
    NodeCategory["AUDIO"] = "audio";
    /** 网络操作 */
    NodeCategory["NETWORK"] = "network";
    /** AI操作 */
    NodeCategory["AI"] = "ai";
    /** 调试操作 */
    NodeCategory["DEBUG"] = "debug";
    /** 自定义操作 */
    NodeCategory["CUSTOM"] = "custom";
})(NodeCategory || (exports.NodeCategory = NodeCategory = {}));
/**
 * 节点基类
 */
var Node = /** @class */ (function (_super) {
    __extends(Node, _super);
    /**
     * 创建节点
     * @param options 节点选项
     */
    function Node(options) {
        var _this = _super.call(this) || this;
        /** 节点类型 */
        _this.nodeType = NodeType.NORMAL;
        /** 节点类别 */
        _this.category = NodeCategory.CUSTOM;
        /** 输入插槽 */
        _this.inputs = new Map();
        /** 输出插槽 */
        _this.outputs = new Map();
        /** 输入连接 */
        _this.inputConnections = new Map();
        /** 输出连接 */
        _this.outputConnections = new Map();
        _this.id = options.id;
        _this.type = options.type;
        _this.metadata = options.metadata || { positionX: 0, positionY: 0 };
        _this.graph = options.graph;
        _this.context = options.context;
        // 初始化插槽
        _this.initializeSockets();
        return _this;
    }
    /**
     * 初始化插槽
     */
    Node.prototype.initializeSockets = function () {
        // 子类实现
    };
    /**
     * 添加输入插槽
     * @param definition 插槽定义
     * @returns 添加的插槽
     */
    Node.prototype.addInput = function (definition) {
        var socket = __assign(__assign({}, definition), { direction: SocketDirection.INPUT, value: definition.defaultValue });
        this.inputs.set(socket.name, socket);
        return socket;
    };
    /**
     * 添加输出插槽
     * @param definition 插槽定义
     * @returns 添加的插槽
     */
    Node.prototype.addOutput = function (definition) {
        var socket = __assign(__assign({}, definition), { direction: SocketDirection.OUTPUT, value: definition.defaultValue });
        this.outputs.set(socket.name, socket);
        // 初始化输出连接数组
        this.outputConnections.set(socket.name, []);
        return socket;
    };
    /**
     * 获取输入插槽
     * @param name 插槽名称
     * @returns 插槽
     */
    Node.prototype.getInput = function (name) {
        return this.inputs.get(name);
    };
    /**
     * 获取输出插槽
     * @param name 插槽名称
     * @returns 插槽
     */
    Node.prototype.getOutput = function (name) {
        return this.outputs.get(name);
    };
    /**
     * 获取所有输入插槽
     * @returns 输入插槽映射
     */
    Node.prototype.getInputs = function () {
        return this.inputs;
    };
    /**
     * 获取所有输出插槽
     * @returns 输出插槽映射
     */
    Node.prototype.getOutputs = function () {
        return this.outputs;
    };
    /**
     * 设置参数值
     * @param name 参数名称
     * @param value 参数值
     */
    Node.prototype.setParameterValue = function (name, value) {
        var input = this.inputs.get(name);
        if (input) {
            input.value = value;
        }
    };
    /**
     * 获取参数值
     * @param name 参数名称
     * @returns 参数值
     */
    Node.prototype.getParameterValue = function (name) {
        var input = this.inputs.get(name);
        if (input) {
            return input.value;
        }
        return undefined;
    };
    /**
     * 连接输入
     * @param inputName 输入插槽名称
     * @param sourceNode 源节点
     * @param sourceOutputName 源输出插槽名称
     * @returns 是否连接成功
     */
    Node.prototype.connectInput = function (inputName, sourceNode, sourceOutputName) {
        var input = this.inputs.get(inputName);
        var sourceOutput = sourceNode.getOutput(sourceOutputName);
        if (!input || !sourceOutput) {
            return false;
        }
        // 检查类型兼容性
        if (input.type !== sourceOutput.type) {
            return false;
        }
        // 如果是数据插槽，检查数据类型兼容性
        if (input.type === SocketType.DATA &&
            input.dataType !== sourceOutput.dataType) {
            return false;
        }
        // 创建连接
        var connection = {
            sourceNode: sourceNode,
            sourceSocketName: sourceOutputName,
            targetNode: this,
            targetSocketName: inputName
        };
        // 存储连接
        this.inputConnections.set(inputName, connection);
        // 更新插槽连接信息
        input.connectedNodeId = sourceNode.id;
        input.connectedSocketName = sourceOutputName;
        // 添加到源节点的输出连接
        var sourceOutputConnections = sourceNode.outputConnections.get(sourceOutputName) || [];
        sourceOutputConnections.push(connection);
        sourceNode.outputConnections.set(sourceOutputName, sourceOutputConnections);
        // 触发连接事件
        this.emit('inputConnected', inputName, sourceNode, sourceOutputName);
        sourceNode.emit('outputConnected', sourceOutputName, this, inputName);
        return true;
    };
    /**
     * 连接流程
     * @param outputName 输出插槽名称
     * @param targetNode 目标节点
     * @param targetInputName 目标输入插槽名称
     * @returns 是否连接成功
     */
    Node.prototype.connectFlow = function (outputName, targetNode, targetInputName) {
        var output = this.outputs.get(outputName);
        var targetInput = targetNode.getInput(targetInputName);
        if (!output || !targetInput) {
            return false;
        }
        // 检查是否为流程插槽
        if (output.type !== SocketType.FLOW || targetInput.type !== SocketType.FLOW) {
            return false;
        }
        // 创建连接
        var connection = {
            sourceNode: this,
            sourceSocketName: outputName,
            targetNode: targetNode,
            targetSocketName: targetInputName
        };
        // 存储连接
        var outputConnections = this.outputConnections.get(outputName) || [];
        outputConnections.push(connection);
        this.outputConnections.set(outputName, outputConnections);
        // 更新目标插槽连接信息
        targetInput.connectedNodeId = this.id;
        targetInput.connectedSocketName = outputName;
        // 存储到目标节点的输入连接
        targetNode.inputConnections.set(targetInputName, connection);
        // 触发连接事件
        this.emit('outputConnected', outputName, targetNode, targetInputName);
        targetNode.emit('inputConnected', targetInputName, this, outputName);
        return true;
    };
    /**
     * 断开输入连接
     * @param inputName 输入插槽名称
     * @returns 是否断开成功
     */
    Node.prototype.disconnectInput = function (inputName) {
        var _this = this;
        var connection = this.inputConnections.get(inputName);
        if (!connection) {
            return false;
        }
        // 获取源节点和源插槽
        var sourceNode = connection.sourceNode;
        var sourceSocketName = connection.sourceSocketName;
        // 从源节点的输出连接中移除
        var sourceOutputConnections = sourceNode.outputConnections.get(sourceSocketName) || [];
        var index = sourceOutputConnections.findIndex(function (conn) {
            return conn.targetNode.id === _this.id && conn.targetSocketName === inputName;
        });
        if (index !== -1) {
            sourceOutputConnections.splice(index, 1);
            sourceNode.outputConnections.set(sourceSocketName, sourceOutputConnections);
        }
        // 更新插槽连接信息
        var input = this.inputs.get(inputName);
        if (input) {
            input.connectedNodeId = undefined;
            input.connectedSocketName = undefined;
        }
        // 移除连接
        this.inputConnections.delete(inputName);
        // 触发断开事件
        this.emit('inputDisconnected', inputName, sourceNode, sourceSocketName);
        sourceNode.emit('outputDisconnected', sourceSocketName, this, inputName);
        return true;
    };
    /**
     * 断开输出连接
     * @param outputName 输出插槽名称
     * @param targetNode 目标节点
     * @param targetInputName 目标输入插槽名称
     * @returns 是否断开成功
     */
    Node.prototype.disconnectOutput = function (outputName, targetNode, targetInputName) {
        var outputConnections = this.outputConnections.get(outputName) || [];
        // 查找连接
        var index = outputConnections.findIndex(function (conn) {
            return conn.targetNode.id === targetNode.id && conn.targetSocketName === targetInputName;
        });
        if (index === -1) {
            return false;
        }
        // 从输出连接中移除
        outputConnections.splice(index, 1);
        this.outputConnections.set(outputName, outputConnections);
        // 从目标节点的输入连接中移除
        targetNode.inputConnections.delete(targetInputName);
        // 更新目标插槽连接信息
        var targetInput = targetNode.getInput(targetInputName);
        if (targetInput) {
            targetInput.connectedNodeId = undefined;
            targetInput.connectedSocketName = undefined;
        }
        // 触发断开事件
        this.emit('outputDisconnected', outputName, targetNode, targetInputName);
        targetNode.emit('inputDisconnected', targetInputName, this, outputName);
        return true;
    };
    /**
     * 断开所有连接
     */
    Node.prototype.disconnectAll = function () {
        // 断开所有输入连接
        for (var _i = 0, _a = this.inputConnections.keys(); _i < _a.length; _i++) {
            var inputName = _a[_i];
            this.disconnectInput(inputName);
        }
        // 断开所有输出连接
        for (var _b = 0, _c = this.outputConnections.entries(); _b < _c.length; _b++) {
            var _d = _c[_b], outputName = _d[0], connections = _d[1];
            // 复制连接数组，因为在断开过程中会修改原数组
            var connectionsToDisconnect = __spreadArray([], connections, true);
            for (var _e = 0, connectionsToDisconnect_1 = connectionsToDisconnect; _e < connectionsToDisconnect_1.length; _e++) {
                var connection = connectionsToDisconnect_1[_e];
                this.disconnectOutput(outputName, connection.targetNode, connection.targetSocketName);
            }
        }
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    Node.prototype.execute = function () {
        // 子类实现
        return null;
    };
    /**
     * 获取输入值
     * @param name 输入名称
     * @returns 输入值
     */
    Node.prototype.getInputValue = function (name) {
        var input = this.inputs.get(name);
        if (!input) {
            return undefined;
        }
        // 如果有连接，从源节点获取值
        if (input.connectedNodeId && input.connectedSocketName) {
            var sourceNode = this.graph.getNode(input.connectedNodeId);
            if (sourceNode) {
                var sourceOutput = sourceNode.getOutput(input.connectedSocketName);
                if (sourceOutput) {
                    // 如果源节点是函数节点，需要执行它
                    if (sourceNode.nodeType === NodeType.FUNCTION) {
                        sourceNode.execute();
                    }
                    return sourceOutput.value;
                }
            }
        }
        // 否则返回当前值
        return input.value;
    };
    /**
     * 设置输出值
     * @param name 输出名称
     * @param value 输出值
     */
    Node.prototype.setOutputValue = function (name, value) {
        var output = this.outputs.get(name);
        if (output) {
            output.value = value;
        }
    };
    /**
     * 触发流程输出
     * @param name 输出名称
     */
    Node.prototype.triggerFlow = function (name) {
        var connections = this.outputConnections.get(name) || [];
        for (var _i = 0, connections_1 = connections; _i < connections_1.length; _i++) {
            var connection = connections_1[_i];
            // 创建纤程并添加到引擎
            var fiber = this.context.engine.createFiber(this, name);
            this.context.engine.addFiber(fiber);
        }
    };
    /**
     * 初始化节点
     */
    Node.prototype.initialize = function () {
        // 子类实现
    };
    /**
     * 销毁节点
     */
    Node.prototype.dispose = function () {
        // 断开所有连接
        this.disconnectAll();
        // 清空插槽
        this.inputs.clear();
        this.outputs.clear();
        // 清空连接
        this.inputConnections.clear();
        this.outputConnections.clear();
        // 移除所有事件监听
        this.removeAllListeners();
    };
    return Node;
}(EventEmitter_1.EventEmitter));
exports.Node = Node;
