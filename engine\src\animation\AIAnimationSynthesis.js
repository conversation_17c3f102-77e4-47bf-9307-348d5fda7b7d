"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAnimationSynthesisComponent = void 0;
var Component_1 = require("../core/Component");
var EventEmitter_1 = require("../utils/EventEmitter");
var AnimationClip_1 = require("./AnimationClip");
var FacialAnimation_1 = require("./FacialAnimation");
/**
 * AI动画合成组件
 */
var AIAnimationSynthesisComponent = exports.AIAnimationSynthesisComponent = /** @class */ (function (_super) {
    __extends(AIAnimationSynthesisComponent, _super);
    /**
     * 构造函数
     * @param entity 实体
     */
    function AIAnimationSynthesisComponent(entity) {
        var _this = _super.call(this, AIAnimationSynthesisComponent.type) || this;
        /** 请求队列 */
        _this.requestQueue = [];
        /** 结果缓存 */
        _this.resultCache = new Map();
        /** 是否正在处理 */
        _this.isProcessing = false;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 模型是否已加载 */
        _this.modelLoaded = false;
        /** AI模型 */
        _this.aiModel = null;
        _this.setEntity(entity);
        return _this;
    }
    /**
     * 请求生成动画
     * @param request 生成请求
     * @returns 请求ID
     */
    AIAnimationSynthesisComponent.prototype.requestAnimation = function (request) {
        // 生成请求ID
        var id = "req_".concat(Date.now(), "_").concat(Math.floor(Math.random() * 1000));
        // 创建完整请求
        var fullRequest = __assign({ id: id }, request);
        // 添加到队列
        this.requestQueue.push(fullRequest);
        // 触发事件
        this.eventEmitter.emit('requestAdded', { request: fullRequest });
        return id;
    };
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    AIAnimationSynthesisComponent.prototype.cancelRequest = function (id) {
        var initialLength = this.requestQueue.length;
        this.requestQueue = this.requestQueue.filter(function (req) { return req.id !== id; });
        var canceled = this.requestQueue.length < initialLength;
        if (canceled) {
            this.eventEmitter.emit('requestCanceled', { id: id });
        }
        return canceled;
    };
    /**
     * 获取请求结果
     * @param id 请求ID
     * @returns 生成结果，如果不存在则返回null
     */
    AIAnimationSynthesisComponent.prototype.getResult = function (id) {
        return this.resultCache.get(id) || null;
    };
    /**
     * 清除结果缓存
     * @param id 请求ID，如果不提供则清除所有缓存
     */
    AIAnimationSynthesisComponent.prototype.clearCache = function (id) {
        if (id) {
            this.resultCache.delete(id);
        }
        else {
            this.resultCache.clear();
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AIAnimationSynthesisComponent.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AIAnimationSynthesisComponent.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 处理请求队列
     */
    AIAnimationSynthesisComponent.prototype.processQueue = function () {
        return __awaiter(this, void 0, void 0, function () {
            var request, startTime, clip, _a, endTime, result, error_1, result;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isProcessing || this.requestQueue.length === 0)
                            return [2 /*return*/];
                        this.isProcessing = true;
                        request = this.requestQueue.shift();
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 9, , 10]);
                        startTime = Date.now();
                        clip = void 0;
                        _a = request.type;
                        switch (_a) {
                            case 'body': return [3 /*break*/, 2];
                            case 'facial': return [3 /*break*/, 4];
                            case 'combined': return [3 /*break*/, 6];
                        }
                        return [3 /*break*/, 8];
                    case 2: return [4 /*yield*/, this.generateBodyAnimation(request)];
                    case 3:
                        clip = _b.sent();
                        return [3 /*break*/, 8];
                    case 4: return [4 /*yield*/, this.generateFacialAnimation(request)];
                    case 5:
                        clip = _b.sent();
                        return [3 /*break*/, 8];
                    case 6: return [4 /*yield*/, this.generateCombinedAnimation(request)];
                    case 7:
                        clip = _b.sent();
                        return [3 /*break*/, 8];
                    case 8:
                        endTime = Date.now();
                        result = {
                            id: request.id,
                            success: !!clip,
                            clip: clip,
                            generationTime: endTime - startTime,
                            userData: request.userData
                        };
                        // 缓存结果
                        this.resultCache.set(request.id, result);
                        // 触发事件
                        this.eventEmitter.emit('generationComplete', { result: result });
                        return [3 /*break*/, 10];
                    case 9:
                        error_1 = _b.sent();
                        result = {
                            id: request.id,
                            success: false,
                            error: error_1 instanceof Error ? error_1.message : String(error_1),
                            userData: request.userData
                        };
                        // 缓存结果
                        this.resultCache.set(request.id, result);
                        // 触发事件
                        this.eventEmitter.emit('generationError', { result: result });
                        return [3 /*break*/, 10];
                    case 10:
                        this.isProcessing = false;
                        // 继续处理队列
                        if (this.requestQueue.length > 0) {
                            this.processQueue();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 设置AI模型
     * @param model AI模型
     */
    AIAnimationSynthesisComponent.prototype.setAIModel = function (model) {
        this.aiModel = model;
        this.modelLoaded = true;
    };
    /**
     * 生成身体动画
     * @param request 生成请求
     * @returns 生成的动画片段
     */
    AIAnimationSynthesisComponent.prototype.generateBodyAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var aiRequest, result, clip;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.aiModel) return [3 /*break*/, 2];
                        aiRequest = {
                            id: request.id,
                            prompt: request.prompt,
                            type: 'body',
                            duration: request.duration,
                            loop: request.loop,
                            style: request.style,
                            intensity: request.intensity,
                            seed: request.seed,
                            userData: request.userData
                        };
                        return [4 /*yield*/, this.aiModel.generateBodyAnimation(aiRequest)];
                    case 1:
                        result = _a.sent();
                        // 返回生成的动画片段
                        return [2 /*return*/, result.clip];
                    case 2:
                        clip = new AnimationClip_1.AnimationClip({
                            name: request.prompt,
                            duration: request.duration,
                            loopMode: request.loop ? AnimationClip_1.LoopMode.REPEAT : AnimationClip_1.LoopMode.NONE
                        });
                        // 模拟AI处理时间
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 500); })];
                    case 3:
                        // 模拟AI处理时间
                        _a.sent();
                        return [2 /*return*/, clip];
                }
            });
        });
    };
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成的面部动画片段
     */
    AIAnimationSynthesisComponent.prototype.generateFacialAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var aiRequest, result, clip, frameCount, i, time, expressionValues, visemeValues, expressionIndex, visemeIndex, keyframe;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.aiModel) return [3 /*break*/, 2];
                        aiRequest = {
                            id: request.id,
                            prompt: request.prompt,
                            type: 'facial',
                            duration: request.duration,
                            loop: request.loop,
                            style: request.style,
                            intensity: request.intensity,
                            seed: request.seed,
                            userData: request.userData
                        };
                        return [4 /*yield*/, this.aiModel.generateFacialAnimation(aiRequest)];
                    case 1:
                        result = _a.sent();
                        // 返回生成的动画片段
                        return [2 /*return*/, result.clip];
                    case 2:
                        clip = {
                            name: request.prompt,
                            duration: request.duration,
                            loop: request.loop,
                            keyframes: []
                        };
                        frameCount = Math.max(2, Math.floor(request.duration * 5));
                        for (i = 0; i < frameCount; i++) {
                            time = (i / (frameCount - 1)) * request.duration;
                            expressionValues = Object.values(FacialAnimation_1.FacialExpressionType);
                            visemeValues = Object.values(FacialAnimation_1.VisemeType);
                            expressionIndex = Math.floor(Math.random() * expressionValues.length);
                            visemeIndex = Math.floor(Math.random() * visemeValues.length);
                            keyframe = {
                                time: time,
                                expression: expressionValues[expressionIndex],
                                expressionWeight: Math.random(),
                                viseme: visemeValues[visemeIndex],
                                visemeWeight: Math.random()
                            };
                            clip.keyframes.push(keyframe);
                        }
                        // 模拟AI处理时间
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 500); })];
                    case 3:
                        // 模拟AI处理时间
                        _a.sent();
                        return [2 /*return*/, clip];
                }
            });
        });
    };
    /**
     * 生成组合动画
     * @param request 生成请求
     * @returns 生成的动画片段
     */
    AIAnimationSynthesisComponent.prototype.generateCombinedAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var aiRequest, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.aiModel) return [3 /*break*/, 2];
                        aiRequest = {
                            id: request.id,
                            prompt: request.prompt,
                            type: 'combined',
                            duration: request.duration,
                            loop: request.loop,
                            style: request.style,
                            intensity: request.intensity,
                            seed: request.seed,
                            userData: request.userData
                        };
                        return [4 /*yield*/, this.aiModel.generateCombinedAnimation(aiRequest)];
                    case 1:
                        result = _a.sent();
                        // 返回生成的动画片段
                        return [2 /*return*/, result.clip];
                    case 2: 
                    // 如果没有AI模型，使用面部动画生成
                    return [2 /*return*/, this.generateFacialAnimation(request)];
                }
            });
        });
    };
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    AIAnimationSynthesisComponent.prototype.update = function (_deltaTime) {
        // 如果有请求且未在处理，则开始处理
        if (this.requestQueue.length > 0 && !this.isProcessing && this.modelLoaded) {
            this.processQueue();
        }
    };
    /** 组件类型 */
    AIAnimationSynthesisComponent.type = 'AIAnimationSynthesis';
    return AIAnimationSynthesisComponent;
}(Component_1.Component));
