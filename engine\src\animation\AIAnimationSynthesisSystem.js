"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAnimationSynthesisSystem = void 0;
var System_1 = require("../core/System");
var EventEmitter_1 = require("../utils/EventEmitter");
var AIAnimationSynthesis_1 = require("./AIAnimationSynthesis");
var ai_1 = require("./ai");
/**
 * AI动画合成系统
 */
var AIAnimationSynthesisSystem = exports.AIAnimationSynthesisSystem = /** @class */ (function (_super) {
    __extends(AIAnimationSynthesisSystem, _super);
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    function AIAnimationSynthesisSystem(_world, config) {
        var _this = _super.call(this) || this;
        /** AI动画合成组件 */
        _this.components = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** AI模型 */
        _this.aiModel = null;
        /** 模型是否已加载 */
        _this.modelLoaded = false;
        /** 模型加载进度 */
        _this.modelLoadProgress = 0;
        _this.config = __assign(__assign({}, AIAnimationSynthesisSystem.DEFAULT_CONFIG), config);
        // 初始化模型
        _this.initModel();
        return _this;
    }
    /**
     * 初始化AI模型
     */
    AIAnimationSynthesisSystem.prototype.initModel = function () {
        return __awaiter(this, void 0, void 0, function () {
            var i, success, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.debug) {
                            console.log('正在初始化AI模型...');
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 7, , 8]);
                        // 创建AI模型
                        if (this.config.useLocalModel) {
                            this.aiModel = new ai_1.LocalAIAnimationModel({
                                debug: this.config.debug,
                                batchSize: this.config.batchSize
                            });
                        }
                        else {
                            // 使用远程模型或其他模型实现
                            this.aiModel = new ai_1.LocalAIAnimationModel({
                                debug: this.config.debug,
                                batchSize: this.config.batchSize
                            });
                        }
                        i = 0;
                        _a.label = 2;
                    case 2:
                        if (!(i <= 10)) return [3 /*break*/, 5];
                        this.modelLoadProgress = i / 10;
                        this.eventEmitter.emit('modelLoadProgress', { progress: this.modelLoadProgress });
                        if (!(i < 10)) return [3 /*break*/, 4];
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        i++;
                        return [3 /*break*/, 2];
                    case 5: return [4 /*yield*/, this.aiModel.initialize()];
                    case 6:
                        success = _a.sent();
                        if (success) {
                            this.modelLoaded = true;
                            this.eventEmitter.emit('modelLoaded', { success: true });
                            if (this.config.debug) {
                                console.log('AI模型初始化完成');
                            }
                        }
                        else {
                            throw new Error('模型初始化失败');
                        }
                        return [3 /*break*/, 8];
                    case 7:
                        error_1 = _a.sent();
                        this.modelLoaded = false;
                        this.aiModel = null;
                        this.eventEmitter.emit('modelLoaded', { success: false, error: error_1 });
                        if (this.config.debug) {
                            console.error('AI模型初始化失败:', error_1);
                        }
                        return [3 /*break*/, 8];
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 创建AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件
     */
    AIAnimationSynthesisSystem.prototype.createAIAnimationSynthesis = function (entity) {
        // 检查是否已存在组件
        if (this.components.has(entity)) {
            return this.components.get(entity);
        }
        // 创建新组件
        var component = new AIAnimationSynthesis_1.AIAnimationSynthesisComponent(entity);
        this.components.set(entity, component);
        // 如果AI模型已加载，设置AI模型
        if (this.modelLoaded && this.aiModel) {
            component.setAIModel(this.aiModel);
        }
        if (this.config.debug) {
            console.log("\u521B\u5EFAAI\u52A8\u753B\u5408\u6210\u7EC4\u4EF6: ".concat(entity.id));
        }
        return component;
    };
    /**
     * 移除AI动画合成组件
     * @param entity 实体
     */
    AIAnimationSynthesisSystem.prototype.removeAIAnimationSynthesis = function (entity) {
        if (this.components.has(entity)) {
            this.components.delete(entity);
            if (this.config.debug) {
                console.log("\u79FB\u9664AI\u52A8\u753B\u5408\u6210\u7EC4\u4EF6: ".concat(entity.id));
            }
        }
    };
    /**
     * 获取AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件，如果不存在则返回null
     */
    AIAnimationSynthesisSystem.prototype.getAIAnimationSynthesis = function (entity) {
        return this.components.get(entity) || null;
    };
    /**
     * 生成身体动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    AIAnimationSynthesisSystem.prototype.generateBodyAnimation = function (entity, prompt, duration, options) {
        var _a;
        if (duration === void 0) { duration = 5.0; }
        if (options === void 0) { options = {}; }
        var component = this.getAIAnimationSynthesis(entity);
        if (!component)
            return null;
        var request = {
            prompt: prompt,
            duration: duration,
            type: 'body',
            loop: (_a = options.loop) !== null && _a !== void 0 ? _a : false,
            referenceClip: options.referenceClip,
            style: options.style,
            intensity: options.intensity,
            seed: options.seed,
            userData: options.userData
        };
        return component.requestAnimation(request);
    };
    /**
     * 生成面部动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    AIAnimationSynthesisSystem.prototype.generateFacialAnimation = function (entity, prompt, duration, options) {
        var _a;
        if (duration === void 0) { duration = 5.0; }
        if (options === void 0) { options = {}; }
        var component = this.getAIAnimationSynthesis(entity);
        if (!component)
            return null;
        var request = {
            prompt: prompt,
            duration: duration,
            type: 'facial',
            loop: (_a = options.loop) !== null && _a !== void 0 ? _a : false,
            referenceClip: options.referenceClip,
            style: options.style,
            intensity: options.intensity,
            seed: options.seed,
            userData: options.userData
        };
        return component.requestAnimation(request);
    };
    /**
     * 生成组合动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    AIAnimationSynthesisSystem.prototype.generateCombinedAnimation = function (entity, prompt, duration, options) {
        var _a;
        if (duration === void 0) { duration = 5.0; }
        if (options === void 0) { options = {}; }
        var component = this.getAIAnimationSynthesis(entity);
        if (!component)
            return null;
        var request = {
            prompt: prompt,
            duration: duration,
            type: 'combined',
            loop: (_a = options.loop) !== null && _a !== void 0 ? _a : false,
            referenceClip: options.referenceClip,
            style: options.style,
            intensity: options.intensity,
            seed: options.seed,
            userData: options.userData
        };
        return component.requestAnimation(request);
    };
    /**
     * 取消生成请求
     * @param entity 实体
     * @param requestId 请求ID
     * @returns 是否成功取消
     */
    AIAnimationSynthesisSystem.prototype.cancelRequest = function (entity, requestId) {
        var component = this.getAIAnimationSynthesis(entity);
        if (!component)
            return false;
        return component.cancelRequest(requestId);
    };
    /**
     * 获取生成结果
     * @param entity 实体
     * @param requestId 请求ID
     * @returns 生成结果，如果不存在则返回null
     */
    AIAnimationSynthesisSystem.prototype.getResult = function (entity, requestId) {
        var component = this.getAIAnimationSynthesis(entity);
        if (!component)
            return null;
        return component.getResult(requestId);
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AIAnimationSynthesisSystem.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AIAnimationSynthesisSystem.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    AIAnimationSynthesisSystem.prototype.update = function (deltaTime) {
        // 更新所有AI动画合成组件
        for (var _i = 0, _a = this.components.values(); _i < _a.length; _i++) {
            var component = _a[_i];
            component.update(deltaTime);
        }
    };
    /** 系统类型 */
    AIAnimationSynthesisSystem.type = 'AIAnimationSynthesis';
    /** 默认配置 */
    AIAnimationSynthesisSystem.DEFAULT_CONFIG = {
        debug: false,
        useLocalModel: true,
        batchSize: 1,
        sampleRate: 30,
        maxContextLength: 1024
    };
    return AIAnimationSynthesisSystem;
}(System_1.System));
