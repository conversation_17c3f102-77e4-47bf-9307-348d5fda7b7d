"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftBodySystem = void 0;
/**
 * 软体物理系统
 * 基于粒子和约束实现软体物理模拟
 */
var THREE = require("three");
var System_1 = require("../../core/System");
var PhysicsSystem_1 = require("../PhysicsSystem");
var SpatialPartitioning_1 = require("./optimization/SpatialPartitioning");
var SoftBodyLOD_1 = require("./optimization/SoftBodyLOD");
var SoftRigidInteraction_1 = require("./interaction/SoftRigidInteraction");
var SoftBodyCutter_1 = require("./deformation/SoftBodyCutter");
/**
 * 软体物理系统
 * 管理所有软体物理组件
 */
var SoftBodySystem = /** @class */ (function (_super) {
    __extends(SoftBodySystem, _super);
    /**
     * 创建软体物理系统
     * @param options 软体物理系统选项
     */
    function SoftBodySystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 2) || this;
        /** 软体组件映射 */
        _this.softBodies = new Map();
        /** 是否显示调试信息 */
        _this.debug = false;
        /** 迭代次数 */
        _this.iterations = 10;
        /** 空间分区系统 */
        _this.spatialPartitioning = null;
        /** LOD系统 */
        _this.lodSystem = null;
        /** 软体与刚体交互系统 */
        _this.softRigidInteraction = null;
        /** 是否启用空间分区 */
        _this.useSpatialPartitioning = false;
        /** 是否启用LOD系统 */
        _this.useLOD = false;
        /** 是否启用软体与刚体交互 */
        _this.useSoftRigidInteraction = false;
        /** 软体切割系统 */
        _this.softBodyCutter = null;
        /** 是否启用软体切割 */
        _this.useSoftBodyCutter = false;
        _this.debug = options.debug || false;
        _this.iterations = options.iterations || 10;
        _this.useSpatialPartitioning = options.useSpatialPartitioning || false;
        _this.useLOD = options.useLOD || false;
        _this.useSoftRigidInteraction = options.useSoftRigidInteraction || false;
        _this.useSoftBodyCutter = options.useSoftBodyCutter || false;
        // 如果提供了物理系统，直接使用
        if (options.physicsSystem) {
            _this.physicsSystem = options.physicsSystem;
            _this.physicsWorld = _this.physicsSystem.getPhysicsWorld();
        }
        // 创建空间分区系统
        if (_this.useSpatialPartitioning) {
            _this.spatialPartitioning = new SpatialPartitioning_1.SpatialPartitioning();
        }
        // 创建LOD系统
        if (_this.useLOD) {
            _this.lodSystem = new SoftBodyLOD_1.SoftBodyLOD();
        }
        // 创建软体与刚体交互系统
        if (_this.useSoftRigidInteraction) {
            _this.softRigidInteraction = new SoftRigidInteraction_1.SoftRigidInteraction({
                useSpatialPartitioning: _this.useSpatialPartitioning
            });
        }
        // 创建软体切割系统
        if (_this.useSoftBodyCutter) {
            _this.softBodyCutter = new SoftBodyCutter_1.SoftBodyCutter();
        }
        return _this;
    }
    /**
     * 初始化系统
     */
    SoftBodySystem.prototype.initialize = function () {
        var _a;
        // 如果没有物理系统，尝试从引擎获取
        if (!this.physicsSystem) {
            this.physicsSystem = this.engine.getSystem(PhysicsSystem_1.PhysicsSystem);
            if (this.physicsSystem) {
                this.physicsWorld = this.physicsSystem.getPhysicsWorld();
            }
            else {
                console.error('SoftBodySystem 需要 PhysicsSystem 才能工作');
            }
        }
        // 创建调试渲染器
        if (this.debug) {
            this.debugRenderer = new THREE.Object3D();
            this.debugRenderer.name = 'SoftBodyDebugRenderer';
            (_a = this.engine.getScene()) === null || _a === void 0 ? void 0 : _a.add(this.debugRenderer);
        }
    };
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    SoftBodySystem.prototype.addSoftBody = function (softBody) {
        if (!softBody.entity)
            return;
        var entityId = softBody.entity.id;
        this.softBodies.set(entityId, softBody);
        // 初始化软体
        if (this.physicsWorld) {
            softBody.initialize(this.physicsWorld);
        }
        // 添加到LOD系统
        if (this.useLOD && this.lodSystem) {
            this.lodSystem.addSoftBody(softBody);
        }
        // 添加到软体与刚体交互系统
        if (this.useSoftRigidInteraction && this.softRigidInteraction) {
            this.softRigidInteraction.addSoftBody(softBody);
        }
    };
    /**
     * 移除软体组件
     * @param entityId 实体ID
     */
    SoftBodySystem.prototype.removeSoftBody = function (entityId) {
        var softBody = this.softBodies.get(entityId);
        if (softBody) {
            // 从LOD系统移除
            if (this.useLOD && this.lodSystem) {
                this.lodSystem.removeSoftBody(softBody);
            }
            // 从软体与刚体交互系统移除
            if (this.useSoftRigidInteraction && this.softRigidInteraction) {
                this.softRigidInteraction.removeSoftBody(softBody);
            }
            softBody.destroy();
            this.softBodies.delete(entityId);
        }
    };
    /**
     * 获取软体组件
     * @param entityId 实体ID
     * @returns 软体组件
     */
    SoftBodySystem.prototype.getSoftBody = function (entityId) {
        return this.softBodies.get(entityId);
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    SoftBodySystem.prototype.update = function (deltaTime) {
        // 更新LOD系统
        if (this.useLOD && this.lodSystem) {
            this.lodSystem.update();
        }
        // 更新所有软体
        for (var _i = 0, _a = this.softBodies.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], _entityId = _b[0], softBody = _b[1];
            if (softBody.isInitialized()) {
                // 获取当前软体的迭代次数（考虑LOD）
                var iterations = this.iterations;
                if (this.useLOD && this.lodSystem) {
                    var level = this.lodSystem.getLODLevel(softBody);
                    iterations = this.lodSystem.getIterationsForLevel(level);
                }
                // 执行内部约束求解迭代
                for (var i = 0; i < iterations; i++) {
                    softBody.solveConstraints(deltaTime / iterations);
                }
                // 更新网格
                softBody.updateMesh();
            }
        }
        // 更新空间分区
        if (this.useSpatialPartitioning && this.spatialPartitioning) {
            // 收集所有粒子
            var allParticles = [];
            for (var _c = 0, _d = this.softBodies.entries(); _c < _d.length; _c++) {
                var _e = _d[_c], _entityId = _e[0], softBody = _e[1];
                var particles = softBody.getParticles();
                if (particles) {
                    allParticles.push.apply(allParticles, particles);
                }
            }
            // 更新空间分区
            this.spatialPartitioning.updateAll(allParticles);
        }
        // 更新软体与刚体交互
        if (this.useSoftRigidInteraction && this.softRigidInteraction) {
            this.softRigidInteraction.update(deltaTime);
        }
        // 检查软体撕裂
        if (this.useSoftBodyCutter && this.softBodyCutter) {
            for (var _f = 0, _g = this.softBodies.entries(); _f < _g.length; _f++) {
                var _h = _g[_f], _entityId = _h[0], softBody = _h[1];
                this.softBodyCutter.checkTearing(softBody);
            }
        }
        // 更新调试渲染
        if (this.debug && this.debugRenderer) {
            this.updateDebugRenderer();
        }
    };
    /**
     * 更新调试渲染器
     */
    SoftBodySystem.prototype.updateDebugRenderer = function () {
        // 清除旧的调试对象
        while (this.debugRenderer.children.length > 0) {
            var child = this.debugRenderer.children[0];
            this.debugRenderer.remove(child);
            if (child instanceof THREE.Mesh) {
                child.geometry.dispose();
                if (Array.isArray(child.material)) {
                    child.material.forEach(function (material) { return material.dispose(); });
                }
                else {
                    child.material.dispose();
                }
            }
        }
        // 为每个软体创建调试可视化
        for (var _i = 0, _a = this.softBodies.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], _entityId = _b[0], softBody = _b[1];
            var debugMesh = softBody.createDebugMesh();
            if (debugMesh) {
                this.debugRenderer.add(debugMesh);
            }
        }
    };
    /**
     * 销毁系统
     */
    SoftBodySystem.prototype.destroy = function () {
        // 销毁所有软体
        for (var _i = 0, _a = this.softBodies.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], _softBody = _b[1];
            this.removeSoftBody(entityId);
        }
        // 清除调试渲染器
        if (this.debug && this.debugRenderer) {
            if (this.debugRenderer.parent) {
                this.debugRenderer.parent.remove(this.debugRenderer);
            }
            while (this.debugRenderer.children.length > 0) {
                var child = this.debugRenderer.children[0];
                this.debugRenderer.remove(child);
                if (child instanceof THREE.Mesh) {
                    child.geometry.dispose();
                    if (Array.isArray(child.material)) {
                        child.material.forEach(function (material) { return material.dispose(); });
                    }
                    else {
                        child.material.dispose();
                    }
                }
            }
        }
        // 清空空间分区
        if (this.spatialPartitioning) {
            this.spatialPartitioning.clear();
            this.spatialPartitioning = null;
        }
        // 清空LOD系统
        this.lodSystem = null;
        // 清空软体与刚体交互系统
        this.softRigidInteraction = null;
        // 清空软体切割系统
        this.softBodyCutter = null;
        this.softBodies.clear();
    };
    /**
     * 添加刚体到交互系统
     * @param rigidBody 刚体组件
     */
    SoftBodySystem.prototype.addRigidBody = function (rigidBody) {
        if (this.useSoftRigidInteraction && this.softRigidInteraction) {
            this.softRigidInteraction.addRigidBody(rigidBody);
        }
    };
    /**
     * 移除刚体从交互系统
     * @param rigidBody 刚体组件
     */
    SoftBodySystem.prototype.removeRigidBody = function (rigidBody) {
        if (this.useSoftRigidInteraction && this.softRigidInteraction) {
            this.softRigidInteraction.removeRigidBody(rigidBody);
        }
    };
    /**
     * 设置相机实体（用于LOD系统）
     * @param cameraEntity 相机实体
     */
    SoftBodySystem.prototype.setCameraEntity = function (cameraEntity) {
        if (this.useLOD && this.lodSystem) {
            this.lodSystem.setCameraEntity(cameraEntity);
        }
    };
    /**
     * 使用平面切割软体
     * @param entityId 实体ID
     * @param plane 切割平面
     * @returns 是否成功切割
     */
    SoftBodySystem.prototype.cutSoftBodyWithPlane = function (entityId, plane) {
        if (!this.useSoftBodyCutter || !this.softBodyCutter)
            return false;
        var softBody = this.softBodies.get(entityId);
        if (!softBody)
            return false;
        return this.softBodyCutter.cutWithPlane(softBody, plane);
    };
    /**
     * 使用射线切割软体
     * @param entityId 实体ID
     * @param ray 切割射线
     * @returns 是否成功切割
     */
    SoftBodySystem.prototype.cutSoftBodyWithRay = function (entityId, ray) {
        if (!this.useSoftBodyCutter || !this.softBodyCutter)
            return false;
        var softBody = this.softBodies.get(entityId);
        if (!softBody)
            return false;
        return this.softBodyCutter.cutWithRay(softBody, ray);
    };
    /**
     * 设置撕裂阈值
     * @param threshold 撕裂阈值
     */
    SoftBodySystem.prototype.setTearingThreshold = function (threshold) {
        if (this.softBodyCutter) {
            this.softBodyCutter.setTearingThreshold(threshold);
        }
    };
    /**
     * 启用/禁用撕裂
     * @param enabled 是否启用
     */
    SoftBodySystem.prototype.setTearingEnabled = function (enabled) {
        if (this.softBodyCutter) {
            if (enabled) {
                this.softBodyCutter.enableTearing();
            }
            else {
                this.softBodyCutter.disableTearing();
            }
        }
    };
    return SoftBodySystem;
}(System_1.System));
exports.SoftBodySystem = SoftBodySystem;
