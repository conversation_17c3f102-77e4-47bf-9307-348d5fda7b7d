"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabState = exports.GrabEventType = void 0;
/**
 * 抓取状态
 * 用于管理抓取系统的状态
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
var GrabbableComponent_1 = require("../components/GrabbableComponent");
/**
 * 抓取事件类型
 */
var GrabEventType;
(function (GrabEventType) {
    /** 抓取开始 */
    GrabEventType["GRAB_START"] = "grabStart";
    /** 抓取结束 */
    GrabEventType["GRAB_END"] = "grabEnd";
    /** 抓取更新 */
    GrabEventType["GRAB_UPDATE"] = "grabUpdate";
    /** 抓取状态变化 */
    GrabEventType["STATE_CHANGE"] = "stateChange";
})(GrabEventType || (exports.GrabEventType = GrabEventType = {}));
/**
 * 抓取状态
 */
var GrabState = /** @class */ (function () {
    /**
     * 私有构造函数
     */
    function GrabState() {
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 当前被抓取的实体映射 - 实体ID到抓取数据 */
        this.grabbedEntities = new Map();
        /** 当前抓取者映射 - 实体ID到被抓取实体 */
        this.grabbers = new Map();
        // 私有构造函数，防止直接实例化
    }
    /**
     * 获取单例实例
     */
    GrabState.getInstance = function () {
        if (!GrabState.instance) {
            GrabState.instance = new GrabState();
        }
        return GrabState.instance;
    };
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    GrabState.prototype.addEventListener = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    GrabState.prototype.removeEventListener = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 触发事件
     * @param event 事件类型
     * @param data 事件数据
     */
    GrabState.prototype.dispatchEvent = function (event, data) {
        this.eventEmitter.emit(event, data);
    };
    /**
     * 注册抓取
     * @param grabbed 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    GrabState.prototype.registerGrab = function (grabbed, grabber, hand) {
        // 创建抓取数据
        var grabData = {
            entity: grabbed,
            grabber: grabber,
            hand: hand,
            timestamp: Date.now()
        };
        // 添加到被抓取实体映射
        this.grabbedEntities.set(grabbed.id, grabData);
        // 更新抓取者映射
        var grabberData = this.grabbers.get(grabber.id);
        if (!grabberData) {
            grabberData = {
                entity: grabber
            };
            this.grabbers.set(grabber.id, grabberData);
        }
        if (hand === GrabbableComponent_1.Hand.LEFT) {
            grabberData.leftGrabbed = grabbed;
        }
        else if (hand === GrabbableComponent_1.Hand.RIGHT) {
            grabberData.rightGrabbed = grabbed;
        }
        // 触发事件
        this.dispatchEvent(GrabEventType.GRAB_START, {
            grabber: grabber,
            grabbed: grabbed,
            hand: hand,
            timestamp: grabData.timestamp
        });
        // 触发状态变化事件
        this.dispatchEvent(GrabEventType.STATE_CHANGE, {
            grabber: grabber,
            grabbed: grabbed,
            hand: hand,
            timestamp: grabData.timestamp,
            type: 'register'
        });
    };
    /**
     * 注销抓取
     * @param grabbed 被抓取实体
     */
    GrabState.prototype.unregisterGrab = function (grabbed) {
        // 获取抓取数据
        var grabData = this.grabbedEntities.get(grabbed.id);
        if (!grabData)
            return;
        // 从被抓取实体映射中移除
        this.grabbedEntities.delete(grabbed.id);
        // 更新抓取者映射
        var grabber = grabData.grabber;
        var hand = grabData.hand;
        var grabberData = this.grabbers.get(grabber.id);
        if (grabberData) {
            if (hand === GrabbableComponent_1.Hand.LEFT) {
                grabberData.leftGrabbed = undefined;
            }
            else if (hand === GrabbableComponent_1.Hand.RIGHT) {
                grabberData.rightGrabbed = undefined;
            }
            // 如果抓取者没有抓取任何实体，则从映射中移除
            if (!grabberData.leftGrabbed && !grabberData.rightGrabbed) {
                this.grabbers.delete(grabber.id);
            }
        }
        // 触发事件
        this.dispatchEvent(GrabEventType.GRAB_END, {
            grabber: grabber,
            grabbed: grabbed,
            hand: hand,
            timestamp: Date.now(),
            grabDuration: Date.now() - grabData.timestamp
        });
        // 触发状态变化事件
        this.dispatchEvent(GrabEventType.STATE_CHANGE, {
            grabber: grabber,
            grabbed: grabbed,
            hand: hand,
            timestamp: Date.now(),
            type: 'unregister'
        });
    };
    /**
     * 更新抓取
     * @param grabbed 被抓取实体
     * @param data 更新数据
     */
    GrabState.prototype.updateGrab = function (grabbed, data) {
        if (data === void 0) { data = {}; }
        // 获取抓取数据
        var grabData = this.grabbedEntities.get(grabbed.id);
        if (!grabData)
            return;
        // 触发更新事件
        this.dispatchEvent(GrabEventType.GRAB_UPDATE, __assign({ grabber: grabData.grabber, grabbed: grabbed, hand: grabData.hand, timestamp: Date.now(), grabDuration: Date.now() - grabData.timestamp }, data));
    };
    /**
     * 检查实体是否被抓取
     * @param entity 实体
     * @returns 是否被抓取
     */
    GrabState.prototype.isGrabbed = function (entity) {
        return this.grabbedEntities.has(entity.id);
    };
    /**
     * 获取抓取者
     * @param grabbed 被抓取实体
     * @returns 抓取者
     */
    GrabState.prototype.getGrabber = function (grabbed) {
        var grabData = this.grabbedEntities.get(grabbed.id);
        return grabData ? grabData.grabber : undefined;
    };
    /**
     * 获取抓取手
     * @param grabbed 被抓取实体
     * @returns 抓取手
     */
    GrabState.prototype.getGrabHand = function (grabbed) {
        var grabData = this.grabbedEntities.get(grabbed.id);
        return grabData ? grabData.hand : undefined;
    };
    /**
     * 获取被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 被抓取实体
     */
    GrabState.prototype.getGrabbedEntity = function (grabber, hand) {
        var grabberData = this.grabbers.get(grabber.id);
        if (!grabberData)
            return undefined;
        if (hand === GrabbableComponent_1.Hand.LEFT) {
            return grabberData.leftGrabbed;
        }
        else if (hand === GrabbableComponent_1.Hand.RIGHT) {
            return grabberData.rightGrabbed;
        }
        return undefined;
    };
    /**
     * 获取所有被抓取的实体
     * @returns 被抓取的实体数组
     */
    GrabState.prototype.getAllGrabbedEntities = function () {
        return Array.from(this.grabbedEntities.values()).map(function (data) { return data.entity; });
    };
    /**
     * 获取所有抓取者
     * @returns 抓取者数组
     */
    GrabState.prototype.getAllGrabbers = function () {
        return Array.from(this.grabbers.values()).map(function (data) { return data.entity; });
    };
    /**
     * 清除所有抓取状态
     */
    GrabState.prototype.clear = function () {
        // 保存当前状态的副本，用于触发事件
        var currentGrabs = Array.from(this.grabbedEntities.values());
        // 清除映射
        this.grabbedEntities.clear();
        this.grabbers.clear();
        // 触发事件
        for (var _i = 0, currentGrabs_1 = currentGrabs; _i < currentGrabs_1.length; _i++) {
            var grabData = currentGrabs_1[_i];
            this.dispatchEvent(GrabEventType.GRAB_END, {
                grabber: grabData.grabber,
                grabbed: grabData.entity,
                hand: grabData.hand,
                timestamp: Date.now(),
                grabDuration: Date.now() - grabData.timestamp,
                reason: 'clear'
            });
        }
        // 触发状态变化事件
        this.dispatchEvent(GrabEventType.STATE_CHANGE, {
            grabber: undefined,
            grabbed: undefined,
            hand: undefined,
            timestamp: Date.now(),
            type: 'clear'
        });
    };
    return GrabState;
}());
exports.GrabState = GrabState;
