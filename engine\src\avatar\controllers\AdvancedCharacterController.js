"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedCharacterController = exports.CharacterState = exports.CharacterMovementMode = void 0;
/**
 * 高级角色控制器
 * 提供更丰富的角色控制功能，包括高级动作控制、环境感知和响应
 */
var THREE = require("three");
var EventEmitter_1 = require("../../utils/EventEmitter");
var PhysicsSystem_1 = require("../../physics/PhysicsSystem");
var AnimationSystem_1 = require("../../animation/AnimationSystem");
var InputSystem_1 = require("../../input/InputSystem");
var Debug_1 = require("../../utils/Debug");
/**
 * 角色移动模式
 */
var CharacterMovementMode;
(function (CharacterMovementMode) {
    /** 行走模式 */
    CharacterMovementMode["WALKING"] = "walking";
    /** 跑步模式 */
    CharacterMovementMode["RUNNING"] = "running";
    /** 潜行模式 */
    CharacterMovementMode["CROUCHING"] = "crouching";
    /** 爬行模式 */
    CharacterMovementMode["CRAWLING"] = "crawling";
    /** 游泳模式 */
    CharacterMovementMode["SWIMMING"] = "swimming";
    /** 攀爬模式 */
    CharacterMovementMode["CLIMBING"] = "climbing";
    /** 飞行模式 */
    CharacterMovementMode["FLYING"] = "flying";
})(CharacterMovementMode || (exports.CharacterMovementMode = CharacterMovementMode = {}));
/**
 * 角色状态
 */
var CharacterState;
(function (CharacterState) {
    /** 空闲 */
    CharacterState["IDLE"] = "idle";
    /** 行走 */
    CharacterState["WALK"] = "walk";
    /** 跑步 */
    CharacterState["RUN"] = "run";
    /** 跳跃 */
    CharacterState["JUMP"] = "jump";
    /** 下落 */
    CharacterState["FALL"] = "fall";
    /** 着陆 */
    CharacterState["LAND"] = "land";
    /** 潜行 */
    CharacterState["CROUCH"] = "crouch";
    /** 爬行 */
    CharacterState["CRAWL"] = "crawl";
    /** 游泳 */
    CharacterState["SWIM"] = "swim";
    /** 攀爬 */
    CharacterState["CLIMB"] = "climb";
    /** 飞行 */
    CharacterState["FLY"] = "fly";
    /** 受伤 */
    CharacterState["HURT"] = "hurt";
    /** 死亡 */
    CharacterState["DEATH"] = "death";
    /** 交互 */
    CharacterState["INTERACT"] = "interact";
    /** 攻击 */
    CharacterState["ATTACK"] = "attack";
    /** 防御 */
    CharacterState["DEFEND"] = "defend";
    /** 使用物品 */
    CharacterState["USE_ITEM"] = "use_item";
})(CharacterState || (exports.CharacterState = CharacterState = {}));
/**
 * 高级角色控制器
 */
var AdvancedCharacterController = exports.AdvancedCharacterController = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 配置
     */
    function AdvancedCharacterController(config) {
        if (config === void 0) { config = {}; }
        /** 关联的实体 */
        this.entity = null;
        /** 世界 */
        this.world = null;
        /** 物理系统 */
        this.physicsSystem = null;
        /** 动画系统 */
        this.animationSystem = null;
        /** 输入系统 */
        this.inputSystem = null;
        /** 物理控制器 */
        this.physicsController = null;
        /** 动画状态机 */
        this.stateMachine = null;
        /** 当前移动模式 */
        this.movementMode = CharacterMovementMode.WALKING;
        /** 当前状态 */
        this.currentState = CharacterState.IDLE;
        /** 移动方向 */
        this.moveDirection = new THREE.Vector3();
        /** 速度 */
        this.velocity = new THREE.Vector3();
        /** 是否在地面上 */
        this.isGrounded = true;
        /** 是否正在跳跃 */
        this.isJumping = false;
        /** 是否正在跑步 */
        this.isRunning = false;
        /** 是否正在潜行 */
        this.isCrouching = false;
        /** 是否正在爬行 */
        this.isCrawling = false;
        /** 是否正在游泳 */
        this.isSwimming = false;
        /** 是否正在攀爬 */
        this.isClimbing = false;
        /** 是否正在飞行 */
        this.isFlying = false;
        /** 是否正在交互 */
        this.isInteracting = false;
        /** 是否正在攻击 */
        this.isAttacking = false;
        /** 是否正在防御 */
        this.isDefending = false;
        /** 是否正在使用物品 */
        this.isUsingItem = false;
        /** 是否已初始化 */
        this.initialized = false;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        this.config = __assign(__assign({}, AdvancedCharacterController.DEFAULT_CONFIG), config);
    }
    /**
     * 初始化控制器
     * @param entity 实体
     * @param world 世界
     */
    AdvancedCharacterController.prototype.initialize = function (entity, world) {
        if (this.initialized)
            return;
        this.entity = entity;
        this.world = world;
        // 获取系统
        this.physicsSystem = world.getSystem(PhysicsSystem_1.PhysicsSystem);
        this.animationSystem = world.getSystem(AnimationSystem_1.AnimationSystem);
        this.inputSystem = world.getSystem(InputSystem_1.InputSystem);
        // 初始化物理控制器
        if (this.config.usePhysics && this.physicsSystem) {
            this.initPhysicsController();
        }
        // 初始化动画状态机
        if (this.config.useStateMachine && this.animationSystem) {
            this.initAnimationStateMachine();
        }
        // 初始化输入处理
        if (this.inputSystem) {
            this.initInputHandling();
        }
        this.initialized = true;
        if (this.config.debug) {
            Debug_1.Debug.log('高级角色控制器初始化完成', this);
        }
    };
    /**
     * 更新控制器
     * @param deltaTime 时间增量（秒）
     */
    AdvancedCharacterController.prototype.update = function (deltaTime) {
        if (!this.initialized || !this.entity)
            return;
        // 更新物理
        this.updatePhysics(deltaTime);
        // 更新动画
        this.updateAnimation(deltaTime);
        // 更新环境感知
        if (this.config.useEnvironmentAwareness) {
            this.updateEnvironmentAwareness(deltaTime);
        }
        // 发出更新事件
        this.eventEmitter.emit('update', { deltaTime: deltaTime });
    };
    /**
     * 初始化物理控制器
     */
    AdvancedCharacterController.prototype.initPhysicsController = function () {
        // 实现物理控制器初始化
    };
    /**
     * 初始化动画状态机
     */
    AdvancedCharacterController.prototype.initAnimationStateMachine = function () {
        // 实现动画状态机初始化
    };
    /**
     * 初始化输入处理
     */
    AdvancedCharacterController.prototype.initInputHandling = function () {
        // 实现输入处理初始化
    };
    /**
     * 更新物理
     * @param deltaTime 时间增量（秒）
     */
    AdvancedCharacterController.prototype.updatePhysics = function (deltaTime) {
        // 实现物理更新
        // deltaTime 用于物理计算和状态更新
        if (this.physicsController && this.entity) {
            // 这里可以添加物理更新逻辑
        }
    };
    /**
     * 更新动画
     * @param deltaTime 时间增量（秒）
     */
    AdvancedCharacterController.prototype.updateAnimation = function (deltaTime) {
        // 实现动画更新
        // deltaTime 用于动画状态机更新
        if (this.stateMachine && this.animationSystem && deltaTime > 0) {
            // 这里可以添加动画更新逻辑
            // 例如：this.stateMachine.update(deltaTime);
        }
    };
    /**
     * 更新环境感知
     * @param deltaTime 时间增量（秒）
     */
    AdvancedCharacterController.prototype.updateEnvironmentAwareness = function (deltaTime) {
        // 实现环境感知更新
        // deltaTime 用于时间相关的环境感知逻辑
        if (deltaTime > 0 && this.config.useEnvironmentAwareness) {
            // 这里可以添加环境感知更新逻辑
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AdvancedCharacterController.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    AdvancedCharacterController.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 获取当前移动模式
     */
    AdvancedCharacterController.prototype.getMovementMode = function () {
        return this.movementMode;
    };
    /**
     * 设置移动模式
     * @param mode 移动模式
     */
    AdvancedCharacterController.prototype.setMovementMode = function (mode) {
        this.movementMode = mode;
    };
    /**
     * 获取当前状态
     */
    AdvancedCharacterController.prototype.getCurrentState = function () {
        return this.currentState;
    };
    /**
     * 设置当前状态
     * @param state 状态
     */
    AdvancedCharacterController.prototype.setCurrentState = function (state) {
        this.currentState = state;
    };
    /**
     * 获取移动方向
     */
    AdvancedCharacterController.prototype.getMoveDirection = function () {
        return this.moveDirection.clone();
    };
    /**
     * 设置移动方向
     * @param direction 移动方向
     */
    AdvancedCharacterController.prototype.setMoveDirection = function (direction) {
        this.moveDirection.copy(direction);
    };
    /**
     * 获取速度
     */
    AdvancedCharacterController.prototype.getVelocity = function () {
        return this.velocity.clone();
    };
    /**
     * 设置速度
     * @param velocity 速度
     */
    AdvancedCharacterController.prototype.setVelocity = function (velocity) {
        this.velocity.copy(velocity);
    };
    /**
     * 是否在地面上
     */
    AdvancedCharacterController.prototype.getIsGrounded = function () {
        return this.isGrounded;
    };
    /**
     * 设置是否在地面上
     * @param grounded 是否在地面上
     */
    AdvancedCharacterController.prototype.setIsGrounded = function (grounded) {
        this.isGrounded = grounded;
    };
    /** 默认配置 */
    AdvancedCharacterController.DEFAULT_CONFIG = {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        crouchSpeed: 1.0,
        crawlSpeed: 0.5,
        swimSpeed: 1.5,
        climbSpeed: 1.0,
        flySpeed: 8.0,
        jumpForce: 5.0,
        gravity: 9.8,
        turnSpeed: 2.0,
        airControl: 0.3,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true,
        debug: false
    };
    return AdvancedCharacterController;
}());
