"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BandwidthController = exports.DataPriority = exports.BandwidthControlStrategy = void 0;
/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
/**
 * 带宽控制策略
 */
var BandwidthControlStrategy;
(function (BandwidthControlStrategy) {
    /** 固定带宽 */
    BandwidthControlStrategy["FIXED"] = "fixed";
    /** 自适应带宽 */
    BandwidthControlStrategy["ADAPTIVE"] = "adaptive";
    /** 质量优先 */
    BandwidthControlStrategy["QUALITY_FIRST"] = "quality_first";
    /** 性能优先 */
    BandwidthControlStrategy["PERFORMANCE_FIRST"] = "performance_first";
})(BandwidthControlStrategy || (exports.BandwidthControlStrategy = BandwidthControlStrategy = {}));
/**
 * 数据优先级
 */
var DataPriority;
(function (DataPriority) {
    /** 最高优先级 */
    DataPriority[DataPriority["HIGHEST"] = 0] = "HIGHEST";
    /** 高优先级 */
    DataPriority[DataPriority["HIGH"] = 1] = "HIGH";
    /** 中等优先级 */
    DataPriority[DataPriority["MEDIUM"] = 2] = "MEDIUM";
    /** 低优先级 */
    DataPriority[DataPriority["LOW"] = 3] = "LOW";
    /** 最低优先级 */
    DataPriority[DataPriority["LOWEST"] = 4] = "LOWEST";
})(DataPriority || (exports.DataPriority = DataPriority = {}));
/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
var BandwidthController = /** @class */ (function (_super) {
    __extends(BandwidthController, _super);
    /**
     * 创建带宽控制器
     * @param config 配置
     */
    function BandwidthController(config) {
        var _a;
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 当前上行带宽使用（字节/秒） */
        _this.currentUploadUsage = 0;
        /** 当前下行带宽使用（字节/秒） */
        _this.currentDownloadUsage = 0;
        /** 上行数据计数（当前周期） */
        _this.uploadByteCount = 0;
        /** 下行数据计数（当前周期） */
        _this.downloadByteCount = 0;
        /** 上次计数重置时间 */
        _this.lastResetTime = Date.now();
        /** 调整定时器ID */
        _this.adjustTimerId = null;
        /** 优先级队列（按优先级存储待发送数据） */
        _this.priorityQueues = new Map();
        /** 优先级带宽分配 */
        _this.priorityBandwidthAllocation = new Map();
        /** 最近的网络质量数据 */
        _this.latestNetworkQuality = null;
        // 默认配置
        _this.config = __assign({ maxUploadBandwidth: 1024 * 1024, maxDownloadBandwidth: 1024 * 1024, strategy: BandwidthControlStrategy.ADAPTIVE, targetUsage: 0.8, priorityAllocation: (_a = {},
                _a[DataPriority.HIGHEST] = 0.4,
                _a[DataPriority.HIGH] = 0.3,
                _a[DataPriority.MEDIUM] = 0.2,
                _a[DataPriority.LOW] = 0.07,
                _a[DataPriority.LOWEST] = 0.03,
                _a), autoAdjust: true, adjustInterval: 1000 }, config);
        // 初始化带宽限制
        _this.currentUploadLimit = _this.config.maxUploadBandwidth;
        _this.currentDownloadLimit = _this.config.maxDownloadBandwidth;
        // 初始化优先级队列
        _this.initPriorityQueues();
        // 初始化优先级带宽分配
        _this.updatePriorityBandwidthAllocation();
        // 如果启用自动调整，则启动调整定时器
        if (_this.config.autoAdjust) {
            _this.startAutoAdjust();
        }
        return _this;
    }
    /**
     * 初始化优先级队列
     */
    BandwidthController.prototype.initPriorityQueues = function () {
        this.priorityQueues.set(DataPriority.HIGHEST, []);
        this.priorityQueues.set(DataPriority.HIGH, []);
        this.priorityQueues.set(DataPriority.MEDIUM, []);
        this.priorityQueues.set(DataPriority.LOW, []);
        this.priorityQueues.set(DataPriority.LOWEST, []);
    };
    /**
     * 更新优先级带宽分配
     */
    BandwidthController.prototype.updatePriorityBandwidthAllocation = function () {
        var priorityAllocation = this.config.priorityAllocation;
        // 计算总分配比例
        var totalAllocation = Object.values(priorityAllocation).reduce(function (sum, value) { return sum + value; }, 0);
        // 如果总分配比例不为1，则进行归一化
        var normalizationFactor = totalAllocation !== 0 ? 1 / totalAllocation : 1;
        // 更新每个优先级的带宽分配
        for (var _i = 0, _a = Object.values(DataPriority); _i < _a.length; _i++) {
            var priority = _a[_i];
            if (typeof priority === 'number') {
                var allocation = priorityAllocation[priority] * normalizationFactor;
                this.priorityBandwidthAllocation.set(priority, allocation);
            }
        }
    };
    /**
     * 启动自动调整
     */
    BandwidthController.prototype.startAutoAdjust = function () {
        var _this = this;
        if (this.adjustTimerId !== null) {
            return;
        }
        this.adjustTimerId = window.setInterval(function () {
            _this.adjustBandwidth();
            _this.resetCounters();
        }, this.config.adjustInterval);
    };
    /**
     * 停止自动调整
     */
    BandwidthController.prototype.stopAutoAdjust = function () {
        if (this.adjustTimerId !== null) {
            clearInterval(this.adjustTimerId);
            this.adjustTimerId = null;
        }
    };
    /**
     * 调整带宽
     */
    BandwidthController.prototype.adjustBandwidth = function () {
        // 根据策略调整带宽
        switch (this.config.strategy) {
            case BandwidthControlStrategy.FIXED:
                // 固定带宽，不进行调整
                break;
            case BandwidthControlStrategy.ADAPTIVE:
                this.adjustAdaptive();
                break;
            case BandwidthControlStrategy.QUALITY_FIRST:
                this.adjustQualityFirst();
                break;
            case BandwidthControlStrategy.PERFORMANCE_FIRST:
                this.adjustPerformanceFirst();
                break;
        }
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
        // 触发带宽调整事件
        this.emit('bandwidthAdjusted', this.getBandwidthUsage());
    };
    /**
     * 自适应调整带宽
     */
    BandwidthController.prototype.adjustAdaptive = function () {
        var now = Date.now();
        var elapsed = (now - this.lastResetTime) / 1000; // 转换为秒
        if (elapsed > 0) {
            // 计算当前带宽使用
            this.currentUploadUsage = this.uploadByteCount / elapsed;
            this.currentDownloadUsage = this.downloadByteCount / elapsed;
            // 计算使用率
            var uploadUsageRatio = this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0;
            var downloadUsageRatio = this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0;
            // 根据使用率调整带宽限制
            if (uploadUsageRatio > this.config.targetUsage * 1.1) {
                // 使用率过高，降低限制
                this.currentUploadLimit = Math.max(this.currentUploadLimit * 0.9, this.currentUploadUsage);
            }
            else if (uploadUsageRatio < this.config.targetUsage * 0.8) {
                // 使用率过低，提高限制
                this.currentUploadLimit = Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth);
            }
            if (downloadUsageRatio > this.config.targetUsage * 1.1) {
                // 使用率过高，降低限制
                this.currentDownloadLimit = Math.max(this.currentDownloadLimit * 0.9, this.currentDownloadUsage);
            }
            else if (downloadUsageRatio < this.config.targetUsage * 0.8) {
                // 使用率过低，提高限制
                this.currentDownloadLimit = Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth);
            }
        }
    };
    /**
     * 质量优先调整带宽
     */
    BandwidthController.prototype.adjustQualityFirst = function () {
        // 如果没有网络质量数据，则使用自适应调整
        if (!this.latestNetworkQuality) {
            this.adjustAdaptive();
            return;
        }
        // 根据网络质量调整带宽
        switch (this.latestNetworkQuality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，使用最大带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，使用90%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.9;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.9;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，使用70%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.7;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.7;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，使用50%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.5;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.5;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，使用30%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.3;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.3;
                break;
            default:
                // 未知质量，使用自适应调整
                this.adjustAdaptive();
                break;
        }
    };
    /**
     * 性能优先调整带宽
     */
    BandwidthController.prototype.adjustPerformanceFirst = function () {
        // 如果没有网络质量数据，则使用自适应调整
        if (!this.latestNetworkQuality) {
            this.adjustAdaptive();
            return;
        }
        // 根据网络质量调整带宽，但更加激进地减少带宽使用
        switch (this.latestNetworkQuality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，使用80%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.8;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.8;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，使用60%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.6;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.6;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，使用40%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.4;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.4;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，使用20%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.2;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.2;
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，使用10%带宽
                this.currentUploadLimit = this.config.maxUploadBandwidth * 0.1;
                this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.1;
                break;
            default:
                // 未知质量，使用自适应调整
                this.adjustAdaptive();
                break;
        }
    };
    /**
     * 重置计数器
     */
    BandwidthController.prototype.resetCounters = function () {
        this.uploadByteCount = 0;
        this.downloadByteCount = 0;
        this.lastResetTime = Date.now();
    };
    /**
     * 记录上行数据
     * @param bytes 字节数
     */
    BandwidthController.prototype.recordUpload = function (bytes) {
        this.uploadByteCount += bytes;
    };
    /**
     * 记录下行数据
     * @param bytes 字节数
     */
    BandwidthController.prototype.recordDownload = function (bytes) {
        this.downloadByteCount += bytes;
    };
    /**
     * 获取带宽使用数据
     * @returns 带宽使用数据
     */
    BandwidthController.prototype.getBandwidthUsage = function () {
        return {
            upload: this.currentUploadUsage,
            download: this.currentDownloadUsage,
            uploadLimit: this.currentUploadLimit,
            downloadLimit: this.currentDownloadLimit,
            uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
            downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
            timestamp: Date.now(),
        };
    };
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    BandwidthController.prototype.setNetworkQuality = function (quality) {
        this.latestNetworkQuality = quality;
    };
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    BandwidthController.prototype.setStrategy = function (strategy) {
        this.config.strategy = strategy;
    };
    /**
     * 获取带宽控制策略
     * @returns 带宽控制策略
     */
    BandwidthController.prototype.getStrategy = function () {
        return this.config.strategy;
    };
    /**
     * 设置最大上行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    BandwidthController.prototype.setMaxUploadBandwidth = function (bandwidth) {
        this.config.maxUploadBandwidth = bandwidth;
        // 确保当前限制不超过最大值
        if (this.currentUploadLimit > bandwidth) {
            this.currentUploadLimit = bandwidth;
        }
    };
    /**
     * 设置最大下行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    BandwidthController.prototype.setMaxDownloadBandwidth = function (bandwidth) {
        this.config.maxDownloadBandwidth = bandwidth;
        // 确保当前限制不超过最大值
        if (this.currentDownloadLimit > bandwidth) {
            this.currentDownloadLimit = bandwidth;
        }
    };
    /**
     * 销毁控制器
     */
    BandwidthController.prototype.dispose = function () {
        this.stopAutoAdjust();
        this.removeAllListeners();
    };
    return BandwidthController;
}(EventEmitter_1.EventEmitter));
exports.BandwidthController = BandwidthController;
