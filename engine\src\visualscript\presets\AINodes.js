"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAINodes = exports.GenerateFacialAnimationNode = exports.GenerateBodyAnimationNode = void 0;
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var AIAnimationSynthesisSystem_1 = require("../../animation/AIAnimationSynthesisSystem");
// 导入其他AI节点模块
var AIModelNodes_1 = require("./AIModelNodes");
var AIEmotionNodes_1 = require("./AIEmotionNodes");
var AINLPNodes_1 = require("./AINLPNodes");
/**
 * 生成身体动画节点
 * 使用AI生成身体动画
 */
var GenerateBodyAnimationNode = /** @class */ (function (_super) {
    __extends(GenerateBodyAnimationNode, _super);
    function GenerateBodyAnimationNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 请求ID */
        _this.requestId = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    GenerateBodyAnimationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标实体'
        });
        this.addInput({
            name: 'prompt',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '提示文本',
            defaultValue: '走路'
        });
        this.addInput({
            name: 'duration',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '持续时间（秒）',
            defaultValue: 5.0
        });
        this.addInput({
            name: 'loop',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否循环',
            defaultValue: false
        });
        this.addInput({
            name: 'style',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '动画风格',
            defaultValue: 'natural',
            optional: true
        });
        this.addInput({
            name: 'intensity',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '动画强度',
            defaultValue: 1.0,
            optional: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'animationClip',
            type: Node_1.SocketType.DATA,
            dataType: 'AnimationClip',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成的动画片段'
        });
        this.addOutput({
            name: 'generationTime',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成时间（毫秒）'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    GenerateBodyAnimationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var entity, prompt, duration, loop, style, intensity, aiAnimationSystem, component, result, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        entity = this.getInputValue('entity');
                        prompt = this.getInputValue('prompt');
                        duration = this.getInputValue('duration');
                        loop = this.getInputValue('loop');
                        style = this.getInputValue('style');
                        intensity = this.getInputValue('intensity');
                        // 检查输入值是否有效
                        if (!entity || !prompt) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        aiAnimationSystem = this.graph.getWorld().getSystem(AIAnimationSynthesisSystem_1.AIAnimationSynthesisSystem);
                        if (!aiAnimationSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 生成身体动画
                        this.requestId = aiAnimationSystem.generateBodyAnimation(entity, prompt, duration, {
                            loop: loop,
                            style: style,
                            intensity: intensity
                        });
                        if (!this.requestId) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        component = aiAnimationSystem.getAIAnimationSynthesis(entity);
                        if (!component) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, component.waitForResult(this.requestId)];
                    case 2:
                        result = _a.sent();
                        if (result && result.success && result.clip) {
                            // 设置输出值
                            this.setOutputValue('animationClip', result.clip);
                            this.setOutputValue('generationTime', result.generationTime);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 取消执行
     */
    GenerateBodyAnimationNode.prototype.cancel = function () {
        if (this.requestId) {
            // 获取AI动画合成系统
            var aiAnimationSystem = this.graph.getWorld().getSystem(AIAnimationSynthesisSystem_1.AIAnimationSynthesisSystem);
            if (aiAnimationSystem) {
                // 取消请求
                var component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity'));
                if (component) {
                    component.cancelRequest(this.requestId);
                }
            }
            this.requestId = null;
        }
    };
    return GenerateBodyAnimationNode;
}(AsyncNode_1.AsyncNode));
exports.GenerateBodyAnimationNode = GenerateBodyAnimationNode;
/**
 * 生成面部动画节点
 * 使用AI生成面部动画
 */
var GenerateFacialAnimationNode = /** @class */ (function (_super) {
    __extends(GenerateFacialAnimationNode, _super);
    function GenerateFacialAnimationNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 请求ID */
        _this.requestId = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    GenerateFacialAnimationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标实体'
        });
        this.addInput({
            name: 'prompt',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '提示文本',
            defaultValue: '你好，世界！'
        });
        this.addInput({
            name: 'duration',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '持续时间（秒）',
            defaultValue: 3.0
        });
        this.addInput({
            name: 'loop',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否循环',
            defaultValue: false
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'facialAnimationClip',
            type: Node_1.SocketType.DATA,
            dataType: 'FacialAnimationClip',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成的面部动画片段'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    GenerateFacialAnimationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var entity, prompt, duration, loop, aiAnimationSystem, component, result, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        entity = this.getInputValue('entity');
                        prompt = this.getInputValue('prompt');
                        duration = this.getInputValue('duration');
                        loop = this.getInputValue('loop');
                        // 检查输入值是否有效
                        if (!entity || !prompt) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        aiAnimationSystem = this.graph.getWorld().getSystem(AIAnimationSynthesisSystem_1.AIAnimationSynthesisSystem);
                        if (!aiAnimationSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 生成面部动画
                        this.requestId = aiAnimationSystem.generateFacialAnimation(entity, prompt, duration, {
                            loop: loop
                        });
                        if (!this.requestId) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        component = aiAnimationSystem.getAIAnimationSynthesis(entity);
                        if (!component) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, component.waitForResult(this.requestId)];
                    case 2:
                        result = _a.sent();
                        if (result && result.success && result.clip) {
                            // 设置输出值
                            this.setOutputValue('facialAnimationClip', result.clip);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 取消执行
     */
    GenerateFacialAnimationNode.prototype.cancel = function () {
        if (this.requestId) {
            // 获取AI动画合成系统
            var aiAnimationSystem = this.graph.getWorld().getSystem(AIAnimationSynthesisSystem_1.AIAnimationSynthesisSystem);
            if (aiAnimationSystem) {
                // 取消请求
                var component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity'));
                if (component) {
                    component.cancelRequest(this.requestId);
                }
            }
            this.requestId = null;
        }
    };
    return GenerateFacialAnimationNode;
}(AsyncNode_1.AsyncNode));
exports.GenerateFacialAnimationNode = GenerateFacialAnimationNode;
/**
 * 注册AI节点
 * @param registry 节点注册表
 */
function registerAINodes(registry) {
    // 注册生成身体动画节点
    registry.registerNodeType({
        type: 'ai/animation/generateBodyAnimation',
        category: Node_1.NodeCategory.AI,
        constructor: GenerateBodyAnimationNode,
        label: '生成身体动画',
        description: '使用AI生成身体动画',
        icon: 'body',
        color: '#673AB7',
        tags: ['ai', 'animation', 'body']
    });
    // 注册生成面部动画节点
    registry.registerNodeType({
        type: 'ai/animation/generateFacialAnimation',
        category: Node_1.NodeCategory.AI,
        constructor: GenerateFacialAnimationNode,
        label: '生成面部动画',
        description: '使用AI生成面部动画',
        icon: 'face',
        color: '#673AB7',
        tags: ['ai', 'animation', 'facial']
    });
    // 注册AI模型节点
    (0, AIModelNodes_1.registerAIModelNodes)(registry);
    // 注册AI情感节点
    (0, AIEmotionNodes_1.registerAIEmotionNodes)(registry);
    // 注册AI自然语言处理节点
    (0, AINLPNodes_1.registerAINLPNodes)(registry);
    console.log('已注册所有AI节点类型');
}
exports.registerAINodes = registerAINodes;
