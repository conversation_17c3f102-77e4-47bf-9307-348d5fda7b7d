"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionControlSystem = exports.ActionPriority = exports.ActionType = void 0;
var System_1 = require("../../core/System");
var EventEmitter_1 = require("../../utils/EventEmitter");
var Debug_1 = require("../../utils/Debug");
var AnimationSystem_1 = require("../../animation/AnimationSystem");
var PhysicsSystem_1 = require("../../physics/PhysicsSystem");
var recording_1 = require("../recording");
/**
 * 动作类型
 */
var ActionType;
(function (ActionType) {
    /** 基础动作 */
    ActionType["BASIC"] = "basic";
    /** 组合动作 */
    ActionType["COMBO"] = "combo";
    /** 环境交互动作 */
    ActionType["ENVIRONMENT"] = "environment";
    /** 物品使用动作 */
    ActionType["ITEM"] = "item";
    /** 战斗动作 */
    ActionType["COMBAT"] = "combat";
    /** 社交动作 */
    ActionType["SOCIAL"] = "social";
    /** 情感动作 */
    ActionType["EMOTE"] = "emote";
    /** 自定义动作 */
    ActionType["CUSTOM"] = "custom";
    /** 物理交互动作 */
    ActionType["PHYSICS_INTERACTION"] = "physics_interaction";
    /** 环境响应动作 */
    ActionType["ENVIRONMENT_RESPONSE"] = "environment_response";
    /** 姿势动作 */
    ActionType["POSE"] = "pose";
    /** 手势动作 */
    ActionType["GESTURE"] = "gesture";
    /** 面部表情动作 */
    ActionType["FACIAL"] = "facial";
    /** 对话动作 */
    ActionType["DIALOGUE"] = "dialogue";
    /** 移动动作 */
    ActionType["LOCOMOTION"] = "locomotion";
})(ActionType || (exports.ActionType = ActionType = {}));
/**
 * 动作优先级
 */
var ActionPriority;
(function (ActionPriority) {
    /** 低优先级 */
    ActionPriority[ActionPriority["LOW"] = 0] = "LOW";
    /** 中优先级 */
    ActionPriority[ActionPriority["MEDIUM"] = 1] = "MEDIUM";
    /** 高优先级 */
    ActionPriority[ActionPriority["HIGH"] = 2] = "HIGH";
    /** 最高优先级 */
    ActionPriority[ActionPriority["CRITICAL"] = 3] = "CRITICAL";
})(ActionPriority || (exports.ActionPriority = ActionPriority = {}));
/**
 * 动作控制系统
 */
var ActionControlSystem = exports.ActionControlSystem = /** @class */ (function (_super) {
    __extends(ActionControlSystem, _super);
    /**
     * 构造函数
     * @param config 配置
     */
    function ActionControlSystem(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, 100) || this;
        /** 动画系统 */
        _this.animationSystem = null;
        /** 物理系统 */
        _this.physicsSystem = null;
        /** 动作库 */
        _this.actionLibrary = new Map();
        /** 活动动作 */
        _this.activeActions = new Map();
        /** 动作队列 */
        _this.actionQueues = new Map();
        /** 角色控制器映射 */
        _this.characterControllers = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 当前时间 */
        _this.currentTime = 0;
        /** 动作录制器映射 */
        _this.actionRecorders = new Map();
        /** 动作回放器映射 */
        _this.actionPlaybacks = new Map();
        /** 录制的动作 */
        _this.recordings = new Map();
        _this.config = __assign(__assign({}, ActionControlSystem.DEFAULT_CONFIG), config);
        if (_this.config.debug) {
            Debug_1.Debug.log('动作控制系统初始化', _this);
        }
        return _this;
    }
    /**
     * 初始化系统
     */
    ActionControlSystem.prototype.initialize = function () {
        _super.prototype.initialize.call(this);
        // 获取系统
        if (this.world) {
            this.animationSystem = this.world.getSystem(AnimationSystem_1.AnimationSystem);
            this.physicsSystem = this.world.getSystem(PhysicsSystem_1.PhysicsSystem);
        }
    };
    /**
     * 注册角色控制器
     * @param entity 实体
     * @param controller 控制器
     */
    ActionControlSystem.prototype.registerController = function (entity, controller) {
        this.characterControllers.set(entity, controller);
        // 初始化实体的动作队列
        if (this.config.enableActionQueue && !this.actionQueues.has(entity)) {
            this.actionQueues.set(entity, []);
        }
        // 初始化实体的活动动作
        if (!this.activeActions.has(entity)) {
            this.activeActions.set(entity, []);
        }
        if (this.config.debug) {
            Debug_1.Debug.log("\u6CE8\u518C\u89D2\u8272\u63A7\u5236\u5668: ".concat(entity.id), controller);
        }
    };
    /**
     * 注册动作
     * @param action 动作数据
     */
    ActionControlSystem.prototype.registerAction = function (action) {
        this.actionLibrary.set(action.id, action);
        if (this.config.debug) {
            Debug_1.Debug.log("\u6CE8\u518C\u52A8\u4F5C: ".concat(action.id), action);
        }
    };
    /**
     * 播放动作
     * @param entity 实体
     * @param actionId 动作ID
     * @param params 动作参数
     * @returns 是否成功播放
     */
    ActionControlSystem.prototype.playAction = function (entity, actionId, params) {
        // 获取动作数据
        var actionData = this.actionLibrary.get(actionId);
        if (!actionData) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u52A8\u4F5C\u4E0D\u5B58\u5728: ".concat(actionId));
            }
            return false;
        }
        // 合并参数
        var mergedParams = __assign(__assign({}, actionData.params), params);
        var actionWithParams = __assign(__assign({}, actionData), { params: mergedParams });
        // 检查是否有活动动作
        var activeActions = this.activeActions.get(entity) || [];
        // 如果启用动作队列，且有不可中断的高优先级动作正在播放，则将动作加入队列
        if (this.config.enableActionQueue) {
            var hasHigherPriorityAction = activeActions.some(function (action) { return action.isPlaying &&
                action.data.priority > actionWithParams.priority &&
                !action.data.interruptible; });
            if (hasHigherPriorityAction) {
                var queue = this.actionQueues.get(entity) || [];
                queue.push(actionWithParams);
                this.actionQueues.set(entity, queue);
                if (this.config.debug) {
                    Debug_1.Debug.log("\u52A8\u4F5C\u52A0\u5165\u961F\u5217: ".concat(actionId), { entity: entity, action: actionWithParams });
                }
                return true;
            }
        }
        // 停止可中断的低优先级动作
        for (var _i = 0, activeActions_1 = activeActions; _i < activeActions_1.length; _i++) {
            var activeAction = activeActions_1[_i];
            if (activeAction.isPlaying &&
                activeAction.data.priority <= actionWithParams.priority &&
                activeAction.data.interruptible) {
                this.stopAction(entity, activeAction.data.id);
            }
        }
        // 创建动作实例
        var actionInstance = {
            data: actionWithParams,
            entity: entity,
            startTime: this.currentTime,
            endTime: actionWithParams.duration > 0 ? this.currentTime + actionWithParams.duration : Infinity,
            isPlaying: true,
            isCompleted: false,
            triggeredEvents: new Set()
        };
        // 添加到活动动作
        activeActions.push(actionInstance);
        this.activeActions.set(entity, activeActions);
        // 播放动画
        if (this.animationSystem) {
            // 这里应该调用动画系统播放动画
        }
        // 发出动作开始事件
        this.eventEmitter.emit('actionStart', { entity: entity, action: actionWithParams });
        if (this.config.debug) {
            Debug_1.Debug.log("\u64AD\u653E\u52A8\u4F5C: ".concat(actionId), { entity: entity, action: actionWithParams });
        }
        return true;
    };
    /**
     * 停止动作
     * @param entity 实体
     * @param actionId 动作ID
     * @returns 是否成功停止
     */
    ActionControlSystem.prototype.stopAction = function (entity, actionId) {
        // 实现停止动作的逻辑
        return true;
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    ActionControlSystem.prototype.update = function (deltaTime) {
        // 更新当前时间
        this.currentTime += deltaTime;
        // 更新所有实体的动作
        for (var _i = 0, _a = this.activeActions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], actions = _b[1];
            this.updateEntityActions(entity, actions, deltaTime);
        }
        // 处理动作队列
        if (this.config.enableActionQueue) {
            this.processActionQueues();
        }
    };
    /**
     * 更新实体动作
     * @param entity 实体
     * @param actions 动作实例数组
     * @param deltaTime 时间增量（秒）
     */
    ActionControlSystem.prototype.updateEntityActions = function (entity, actions, deltaTime) {
        // 实现更新实体动作的逻辑
    };
    /**
     * 处理动作队列
     */
    ActionControlSystem.prototype.processActionQueues = function () {
        // 实现处理动作队列的逻辑
    };
    /**
     * 创建动作录制器
     * @param entity 实体
     * @param config 录制器配置
     * @returns 动作录制器
     */
    ActionControlSystem.prototype.createActionRecorder = function (entity, config) {
        if (this.actionRecorders.has(entity)) {
            return this.actionRecorders.get(entity);
        }
        var recorder = new recording_1.ActionRecorder(entity, this, config);
        this.actionRecorders.set(entity, recorder);
        if (this.config.debug) {
            Debug_1.Debug.log("\u521B\u5EFA\u52A8\u4F5C\u5F55\u5236\u5668: ".concat(entity.id));
        }
        return recorder;
    };
    /**
     * 创建动作回放器
     * @param entity 实体
     * @param config 回放器配置
     * @returns 动作回放器
     */
    ActionControlSystem.prototype.createActionPlayback = function (entity, config) {
        if (this.actionPlaybacks.has(entity)) {
            return this.actionPlaybacks.get(entity);
        }
        var playback = new recording_1.ActionPlayback(entity, this, config);
        this.actionPlaybacks.set(entity, playback);
        if (this.config.debug) {
            Debug_1.Debug.log("\u521B\u5EFA\u52A8\u4F5C\u56DE\u653E\u5668: ".concat(entity.id));
        }
        return playback;
    };
    /**
     * 开始录制动作
     * @param entity 实体
     * @param name 录制名称
     * @returns 是否成功开始录制
     */
    ActionControlSystem.prototype.startRecording = function (entity, name) {
        if (!this.config.enableActionRecording) {
            if (this.config.debug) {
                Debug_1.Debug.warn('动作录制功能未启用');
            }
            return false;
        }
        var recorder = this.actionRecorders.get(entity);
        if (!recorder) {
            recorder = this.createActionRecorder(entity);
        }
        return recorder.startRecording(name);
    };
    /**
     * 停止录制动作
     * @param entity 实体
     * @returns 录制数据
     */
    ActionControlSystem.prototype.stopRecording = function (entity) {
        var recorder = this.actionRecorders.get(entity);
        if (!recorder) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u5B9E\u4F53\u6CA1\u6709\u52A8\u4F5C\u5F55\u5236\u5668: ".concat(entity.id));
            }
            return null;
        }
        var recording = recorder.stopRecording();
        if (recording) {
            this.recordings.set(recording.id, recording);
        }
        return recording;
    };
    /**
     * 播放录制的动作
     * @param entity 实体
     * @param recordingId 录制ID
     * @param speed 播放速度
     * @returns 是否成功开始播放
     */
    ActionControlSystem.prototype.playRecording = function (entity, recordingId, speed) {
        if (speed === void 0) { speed = 1.0; }
        if (!this.config.enableActionPlayback) {
            if (this.config.debug) {
                Debug_1.Debug.warn('动作回放功能未启用');
            }
            return false;
        }
        var recording = this.recordings.get(recordingId);
        if (!recording) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u5F55\u5236\u4E0D\u5B58\u5728: ".concat(recordingId));
            }
            return false;
        }
        var playback = this.actionPlaybacks.get(entity);
        if (!playback) {
            playback = this.createActionPlayback(entity);
        }
        // 设置播放速度
        playback.setPlaybackSpeed(speed);
        // 加载录制
        playback.loadRecording(recording);
        // 开始播放
        return playback.startPlayback();
    };
    /**
     * 暂停播放
     * @param entity 实体
     * @returns 是否成功暂停
     */
    ActionControlSystem.prototype.pausePlayback = function (entity) {
        var playback = this.actionPlaybacks.get(entity);
        if (!playback) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u5B9E\u4F53\u6CA1\u6709\u52A8\u4F5C\u56DE\u653E\u5668: ".concat(entity.id));
            }
            return false;
        }
        return playback.pausePlayback();
    };
    /**
     * 继续播放
     * @param entity 实体
     * @returns 是否成功继续
     */
    ActionControlSystem.prototype.resumePlayback = function (entity) {
        var playback = this.actionPlaybacks.get(entity);
        if (!playback) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u5B9E\u4F53\u6CA1\u6709\u52A8\u4F5C\u56DE\u653E\u5668: ".concat(entity.id));
            }
            return false;
        }
        return playback.startPlayback();
    };
    /**
     * 停止播放
     * @param entity 实体
     * @returns 是否成功停止
     */
    ActionControlSystem.prototype.stopPlayback = function (entity) {
        var playback = this.actionPlaybacks.get(entity);
        if (!playback) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u5B9E\u4F53\u6CA1\u6709\u52A8\u4F5C\u56DE\u653E\u5668: ".concat(entity.id));
            }
            return false;
        }
        return playback.stopPlayback();
    };
    /**
     * 获取录制
     * @param recordingId 录制ID
     * @returns 录制数据
     */
    ActionControlSystem.prototype.getRecording = function (recordingId) {
        return this.recordings.get(recordingId);
    };
    /**
     * 获取所有录制
     * @returns 录制数据数组
     */
    ActionControlSystem.prototype.getAllRecordings = function () {
        return Array.from(this.recordings.values());
    };
    /**
     * 添加录制
     * @param recording 录制数据
     */
    ActionControlSystem.prototype.addRecording = function (recording) {
        this.recordings.set(recording.id, recording);
        if (this.config.debug) {
            Debug_1.Debug.log("\u6DFB\u52A0\u5F55\u5236: ".concat(recording.id));
        }
    };
    /**
     * 删除录制
     * @param recordingId 录制ID
     * @returns 是否成功删除
     */
    ActionControlSystem.prototype.removeRecording = function (recordingId) {
        var result = this.recordings.delete(recordingId);
        if (result && this.config.debug) {
            Debug_1.Debug.log("\u5220\u9664\u5F55\u5236: ".concat(recordingId));
        }
        return result;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionControlSystem.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionControlSystem.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
        return this;
    };
    /** 系统类型 */
    ActionControlSystem.type = 'ActionControlSystem';
    /** 默认配置 */
    ActionControlSystem.DEFAULT_CONFIG = {
        debug: false,
        maxConcurrentActions: 5,
        enableActionBlending: true,
        enableActionQueue: true,
        enableActionEvents: true,
        enablePhysicsDrivenActions: true,
        enableActionRecording: true,
        enableActionPlayback: true
    };
    return ActionControlSystem;
}(System_1.System));
