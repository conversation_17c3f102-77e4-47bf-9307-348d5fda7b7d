"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticleEmitter = exports.EmitterShapeType = void 0;
/**
 * 粒子发射器
 * 用于控制粒子的生成和行为
 */
var THREE = require("three");
var Component_1 = require("../core/Component");
/**
 * 粒子发射器形状类型
 */
var EmitterShapeType;
(function (EmitterShapeType) {
    /** 点发射器 */
    EmitterShapeType["POINT"] = "point";
    /** 球形发射器 */
    EmitterShapeType["SPHERE"] = "sphere";
    /** 圆形发射器 */
    EmitterShapeType["CIRCLE"] = "circle";
    /** 矩形发射器 */
    EmitterShapeType["RECTANGLE"] = "rectangle";
    /** 圆锥发射器 */
    EmitterShapeType["CONE"] = "cone";
    /** 圆环发射器 */
    EmitterShapeType["TORUS"] = "torus";
    /** 网格发射器 */
    EmitterShapeType["MESH"] = "mesh";
})(EmitterShapeType || (exports.EmitterShapeType = EmitterShapeType = {}));
/**
 * 粒子发射器组件
 */
var ParticleEmitter = exports.ParticleEmitter = /** @class */ (function (_super) {
    __extends(ParticleEmitter, _super);
    /**
     * 创建粒子发射器
     * @param options 发射器选项
     */
    function ParticleEmitter(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, ParticleEmitter.type) || this;
        /** 粒子纹理 */
        _this.texture = null;
        /** 粒子材质 */
        _this.material = null;
        /** 粒子大小范围 */
        _this.particleSize = [1, 1];
        /** 粒子颜色范围 */
        _this.particleColor = [
            new THREE.Color().setRGB(1, 1, 1),
            new THREE.Color().setRGB(1, 1, 1)
        ];
        /** 粒子透明度范围 */
        _this.particleOpacity = [1, 0];
        /** 粒子生命周期范围（秒） */
        _this.particleLifetime = [1, 1];
        /** 粒子速度范围 */
        _this.particleVelocity = [1, 1];
        /** 粒子加速度 */
        _this.particleAcceleration = new THREE.Vector3();
        /** 粒子旋转速度范围 */
        _this.particleRotationSpeed = [0, 0];
        /** 粒子缩放速度范围 */
        _this.particleScaleSpeed = [
            new THREE.Vector2(0, 0),
            new THREE.Vector2(0, 0)
        ];
        /** 粒子发射率（每秒） */
        _this.emissionRate = 10;
        /** 粒子发射角度范围 */
        _this.emissionAngle = [0, Math.PI * 2];
        /** 粒子发射力度范围 */
        _this.emissionForce = [1, 1];
        /** 粒子发射方向 */
        _this.emissionDirection = new THREE.Vector3(0, 1, 0);
        /** 粒子发射扩散度 */
        _this.emissionSpread = 0;
        /** 粒子重力 */
        _this.gravity = new THREE.Vector3(0, -9.8, 0);
        /** 粒子阻力 */
        _this.drag = 0;
        /** 是否启用碰撞 */
        _this.enableCollision = false;
        /** 是否启用粒子排序 */
        _this.enableSorting = false;
        /** 是否自动开始发射 */
        _this.autoStart = true;
        /** 发射持续时间（秒），0表示无限 */
        _this.duration = 0;
        /** 最大粒子数量 */
        _this.maxParticles = 1000;
        /** 是否循环发射 */
        _this.loop = false;
        /** 爆发模式参数 */
        _this.burst = null;
        /** 是否活跃 */
        _this.active = false;
        /** 已发射时间 */
        _this.elapsedTime = 0;
        /** 上次发射时间 */
        _this.lastEmitTime = 0;
        /** 活跃粒子列表 */
        _this.particles = [];
        /** 粒子系统引用 */
        _this.particleSystem = null;
        /** 所属实体 */
        _this.entity = null;
        /** 世界变换矩阵 */
        _this.worldMatrix = new THREE.Matrix4();
        /** 临时四元数 */
        _this.tempQuaternion = new THREE.Quaternion();
        _this.name = options.name || '粒子发射器';
        _this.shapeType = options.shapeType || EmitterShapeType.POINT;
        _this.shapeParams = options.shapeParams || {};
        // 设置纹理
        if (options.texture) {
            if (typeof options.texture === 'string') {
                // 加载纹理
                _this.texture = new THREE.TextureLoader().load(options.texture);
            }
            else {
                _this.texture = options.texture;
            }
        }
        // 设置材质
        _this.material = options.material || null;
        // 设置粒子属性
        _this.setParticleSize(options.particleSize || 1);
        _this.setParticleColor(options.particleColor || new THREE.Color(1, 1, 1));
        _this.setParticleOpacity(options.particleOpacity || [1, 0]);
        _this.setParticleLifetime(options.particleLifetime || 1);
        _this.setParticleVelocity(options.particleVelocity || 1);
        if (options.particleAcceleration) {
            _this.particleAcceleration.copy(options.particleAcceleration);
        }
        _this.setParticleRotationSpeed(options.particleRotationSpeed || 0);
        _this.setParticleScaleSpeed(options.particleScaleSpeed || new THREE.Vector2(0, 0));
        // 设置发射器属性
        _this.emissionRate = options.emissionRate || 10;
        _this.setEmissionAngle(options.emissionAngle || [0, Math.PI * 2]);
        _this.setEmissionForce(options.emissionForce || 1);
        if (options.emissionDirection) {
            _this.emissionDirection.copy(options.emissionDirection).normalize();
        }
        _this.emissionSpread = options.emissionSpread || 0;
        if (options.gravity) {
            _this.gravity.copy(options.gravity);
        }
        _this.drag = options.drag || 0;
        _this.enableCollision = options.enableCollision || false;
        _this.enableSorting = options.enableSorting || false;
        _this.autoStart = options.autoStart !== undefined ? options.autoStart : true;
        _this.duration = options.duration || 0;
        _this.maxParticles = options.maxParticles || 1000;
        _this.loop = options.loop || false;
        // 设置爆发模式
        if (options.burst) {
            _this.burst = {
                count: options.burst.count,
                interval: options.burst.interval,
                cycles: options.burst.cycles,
                currentCycle: 0,
                lastTime: 0
            };
        }
        return _this;
    }
    /**
     * 初始化发射器
     * @param particleSystem 粒子系统
     */
    ParticleEmitter.prototype.initialize = function (particleSystem) {
        this.particleSystem = particleSystem;
        // 如果设置了自动开始，则启动发射器
        if (this.autoStart) {
            this.start();
        }
    };
    /**
     * 设置所属实体
     * @param entity 实体
     */
    ParticleEmitter.prototype.setEntity = function (entity) {
        this.entity = entity;
    };
    /**
     * 获取所属实体
     * @returns 实体
     */
    ParticleEmitter.prototype.getEntity = function () {
        return this.entity;
    };
    /**
     * 设置粒子大小
     * @param size 粒子大小或范围
     */
    ParticleEmitter.prototype.setParticleSize = function (size) {
        if (typeof size === 'number') {
            this.particleSize = [size, size];
        }
        else {
            this.particleSize = size;
        }
    };
    /**
     * 设置粒子颜色
     * @param color 粒子颜色或范围
     */
    ParticleEmitter.prototype.setParticleColor = function (color) {
        if (color instanceof THREE.Color) {
            this.particleColor = [color.clone(), color.clone()];
        }
        else {
            this.particleColor = [color[0].clone(), color[1].clone()];
        }
    };
    /**
     * 设置粒子透明度
     * @param opacity 粒子透明度或范围
     */
    ParticleEmitter.prototype.setParticleOpacity = function (opacity) {
        if (typeof opacity === 'number') {
            this.particleOpacity = [opacity, opacity];
        }
        else {
            this.particleOpacity = opacity;
        }
    };
    /**
     * 设置粒子生命周期
     * @param lifetime 粒子生命周期或范围（秒）
     */
    ParticleEmitter.prototype.setParticleLifetime = function (lifetime) {
        if (typeof lifetime === 'number') {
            this.particleLifetime = [lifetime, lifetime];
        }
        else {
            this.particleLifetime = lifetime;
        }
    };
    /**
     * 设置粒子速度
     * @param velocity 粒子速度或范围
     */
    ParticleEmitter.prototype.setParticleVelocity = function (velocity) {
        if (typeof velocity === 'number') {
            this.particleVelocity = [velocity, velocity];
        }
        else {
            this.particleVelocity = velocity;
        }
    };
    /**
     * 设置粒子旋转速度
     * @param rotationSpeed 粒子旋转速度或范围
     */
    ParticleEmitter.prototype.setParticleRotationSpeed = function (rotationSpeed) {
        if (typeof rotationSpeed === 'number') {
            this.particleRotationSpeed = [rotationSpeed, rotationSpeed];
        }
        else {
            this.particleRotationSpeed = rotationSpeed;
        }
    };
    /**
     * 设置粒子缩放速度
     * @param scaleSpeed 粒子缩放速度或范围
     */
    ParticleEmitter.prototype.setParticleScaleSpeed = function (scaleSpeed) {
        if (scaleSpeed instanceof THREE.Vector2) {
            this.particleScaleSpeed = [scaleSpeed.clone(), scaleSpeed.clone()];
        }
        else {
            this.particleScaleSpeed = [scaleSpeed[0].clone(), scaleSpeed[1].clone()];
        }
    };
    /**
     * 设置发射角度
     * @param angle 发射角度或范围
     */
    ParticleEmitter.prototype.setEmissionAngle = function (angle) {
        if (typeof angle === 'number') {
            this.emissionAngle = [angle, angle];
        }
        else {
            this.emissionAngle = angle;
        }
    };
    /**
     * 设置发射力度
     * @param force 发射力度或范围
     */
    ParticleEmitter.prototype.setEmissionForce = function (force) {
        if (typeof force === 'number') {
            this.emissionForce = [force, force];
        }
        else {
            this.emissionForce = force;
        }
    };
    /**
     * 开始发射粒子
     */
    ParticleEmitter.prototype.start = function () {
        if (!this.active) {
            this.active = true;
            this.elapsedTime = 0;
            this.lastEmitTime = 0;
            if (this.burst) {
                this.burst.currentCycle = 0;
                this.burst.lastTime = 0;
            }
        }
    };
    /**
     * 停止发射粒子
     */
    ParticleEmitter.prototype.stop = function () {
        this.active = false;
    };
    /**
     * 清除所有粒子
     */
    ParticleEmitter.prototype.clearParticles = function () {
        // 释放所有活跃粒子
        for (var _i = 0, _a = this.particles; _i < _a.length; _i++) {
            var particle = _a[_i];
            if (this.particleSystem) {
                this.particleSystem.releaseParticle(particle);
            }
        }
        this.particles = [];
    };
    /**
     * 重置发射器
     */
    ParticleEmitter.prototype.reset = function () {
        this.stop();
        this.clearParticles();
        this.elapsedTime = 0;
        this.lastEmitTime = 0;
        if (this.burst) {
            this.burst.currentCycle = 0;
            this.burst.lastTime = 0;
        }
    };
    /**
     * 更新发射器
     * @param deltaTime 帧间隔时间（秒）
     */
    ParticleEmitter.prototype.update = function (deltaTime) {
        // 更新发射器时间
        this.elapsedTime += deltaTime;
        // 检查发射持续时间
        if (this.duration > 0 && this.elapsedTime >= this.duration) {
            if (this.loop) {
                // 循环模式，重置时间
                this.elapsedTime = 0;
            }
            else {
                // 非循环模式，停止发射
                this.stop();
            }
        }
        // 更新世界矩阵
        this.updateWorldMatrix();
        // 如果活跃且有粒子系统，发射新粒子
        if (this.active && this.particleSystem) {
            // 计算本帧应该发射的粒子数量
            var emitCount = 0;
            if (this.burst) {
                // 爆发模式
                if (this.burst.cycles === 0 || this.burst.currentCycle < this.burst.cycles) {
                    if (this.elapsedTime - this.burst.lastTime >= this.burst.interval) {
                        emitCount = this.burst.count;
                        this.burst.lastTime = this.elapsedTime;
                        this.burst.currentCycle++;
                    }
                }
            }
            else {
                // 连续发射模式
                var emitInterval = 1 / this.emissionRate;
                var timeSinceLastEmit = this.elapsedTime - this.lastEmitTime;
                if (timeSinceLastEmit >= emitInterval) {
                    emitCount = Math.floor(timeSinceLastEmit / emitInterval);
                    this.lastEmitTime = this.elapsedTime;
                }
            }
            // 发射粒子
            for (var i = 0; i < emitCount; i++) {
                if (this.particles.length < this.maxParticles) {
                    this.emitParticle();
                }
            }
        }
        // 更新所有活跃粒子
        this.updateParticles(deltaTime);
    };
    /**
     * 更新世界矩阵
     */
    ParticleEmitter.prototype.updateWorldMatrix = function () {
        if (this.entity) {
            // 获取实体的世界变换
            var transform = this.entity.getComponent('Transform');
            if (transform) {
                this.worldMatrix.copy(transform.getWorldMatrix());
            }
            else {
                this.worldMatrix.identity();
            }
        }
        else {
            this.worldMatrix.identity();
        }
    };
    /**
     * 发射单个粒子
     */
    ParticleEmitter.prototype.emitParticle = function () {
        if (!this.particleSystem)
            return;
        // 从粒子系统创建新粒子
        var particle = this.particleSystem.createParticle();
        if (!particle)
            return;
        // 设置粒子初始属性
        this.initializeParticle(particle);
        // 添加到活跃粒子列表
        this.particles.push(particle);
    };
    /**
     * 初始化粒子属性
     * @param particle 粒子
     */
    ParticleEmitter.prototype.initializeParticle = function (particle) {
        // 设置粒子位置
        this.setParticlePosition(particle);
        // 设置粒子速度
        this.setParticleVelocityVector(particle);
        // 设置粒子生命周期
        particle.lifetime = this.randomRange(this.particleLifetime[0], this.particleLifetime[1]);
        // 设置粒子大小
        var size = this.randomRange(this.particleSize[0], this.particleSize[1]);
        particle.setScale(size, size);
        // 设置粒子颜色
        particle.startColor.copy(this.particleColor[0]);
        particle.endColor.copy(this.particleColor[1]);
        particle.color.copy(particle.startColor);
        // 设置粒子透明度
        particle.startOpacity = this.particleOpacity[0];
        particle.endOpacity = this.particleOpacity[1];
        particle.opacity = particle.startOpacity;
        // 设置粒子旋转
        particle.rotation = Math.random() * Math.PI * 2;
        particle.rotationSpeed = this.randomRange(this.particleRotationSpeed[0], this.particleRotationSpeed[1]);
        // 设置粒子缩放速度
        var scaleSpeedX = this.randomRange(this.particleScaleSpeed[0].x, this.particleScaleSpeed[1].x);
        var scaleSpeedY = this.randomRange(this.particleScaleSpeed[0].y, this.particleScaleSpeed[1].y);
        particle.scaleSpeed.set(scaleSpeedX, scaleSpeedY);
        // 设置粒子物理属性
        particle.drag = this.drag;
        particle.gravityScale = 1;
        // 重置粒子年龄
        particle.age = 0;
        particle.normalizedAge = 0;
    };
    /**
     * 设置粒子初始位置
     * @param particle 粒子
     */
    ParticleEmitter.prototype.setParticlePosition = function (particle) {
        // 根据发射器形状设置粒子位置
        switch (this.shapeType) {
            case EmitterShapeType.POINT:
                // 点发射器，位置就是发射器位置
                particle.setPosition(0, 0, 0);
                break;
            case EmitterShapeType.SPHERE:
                // 球形发射器
                var radius = this.shapeParams.radius || 1;
                var phi = Math.random() * Math.PI * 2;
                var theta = Math.random() * Math.PI;
                particle.setPosition(radius * Math.sin(theta) * Math.cos(phi), radius * Math.sin(theta) * Math.sin(phi), radius * Math.cos(theta));
                break;
            case EmitterShapeType.CIRCLE:
                // 圆形发射器
                var circleRadius = this.shapeParams.radius || 1;
                var angle = Math.random() * Math.PI * 2;
                particle.setPosition(circleRadius * Math.cos(angle), circleRadius * Math.sin(angle), 0);
                break;
            case EmitterShapeType.RECTANGLE:
                // 矩形发射器
                var width = this.shapeParams.width || 1;
                var height = this.shapeParams.height || 1;
                particle.setPosition((Math.random() - 0.5) * width, (Math.random() - 0.5) * height, 0);
                break;
            case EmitterShapeType.CONE:
                // 圆锥发射器
                var coneRadius = this.shapeParams.radius || 1;
                var coneAngle = Math.random() * Math.PI * 2;
                var coneHeight = this.shapeParams.height || 1;
                particle.setPosition(coneRadius * Math.cos(coneAngle), coneRadius * Math.sin(coneAngle), coneHeight);
                break;
            case EmitterShapeType.TORUS:
                // 圆环发射器
                var torusRadius = this.shapeParams.radius || 1;
                var tubeRadius = this.shapeParams.tubeRadius || 0.1;
                var u = Math.random() * Math.PI * 2;
                var v = Math.random() * Math.PI * 2;
                particle.setPosition((torusRadius + tubeRadius * Math.cos(v)) * Math.cos(u), (torusRadius + tubeRadius * Math.cos(v)) * Math.sin(u), tubeRadius * Math.sin(v));
                break;
            case EmitterShapeType.MESH:
                // 网格发射器
                // 需要实现从网格表面采样点的逻辑
                particle.setPosition(0, 0, 0);
                break;
            default:
                particle.setPosition(0, 0, 0);
                break;
        }
        // 应用世界变换
        particle.position.applyMatrix4(this.worldMatrix);
        particle.previousPosition.copy(particle.position);
    };
    /**
     * 设置粒子初始速度向量
     * @param particle 粒子
     */
    ParticleEmitter.prototype.setParticleVelocityVector = function (particle) {
        // 计算发射方向
        var direction = new THREE.Vector3();
        if (this.emissionSpread <= 0) {
            // 无扩散，使用固定方向
            direction.copy(this.emissionDirection);
        }
        else {
            // 有扩散，在锥体内随机方向
            var angle = this.randomRange(this.emissionAngle[0], this.emissionAngle[1]);
            var spread = this.emissionSpread * Math.random();
            // 创建一个随机旋转四元数
            this.tempQuaternion.setFromAxisAngle(new THREE.Vector3(0, 0, 1), angle);
            // 应用旋转到方向向量
            direction.copy(this.emissionDirection).applyQuaternion(this.tempQuaternion);
            // 应用扩散
            if (spread > 0) {
                var perpendicular = new THREE.Vector3().crossVectors(direction, direction.y !== 0 || direction.z !== 0
                    ? new THREE.Vector3(1, 0, 0)
                    : new THREE.Vector3(0, 1, 0)).normalize();
                var perpendicular2 = new THREE.Vector3().crossVectors(direction, perpendicular).normalize();
                var spreadAngle = spread * Math.PI;
                var randomAngle = Math.random() * Math.PI * 2;
                perpendicular.multiplyScalar(Math.sin(spreadAngle) * Math.cos(randomAngle));
                perpendicular2.multiplyScalar(Math.sin(spreadAngle) * Math.sin(randomAngle));
                direction.multiplyScalar(Math.cos(spreadAngle))
                    .add(perpendicular)
                    .add(perpendicular2)
                    .normalize();
            }
        }
        // 应用发射力度
        var force = this.randomRange(this.emissionForce[0], this.emissionForce[1]);
        var velocity = this.randomRange(this.particleVelocity[0], this.particleVelocity[1]);
        // 设置粒子速度
        particle.velocity.copy(direction).multiplyScalar(force * velocity);
        // 应用世界旋转
        var worldRotation = new THREE.Quaternion().setFromRotationMatrix(this.worldMatrix);
        particle.velocity.applyQuaternion(worldRotation);
    };
    /**
     * 更新所有活跃粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    ParticleEmitter.prototype.updateParticles = function (deltaTime) {
        // 遍历所有活跃粒子
        for (var i = this.particles.length - 1; i >= 0; i--) {
            var particle = this.particles[i];
            // 应用重力
            if (particle.gravityScale !== 0) {
                particle.applyGravity(this.gravity);
            }
            // 更新粒子
            var isActive = particle.update(deltaTime);
            // 如果粒子不再活跃，从列表中移除
            if (!isActive) {
                if (this.particleSystem) {
                    this.particleSystem.releaseParticle(particle);
                }
                this.particles.splice(i, 1);
            }
        }
    };
    /**
     * 对粒子进行排序
     * @param cameraPosition 相机位置
     */
    ParticleEmitter.prototype.sortParticles = function (cameraPosition) {
        if (!this.enableSorting || this.particles.length <= 1) {
            return;
        }
        // 按照到相机的距离排序
        this.particles.sort(function (a, b) {
            var distA = a.position.distanceToSquared(cameraPosition);
            var distB = b.position.distanceToSquared(cameraPosition);
            return distB - distA; // 从远到近排序
        });
    };
    /**
     * 获取活跃粒子数量
     * @returns 活跃粒子数量
     */
    ParticleEmitter.prototype.getActiveParticleCount = function () {
        return this.particles.length;
    };
    /**
     * 获取所有活跃粒子
     * @returns 活跃粒子数组
     */
    ParticleEmitter.prototype.getParticles = function () {
        return __spreadArray([], this.particles, true);
    };
    /**
     * 检查发射器是否活跃
     * @returns 是否活跃
     */
    ParticleEmitter.prototype.isActive = function () {
        return this.active;
    };
    /**
     * 生成指定范围内的随机数
     * @param min 最小值
     * @param max 最大值
     * @returns 随机数
     */
    ParticleEmitter.prototype.randomRange = function (min, max) {
        return min + Math.random() * (max - min);
    };
    /** 组件类型 */
    ParticleEmitter.type = 'ParticleEmitter';
    return ParticleEmitter;
}(Component_1.Component));
