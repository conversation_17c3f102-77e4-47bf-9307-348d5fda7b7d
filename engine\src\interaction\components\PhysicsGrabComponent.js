"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsGrabComponent = exports.Hand = void 0;
/**
 * 物理抓取组件
 * 用于处理物理对象的抓取
 */
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var BodyType_1 = require("../../physics/types/BodyType");
// 临时定义缺失的类型
var Hand;
(function (Hand) {
    Hand["LEFT"] = "left";
    Hand["RIGHT"] = "right";
})(Hand || (exports.Hand = Hand = {}));
/**
 * 物理抓取组件
 */
var PhysicsGrabComponent = exports.PhysicsGrabComponent = /** @class */ (function (_super) {
    __extends(PhysicsGrabComponent, _super);
    /**
     * 构造函数
     * @param config 组件配置
     */
    function PhysicsGrabComponent(config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, PhysicsGrabComponent.TYPE) || this;
        /** 抓取偏移 */
        _this._grabOffset = new three_1.Vector3();
        /** 抓取旋转偏移 */
        _this._grabRotationOffset = new three_1.Quaternion();
        /** 是否被抓取 */
        _this._isGrabbed = false;
        // 初始化属性
        _this._grabForce = config.grabForce || 10.0;
        _this._grabDamping = config.grabDamping || 0.5;
        _this._keepOriginalBodyType = config.keepOriginalBodyType !== undefined ? config.keepOriginalBodyType : false;
        _this._grabBodyType = config.grabBodyType || BodyType_1.BodyType.KINEMATIC;
        return _this;
    }
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabForce", {
        /**
         * 获取抓取力
         */
        get: function () {
            return this._grabForce;
        },
        /**
         * 设置抓取力
         */
        set: function (value) {
            this._grabForce = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabDamping", {
        /**
         * 获取抓取阻尼
         */
        get: function () {
            return this._grabDamping;
        },
        /**
         * 设置抓取阻尼
         */
        set: function (value) {
            this._grabDamping = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "keepOriginalBodyType", {
        /**
         * 获取是否保持原始物理类型
         */
        get: function () {
            return this._keepOriginalBodyType;
        },
        /**
         * 设置是否保持原始物理类型
         */
        set: function (value) {
            this._keepOriginalBodyType = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabBodyType", {
        /**
         * 获取抓取时的物理类型
         */
        get: function () {
            return this._grabBodyType;
        },
        /**
         * 设置抓取时的物理类型
         */
        set: function (value) {
            this._grabBodyType = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "isGrabbed", {
        /**
         * 获取是否被抓取
         */
        get: function () {
            return this._isGrabbed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabber", {
        /**
         * 获取抓取者
         */
        get: function () {
            return this._grabber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "hand", {
        /**
         * 获取抓取手
         */
        get: function () {
            return this._hand;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabOffset", {
        /**
         * 获取抓取偏移
         */
        get: function () {
            return this._grabOffset.clone();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PhysicsGrabComponent.prototype, "grabRotationOffset", {
        /**
         * 获取抓取旋转偏移
         */
        get: function () {
            return this._grabRotationOffset.clone();
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 开始抓取
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 是否成功
     */
    PhysicsGrabComponent.prototype.startGrab = function (grabber, hand) {
        // 如果已经被抓取，则返回失败
        if (this._isGrabbed) {
            return false;
        }
        // 获取物理体组件
        var entity = this.getEntity();
        if (!entity)
            return false;
        var physicsBody = entity.getComponent('PhysicsBody');
        if (!physicsBody) {
            console.warn('PhysicsGrabComponent', "Entity ".concat(entity.id, " does not have a PhysicsBodyComponent"));
            return false;
        }
        // 保存原始物理类型
        this._originalBodyType = physicsBody.bodyType;
        // 如果不保持原始物理类型，则更改物理类型
        if (!this._keepOriginalBodyType) {
            physicsBody.bodyType = this._grabBodyType;
        }
        // 设置抓取状态
        this._isGrabbed = true;
        this._grabber = grabber;
        this._hand = hand;
        // 计算抓取偏移
        this.calculateGrabOffset(grabber, hand);
        return true;
    };
    /**
     * 结束抓取
     * @returns 是否成功
     */
    PhysicsGrabComponent.prototype.endGrab = function () {
        // 如果未被抓取，则返回失败
        if (!this._isGrabbed || !this._grabber) {
            return false;
        }
        // 获取物理体组件
        var entity = this.getEntity();
        if (entity) {
            var physicsBody = entity.getComponent('PhysicsBody');
            if (physicsBody && this._originalBodyType !== undefined && !this._keepOriginalBodyType) {
                // 恢复原始物理类型
                physicsBody.bodyType = this._originalBodyType;
            }
        }
        // 重置抓取状态
        this._isGrabbed = false;
        this._grabber = undefined;
        this._hand = undefined;
        this._originalBodyType = undefined;
        return true;
    };
    /**
     * 计算抓取偏移
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    PhysicsGrabComponent.prototype.calculateGrabOffset = function (grabber, hand) {
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform)
            return;
        // 获取被抓取实体的变换组件
        var entityTransform = this.entity.getComponent('Transform');
        if (!entityTransform)
            return;
        // 计算位置偏移
        this._grabOffset.set(entityTransform.getPosition().x - grabberTransform.getPosition().x, entityTransform.getPosition().y - grabberTransform.getPosition().y, entityTransform.getPosition().z - grabberTransform.getPosition().z);
        // 计算旋转偏移
        var grabberRotation = new three_1.Quaternion(grabberTransform.rotation.x, grabberTransform.rotation.y, grabberTransform.rotation.z, grabberTransform.rotation.w);
        var entityRotation = new three_1.Quaternion(entityTransform.rotation.x, entityTransform.rotation.y, entityTransform.rotation.z, entityTransform.rotation.w);
        // 计算相对旋转
        this._grabRotationOffset.copy(grabberRotation).invert().multiply(entityRotation);
    };
    /**
     * 更新抓取
     * @param deltaTime 时间增量（秒）
     */
    PhysicsGrabComponent.prototype.update = function (deltaTime) {
        // 如果未被抓取，则返回
        if (!this._isGrabbed || !this._grabber) {
            return;
        }
        // 获取物理体组件
        var physicsBody = this.entity.getComponent(PhysicsBodyComponent.TYPE);
        if (!physicsBody)
            return;
        // 获取抓取者的变换组件
        var grabberTransform = this._grabber.getComponent('Transform');
        if (!grabberTransform)
            return;
        // 计算目标位置
        var targetPosition = new three_1.Vector3(grabberTransform.getPosition().x, grabberTransform.getPosition().y, grabberTransform.getPosition().z).add(this._grabOffset);
        // 计算目标旋转
        var grabberRotation = new three_1.Quaternion(grabberTransform.rotation.x, grabberTransform.rotation.y, grabberTransform.rotation.z, grabberTransform.rotation.w);
        var targetRotation = new three_1.Quaternion().copy(grabberRotation).multiply(this._grabRotationOffset);
        // 根据物理类型更新位置和旋转
        if (physicsBody.bodyType === BodyType_1.BodyType.KINEMATIC) {
            // 直接设置位置和旋转
            physicsBody.setKinematicTarget(targetPosition, targetRotation);
        }
        else {
            // 应用力和扭矩
            var currentPosition = new three_1.Vector3(physicsBody.getPosition().x, physicsBody.getPosition().y, physicsBody.getPosition().z);
            var direction = new three_1.Vector3().subVectors(targetPosition, currentPosition);
            var distance = direction.length();
            direction.normalize();
            // 计算力
            var force = direction.multiplyScalar(distance * this._grabForce);
            physicsBody.applyForce(force.x, force.y, force.z);
            // 应用阻尼
            var velocity = new three_1.Vector3(physicsBody.linearVelocity.x, physicsBody.linearVelocity.y, physicsBody.linearVelocity.z);
            var dampingForce = velocity.multiplyScalar(-this._grabDamping);
            physicsBody.applyForce(dampingForce.x, dampingForce.y, dampingForce.z);
        }
    };
    /** 组件类型 */
    PhysicsGrabComponent.TYPE = 'PhysicsGrabComponent';
    return PhysicsGrabComponent;
}(Component_1.Component));
