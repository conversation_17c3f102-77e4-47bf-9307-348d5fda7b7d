"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataCompressor = exports.CompressionLevel = exports.CompressionAlgorithm = void 0;
/**
 * 数据压缩器
 * 负责压缩和解压缩网络传输的数据
 */
var Debug_1 = require("../utils/Debug");
/**
 * 压缩算法
 */
var CompressionAlgorithm;
(function (CompressionAlgorithm) {
    /** 无压缩 */
    CompressionAlgorithm["NONE"] = "none";
    /** LZ字符串压缩 */
    CompressionAlgorithm["LZ_STRING"] = "lz_string";
    /** MessagePack */
    CompressionAlgorithm["MSGPACK"] = "msgpack";
    /** CBOR (Concise Binary Object Representation) */
    CompressionAlgorithm["CBOR"] = "cbor";
    /** Brotli压缩 */
    CompressionAlgorithm["BROTLI"] = "brotli";
    /** Deflate压缩 */
    CompressionAlgorithm["DEFLATE"] = "deflate";
    /** 增量压缩 */
    CompressionAlgorithm["INCREMENTAL"] = "incremental";
    /** 自定义压缩 */
    CompressionAlgorithm["CUSTOM"] = "custom";
})(CompressionAlgorithm || (exports.CompressionAlgorithm = CompressionAlgorithm = {}));
/**
 * 压缩级别
 */
var CompressionLevel;
(function (CompressionLevel) {
    /** 无压缩 */
    CompressionLevel[CompressionLevel["NONE"] = 0] = "NONE";
    /** 低压缩 */
    CompressionLevel[CompressionLevel["LOW"] = 1] = "LOW";
    /** 中等压缩 */
    CompressionLevel[CompressionLevel["MEDIUM"] = 2] = "MEDIUM";
    /** 高压缩 */
    CompressionLevel[CompressionLevel["HIGH"] = 3] = "HIGH";
    /** 最高压缩 */
    CompressionLevel[CompressionLevel["HIGHEST"] = 4] = "HIGHEST";
})(CompressionLevel || (exports.CompressionLevel = CompressionLevel = {}));
/**
 * 数据压缩器
 * 负责压缩和解压缩网络传输的数据
 */
var DataCompressor = /** @class */ (function () {
    /**
     * 创建数据压缩器
     * @param options 压缩选项
     */
    function DataCompressor(options) {
        if (options === void 0) { options = {}; }
        /** 上次压缩的数据缓存 */
        this.lastCompressedDataCache = new Map();
        /** 是否已加载依赖 */
        this.dependenciesLoaded = false;
        /** 压缩库引用 */
        this.compressionLibs = {};
        // 默认增量压缩选项
        var defaultIncrementalOptions = {
            enabled: true,
            maxDepth: 10,
            includePathInfo: true,
            useBinaryDiff: false,
            compressIncrementalData: true,
            useFieldFiltering: false,
            includedFields: [],
            excludedFields: []
        };
        // 默认配置
        this.options = __assign(__assign({ algorithm: CompressionAlgorithm.LZ_STRING, level: CompressionLevel.MEDIUM, adaptive: true, minSize: 100, useBinaryFormat: false, useTypedArrayOptimization: true, useDictionaryCompression: false, compressionDictionary: undefined, incremental: defaultIncrementalOptions, customCompressFunction: undefined, customDecompressFunction: undefined }, options), { 
            // 确保增量选项被正确合并
            incremental: __assign(__assign({}, defaultIncrementalOptions), (options.incremental || {})) });
        // 初始化统计信息
        this.stats = {
            compressionCount: 0,
            decompressionCount: 0,
            totalOriginalSize: 0,
            totalCompressedSize: 0,
            totalCompressionTime: 0,
            totalDecompressionTime: 0,
            incrementalCompressionCount: 0,
            incrementalSavedBytes: 0,
            adaptiveAlgorithmSwitchCount: 0
        };
        // 设置压缩字典
        if (this.options.useDictionaryCompression && this.options.compressionDictionary) {
            this.compressionDictionary = this.options.compressionDictionary;
        }
        // 加载依赖
        this.loadDependencies();
    }
    /**
     * 加载依赖
     */
    DataCompressor.prototype.loadDependencies = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, lzString, error_1, msgpack, error_2, cbor, error_3, error_4;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.dependenciesLoaded) {
                            return [2 /*return*/];
                        }
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 17, , 18]);
                        _a = this.options.algorithm;
                        switch (_a) {
                            case CompressionAlgorithm.LZ_STRING: return [3 /*break*/, 2];
                            case CompressionAlgorithm.MSGPACK: return [3 /*break*/, 6];
                            case CompressionAlgorithm.CBOR: return [3 /*break*/, 10];
                            case CompressionAlgorithm.BROTLI: return [3 /*break*/, 14];
                            case CompressionAlgorithm.DEFLATE: return [3 /*break*/, 15];
                        }
                        return [3 /*break*/, 16];
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, Promise.resolve().then(function () { return require('lz-string'); })];
                    case 3:
                        lzString = _b.sent();
                        this.compressionLibs.lzString = lzString.default || lzString;
                        Debug_1.Debug.log('DataCompressor', '成功加载LZ-String库');
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _b.sent();
                        Debug_1.Debug.warn('DataCompressor', '无法加载LZ-String库，将使用内置的简单实现:', error_1);
                        return [3 /*break*/, 5];
                    case 5: return [3 /*break*/, 16];
                    case 6:
                        _b.trys.push([6, 8, , 9]);
                        return [4 /*yield*/, Promise.resolve().then(function () { return require('@msgpack/msgpack'); })];
                    case 7:
                        msgpack = _b.sent();
                        this.compressionLibs.msgpack = msgpack;
                        Debug_1.Debug.log('DataCompressor', '成功加载MessagePack库');
                        return [3 /*break*/, 9];
                    case 8:
                        error_2 = _b.sent();
                        Debug_1.Debug.warn('DataCompressor', '无法加载MessagePack库，将使用内置的简单实现:', error_2);
                        return [3 /*break*/, 9];
                    case 9: return [3 /*break*/, 16];
                    case 10:
                        _b.trys.push([10, 12, , 13]);
                        return [4 /*yield*/, Promise.resolve().then(function () { return require('cbor-web'); })];
                    case 11:
                        cbor = _b.sent();
                        this.compressionLibs.cbor = cbor;
                        Debug_1.Debug.log('DataCompressor', '成功加载CBOR库');
                        return [3 /*break*/, 13];
                    case 12:
                        error_3 = _b.sent();
                        Debug_1.Debug.warn('DataCompressor', '无法加载CBOR库，将使用内置的简单实现:', error_3);
                        return [3 /*break*/, 13];
                    case 13: return [3 /*break*/, 16];
                    case 14:
                        // 在浏览器环境中，可以使用CompressionStream API
                        if (typeof CompressionStream !== 'undefined') {
                            Debug_1.Debug.log('DataCompressor', '使用浏览器内置的CompressionStream API');
                        }
                        else {
                            Debug_1.Debug.warn('DataCompressor', '当前环境不支持CompressionStream API，将使用内置的简单实现');
                        }
                        return [3 /*break*/, 16];
                    case 15:
                        // 在浏览器环境中，可以使用CompressionStream API
                        if (typeof CompressionStream !== 'undefined') {
                            Debug_1.Debug.log('DataCompressor', '使用浏览器内置的CompressionStream API');
                        }
                        else {
                            Debug_1.Debug.warn('DataCompressor', '当前环境不支持CompressionStream API，将使用内置的简单实现');
                        }
                        return [3 /*break*/, 16];
                    case 16:
                        this.dependenciesLoaded = true;
                        return [3 /*break*/, 18];
                    case 17:
                        error_4 = _b.sent();
                        Debug_1.Debug.error('DataCompressor', '加载依赖失败:', error_4);
                        return [3 /*break*/, 18];
                    case 18: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 创建增量数据
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 增量数据
     */
    DataCompressor.prototype.createIncrementalData = function (newData, oldData) {
        if (!this.options.incremental.enabled) {
            return newData;
        }
        // 开始计时
        var startTime = performance.now();
        // 创建增量对象
        var incremental = {
            __incremental: true,
            __version: 2,
            __timestamp: Date.now()
        };
        // 如果是全新数据，直接返回完整数据
        if (!oldData) {
            incremental.__complete = true;
            incremental.__data = newData;
            return incremental;
        }
        // 递归比较并创建增量
        var changes = this.createDeepIncrementalChanges(newData, oldData, '', 0, this.options.incremental.maxDepth);
        // 如果没有变化，返回空增量
        if (Object.keys(changes).length === 0) {
            incremental.__empty = true;
            return incremental;
        }
        // 添加变更数据
        incremental.__data = changes;
        // 如果需要包含路径信息
        if (this.options.incremental.includePathInfo) {
            // 收集所有变更路径
            var paths = this.collectChangePaths(changes);
            if (paths.length > 0) {
                incremental.__paths = paths;
            }
        }
        // 计算增量大小和完整数据大小
        var incrementalSize = new TextEncoder().encode(JSON.stringify(incremental)).length;
        var fullSize = new TextEncoder().encode(JSON.stringify(newData)).length;
        // 计算节省的字节数和百分比
        var savedBytes = fullSize - incrementalSize;
        var savingsPercentage = (savedBytes / fullSize) * 100;
        // 更新统计信息
        this.stats.incrementalCompressionCount++;
        this.stats.incrementalSavedBytes += savedBytes;
        // 如果增量数据比完整数据大，则使用完整数据
        if (incrementalSize >= fullSize) {
            incremental.__complete = true;
            incremental.__data = newData;
            return incremental;
        }
        // 添加增量压缩结果信息
        incremental.__incrementalSize = incrementalSize;
        incremental.__fullSize = fullSize;
        incremental.__savedBytes = savedBytes;
        incremental.__savingsPercentage = savingsPercentage;
        incremental.__changedFieldsCount = Object.keys(changes).length;
        // 如果需要压缩增量数据
        if (this.options.incremental.compressIncrementalData) {
            // 压缩增量数据
            var compressedData = this.compressIncrementalData(incremental.__data);
            incremental.__data = compressedData;
            incremental.__compressed = true;
        }
        return incremental;
    };
    /**
     * 应用增量数据
     * @param incrementalData 增量数据
     * @param currentData 当前数据
     * @returns 更新后的数据
     */
    DataCompressor.prototype.applyIncrementalData = function (incrementalData, currentData) {
        // 如果不是增量数据，直接返回
        if (!incrementalData || !incrementalData.__incremental) {
            return incrementalData;
        }
        // 检查增量版本
        var version = incrementalData.__version || 1;
        // 如果是完整数据，直接使用
        if (incrementalData.__complete) {
            return incrementalData.__data || incrementalData;
        }
        // 如果是空增量，保持当前状态
        if (incrementalData.__isEmpty) {
            return currentData;
        }
        // 获取增量数据
        var incrementalChanges = incrementalData.__data;
        // 如果增量数据被压缩，先解压缩
        if (incrementalData.__compressed) {
            incrementalChanges = this.decompressIncrementalData(incrementalChanges);
        }
        // 根据版本应用增量
        var newData;
        if (version === 1) {
            // 版本1：简单属性覆盖
            newData = __assign({}, currentData);
            for (var key in incrementalChanges) {
                newData[key] = incrementalChanges[key];
            }
        }
        else {
            // 版本2：深度增量
            newData = __assign({}, currentData);
            // 应用深度增量变更
            this.applyDeepIncrementalChanges(newData, incrementalChanges);
        }
        return newData;
    };
    /**
     * 创建深度增量变更
     * @param newData 新数据
     * @param oldData 旧数据
     * @param path 当前路径
     * @param depth 当前深度
     * @param maxDepth 最大深度
     * @returns 增量变更
     */
    DataCompressor.prototype.createDeepIncrementalChanges = function (newData, oldData, path, depth, maxDepth) {
        // 如果超过最大深度，则返回完整数据
        if (depth >= maxDepth) {
            return newData;
        }
        // 如果新数据或旧数据为null或undefined，则直接比较
        if (newData === null || newData === undefined || oldData === null || oldData === undefined) {
            return newData !== oldData ? newData : {};
        }
        // 如果类型不同，则返回新数据
        if (typeof newData !== typeof oldData) {
            return newData;
        }
        // 如果是基本类型，则直接比较
        if (typeof newData !== 'object') {
            return newData !== oldData ? newData : {};
        }
        // 如果是数组，则比较数组
        if (Array.isArray(newData)) {
            // 如果旧数据不是数组，则返回新数组
            if (!Array.isArray(oldData)) {
                return newData;
            }
            // 如果数组长度不同，则返回新数组
            if (newData.length !== oldData.length) {
                return newData;
            }
            // 比较数组元素
            var changes_1 = {};
            var hasChanges_1 = false;
            for (var i = 0; i < newData.length; i++) {
                var elementChanges = this.createDeepIncrementalChanges(newData[i], oldData[i], "".concat(path, "[").concat(i, "]"), depth + 1, maxDepth);
                if (Object.keys(elementChanges).length > 0) {
                    changes_1[i] = elementChanges;
                    hasChanges_1 = true;
                }
            }
            // 如果有变化，则返回变化
            if (hasChanges_1) {
                return { __array: true, __changes: changes_1 };
            }
            return {};
        }
        // 如果是对象，则比较对象
        var changes = {};
        var hasChanges = false;
        // 检查新增和修改的属性
        for (var key in newData) {
            // 如果使用字段过滤，则检查是否应该包含该字段
            if (this.options.incremental.useFieldFiltering) {
                if (this.options.incremental.includedFields.length > 0 &&
                    !this.options.incremental.includedFields.includes(key)) {
                    continue;
                }
                if (this.options.incremental.excludedFields.length > 0 &&
                    this.options.incremental.excludedFields.includes(key)) {
                    continue;
                }
            }
            // 如果属性不在旧数据中，则添加
            if (!(key in oldData)) {
                changes[key] = newData[key];
                hasChanges = true;
                continue;
            }
            // 递归比较属性值
            var propertyChanges = this.createDeepIncrementalChanges(newData[key], oldData[key], path ? "".concat(path, ".").concat(key) : key, depth + 1, maxDepth);
            // 如果有变化，则添加
            if (Object.keys(propertyChanges).length > 0) {
                changes[key] = propertyChanges;
                hasChanges = true;
            }
        }
        // 检查删除的属性
        for (var key in oldData) {
            if (!(key in newData)) {
                changes[key] = { __deleted: true };
                hasChanges = true;
            }
        }
        return changes;
    };
    /**
     * 应用深度增量变更
     * @param target 目标对象
     * @param changes 变更对象
     */
    DataCompressor.prototype.applyDeepIncrementalChanges = function (target, changes) {
        for (var key in changes) {
            // 处理删除标记
            if (changes[key] && typeof changes[key] === 'object' && changes[key].__deleted) {
                delete target[key];
                continue;
            }
            // 处理数组变更
            if (changes[key] && typeof changes[key] === 'object' && changes[key].__array) {
                // 确保目标是数组
                if (!Array.isArray(target[key])) {
                    target[key] = [];
                }
                // 应用数组变更
                var arrayChanges = changes[key].__changes;
                for (var index in arrayChanges) {
                    var i = parseInt(index, 10);
                    // 确保数组长度足够
                    while (target[key].length <= i) {
                        target[key].push(undefined);
                    }
                    // 如果是对象变更，则递归应用
                    if (typeof arrayChanges[index] === 'object' && !Array.isArray(arrayChanges[index]) &&
                        Object.keys(arrayChanges[index]).length > 0) {
                        // 如果目标元素不存在，则创建
                        if (target[key][i] === undefined) {
                            target[key][i] = {};
                        }
                        // 递归应用变更
                        this.applyDeepIncrementalChanges(target[key][i], arrayChanges[index]);
                    }
                    else {
                        // 直接替换
                        target[key][i] = arrayChanges[index];
                    }
                }
                continue;
            }
            // 处理对象变更
            if (changes[key] && typeof changes[key] === 'object' && Object.keys(changes[key]).length > 0 &&
                target[key] && typeof target[key] === 'object') {
                // 递归应用变更
                this.applyDeepIncrementalChanges(target[key], changes[key]);
            }
            else {
                // 直接替换
                target[key] = changes[key];
            }
        }
    };
    /**
     * 收集变更路径
     * @param changes 变更对象
     * @param basePath 基础路径
     * @returns 变更路径列表
     */
    DataCompressor.prototype.collectChangePaths = function (changes, basePath) {
        if (basePath === void 0) { basePath = ''; }
        var paths = [];
        for (var key in changes) {
            var currentPath = basePath ? "".concat(basePath, ".").concat(key) : key;
            // 添加当前路径
            paths.push(currentPath);
            // 如果是对象且不是特殊标记，则递归收集
            if (changes[key] && typeof changes[key] === 'object' && !changes[key].__deleted) {
                // 处理数组变更
                if (changes[key].__array) {
                    var arrayChanges = changes[key].__changes;
                    for (var index in arrayChanges) {
                        var arrayPath = "".concat(currentPath, "[").concat(index, "]");
                        paths.push(arrayPath);
                        // 递归收集数组元素的变更路径
                        if (arrayChanges[index] && typeof arrayChanges[index] === 'object') {
                            var elementPaths = this.collectChangePaths(arrayChanges[index], arrayPath);
                            paths.push.apply(paths, elementPaths);
                        }
                    }
                }
                else {
                    // 递归收集对象属性的变更路径
                    var propertyPaths = this.collectChangePaths(changes[key], currentPath);
                    paths.push.apply(paths, propertyPaths);
                }
            }
        }
        return paths;
    };
    /**
     * 压缩增量数据
     * @param data 增量数据
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressIncrementalData = function (data) {
        // 使用当前配置的压缩算法压缩数据
        var result = this.compress(data, {
            algorithm: this.options.algorithm,
            level: this.options.level
        });
        return result.data;
    };
    /**
     * 解压缩增量数据
     * @param data 压缩后的增量数据
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressIncrementalData = function (data) {
        // 使用当前配置的压缩算法解压缩数据
        return this.decompress(data, this.options.algorithm, this.options.level);
    };
    /**
     * 压缩数据
     * @param data 要压缩的数据
     * @param options 压缩选项（可选，覆盖默认选项）
     * @returns 压缩结果
     */
    DataCompressor.prototype.compress = function (data, options) {
        // 合并选项
        var mergedOptions = __assign(__assign(__assign({}, this.options), options), { 
            // 确保增量选项被正确合并
            incremental: __assign(__assign({}, this.options.incremental), ((options === null || options === void 0 ? void 0 : options.incremental) || {})) });
        // 检查是否应该使用增量压缩
        if (mergedOptions.algorithm === CompressionAlgorithm.INCREMENTAL) {
            // 获取上次压缩的数据
            var cacheKey = typeof data === 'string' ? data : JSON.stringify(data);
            var lastData = this.lastCompressedDataCache.get(cacheKey);
            // 创建增量数据
            var incrementalData = this.createIncrementalData(data, lastData);
            // 更新缓存
            this.lastCompressedDataCache.set(cacheKey, data);
            // 计算增量大小
            var incrementalSize = typeof incrementalData === 'string'
                ? new TextEncoder().encode(incrementalData).length
                : new TextEncoder().encode(JSON.stringify(incrementalData)).length;
            // 计算原始大小
            var originalSize_1 = typeof data === 'string'
                ? new TextEncoder().encode(data).length
                : new TextEncoder().encode(JSON.stringify(data)).length;
            // 创建增量压缩结果
            var incrementalResult = {
                isIncremental: true,
                version: 2,
                isComplete: incrementalData.__complete || false,
                isEmpty: incrementalData.__empty || false,
                changedFieldsCount: incrementalData.__changedFieldsCount || 0,
                incrementalSize: incrementalData.__incrementalSize || incrementalSize,
                fullSize: incrementalData.__fullSize || originalSize_1,
                savedBytes: incrementalData.__savedBytes || (originalSize_1 - incrementalSize),
                savingsPercentage: incrementalData.__savingsPercentage || ((originalSize_1 - incrementalSize) / originalSize_1 * 100)
            };
            // 返回压缩结果
            return {
                data: incrementalData,
                originalSize: originalSize_1,
                compressedSize: incrementalSize,
                ratio: incrementalSize / originalSize_1,
                algorithm: CompressionAlgorithm.INCREMENTAL,
                level: mergedOptions.level,
                time: 0,
                incremental: incrementalResult
            };
        }
        // 将数据转换为适当的格式
        var originalData;
        var originalSize;
        if (mergedOptions.useBinaryFormat && !(typeof data === 'string')) {
            // 如果使用二进制格式，则转换为二进制
            if (mergedOptions.useTypedArrayOptimization && this.canUseTypedArray(data)) {
                // 使用类型化数组优化
                originalData = this.convertToTypedArray(data);
            }
            else {
                // 使用标准二进制格式
                originalData = this.convertToBinary(data);
            }
            originalSize = originalData instanceof Uint8Array ? originalData.byteLength : new TextEncoder().encode(originalData).length;
        }
        else {
            // 使用JSON字符串
            originalData = typeof data === 'string' ? data : JSON.stringify(data);
            originalSize = new TextEncoder().encode(originalData).length;
        }
        // 如果数据小于最小压缩大小，则不压缩
        if (originalSize < mergedOptions.minSize) {
            return {
                data: originalData,
                originalSize: originalSize,
                compressedSize: originalSize,
                ratio: 1,
                algorithm: CompressionAlgorithm.NONE,
                level: CompressionLevel.NONE,
                time: 0,
                isBinary: originalData instanceof Uint8Array
            };
        }
        // 如果启用自适应压缩，则根据数据大小选择算法和级别
        var oldAlgorithm = mergedOptions.algorithm;
        var oldLevel = mergedOptions.level;
        if (mergedOptions.adaptive) {
            this.selectAdaptiveCompression(originalSize, mergedOptions);
            // 如果算法或级别发生变化，记录切换次数
            if (oldAlgorithm !== mergedOptions.algorithm || oldLevel !== mergedOptions.level) {
                this.stats.adaptiveAlgorithmSwitchCount++;
            }
        }
        // 开始计时
        var startTime = performance.now();
        // 根据算法压缩数据
        var compressedData;
        var usedDictionary = false;
        try {
            switch (mergedOptions.algorithm) {
                case CompressionAlgorithm.NONE:
                    compressedData = originalData;
                    break;
                case CompressionAlgorithm.LZ_STRING:
                    compressedData = this.compressWithLZString(typeof originalData === 'string' ? originalData : new TextDecoder().decode(originalData), mergedOptions.level);
                    break;
                case CompressionAlgorithm.MSGPACK:
                    compressedData = this.compressWithMessagePack(data, mergedOptions.level);
                    break;
                case CompressionAlgorithm.CBOR:
                    compressedData = this.compressWithCBOR(data, mergedOptions.level);
                    break;
                case CompressionAlgorithm.BROTLI:
                    compressedData = yield this.compressWithBrotli(typeof originalData === 'string' ? new TextEncoder().encode(originalData) : originalData, mergedOptions.level);
                    break;
                case CompressionAlgorithm.DEFLATE:
                    compressedData = yield this.compressWithDeflate(typeof originalData === 'string' ? new TextEncoder().encode(originalData) : originalData, mergedOptions.level);
                    break;
                case CompressionAlgorithm.CUSTOM:
                    if (mergedOptions.customCompressFunction) {
                        compressedData = mergedOptions.customCompressFunction(data);
                    }
                    else {
                        throw new Error('Custom compression function is not defined');
                    }
                    break;
                default:
                    compressedData = originalData;
                    break;
            }
            // 如果启用了字典压缩，则使用字典压缩
            if (mergedOptions.useDictionaryCompression && this.compressionDictionary) {
                compressedData = this.compressWithDictionary(typeof compressedData === 'string' ? new TextEncoder().encode(compressedData) : compressedData, this.compressionDictionary);
                usedDictionary = true;
            }
        }
        catch (error) {
            Debug_1.Debug.error('DataCompressor', 'Compression error:', error);
            // 如果压缩失败，则返回原始数据
            compressedData = originalData;
            mergedOptions.algorithm = CompressionAlgorithm.NONE;
            mergedOptions.level = CompressionLevel.NONE;
        }
        // 计算压缩后大小
        var compressedSize = typeof compressedData === 'string'
            ? new TextEncoder().encode(compressedData).length
            : compressedData.byteLength;
        // 计算压缩比率
        var ratio = compressedSize / originalSize;
        // 计算压缩时间
        var endTime = performance.now();
        var compressionTime = endTime - startTime;
        // 更新统计信息
        this.stats.compressionCount++;
        this.stats.totalOriginalSize += originalSize;
        this.stats.totalCompressedSize += compressedSize;
        this.stats.totalCompressionTime += compressionTime;
        // 返回压缩结果
        return {
            data: compressedData,
            originalSize: originalSize,
            compressedSize: compressedSize,
            ratio: ratio,
            algorithm: mergedOptions.algorithm,
            level: mergedOptions.level,
            time: compressionTime,
            isBinary: compressedData instanceof Uint8Array,
            usedDictionary: usedDictionary
        };
    };
    /**
     * 检查是否可以使用类型化数组优化
     * @param data 数据
     * @returns 是否可以使用类型化数组优化
     */
    DataCompressor.prototype.canUseTypedArray = function (data) {
        // 检查数据是否包含数值数组
        if (Array.isArray(data) && data.every(function (item) { return typeof item === 'number'; })) {
            return true;
        }
        // 检查数据是否包含向量或矩阵
        if (data && typeof data === 'object') {
            // 检查是否是向量
            if ('x' in data && 'y' in data && 'z' in data &&
                typeof data.x === 'number' && typeof data.y === 'number' && typeof data.z === 'number') {
                return true;
            }
            // 检查是否是四元数
            if ('x' in data && 'y' in data && 'z' in data && 'w' in data &&
                typeof data.x === 'number' && typeof data.y === 'number' &&
                typeof data.z === 'number' && typeof data.w === 'number') {
                return true;
            }
            // 检查是否是矩阵
            if ('elements' in data && Array.isArray(data.elements) &&
                data.elements.every(function (item) { return typeof item === 'number'; })) {
                return true;
            }
        }
        return false;
    };
    /**
     * 将数据转换为类型化数组
     * @param data 数据
     * @returns 类型化数组
     */
    DataCompressor.prototype.convertToTypedArray = function (data) {
        // 如果是数值数组，则直接转换为Float32Array
        if (Array.isArray(data) && data.every(function (item) { return typeof item === 'number'; })) {
            var float32Array = new Float32Array(data);
            return new Uint8Array(float32Array.buffer);
        }
        // 如果是向量，则转换为Float32Array
        if (data && typeof data === 'object' && 'x' in data && 'y' in data && 'z' in data) {
            var float32Array = new Float32Array(data.w !== undefined ? 4 : 3);
            float32Array[0] = data.x;
            float32Array[1] = data.y;
            float32Array[2] = data.z;
            if (data.w !== undefined) {
                float32Array[3] = data.w;
            }
            return new Uint8Array(float32Array.buffer);
        }
        // 如果是矩阵，则转换为Float32Array
        if (data && typeof data === 'object' && 'elements' in data && Array.isArray(data.elements)) {
            var float32Array = new Float32Array(data.elements);
            return new Uint8Array(float32Array.buffer);
        }
        // 默认转换为JSON
        return this.convertToBinary(data);
    };
    /**
     * 将数据转换为二进制
     * @param data 数据
     * @returns 二进制数据
     */
    DataCompressor.prototype.convertToBinary = function (data) {
        // 将数据转换为JSON字符串
        var jsonString = JSON.stringify(data);
        // 转换为Uint8Array
        var encoder = new TextEncoder();
        return encoder.encode(jsonString);
    };
    /**
     * 解压缩数据
     * @param compressedData 压缩后的数据
     * @param algorithm 压缩算法
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompress = function (compressedData, algorithm, level) {
        if (algorithm === void 0) { algorithm = this.options.algorithm; }
        if (level === void 0) { level = this.options.level; }
        // 检查是否是增量数据
        if (algorithm === CompressionAlgorithm.INCREMENTAL) {
            // 如果是增量数据，则应用增量
            if (typeof compressedData === 'object' && compressedData !== null && '__incremental' in compressedData) {
                // 获取上次压缩的数据
                var cacheKey = JSON.stringify(compressedData);
                var currentData = this.lastCompressedDataCache.get(cacheKey) || {};
                // 应用增量数据
                return this.applyIncrementalData(compressedData, currentData);
            }
            // 如果不是增量数据，则直接返回
            return compressedData;
        }
        // 如果没有压缩，则直接返回
        if (algorithm === CompressionAlgorithm.NONE) {
            try {
                return typeof compressedData === 'string'
                    ? (compressedData.startsWith('{') || compressedData.startsWith('[')
                        ? JSON.parse(compressedData)
                        : compressedData)
                    : compressedData;
            }
            catch (error) {
                return compressedData;
            }
        }
        // 开始计时
        var startTime = performance.now();
        // 如果启用了字典压缩，则先解压缩字典
        if (this.options.useDictionaryCompression && this.compressionDictionary && compressedData instanceof Uint8Array) {
            try {
                compressedData = this.decompressWithDictionary(compressedData, this.compressionDictionary);
            }
            catch (error) {
                Debug_1.Debug.warn('DataCompressor', '字典解压缩失败:', error);
            }
        }
        // 根据算法解压缩数据
        var decompressedData;
        try {
            switch (algorithm) {
                case CompressionAlgorithm.LZ_STRING:
                    decompressedData = this.decompressWithLZString(compressedData, level);
                    break;
                case CompressionAlgorithm.MSGPACK:
                    decompressedData = this.decompressWithMessagePack(compressedData, level);
                    break;
                case CompressionAlgorithm.CBOR:
                    decompressedData = this.decompressWithCBOR(compressedData, level);
                    break;
                case CompressionAlgorithm.BROTLI:
                    decompressedData = yield this.decompressWithBrotli(compressedData, level);
                    break;
                case CompressionAlgorithm.DEFLATE:
                    decompressedData = yield this.decompressWithDeflate(compressedData, level);
                    break;
                case CompressionAlgorithm.CUSTOM:
                    if (this.options.customDecompressFunction) {
                        decompressedData = this.options.customDecompressFunction(compressedData);
                    }
                    else {
                        throw new Error('Custom decompression function is not defined');
                    }
                    break;
                default:
                    decompressedData = compressedData;
                    break;
            }
            // 如果解压缩后的数据是二进制，则尝试转换为对象
            if (decompressedData instanceof Uint8Array) {
                try {
                    var decoder = new TextDecoder();
                    var jsonString = decoder.decode(decompressedData);
                    if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
                        decompressedData = JSON.parse(jsonString);
                    }
                }
                catch (error) {
                    // 如果转换失败，则保持原样
                }
            }
            // 如果解压缩后的数据是JSON字符串，则解析为对象
            if (typeof decompressedData === 'string' && (decompressedData.startsWith('{') || decompressedData.startsWith('['))) {
                decompressedData = JSON.parse(decompressedData);
            }
        }
        catch (error) {
            Debug_1.Debug.error('DataCompressor', 'Decompression error:', error);
            // 如果解压缩失败，则返回原始数据
            decompressedData = compressedData;
        }
        // 计算解压缩时间
        var endTime = performance.now();
        var decompressionTime = endTime - startTime;
        // 更新统计信息
        this.stats.decompressionCount++;
        this.stats.totalDecompressionTime += decompressionTime;
        return decompressedData;
    };
    /**
     * 使用Brotli解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithBrotli = function (data, level) {
        return __awaiter(this, void 0, void 0, function () {
            var ds_1, writer, decoder, jsonString;
            return __generator(this, function (_a) {
                // 如果浏览器支持DecompressionStream，则使用DecompressionStream
                if (typeof DecompressionStream !== 'undefined') {
                    try {
                        ds_1 = new DecompressionStream('br');
                        writer = ds_1.writable.getWriter();
                        // 写入数据
                        writer.write(data);
                        writer.close();
                        // 读取解压缩后的数据
                        return [2 /*return*/, new Promise(function (resolve, reject) {
                                var reader = ds_1.readable.getReader();
                                var chunks = [];
                                function readChunk() {
                                    reader.read().then(function (_a) {
                                        var done = _a.done, value = _a.value;
                                        if (done) {
                                            // 合并所有块
                                            var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                            var result = new Uint8Array(totalLength);
                                            var offset = 0;
                                            for (var _i = 0, chunks_1 = chunks; _i < chunks_1.length; _i++) {
                                                var chunk = chunks_1[_i];
                                                result.set(chunk, offset);
                                                offset += chunk.length;
                                            }
                                            resolve(result);
                                        }
                                        else {
                                            chunks.push(value);
                                            readChunk();
                                        }
                                    }).catch(reject);
                                }
                                readChunk();
                            })];
                    }
                    catch (error) {
                        Debug_1.Debug.warn('DataCompressor', 'Brotli解压缩失败，使用内置的简单实现:', error);
                    }
                }
                // 如果不支持DecompressionStream，则尝试使用简单的方法
                try {
                    decoder = new TextDecoder();
                    jsonString = decoder.decode(data);
                    if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
                        return [2 /*return*/, new TextEncoder().encode(jsonString)];
                    }
                }
                catch (error) {
                    // 忽略错误
                }
                return [2 /*return*/, data];
            });
        });
    };
    /**
     * 使用Deflate解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithDeflate = function (data, level) {
        return __awaiter(this, void 0, void 0, function () {
            var ds_2, writer, decoder, jsonString;
            return __generator(this, function (_a) {
                // 如果浏览器支持DecompressionStream，则使用DecompressionStream
                if (typeof DecompressionStream !== 'undefined') {
                    try {
                        ds_2 = new DecompressionStream('deflate');
                        writer = ds_2.writable.getWriter();
                        // 写入数据
                        writer.write(data);
                        writer.close();
                        // 读取解压缩后的数据
                        return [2 /*return*/, new Promise(function (resolve, reject) {
                                var reader = ds_2.readable.getReader();
                                var chunks = [];
                                function readChunk() {
                                    reader.read().then(function (_a) {
                                        var done = _a.done, value = _a.value;
                                        if (done) {
                                            // 合并所有块
                                            var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                            var result = new Uint8Array(totalLength);
                                            var offset = 0;
                                            for (var _i = 0, chunks_2 = chunks; _i < chunks_2.length; _i++) {
                                                var chunk = chunks_2[_i];
                                                result.set(chunk, offset);
                                                offset += chunk.length;
                                            }
                                            resolve(result);
                                        }
                                        else {
                                            chunks.push(value);
                                            readChunk();
                                        }
                                    }).catch(reject);
                                }
                                readChunk();
                            })];
                    }
                    catch (error) {
                        Debug_1.Debug.warn('DataCompressor', 'Deflate解压缩失败，使用内置的简单实现:', error);
                    }
                }
                // 如果不支持DecompressionStream，则尝试使用简单的方法
                try {
                    decoder = new TextDecoder();
                    jsonString = decoder.decode(data);
                    if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
                        return [2 /*return*/, new TextEncoder().encode(jsonString)];
                    }
                }
                catch (error) {
                    // 忽略错误
                }
                return [2 /*return*/, data];
            });
        });
    };
    /**
     * 使用字典解压缩
     * @param data 要解压缩的数据
     * @param dictionary 压缩字典
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithDictionary = function (data, dictionary) {
        // 简单的字典解压缩实现
        // 在实际项目中，应该使用更高效的字典解压缩算法
        // 将字典分割为多个短语
        var phrases = [];
        var minPhraseLength = 3;
        var maxPhraseLength = 32;
        for (var i_1 = 0; i_1 < dictionary.length - minPhraseLength; i_1++) {
            for (var length_1 = minPhraseLength; length_1 <= maxPhraseLength && i_1 + length_1 <= dictionary.length; length_1++) {
                phrases.push(dictionary.slice(i_1, i_1 + length_1));
            }
        }
        // 对短语进行排序，优先使用长的短语
        phrases.sort(function (a, b) { return b.length - a.length; });
        // 解压缩数据
        var result = [];
        var i = 0;
        while (i < data.length) {
            // 检查是否是短语引用
            if (data[i] === 0xFF && i + 3 < data.length) {
                // 获取短语索引和长度
                var phraseIndex = (data[i + 1] << 8) | data[i + 2];
                var phraseLength = data[i + 3];
                // 检查短语索引是否有效
                if (phraseIndex < phrases.length) {
                    var phrase = phrases[phraseIndex];
                    // 添加短语
                    for (var j = 0; j < phrase.length; j++) {
                        result.push(phrase[j]);
                    }
                    i += 4;
                }
                else {
                    // 无效的短语索引，添加原始字节
                    result.push(data[i]);
                    i++;
                }
            }
            else {
                // 添加原始字节
                result.push(data[i]);
                i++;
            }
        }
        return new Uint8Array(result);
    };
    /**
     * 选择自适应压缩算法和级别
     * @param dataSize 数据大小
     * @param options 压缩选项
     */
    DataCompressor.prototype.selectAdaptiveCompression = function (dataSize, options) {
        // 根据数据大小选择算法和级别
        if (dataSize < 1024) { // 小于1KB
            options.algorithm = CompressionAlgorithm.LZ_STRING;
            options.level = CompressionLevel.LOW;
        }
        else if (dataSize < 10 * 1024) { // 小于10KB
            options.algorithm = CompressionAlgorithm.LZ_STRING;
            options.level = CompressionLevel.MEDIUM;
        }
        else if (dataSize < 50 * 1024) { // 小于50KB
            options.algorithm = CompressionAlgorithm.MSGPACK;
            options.level = CompressionLevel.MEDIUM;
        }
        else if (dataSize < 100 * 1024) { // 小于100KB
            options.algorithm = CompressionAlgorithm.CBOR;
            options.level = CompressionLevel.MEDIUM;
        }
        else if (dataSize < 500 * 1024) { // 小于500KB
            options.algorithm = CompressionAlgorithm.BROTLI;
            options.level = CompressionLevel.MEDIUM;
        }
        else { // 大于500KB
            options.algorithm = CompressionAlgorithm.BROTLI;
            options.level = CompressionLevel.HIGH;
        }
        // 如果数据大小超过1MB，考虑使用增量压缩
        if (dataSize > 1024 * 1024 && options.incremental.enabled) {
            options.algorithm = CompressionAlgorithm.INCREMENTAL;
        }
        // 如果数据大小超过5MB，启用字典压缩
        if (dataSize > 5 * 1024 * 1024) {
            options.useDictionaryCompression = true;
        }
    };
    /**
     * 使用CBOR压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithCBOR = function (data, level) {
        // 如果已加载CBOR库，则使用CBOR库
        if (this.compressionLibs.cbor) {
            try {
                return this.compressionLibs.cbor.encode(data);
            }
            catch (error) {
                Debug_1.Debug.warn('DataCompressor', 'CBOR压缩失败，使用内置的简单实现:', error);
            }
        }
        // 使用简单的实现
        return this.convertToBinary(data);
    };
    /**
     * 使用CBOR解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithCBOR = function (data, level) {
        // 如果已加载CBOR库，则使用CBOR库
        if (this.compressionLibs.cbor) {
            try {
                return this.compressionLibs.cbor.decode(data);
            }
            catch (error) {
                Debug_1.Debug.warn('DataCompressor', 'CBOR解压缩失败，使用内置的简单实现:', error);
            }
        }
        // 使用简单的实现
        var decoder = new TextDecoder();
        var jsonString = decoder.decode(data);
        try {
            return JSON.parse(jsonString);
        }
        catch (error) {
            return jsonString;
        }
    };
    /**
     * 使用Brotli压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithBrotli = function (data, level) {
        return __awaiter(this, void 0, void 0, function () {
            var cs_1, writer;
            return __generator(this, function (_a) {
                // 如果浏览器支持CompressionStream，则使用CompressionStream
                if (typeof CompressionStream !== 'undefined') {
                    try {
                        cs_1 = new CompressionStream('br');
                        writer = cs_1.writable.getWriter();
                        // 写入数据
                        writer.write(data);
                        writer.close();
                        // 读取压缩后的数据
                        return [2 /*return*/, new Promise(function (resolve, reject) {
                                var reader = cs_1.readable.getReader();
                                var chunks = [];
                                function readChunk() {
                                    reader.read().then(function (_a) {
                                        var done = _a.done, value = _a.value;
                                        if (done) {
                                            // 合并所有块
                                            var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                            var result = new Uint8Array(totalLength);
                                            var offset = 0;
                                            for (var _i = 0, chunks_3 = chunks; _i < chunks_3.length; _i++) {
                                                var chunk = chunks_3[_i];
                                                result.set(chunk, offset);
                                                offset += chunk.length;
                                            }
                                            resolve(result);
                                        }
                                        else {
                                            chunks.push(value);
                                            readChunk();
                                        }
                                    }).catch(reject);
                                }
                                readChunk();
                            })];
                    }
                    catch (error) {
                        Debug_1.Debug.warn('DataCompressor', 'Brotli压缩失败，使用内置的简单实现:', error);
                    }
                }
                // 如果不支持CompressionStream，则使用简单的RLE压缩
                return [2 /*return*/, this.compressWithRLE(data)];
            });
        });
    };
    /**
     * 使用Deflate压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithDeflate = function (data, level) {
        return __awaiter(this, void 0, void 0, function () {
            var cs_2, writer;
            return __generator(this, function (_a) {
                // 如果浏览器支持CompressionStream，则使用CompressionStream
                if (typeof CompressionStream !== 'undefined') {
                    try {
                        cs_2 = new CompressionStream('deflate');
                        writer = cs_2.writable.getWriter();
                        // 写入数据
                        writer.write(data);
                        writer.close();
                        // 读取压缩后的数据
                        return [2 /*return*/, new Promise(function (resolve, reject) {
                                var reader = cs_2.readable.getReader();
                                var chunks = [];
                                function readChunk() {
                                    reader.read().then(function (_a) {
                                        var done = _a.done, value = _a.value;
                                        if (done) {
                                            // 合并所有块
                                            var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                            var result = new Uint8Array(totalLength);
                                            var offset = 0;
                                            for (var _i = 0, chunks_4 = chunks; _i < chunks_4.length; _i++) {
                                                var chunk = chunks_4[_i];
                                                result.set(chunk, offset);
                                                offset += chunk.length;
                                            }
                                            resolve(result);
                                        }
                                        else {
                                            chunks.push(value);
                                            readChunk();
                                        }
                                    }).catch(reject);
                                }
                                readChunk();
                            })];
                    }
                    catch (error) {
                        Debug_1.Debug.warn('DataCompressor', 'Deflate压缩失败，使用内置的简单实现:', error);
                    }
                }
                // 如果不支持CompressionStream，则使用简单的RLE压缩
                return [2 /*return*/, this.compressWithRLE(data)];
            });
        });
    };
    /**
     * 使用RLE压缩
     * @param data 要压缩的数据
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithRLE = function (data) {
        if (!data || data.length < 2) {
            return data;
        }
        var result = [];
        var count = 1;
        var current = data[0];
        for (var i = 1; i < data.length; i++) {
            if (data[i] === current) {
                count++;
            }
            else {
                // 如果重复次数大于3，则使用RLE编码
                if (count > 3) {
                    result.push(0); // 标记为RLE编码
                    result.push(count);
                    result.push(current);
                }
                else {
                    // 否则直接添加原始数据
                    for (var j = 0; j < count; j++) {
                        result.push(current);
                    }
                }
                current = data[i];
                count = 1;
            }
        }
        // 处理最后一组数据
        if (count > 3) {
            result.push(0); // 标记为RLE编码
            result.push(count);
            result.push(current);
        }
        else {
            for (var j = 0; j < count; j++) {
                result.push(current);
            }
        }
        return new Uint8Array(result);
    };
    /**
     * 使用字典压缩
     * @param data 要压缩的数据
     * @param dictionary 压缩字典
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithDictionary = function (data, dictionary) {
        // 简单的字典压缩实现
        // 在实际项目中，应该使用更高效的字典压缩算法
        // 将字典分割为多个短语
        var phrases = [];
        var minPhraseLength = 3;
        var maxPhraseLength = 32;
        for (var i_2 = 0; i_2 < dictionary.length - minPhraseLength; i_2++) {
            for (var length_2 = minPhraseLength; length_2 <= maxPhraseLength && i_2 + length_2 <= dictionary.length; length_2++) {
                phrases.push(dictionary.slice(i_2, i_2 + length_2));
            }
        }
        // 对短语进行排序，优先使用长的短语
        phrases.sort(function (a, b) { return b.length - a.length; });
        // 压缩数据
        var result = [];
        var i = 0;
        while (i < data.length) {
            var matched = false;
            // 查找匹配的短语
            for (var j = 0; j < phrases.length; j++) {
                var phrase = phrases[j];
                if (i + phrase.length <= data.length) {
                    var match = true;
                    for (var k = 0; k < phrase.length; k++) {
                        if (data[i + k] !== phrase[k]) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        // 添加短语索引和长度
                        result.push(0xFF); // 标记为短语引用
                        result.push(j >> 8); // 高8位
                        result.push(j & 0xFF); // 低8位
                        result.push(phrase.length);
                        i += phrase.length;
                        matched = true;
                        break;
                    }
                }
            }
            if (!matched) {
                // 添加原始字节
                result.push(data[i]);
                i++;
            }
        }
        return new Uint8Array(result);
    };
    /**
     * 使用LZ-String压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithLZString = function (data, level) {
        // 在实际项目中，应该使用LZ-String库
        // 这里使用简单的模拟实现
        // 根据压缩级别选择压缩方法
        switch (level) {
            case CompressionLevel.LOW:
                // 简单的RLE压缩
                return this.simpleRLECompress(data);
            case CompressionLevel.MEDIUM:
            case CompressionLevel.HIGH:
            case CompressionLevel.HIGHEST:
                // 使用更高级的压缩算法
                // 在实际项目中，应该使用LZ-String库的不同压缩方法
                return this.simpleRLECompress(data);
            default:
                return data;
        }
    };
    /**
     * 使用LZ-String解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithLZString = function (data, level) {
        // 在实际项目中，应该使用LZ-String库
        // 这里使用简单的模拟实现
        // 根据压缩级别选择解压缩方法
        switch (level) {
            case CompressionLevel.LOW:
                // 简单的RLE解压缩
                return this.simpleRLEDecompress(data);
            case CompressionLevel.MEDIUM:
            case CompressionLevel.HIGH:
            case CompressionLevel.HIGHEST:
                // 使用更高级的解压缩算法
                // 在实际项目中，应该使用LZ-String库的不同解压缩方法
                return this.simpleRLEDecompress(data);
            default:
                return data;
        }
    };
    /**
     * 使用MessagePack压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.compressWithMessagePack = function (data, level) {
        // 在实际项目中，应该使用MessagePack库
        // 这里使用简单的模拟实现
        // 将数据转换为JSON字符串
        var jsonString = JSON.stringify(data);
        // 转换为Uint8Array
        var encoder = new TextEncoder();
        return encoder.encode(jsonString);
    };
    /**
     * 使用MessagePack解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.decompressWithMessagePack = function (data, level) {
        // 在实际项目中，应该使用MessagePack库
        // 这里使用简单的模拟实现
        // 如果是Uint8Array，则转换为字符串
        var jsonString;
        if (data instanceof Uint8Array) {
            var decoder = new TextDecoder();
            jsonString = decoder.decode(data);
        }
        else {
            jsonString = data;
        }
        // 解析JSON字符串
        try {
            return JSON.parse(jsonString);
        }
        catch (error) {
            return jsonString;
        }
    };
    /**
     * 简单的RLE压缩
     * @param data 要压缩的数据
     * @returns 压缩后的数据
     */
    DataCompressor.prototype.simpleRLECompress = function (data) {
        if (!data || data.length < 2) {
            return data;
        }
        var result = '';
        var count = 1;
        var current = data[0];
        for (var i = 1; i < data.length; i++) {
            if (data[i] === current) {
                count++;
            }
            else {
                result += (count > 3 ? "".concat(count).concat(current) : current.repeat(count));
                current = data[i];
                count = 1;
            }
        }
        result += (count > 3 ? "".concat(count).concat(current) : current.repeat(count));
        return result;
    };
    /**
     * 简单的RLE解压缩
     * @param data 要解压缩的数据
     * @returns 解压缩后的数据
     */
    DataCompressor.prototype.simpleRLEDecompress = function (data) {
        if (!data || data.length < 2) {
            return data;
        }
        var result = '';
        var i = 0;
        while (i < data.length) {
            // 检查是否是数字
            var countStart = i;
            while (i < data.length && /\d/.test(data[i])) {
                i++;
            }
            if (i > countStart && i < data.length) {
                // 找到了数字和字符
                var count = parseInt(data.substring(countStart, i), 10);
                var char = data[i];
                result += char.repeat(count);
                i++;
            }
            else {
                // 没有找到数字，直接添加字符
                result += data[i];
                i++;
            }
        }
        return result;
    };
    /**
     * 获取压缩统计信息
     * @returns 统计信息
     */
    DataCompressor.prototype.getStats = function () {
        var _a = this.stats, compressionCount = _a.compressionCount, decompressionCount = _a.decompressionCount, totalOriginalSize = _a.totalOriginalSize, totalCompressedSize = _a.totalCompressedSize, totalCompressionTime = _a.totalCompressionTime, totalDecompressionTime = _a.totalDecompressionTime;
        // 计算平均压缩比率
        var averageRatio = compressionCount > 0 ? totalCompressedSize / totalOriginalSize : 1;
        // 计算平均压缩时间
        var averageCompressionTime = compressionCount > 0 ? totalCompressionTime / compressionCount : 0;
        // 计算平均解压缩时间
        var averageDecompressionTime = decompressionCount > 0 ? totalDecompressionTime / decompressionCount : 0;
        // 计算节省的字节数
        var savedBytes = totalOriginalSize - totalCompressedSize;
        return {
            compressionCount: compressionCount,
            decompressionCount: decompressionCount,
            totalOriginalSize: totalOriginalSize,
            totalCompressedSize: totalCompressedSize,
            totalCompressionTime: totalCompressionTime,
            totalDecompressionTime: totalDecompressionTime,
            averageRatio: averageRatio,
            averageCompressionTime: averageCompressionTime,
            averageDecompressionTime: averageDecompressionTime,
            savedBytes: savedBytes,
            savingsPercentage: compressionCount > 0 ? (savedBytes / totalOriginalSize) * 100 : 0,
        };
    };
    /**
     * 重置统计信息
     */
    DataCompressor.prototype.resetStats = function () {
        this.stats = {
            compressionCount: 0,
            decompressionCount: 0,
            totalOriginalSize: 0,
            totalCompressedSize: 0,
            totalCompressionTime: 0,
            totalDecompressionTime: 0,
        };
    };
    /**
     * 设置压缩选项
     * @param options 压缩选项
     */
    DataCompressor.prototype.setOptions = function (options) {
        this.options = __assign(__assign({}, this.options), options);
    };
    /**
     * 获取压缩选项
     * @returns 压缩选项
     */
    DataCompressor.prototype.getOptions = function () {
        return __assign({}, this.options);
    };
    return DataCompressor;
}());
exports.DataCompressor = DataCompressor;
