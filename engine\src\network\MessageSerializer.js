"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageSerializer = exports.CompressionLevel = void 0;
var Debug_1 = require("../utils/Debug");
/**
 * 压缩级别
 */
var CompressionLevel;
(function (CompressionLevel) {
    /** 不压缩 */
    CompressionLevel[CompressionLevel["NONE"] = 0] = "NONE";
    /** 快速压缩 */
    CompressionLevel[CompressionLevel["FAST"] = 1] = "FAST";
    /** 标准压缩 */
    CompressionLevel[CompressionLevel["STANDARD"] = 2] = "STANDARD";
    /** 最大压缩 */
    CompressionLevel[CompressionLevel["MAX"] = 3] = "MAX";
})(CompressionLevel || (exports.CompressionLevel = CompressionLevel = {}));
/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
var MessageSerializer = /** @class */ (function () {
    /**
     * 创建消息序列化器
     * @param useCompression 是否使用压缩
     * @param compressionLevel 压缩级别
     * @param enableCache 是否启用缓存
     */
    function MessageSerializer(useCompression, compressionLevel, enableCache) {
        if (useCompression === void 0) { useCompression = true; }
        if (compressionLevel === void 0) { compressionLevel = CompressionLevel.STANDARD; }
        if (enableCache === void 0) { enableCache = true; }
        /** 消息缓存 */
        this.messageCache = new Map();
        /** 缓存大小限制 */
        this.cacheSizeLimit = 100;
        /** 是否启用缓存 */
        this.enableCache = true;
        this.useCompression = useCompression;
        this.compressionLevel = compressionLevel;
        this.enableCache = enableCache;
        this.encoder = new TextEncoder();
        this.decoder = new TextDecoder();
    }
    /**
     * 序列化消息
     * @param message 消息对象
     * @returns 序列化后的数据
     */
    MessageSerializer.prototype.serialize = function (message) {
        try {
            // 将消息转换为JSON字符串
            var json = JSON.stringify(message);
            // 计算缓存键
            var cacheKey = this.enableCache ? this.calculateCacheKey(message) : '';
            // 检查缓存
            if (this.enableCache && cacheKey && this.messageCache.has(cacheKey)) {
                return this.messageCache.get(cacheKey);
            }
            if (this.useCompression) {
                // 将JSON字符串转换为Uint8Array
                var data = this.encoder.encode(json);
                // 压缩数据
                var compressed = this.compress(data);
                // 添加到缓存
                if (this.enableCache && cacheKey) {
                    this.addToCache(cacheKey, compressed);
                }
                return compressed;
            }
            else {
                // 不使用压缩，直接返回JSON字符串
                return json;
            }
        }
        catch (error) {
            Debug_1.Debug.error('MessageSerializer', 'Failed to serialize message:', error);
            throw error;
        }
    };
    /**
     * 反序列化消息
     * @param data 序列化后的数据
     * @returns 消息对象
     */
    MessageSerializer.prototype.deserialize = function (data) {
        try {
            var json = void 0;
            if (data instanceof ArrayBuffer) {
                // 如果是ArrayBuffer，则假设是压缩数据
                // 解压缩数据
                var decompressed = this.decompress(data);
                // 将Uint8Array转换为JSON字符串
                json = this.decoder.decode(decompressed);
            }
            else {
                // 如果是字符串，则直接使用
                json = data;
            }
            // 将JSON字符串转换为消息对象
            return JSON.parse(json);
        }
        catch (error) {
            Debug_1.Debug.error('MessageSerializer', 'Failed to deserialize message:', error);
            throw error;
        }
    };
    /**
     * 压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    MessageSerializer.prototype.compress = function (data) {
        try {
            // 使用内置的CompressionStream API进行压缩（如果浏览器支持）
            if (typeof CompressionStream !== 'undefined') {
                // 创建压缩流
                var cs = new CompressionStream('deflate');
                var writer = cs.writable.getWriter();
                var reader_1 = cs.readable.getReader();
                // 写入数据
                writer.write(data);
                writer.close();
                // 读取压缩后的数据
                return new Promise(function (resolve, reject) {
                    var chunks = [];
                    function readChunk() {
                        reader_1.read().then(function (_a) {
                            var done = _a.done, value = _a.value;
                            if (done) {
                                // 合并所有块
                                var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                var result = new Uint8Array(totalLength);
                                var offset = 0;
                                for (var _i = 0, chunks_1 = chunks; _i < chunks_1.length; _i++) {
                                    var chunk = chunks_1[_i];
                                    result.set(chunk, offset);
                                    offset += chunk.length;
                                }
                                resolve(result.buffer);
                            }
                            else {
                                chunks.push(value);
                                readChunk();
                            }
                        }).catch(reject);
                    }
                    readChunk();
                });
            }
            else {
                // 如果浏览器不支持CompressionStream，则使用简单的RLE压缩
                return this.compressRLE(data);
            }
        }
        catch (error) {
            Debug_1.Debug.warn('MessageSerializer', 'Compression failed, using uncompressed data:', error);
            return data.buffer;
        }
    };
    /**
     * 使用RLE算法压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    MessageSerializer.prototype.compressRLE = function (data) {
        if (data.length === 0) {
            return new ArrayBuffer(0);
        }
        var result = [];
        var currentByte = data[0];
        var count = 1;
        for (var i = 1; i < data.length; i++) {
            if (data[i] === currentByte && count < 255) {
                count++;
            }
            else {
                result.push(count);
                result.push(currentByte);
                currentByte = data[i];
                count = 1;
            }
        }
        result.push(count);
        result.push(currentByte);
        var compressed = new Uint8Array(result);
        // 只有当压缩后的大小小于原始大小时才使用压缩数据
        if (compressed.length < data.length) {
            // 添加标记以表示这是RLE压缩数据
            var output = new Uint8Array(compressed.length + 1);
            output[0] = 1; // 1表示RLE压缩
            output.set(compressed, 1);
            return output.buffer;
        }
        else {
            // 否则使用原始数据
            var output = new Uint8Array(data.length + 1);
            output[0] = 0; // 0表示未压缩
            output.set(data, 1);
            return output.buffer;
        }
    };
    /**
     * 解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    MessageSerializer.prototype.decompress = function (data) {
        try {
            var dataView = new Uint8Array(data);
            // 检查压缩类型
            var compressionType = dataView[0];
            if (compressionType === 0) {
                // 未压缩数据
                return dataView.slice(1);
            }
            else if (compressionType === 1) {
                // RLE压缩数据
                return this.decompressRLE(dataView.slice(1));
            }
            else {
                // 使用内置的DecompressionStream API进行解压缩（如果浏览器支持）
                if (typeof DecompressionStream !== 'undefined') {
                    // 创建解压缩流
                    var ds = new DecompressionStream('deflate');
                    var writer = ds.writable.getWriter();
                    var reader_2 = ds.readable.getReader();
                    // 写入数据
                    writer.write(dataView);
                    writer.close();
                    // 读取解压缩后的数据
                    return new Promise(function (resolve, reject) {
                        var chunks = [];
                        function readChunk() {
                            reader_2.read().then(function (_a) {
                                var done = _a.done, value = _a.value;
                                if (done) {
                                    // 合并所有块
                                    var totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                                    var result = new Uint8Array(totalLength);
                                    var offset = 0;
                                    for (var _i = 0, chunks_2 = chunks; _i < chunks_2.length; _i++) {
                                        var chunk = chunks_2[_i];
                                        result.set(chunk, offset);
                                        offset += chunk.length;
                                    }
                                    resolve(result);
                                }
                                else {
                                    chunks.push(value);
                                    readChunk();
                                }
                            }).catch(reject);
                        }
                        readChunk();
                    });
                }
                else {
                    // 如果无法识别压缩类型或浏览器不支持，则返回原始数据
                    return dataView;
                }
            }
        }
        catch (error) {
            Debug_1.Debug.warn('MessageSerializer', 'Decompression failed, using raw data:', error);
            return new Uint8Array(data);
        }
    };
    /**
     * 使用RLE算法解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    MessageSerializer.prototype.decompressRLE = function (data) {
        if (data.length === 0) {
            return new Uint8Array(0);
        }
        var result = [];
        for (var i = 0; i < data.length; i += 2) {
            var count = data[i];
            var value = data[i + 1];
            for (var j = 0; j < count; j++) {
                result.push(value);
            }
        }
        return new Uint8Array(result);
    };
    /**
     * 计算缓存键
     * @param message 消息对象
     * @returns 缓存键
     */
    MessageSerializer.prototype.calculateCacheKey = function (message) {
        // 对于心跳消息，不使用缓存
        if (message.type === 'heartbeat') {
            return '';
        }
        // 对于包含时间戳的消息，移除时间戳再计算缓存键
        var timestamp = message.timestamp, rest = __rest(message, ["timestamp"]);
        // 计算缓存键
        return message.type + '_' + JSON.stringify(rest);
    };
    /**
     * 添加到缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    MessageSerializer.prototype.addToCache = function (key, value) {
        // 如果缓存已满，则移除最早添加的项
        if (this.messageCache.size >= this.cacheSizeLimit) {
            var firstKey = this.messageCache.keys().next().value;
            this.messageCache.delete(firstKey);
        }
        // 添加到缓存
        this.messageCache.set(key, value);
    };
    /**
     * 清除缓存
     */
    MessageSerializer.prototype.clearCache = function () {
        this.messageCache.clear();
    };
    /**
     * 设置是否使用压缩
     * @param useCompression 是否使用压缩
     */
    MessageSerializer.prototype.setUseCompression = function (useCompression) {
        this.useCompression = useCompression;
    };
    /**
     * 是否使用压缩
     * @returns 是否使用压缩
     */
    MessageSerializer.prototype.isUsingCompression = function () {
        return this.useCompression;
    };
    /**
     * 设置压缩级别
     * @param level 压缩级别
     */
    MessageSerializer.prototype.setCompressionLevel = function (level) {
        this.compressionLevel = level;
    };
    /**
     * 获取压缩级别
     * @returns 压缩级别
     */
    MessageSerializer.prototype.getCompressionLevel = function () {
        return this.compressionLevel;
    };
    /**
     * 设置是否启用缓存
     * @param enable 是否启用缓存
     */
    MessageSerializer.prototype.setEnableCache = function (enable) {
        this.enableCache = enable;
    };
    /**
     * 是否启用缓存
     * @returns 是否启用缓存
     */
    MessageSerializer.prototype.isEnableCache = function () {
        return this.enableCache;
    };
    /**
     * 设置缓存大小限制
     * @param limit 缓存大小限制
     */
    MessageSerializer.prototype.setCacheSizeLimit = function (limit) {
        this.cacheSizeLimit = limit;
    };
    /**
     * 获取缓存大小限制
     * @returns 缓存大小限制
     */
    MessageSerializer.prototype.getCacheSizeLimit = function () {
        return this.cacheSizeLimit;
    };
    return MessageSerializer;
}());
exports.MessageSerializer = MessageSerializer;
