"use strict";
/**
 * 媒体流类型定义
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaStreamManager = exports.MediaStreamType = void 0;
var MediaStreamType;
(function (MediaStreamType) {
    /** 音频流 */
    MediaStreamType["AUDIO"] = "audio";
    /** 视频流 */
    MediaStreamType["VIDEO"] = "video";
    /** 音视频流 */
    MediaStreamType["AUDIO_VIDEO"] = "audioVideo";
    /** 屏幕共享 */
    MediaStreamType["SCREEN_SHARE"] = "screenShare";
    /** 摄像头 */
    MediaStreamType["CAMERA"] = "camera";
    /** 麦克风 */
    MediaStreamType["MICROPHONE"] = "microphone";
})(MediaStreamType || (exports.MediaStreamType = MediaStreamType = {}));
var MediaStreamManager = /** @class */ (function () {
    function MediaStreamManager() {
        this.streams = new Map();
    }
    MediaStreamManager.prototype.createStream = function (config) {
        return __awaiter(this, void 0, void 0, function () {
            var constraints, _a, stream, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 8, , 9]);
                        constraints = {};
                        _a = config.type;
                        switch (_a) {
                            case MediaStreamType.AUDIO: return [3 /*break*/, 1];
                            case MediaStreamType.VIDEO: return [3 /*break*/, 2];
                            case MediaStreamType.AUDIO_VIDEO: return [3 /*break*/, 3];
                            case MediaStreamType.SCREEN_SHARE: return [3 /*break*/, 4];
                        }
                        return [3 /*break*/, 6];
                    case 1:
                        constraints.audio = config.audioConstraints || true;
                        return [3 /*break*/, 6];
                    case 2:
                        constraints.video = config.videoConstraints || true;
                        return [3 /*break*/, 6];
                    case 3:
                        constraints.audio = config.audioConstraints || true;
                        constraints.video = config.videoConstraints || true;
                        return [3 /*break*/, 6];
                    case 4: return [4 /*yield*/, navigator.mediaDevices.getDisplayMedia({
                            video: true,
                            audio: config.audio || false
                        })];
                    case 5: 
                    // @ts-ignore - getDisplayMedia 可能不在所有浏览器中可用
                    return [2 /*return*/, _b.sent()];
                    case 6: return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];
                    case 7:
                        stream = _b.sent();
                        return [2 /*return*/, stream];
                    case 8:
                        error_1 = _b.sent();
                        console.error('创建媒体流失败:', error_1);
                        return [2 /*return*/, null];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    MediaStreamManager.prototype.addStream = function (id, stream) {
        this.streams.set(id, stream);
    };
    MediaStreamManager.prototype.getStream = function (id) {
        return this.streams.get(id);
    };
    MediaStreamManager.prototype.removeStream = function (id) {
        var stream = this.streams.get(id);
        if (stream) {
            stream.getTracks().forEach(function (track) { return track.stop(); });
            this.streams.delete(id);
        }
    };
    MediaStreamManager.prototype.stopAllStreams = function () {
        var _this = this;
        this.streams.forEach(function (stream, id) {
            _this.removeStream(id);
        });
    };
    return MediaStreamManager;
}());
exports.MediaStreamManager = MediaStreamManager;
