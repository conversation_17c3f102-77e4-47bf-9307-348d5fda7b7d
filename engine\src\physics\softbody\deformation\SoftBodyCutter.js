"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftBodyCutter = void 0;
/**
 * 软体切割系统
 * 实现软体的切割和撕裂功能
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var SoftBodyComponent_1 = require("../SoftBodyComponent");
/**
 * 软体切割系统
 * 提供软体的切割和撕裂功能
 */
var SoftBodyCutter = /** @class */ (function () {
    /**
     * 创建软体切割系统
     * @param options 软体切割系统选项
     */
    function SoftBodyCutter(options) {
        if (options === void 0) { options = {}; }
        this.enabled = options.enabled !== undefined ? options.enabled : true;
        this.tearingEnabled = options.tearingEnabled !== undefined ? options.tearingEnabled : true;
        this.tearingThreshold = options.tearingThreshold || 1.5; // 默认拉伸1.5倍时撕裂
    }
    /**
     * 使用平面切割软体
     * @param softBody 软体组件
     * @param plane 切割平面
     * @returns 是否成功切割
     */
    SoftBodyCutter.prototype.cutWithPlane = function (softBody, plane) {
        if (!this.enabled || !softBody.isInitialized())
            return false;
        // 获取粒子数组
        var particles = softBody.getParticles();
        if (!particles)
            return false;
        // 创建平面法线和点的CANNON向量
        var normal = new CANNON.Vec3(plane.normal.x, plane.normal.y, plane.normal.z);
        var point = new CANNON.Vec3(plane.point.x, plane.point.y, plane.point.z);
        // 标记每个粒子在平面的哪一侧
        var particleSides = [];
        for (var _i = 0, particles_1 = particles; _i < particles_1.length; _i++) {
            var particle = particles_1[_i];
            // 计算粒子到平面的有向距离
            var toParticle = new CANNON.Vec3();
            toParticle.copy(particle.position);
            toParticle.vsub(point, toParticle);
            // 点积确定在哪一侧（正值在正面，负值在背面）
            var side = normal.dot(toParticle) >= 0;
            particleSides.push(side);
        }
        // 根据软体类型执行不同的切割策略
        switch (softBody.getType()) {
            case SoftBodyComponent_1.SoftBodyType.CLOTH:
                return this.cutCloth(softBody, particleSides);
            case SoftBodyComponent_1.SoftBodyType.ROPE:
                return this.cutRope(softBody, particleSides);
            case SoftBodyComponent_1.SoftBodyType.VOLUME:
            case SoftBodyComponent_1.SoftBodyType.BALLOON:
            case SoftBodyComponent_1.SoftBodyType.JELLY:
                return this.cutVolume(softBody, particleSides);
            default:
                return false;
        }
    };
    /**
     * 使用射线切割软体
     * @param softBody 软体组件
     * @param ray 切割射线
     * @returns 是否成功切割
     */
    SoftBodyCutter.prototype.cutWithRay = function (softBody, ray) {
        if (!this.enabled || !softBody.isInitialized())
            return false;
        // 创建THREE射线
        var threeRay = new THREE.Ray(ray.origin, ray.direction.normalize());
        // 获取粒子数组
        var particles = softBody.getParticles();
        if (!particles)
            return false;
        // 找出被射线穿过的约束
        var cutConstraints = [];
        // 获取约束数组
        var constraints = softBody.getConstraints();
        if (!constraints)
            return false;
        // 检查每个约束是否被射线穿过
        for (var i = 0; i < constraints.length; i++) {
            var constraint = constraints[i];
            var particleA = particles[constraint.particleA];
            var particleB = particles[constraint.particleB];
            // 创建线段
            var segmentStart = new THREE.Vector3(particleA.getPosition().x, particleA.getPosition().y, particleA.getPosition().z);
            var segmentEnd = new THREE.Vector3(particleB.getPosition().x, particleB.getPosition().y, particleB.getPosition().z);
            // 计算射线与线段的最近点
            var closestPoint = new THREE.Vector3();
            var lineSegment = new THREE.Line3(segmentStart, segmentEnd);
            lineSegment.closestPointToPoint(ray.origin, true, closestPoint);
            // 计算最近点到射线起点的距离
            var distance = ray.origin.distanceTo(closestPoint);
            // 如果距离小于阈值，认为射线穿过了约束
            var threshold = 0.1; // 可调整的阈值
            if (distance < threshold && distance < ray.length) {
                cutConstraints.push(i);
            }
        }
        // 如果找到要切割的约束，执行切割
        if (cutConstraints.length > 0) {
            return softBody.removeConstraints(cutConstraints);
        }
        return false;
    };
    /**
     * 检查软体是否需要撕裂
     * @param softBody 软体组件
     * @returns 是否发生撕裂
     */
    SoftBodyCutter.prototype.checkTearing = function (softBody) {
        if (!this.enabled || !this.tearingEnabled || !softBody.isInitialized())
            return false;
        // 获取约束数组
        var constraints = softBody.getConstraints();
        if (!constraints)
            return false;
        // 获取粒子数组
        var particles = softBody.getParticles();
        if (!particles)
            return false;
        // 检查每个约束是否超过撕裂阈值
        var tornConstraints = [];
        for (var i = 0; i < constraints.length; i++) {
            var constraint = constraints[i];
            var particleA = particles[constraint.particleA];
            var particleB = particles[constraint.particleB];
            // 计算当前长度
            var currentLength = particleA.position.distanceTo(particleB.position);
            // 计算拉伸比例
            var stretchRatio = currentLength / constraint.restLength;
            // 如果超过撕裂阈值，添加到撕裂列表
            if (stretchRatio > this.tearingThreshold) {
                tornConstraints.push(i);
            }
        }
        // 如果有约束需要撕裂，执行撕裂
        if (tornConstraints.length > 0) {
            return softBody.removeConstraints(tornConstraints);
        }
        return false;
    };
    /**
     * 切割布料
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    SoftBodyCutter.prototype.cutCloth = function (softBody, particleSides) {
        // 获取约束数组
        var constraints = softBody.getConstraints();
        if (!constraints)
            return false;
        // 找出跨越平面的约束
        var cutConstraints = [];
        for (var i = 0; i < constraints.length; i++) {
            var constraint = constraints[i];
            var sideA = particleSides[constraint.particleA];
            var sideB = particleSides[constraint.particleB];
            // 如果约束的两个粒子在平面的不同侧，切割该约束
            if (sideA !== sideB) {
                cutConstraints.push(i);
            }
        }
        // 如果找到要切割的约束，执行切割
        if (cutConstraints.length > 0) {
            return softBody.removeConstraints(cutConstraints);
        }
        return false;
    };
    /**
     * 切割绳索
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    SoftBodyCutter.prototype.cutRope = function (softBody, particleSides) {
        // 对于绳索，只需要切割一个约束即可分离
        // 获取约束数组
        var constraints = softBody.getConstraints();
        if (!constraints)
            return false;
        // 找出跨越平面的约束
        for (var i = 0; i < constraints.length; i++) {
            var constraint = constraints[i];
            var sideA = particleSides[constraint.particleA];
            var sideB = particleSides[constraint.particleB];
            // 如果约束的两个粒子在平面的不同侧，切割该约束
            if (sideA !== sideB) {
                return softBody.removeConstraints([i]);
            }
        }
        return false;
    };
    /**
     * 切割体积软体
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    SoftBodyCutter.prototype.cutVolume = function (softBody, particleSides) {
        // 获取约束数组
        var constraints = softBody.getConstraints();
        if (!constraints)
            return false;
        // 找出跨越平面的约束
        var cutConstraints = [];
        for (var i = 0; i < constraints.length; i++) {
            var constraint = constraints[i];
            var sideA = particleSides[constraint.particleA];
            var sideB = particleSides[constraint.particleB];
            // 如果约束的两个粒子在平面的不同侧，切割该约束
            if (sideA !== sideB) {
                cutConstraints.push(i);
            }
        }
        // 如果找到要切割的约束，执行切割
        if (cutConstraints.length > 0) {
            return softBody.removeConstraints(cutConstraints);
        }
        return false;
    };
    /**
     * 启用切割
     */
    SoftBodyCutter.prototype.enable = function () {
        this.enabled = true;
    };
    /**
     * 禁用切割
     */
    SoftBodyCutter.prototype.disable = function () {
        this.enabled = false;
    };
    /**
     * 启用撕裂
     */
    SoftBodyCutter.prototype.enableTearing = function () {
        this.tearingEnabled = true;
    };
    /**
     * 禁用撕裂
     */
    SoftBodyCutter.prototype.disableTearing = function () {
        this.tearingEnabled = false;
    };
    /**
     * 设置撕裂阈值
     * @param threshold 撕裂阈值
     */
    SoftBodyCutter.prototype.setTearingThreshold = function (threshold) {
        this.tearingThreshold = threshold;
    };
    return SoftBodyCutter;
}());
exports.SoftBodyCutter = SoftBodyCutter;
