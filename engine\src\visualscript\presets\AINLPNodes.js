"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAINLPNodes = exports.TextSummaryNode = exports.NamedEntityRecognitionNode = exports.TextClassificationNode = void 0;
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var AINLPSystem_1 = require("../../ai/AINLPSystem");
/**
 * 文本分类节点
 * 对文本进行分类
 */
var TextClassificationNode = /** @class */ (function (_super) {
    __extends(TextClassificationNode, _super);
    function TextClassificationNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    TextClassificationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要分类的文本',
            defaultValue: ''
        });
        this.addInput({
            name: 'categories',
            type: Node_1.SocketType.DATA,
            dataType: 'array',
            direction: Node_1.SocketDirection.INPUT,
            description: '可选的分类类别',
            defaultValue: []
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分类成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分类失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'category',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分类结果'
        });
        this.addOutput({
            name: 'confidence',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '置信度'
        });
        this.addOutput({
            name: 'allCategories',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '所有分类结果及置信度'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    TextClassificationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var text, categories, nlpSystem, result, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        text = this.getInputValue('text');
                        categories = this.getInputValue('categories');
                        // 检查输入值是否有效
                        if (!text) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        nlpSystem = this.graph.getWorld().getSystem(AINLPSystem_1.AINLPSystem);
                        if (!nlpSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, nlpSystem.classifyText(text, { categories: categories })];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('category', result.category);
                            this.setOutputValue('confidence', result.confidence);
                            this.setOutputValue('allCategories', result.allCategories);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('文本分类失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return TextClassificationNode;
}(AsyncNode_1.AsyncNode));
exports.TextClassificationNode = TextClassificationNode;
/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
var NamedEntityRecognitionNode = /** @class */ (function (_super) {
    __extends(NamedEntityRecognitionNode, _super);
    function NamedEntityRecognitionNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    NamedEntityRecognitionNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要识别的文本',
            defaultValue: ''
        });
        this.addInput({
            name: 'entityTypes',
            type: Node_1.SocketType.DATA,
            dataType: 'array',
            direction: Node_1.SocketDirection.INPUT,
            description: '要识别的实体类型',
            defaultValue: []
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '识别成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '识别失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'entities',
            type: Node_1.SocketType.DATA,
            dataType: 'array',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '识别到的实体'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    NamedEntityRecognitionNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var text, entityTypes, nlpSystem, result, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        text = this.getInputValue('text');
                        entityTypes = this.getInputValue('entityTypes');
                        // 检查输入值是否有效
                        if (!text) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        nlpSystem = this.graph.getWorld().getSystem(AINLPSystem_1.AINLPSystem);
                        if (!nlpSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, nlpSystem.recognizeEntities(text, { entityTypes: entityTypes })];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('entities', result.entities);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('命名实体识别失败:', error_2);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return NamedEntityRecognitionNode;
}(AsyncNode_1.AsyncNode));
exports.NamedEntityRecognitionNode = NamedEntityRecognitionNode;
/**
 * 文本摘要节点
 * 生成文本摘要
 */
var TextSummaryNode = /** @class */ (function (_super) {
    __extends(TextSummaryNode, _super);
    function TextSummaryNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    TextSummaryNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要摘要的文本',
            defaultValue: ''
        });
        this.addInput({
            name: 'maxLength',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '最大摘要长度',
            defaultValue: 100
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '摘要成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '摘要失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'summary',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成的摘要'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    TextSummaryNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var text, maxLength, nlpSystem, result, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        text = this.getInputValue('text');
                        maxLength = this.getInputValue('maxLength');
                        // 检查输入值是否有效
                        if (!text) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        nlpSystem = this.graph.getWorld().getSystem(AINLPSystem_1.AINLPSystem);
                        if (!nlpSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, nlpSystem.generateSummary(text, { maxLength: maxLength })];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('summary', result.summary);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_3 = _a.sent();
                        console.error('生成摘要失败:', error_3);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return TextSummaryNode;
}(AsyncNode_1.AsyncNode));
exports.TextSummaryNode = TextSummaryNode;
/**
 * 注册AI自然语言处理节点
 * @param registry 节点注册表
 */
function registerAINLPNodes(registry) {
    // 注册文本分类节点
    registry.registerNodeType({
        type: 'ai/nlp/classifyText',
        category: Node_1.NodeCategory.AI,
        constructor: TextClassificationNode,
        label: '文本分类',
        description: '对文本进行分类',
        icon: 'classify',
        color: '#673AB7',
        tags: ['ai', 'nlp', 'classification']
    });
    // 注册命名实体识别节点
    registry.registerNodeType({
        type: 'ai/nlp/recognizeEntities',
        category: Node_1.NodeCategory.AI,
        constructor: NamedEntityRecognitionNode,
        label: '命名实体识别',
        description: '识别文本中的命名实体',
        icon: 'entity',
        color: '#673AB7',
        tags: ['ai', 'nlp', 'entity', 'recognition']
    });
    // 注册文本摘要节点
    registry.registerNodeType({
        type: 'ai/nlp/generateSummary',
        category: Node_1.NodeCategory.AI,
        constructor: TextSummaryNode,
        label: '生成文本摘要',
        description: '生成文本摘要',
        icon: 'summary',
        color: '#673AB7',
        tags: ['ai', 'nlp', 'summary']
    });
}
exports.registerAINLPNodes = registerAINLPNodes;
