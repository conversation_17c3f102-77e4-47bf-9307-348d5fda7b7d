"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputComponent = void 0;
/**
 * 输入组件
 * 用于处理实体的输入
 */
var Component_1 = require("../../core/Component");
var InputManager_1 = require("../InputManager");
/**
 * 输入组件
 */
var InputComponent = exports.InputComponent = /** @class */ (function (_super) {
    __extends(InputComponent, _super);
    /**
     * 创建输入组件
     * @param options 选项
     */
    function InputComponent(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, InputComponent.TYPE) || this;
        /** 是否启用 */
        _this.enabled = true;
        /** 输入动作映射 */
        _this.actions = new Map();
        /** 输入绑定映射 */
        _this.bindings = new Map();
        /** 输入映射映射 */
        _this.mappings = new Map();
        _this.enabled = options.enabled !== undefined ? options.enabled : true;
        _this.inputManager = InputManager_1.InputManager.getInstance();
        // 添加输入动作
        if (options.actions) {
            for (var _i = 0, _a = options.actions; _i < _a.length; _i++) {
                var action = _a[_i];
                _this.addAction(action);
            }
        }
        // 添加输入绑定
        if (options.bindings) {
            for (var _b = 0, _c = options.bindings; _b < _c.length; _b++) {
                var binding = _c[_b];
                _this.addBinding(binding);
            }
        }
        // 添加输入映射
        if (options.mappings) {
            for (var _d = 0, _e = options.mappings; _d < _e.length; _d++) {
                var mapping = _e[_d];
                _this.addMapping(mapping);
            }
        }
        return _this;
    }
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    InputComponent.prototype.update = function (deltaTime) {
        if (!this.enabled)
            return;
        // 处理输入动作
        this.processActions();
    };
    /**
     * 处理输入动作
     */
    InputComponent.prototype.processActions = function () {
        // 处理所有动作
        for (var _i = 0, _a = this.actions.values(); _i < _a.length; _i++) {
            var action = _a[_i];
            // 获取动作的绑定
            var binding = this.bindings.get(action.getName());
            if (!binding)
                continue;
            // 获取绑定的映射
            var mapping = this.mappings.get(binding.getMappingName());
            if (!mapping)
                continue;
            // 获取映射的设备
            var device = this.inputManager.getDevice(mapping.getDeviceName());
            if (!device)
                continue;
            // 检查设备输入是否满足映射条件
            var value = mapping.evaluate(device);
            // 更新动作状态
            action.update(value);
        }
    };
    /**
     * 添加输入动作
     * @param action 输入动作
     */
    InputComponent.prototype.addAction = function (action) {
        this.actions.set(action.getName(), action);
    };
    /**
     * 获取输入动作
     * @param name 动作名称
     * @returns 输入动作
     */
    InputComponent.prototype.getAction = function (name) {
        return this.actions.get(name);
    };
    /**
     * 添加输入绑定
     * @param binding 输入绑定
     */
    InputComponent.prototype.addBinding = function (binding) {
        this.bindings.set(binding.getName(), binding);
    };
    /**
     * 获取输入绑定
     * @param name 绑定名称
     * @returns 输入绑定
     */
    InputComponent.prototype.getBinding = function (name) {
        return this.bindings.get(name);
    };
    /**
     * 添加输入映射
     * @param mapping 输入映射
     */
    InputComponent.prototype.addMapping = function (mapping) {
        this.mappings.set(mapping.getName(), mapping);
    };
    /**
     * 获取输入映射
     * @param name 映射名称
     * @returns 输入映射
     */
    InputComponent.prototype.getMapping = function (name) {
        return this.mappings.get(name);
    };
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    InputComponent.prototype.setEnabled = function (enabled) {
        this.enabled = enabled;
    };
    /**
     * 检查是否启用
     * @returns 是否启用
     */
    InputComponent.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 获取所有输入动作
     * @returns 输入动作列表
     */
    InputComponent.prototype.getActions = function () {
        return Array.from(this.actions.values());
    };
    /**
     * 获取所有输入绑定
     * @returns 输入绑定列表
     */
    InputComponent.prototype.getBindings = function () {
        return Array.from(this.bindings.values());
    };
    /**
     * 获取所有输入映射
     * @returns 输入映射列表
     */
    InputComponent.prototype.getMappings = function () {
        return Array.from(this.mappings.values());
    };
    /**
     * 清除所有输入动作
     */
    InputComponent.prototype.clearActions = function () {
        this.actions.clear();
    };
    /**
     * 清除所有输入绑定
     */
    InputComponent.prototype.clearBindings = function () {
        this.bindings.clear();
    };
    /**
     * 清除所有输入映射
     */
    InputComponent.prototype.clearMappings = function () {
        this.mappings.clear();
    };
    /**
     * 清除所有输入
     */
    InputComponent.prototype.clearAll = function () {
        this.clearActions();
        this.clearBindings();
        this.clearMappings();
    };
    /** 组件类型 */
    InputComponent.TYPE = 'InputComponent';
    return InputComponent;
}(Component_1.Component));
