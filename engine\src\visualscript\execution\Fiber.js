"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fiber = void 0;
/**
 * 视觉脚本执行纤程
 * 负责执行视觉脚本的一条执行路径
 */
var Fiber = /** @class */ (function () {
    /**
     * 创建纤程
     * @param options 纤程选项
     */
    function Fiber(options) {
        /** 执行步数 */
        this.steps = 0;
        /** 最大执行步数 */
        this.maxSteps = 1000;
        /** 开始时间 */
        this.startTime = 0;
        /** 最大执行时间（毫秒） */
        this.maxTime = 1000;
        /** 是否已完成 */
        this.completed = false;
        /** 是否已暂停 */
        this.paused = false;
        this.engine = options.engine;
        this.currentNode = options.sourceNode;
        this.currentOutputName = options.outputName;
        this.callback = options.callback;
        this.startTime = Date.now();
    }
    /**
     * 执行一步
     * @returns 执行结果
     */
    Fiber.prototype.executeStep = function () {
        // 如果已完成或已暂停，直接返回
        if (this.completed || this.paused) {
            return {
                completed: this.completed,
                pause: this.paused
            };
        }
        // 检查是否超过最大步数或最大时间
        if (this.steps >= this.maxSteps || Date.now() - this.startTime >= this.maxTime) {
            this.paused = true;
            return {
                completed: false,
                pause: true
            };
        }
        // 增加步数
        this.steps++;
        try {
            // 获取当前节点的输出连接
            var connections = this.getOutputConnections();
            // 如果没有连接，标记为完成
            if (connections.length === 0) {
                this.completed = true;
                // 调用完成回调
                if (this.callback) {
                    this.callback();
                }
                return {
                    completed: true,
                    pause: false
                };
            }
            // 执行所有连接
            for (var i = 0; i < connections.length; i++) {
                var connection = connections[i];
                // 获取目标节点
                var targetNode = connection.targetNode;
                // 设置当前节点和输出名称
                this.currentNode = targetNode;
                this.currentOutputName = '';
                // 执行目标节点
                var result = targetNode.execute();
                // 返回执行结果
                return {
                    completed: false,
                    pause: false,
                    node: targetNode,
                    result: result
                };
            }
            // 如果没有执行任何节点，标记为完成
            this.completed = true;
            // 调用完成回调
            if (this.callback) {
                this.callback();
            }
            return {
                completed: true,
                pause: false
            };
        }
        catch (error) {
            // 发生错误，标记为完成
            this.completed = true;
            return {
                completed: true,
                pause: false,
                error: error
            };
        }
    };
    /**
     * 获取输出连接
     * @returns 输出连接列表
     */
    Fiber.prototype.getOutputConnections = function () {
        var _a, _b, _c;
        // 获取当前节点的输出连接
        var outputConnections = ((_a = this.currentNode.getOutputs().get(this.currentOutputName)) === null || _a === void 0 ? void 0 : _a.connectedNodeId)
            ? [{
                    sourceNode: this.currentNode,
                    sourceSocketName: this.currentOutputName,
                    targetNode: this.engine.getNode((_b = this.currentNode.getOutputs().get(this.currentOutputName)) === null || _b === void 0 ? void 0 : _b.connectedNodeId),
                    targetSocketName: (_c = this.currentNode.getOutputs().get(this.currentOutputName)) === null || _c === void 0 ? void 0 : _c.connectedSocketName
                }]
            : [];
        return outputConnections;
    };
    /**
     * 暂停执行
     */
    Fiber.prototype.pause = function () {
        this.paused = true;
    };
    /**
     * 恢复执行
     */
    Fiber.prototype.resume = function () {
        this.paused = false;
    };
    /**
     * 重置执行状态
     */
    Fiber.prototype.reset = function () {
        this.steps = 0;
        this.startTime = Date.now();
        this.completed = false;
        this.paused = false;
    };
    /**
     * 获取当前节点
     * @returns 当前节点
     */
    Fiber.prototype.getCurrentNode = function () {
        return this.currentNode;
    };
    /**
     * 获取当前输出名称
     * @returns 当前输出名称
     */
    Fiber.prototype.getCurrentOutputName = function () {
        return this.currentOutputName;
    };
    /**
     * 获取执行步数
     * @returns 执行步数
     */
    Fiber.prototype.getSteps = function () {
        return this.steps;
    };
    /**
     * 获取执行时间（毫秒）
     * @returns 执行时间
     */
    Fiber.prototype.getExecutionTime = function () {
        return Date.now() - this.startTime;
    };
    /**
     * 是否已完成
     * @returns 是否已完成
     */
    Fiber.prototype.isCompleted = function () {
        return this.completed;
    };
    /**
     * 是否已暂停
     * @returns 是否已暂停
     */
    Fiber.prototype.isPaused = function () {
        return this.paused;
    };
    /**
     * 设置最大执行步数
     * @param steps 最大执行步数
     */
    Fiber.prototype.setMaxSteps = function (steps) {
        this.maxSteps = steps;
    };
    /**
     * 设置最大执行时间（毫秒）
     * @param time 最大执行时间
     */
    Fiber.prototype.setMaxTime = function (time) {
        this.maxTime = time;
    };
    return Fiber;
}());
exports.Fiber = Fiber;
