"use strict";
/**
 * 可视化脚本节点基类
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VisualScriptNode = void 0;
var VisualScriptNode = /** @class */ (function () {
    function VisualScriptNode(nodeType, name, id) {
        /** 输入端口 */
        this.inputs = new Map();
        /** 输出端口 */
        this.outputs = new Map();
        /** 执行上下文 */
        this.context = null;
        this.nodeType = nodeType;
        this.name = name;
        this.id = id || this.generateId();
    }
    /**
     * 生成唯一ID
     */
    VisualScriptNode.prototype.generateId = function () {
        return "node_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
    };
    /**
     * 添加输入端口
     */
    VisualScriptNode.prototype.addInput = function (name, type, label, defaultValue) {
        this.inputs.set(name, { name: name, type: type, label: label, defaultValue: defaultValue });
    };
    /**
     * 添加输出端口
     */
    VisualScriptNode.prototype.addOutput = function (name, type, label) {
        this.outputs.set(name, { name: name, type: type, label: label });
    };
    /**
     * 获取输入端口
     */
    VisualScriptNode.prototype.getInputs = function () {
        return Array.from(this.inputs.values());
    };
    /**
     * 获取输出端口
     */
    VisualScriptNode.prototype.getOutputs = function () {
        return Array.from(this.outputs.values());
    };
    /**
     * 获取输入端口
     */
    VisualScriptNode.prototype.getInput = function (name) {
        return this.inputs.get(name) || null;
    };
    /**
     * 获取输出端口
     */
    VisualScriptNode.prototype.getOutput = function (name) {
        return this.outputs.get(name) || null;
    };
    /**
     * 设置执行上下文
     */
    VisualScriptNode.prototype.setContext = function (context) {
        this.context = context;
    };
    /**
     * 获取执行上下文
     */
    VisualScriptNode.prototype.getContext = function () {
        return this.context;
    };
    /**
     * 验证输入
     */
    VisualScriptNode.prototype.validateInputs = function (inputs) {
        for (var _i = 0, _a = this.inputs.values(); _i < _a.length; _i++) {
            var input = _a[_i];
            if (input.defaultValue === undefined && inputs[input.name] === undefined) {
                console.warn("\u8282\u70B9 ".concat(this.name, " \u7F3A\u5C11\u5FC5\u9700\u7684\u8F93\u5165: ").concat(input.name));
                return false;
            }
        }
        return true;
    };
    /**
     * 获取输入值（包含默认值）
     */
    VisualScriptNode.prototype.getInputValue = function (inputs, name) {
        var input = this.inputs.get(name);
        if (!input)
            return undefined;
        return inputs[name] !== undefined ? inputs[name] : input.defaultValue;
    };
    /**
     * 克隆节点
     */
    VisualScriptNode.prototype.clone = function () {
        // 这是一个抽象方法，子类应该实现具体的克隆逻辑
        throw new Error('克隆方法必须在子类中实现');
    };
    /**
     * 序列化节点
     */
    VisualScriptNode.prototype.serialize = function () {
        return {
            id: this.id,
            nodeType: this.nodeType,
            name: this.name,
            inputs: Array.from(this.inputs.values()),
            outputs: Array.from(this.outputs.values())
        };
    };
    /**
     * 反序列化节点
     */
    VisualScriptNode.deserialize = function (data) {
        // 这是一个静态方法，需要在具体的节点类中实现
        throw new Error('反序列化方法必须在具体的节点类中实现');
    };
    return VisualScriptNode;
}());
exports.VisualScriptNode = VisualScriptNode;
