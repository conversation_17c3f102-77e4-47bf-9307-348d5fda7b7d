"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkQualityMonitor = exports.NetworkIssueType = exports.NetworkQualityLevel = void 0;
/**
 * 网络质量监控器
 * 负责监控网络连接质量并提供相关数据
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var i18n_1 = require("../i18n");
/**
 * 网络质量等级
 */
var NetworkQualityLevel;
(function (NetworkQualityLevel) {
    /** 未知 */
    NetworkQualityLevel["UNKNOWN"] = "unknown";
    /** 极差 */
    NetworkQualityLevel["VERY_BAD"] = "very_bad";
    /** 差 */
    NetworkQualityLevel["BAD"] = "bad";
    /** 一般 */
    NetworkQualityLevel["MEDIUM"] = "medium";
    /** 良好 */
    NetworkQualityLevel["GOOD"] = "good";
    /** 极好 */
    NetworkQualityLevel["EXCELLENT"] = "excellent";
})(NetworkQualityLevel || (exports.NetworkQualityLevel = NetworkQualityLevel = {}));
/**
 * 网络问题类型
 */
var NetworkIssueType;
(function (NetworkIssueType) {
    /** 无问题 */
    NetworkIssueType["NONE"] = "none";
    /** 高延迟 */
    NetworkIssueType["HIGH_LATENCY"] = "high_latency";
    /** 丢包 */
    NetworkIssueType["PACKET_LOSS"] = "packet_loss";
    /** 高抖动 */
    NetworkIssueType["HIGH_JITTER"] = "high_jitter";
    /** 低带宽 */
    NetworkIssueType["LOW_BANDWIDTH"] = "low_bandwidth";
    /** 连接不稳定 */
    NetworkIssueType["UNSTABLE_CONNECTION"] = "unstable_connection";
    /** 连接中断 */
    NetworkIssueType["CONNECTION_INTERRUPTED"] = "connection_interrupted";
    /** 网络拥塞 */
    NetworkIssueType["NETWORK_CONGESTION"] = "network_congestion";
    /** 带宽波动 */
    NetworkIssueType["BANDWIDTH_FLUCTUATION"] = "bandwidth_fluctuation";
    /** 服务器响应慢 */
    NetworkIssueType["SLOW_SERVER_RESPONSE"] = "slow_server_response";
    /** DNS解析问题 */
    NetworkIssueType["DNS_RESOLUTION_ISSUE"] = "dns_resolution_issue";
})(NetworkIssueType || (exports.NetworkIssueType = NetworkIssueType = {}));
/**
 * 网络质量监控器
 * 负责监控网络连接质量并提供相关数据
 */
var NetworkQualityMonitor = /** @class */ (function (_super) {
    __extends(NetworkQualityMonitor, _super);
    /**
     * 创建网络质量监控器
     * @param config 配置
     */
    function NetworkQualityMonitor(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 历史质量数据 */
        _this.qualityHistory = [];
        /** 采样定时器ID */
        _this.sampleTimerId = null;
        /** 诊断定时器ID */
        _this.diagnosticsTimerId = null;
        /** 上次发送的探测包时间戳 */
        _this.lastProbeSentTime = 0;
        /** 探测包序列号 */
        _this.probeSequence = 0;
        /** 待确认的探测包 */
        _this.pendingProbes = new Map();
        /** 接收到的探测包响应时间 */
        _this.probeResponses = [];
        /** 丢失的探测包数量 */
        _this.lostProbes = 0;
        /** 总发送的探测包数量 */
        _this.totalProbes = 0;
        /** 带宽测量开始时间 */
        _this.bandwidthMeasureStartTime = 0;
        /** 带宽测量接收字节数 */
        _this.bandwidthMeasureBytes = 0;
        /** 上行带宽测量字节数 */
        _this.uploadBandwidthBytes = 0;
        /** 下行带宽测量字节数 */
        _this.downloadBandwidthBytes = 0;
        /** 当前检测到的网络问题 */
        _this.activeIssues = new Map();
        /** 历史网络问题 */
        _this.issueHistory = [];
        /** 连接稳定性历史数据 */
        _this.stabilityHistory = [];
        /** 网络拥塞历史数据 */
        _this.congestionHistory = [];
        /** RTT历史数据 */
        _this.rttHistory = [];
        /** 带宽利用率历史数据 */
        _this.bandwidthUtilizationHistory = [];
        /** 可靠性历史数据 */
        _this.reliabilityHistory = [];
        /** 带宽测量窗口大小（毫秒） */
        _this.bandwidthMeasureWindowSize = 5000;
        /** 上次带宽测量时间 */
        _this.lastBandwidthMeasureTime = 0;
        /** 带宽测量样本数 */
        _this.bandwidthMeasureSamples = 0;
        /** 最大测量带宽 */
        _this.maxMeasuredBandwidth = 0;
        /** 网络预测数据 */
        _this.predictionData = [];
        /** 网络事件日志 */
        _this.networkEventLog = [];
        /** 报告生成定时器ID */
        _this.reportTimerId = null;
        /** 网络路径分析数据 */
        _this.pathAnalysisData = [];
        /** 网络类型 */
        _this.networkType = 'unknown';
        /** 连接类型 */
        _this.connectionType = 'unknown';
        /** 信号强度 */
        _this.signalStrength = 0;
        /** DNS解析时间 */
        _this.dnsResolutionTime = 0;
        /** 服务器响应时间 */
        _this.serverResponseTime = 0;
        /** 网络接口状态 */
        _this.interfaceStatus = 'unknown';
        /** 网络地址 */
        _this.networkAddress = '';
        /** 错误计数 */
        _this.errorCount = 0;
        /** 警告计数 */
        _this.warningCount = 0;
        // 默认配置
        _this.config = __assign({ sampleInterval: 1000, historyDuration: 60000, autoSample: true, rttThresholds: {
                excellent: 50,
                good: 100,
                medium: 200,
                bad: 300,
            }, packetLossThresholds: {
                excellent: 0.01,
                good: 0.03,
                medium: 0.06,
                bad: 0.1,
            }, jitterThresholds: {
                excellent: 10,
                good: 30,
                medium: 70,
                bad: 150,
            }, bandwidthThresholds: {
                excellent: 1000000,
                good: 500000,
                medium: 200000,
                bad: 50000, // 50KB/s
            }, enableDiagnostics: true, diagnosticsInterval: 5000, detailedLogging: false, continuousMeasurement: false, autoTroubleshoot: false, historySize: 100, pingInterval: 2000, enableAdvancedDiagnostics: false, enablePathAnalysis: false, enableNetworkTypeDetection: true, enableConnectionTypeDetection: true, enableSignalStrengthDetection: false, enableDnsMonitoring: false, enableServerResponseTimeMonitoring: true, enableInterfaceMonitoring: false, enableNetworkAddressMonitoring: false, enableErrorCounting: true, enableWarningCounting: true, enableNetworkPrediction: false, predictionWindowSize: 10, enableNetworkEventLogging: true, enableReportGeneration: false, reportGenerationInterval: 300000 }, config);
        // 初始化当前质量数据
        _this.currentQuality = {
            rtt: 0,
            packetLoss: 0,
            jitter: 0,
            bandwidth: 0,
            uploadBandwidth: 0,
            downloadBandwidth: 0,
            stability: 1.0,
            congestion: 0.0,
            level: NetworkQualityLevel.UNKNOWN,
            timestamp: Date.now(),
            issues: [],
            bandwidthUtilization: 0,
            qualityScore: 100,
            reliability: 1.0,
            latencyTrend: 0,
            networkType: _this.networkType,
            connectionType: _this.connectionType,
            signalStrength: _this.signalStrength,
            hopCount: 0,
            serverResponseTime: _this.serverResponseTime,
            connectionEstablishTime: 0,
            dataTransferRate: 0,
            interfaceStatus: _this.interfaceStatus,
            dnsResolutionTime: _this.dnsResolutionTime,
            errorCount: _this.errorCount,
            warningCount: _this.warningCount,
            prediction: {
                rtt: 0,
                packetLoss: 0,
                bandwidth: 0,
                stability: 1.0,
                confidence: 0.5
            }
        };
        // 如果启用自动采样，则启动采样定时器
        if (_this.config.autoSample) {
            _this.startSampling();
        }
        // 如果启用诊断，则启动诊断定时器
        if (_this.config.enableDiagnostics) {
            _this.startDiagnostics();
        }
        if (_this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络质量监控器已创建，启用详细日志记录');
        }
        return _this;
    }
    /**
     * 启动采样
     */
    NetworkQualityMonitor.prototype.startSampling = function () {
        var _this = this;
        if (this.sampleTimerId !== null) {
            return;
        }
        this.sampleTimerId = window.setInterval(function () {
            _this.sample();
        }, this.config.sampleInterval);
        // 立即进行一次采样
        this.sample();
        // 如果启用了报告生成，也启动报告生成
        if (this.config.enableReportGeneration) {
            this.startReportGeneration();
        }
        // 记录网络事件
        if (this.config.enableNetworkEventLogging) {
            this.logNetworkEvent('monitorStart', {
                timestamp: Date.now(),
                config: __assign({}, this.config)
            });
        }
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络质量监控已启动');
        }
    };
    /**
     * 停止采样
     */
    NetworkQualityMonitor.prototype.stopSampling = function () {
        if (this.sampleTimerId !== null) {
            clearInterval(this.sampleTimerId);
            this.sampleTimerId = null;
            // 记录网络事件
            if (this.config.enableNetworkEventLogging) {
                this.logNetworkEvent('monitorStop', {
                    timestamp: Date.now()
                });
            }
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkQualityMonitor', '网络质量监控已停止');
            }
        }
    };
    /**
     * 进行一次采样
     */
    NetworkQualityMonitor.prototype.sample = function () {
        // 发送探测包
        this.sendProbe();
        // 检查超时的探测包
        this.checkTimeoutProbes();
        // 计算网络质量
        this.calculateNetworkQuality();
        // 清理过期的历史数据
        this.cleanupHistory();
    };
    /**
     * 发送探测包
     */
    NetworkQualityMonitor.prototype.sendProbe = function () {
        var now = Date.now();
        this.lastProbeSentTime = now;
        // 生成探测包序列号
        var sequence = this.probeSequence++;
        // 记录发送时间
        this.pendingProbes.set(sequence, now);
        // 增加总探测包计数
        this.totalProbes++;
        // 触发发送探测包事件
        this.emit('sendProbe', { sequence: sequence, timestamp: now });
    };
    /**
     * 接收探测包响应
     * @param sequence 探测包序列号
     * @param size 响应大小（字节）
     */
    NetworkQualityMonitor.prototype.receiveProbeResponse = function (sequence, size) {
        if (size === void 0) { size = 0; }
        var now = Date.now();
        // 检查是否存在该探测包
        if (this.pendingProbes.has(sequence)) {
            var sentTime = this.pendingProbes.get(sequence);
            var rtt = now - sentTime;
            // 记录RTT
            this.probeResponses.push(rtt);
            // 从待确认列表中移除
            this.pendingProbes.delete(sequence);
            // 更新带宽测量
            this.updateBandwidthMeasurement(size);
            // 触发接收探测包响应事件
            this.emit('receiveProbeResponse', { sequence: sequence, rtt: rtt, timestamp: now });
        }
    };
    /**
     * 检查超时的探测包
     */
    NetworkQualityMonitor.prototype.checkTimeoutProbes = function () {
        var now = Date.now();
        var timeout = this.config.sampleInterval * 2; // 超时时间为采样间隔的2倍
        for (var _i = 0, _a = this.pendingProbes.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], sequence = _b[0], sentTime = _b[1];
            if (now - sentTime > timeout) {
                // 标记为丢失
                this.lostProbes++;
                // 从待确认列表中移除
                this.pendingProbes.delete(sequence);
                // 触发探测包丢失事件
                this.emit('probeLost', { sequence: sequence, timestamp: now });
            }
        }
    };
    /**
     * 更新带宽测量
     * @param bytes 接收到的字节数
     */
    NetworkQualityMonitor.prototype.updateBandwidthMeasurement = function (bytes) {
        var now = Date.now();
        // 如果是第一次测量或者已经过了测量窗口时间，则重置测量
        if (this.bandwidthMeasureStartTime === 0 || now - this.bandwidthMeasureStartTime > this.bandwidthMeasureWindowSize) {
            // 保存上次测量结果
            if (this.bandwidthMeasureStartTime > 0) {
                this.lastBandwidthMeasureTime = now;
            }
            this.bandwidthMeasureStartTime = now;
            this.bandwidthMeasureBytes = 0;
            this.bandwidthMeasureSamples = 0;
        }
        // 累加接收到的字节数
        this.bandwidthMeasureBytes += bytes;
        this.bandwidthMeasureSamples++;
        // 计算带宽（字节/秒）
        var duration = (now - this.bandwidthMeasureStartTime) / 1000; // 转换为秒
        if (duration > 0) {
            // 计算当前带宽
            var currentBandwidth = this.bandwidthMeasureBytes / duration;
            // 更新最大测量带宽
            if (currentBandwidth > this.maxMeasuredBandwidth) {
                this.maxMeasuredBandwidth = currentBandwidth;
            }
            // 使用指数移动平均平滑带宽值
            var alpha = 0.3; // 平滑因子
            if (this.currentQuality.bandwidth === 0) {
                this.currentQuality.bandwidth = currentBandwidth;
            }
            else {
                this.currentQuality.bandwidth = alpha * currentBandwidth + (1 - alpha) * this.currentQuality.bandwidth;
            }
            // 计算上行和下行带宽
            // 这里使用更准确的估计，基于实际测量的上行和下行数据
            this.currentQuality.uploadBandwidth = this.uploadBandwidthBytes / duration;
            this.currentQuality.downloadBandwidth = this.downloadBandwidthBytes / duration;
            // 计算带宽利用率（相对于最大测量带宽）
            if (this.maxMeasuredBandwidth > 0) {
                this.currentQuality.bandwidthUtilization = this.currentQuality.bandwidth / this.maxMeasuredBandwidth;
                // 更新带宽利用率历史
                this.bandwidthUtilizationHistory.push(this.currentQuality.bandwidthUtilization);
                if (this.bandwidthUtilizationHistory.length > 10) {
                    this.bandwidthUtilizationHistory.shift();
                }
            }
        }
    };
    /**
     * 记录上行数据
     * @param bytes 字节数
     */
    NetworkQualityMonitor.prototype.recordUpload = function (bytes) {
        this.uploadBandwidthBytes += bytes;
    };
    /**
     * 记录下行数据
     * @param bytes 字节数
     */
    NetworkQualityMonitor.prototype.recordDownload = function (bytes) {
        this.downloadBandwidthBytes += bytes;
    };
    /**
     * 计算网络质量
     */
    NetworkQualityMonitor.prototype.calculateNetworkQuality = function () {
        var now = Date.now();
        // 计算RTT
        if (this.probeResponses.length > 0) {
            // 计算平均RTT
            var sum = this.probeResponses.reduce(function (acc, rtt) { return acc + rtt; }, 0);
            this.currentQuality.rtt = sum / this.probeResponses.length;
            // 计算抖动（RTT的标准差）
            if (this.probeResponses.length > 1) {
                var mean_1 = this.currentQuality.rtt;
                var squaredDiffs = this.probeResponses.map(function (rtt) { return Math.pow(rtt - mean_1, 2); });
                var variance = squaredDiffs.reduce(function (acc, diff) { return acc + diff; }, 0) / this.probeResponses.length;
                this.currentQuality.jitter = Math.sqrt(variance);
            }
            // 清空响应列表
            this.probeResponses = [];
        }
        // 计算丢包率
        if (this.totalProbes > 0) {
            this.currentQuality.packetLoss = this.lostProbes / this.totalProbes;
        }
        // 计算上行和下行带宽
        // 注意：这里只是一个简化的模拟，实际应用中需要更准确的测量
        if (this.currentQuality.bandwidth > 0) {
            // 假设上行带宽占总带宽的40%，下行占60%
            this.currentQuality.uploadBandwidth = this.currentQuality.bandwidth * 0.4;
            this.currentQuality.downloadBandwidth = this.currentQuality.bandwidth * 0.6;
        }
        // 计算连接稳定性
        this.currentQuality.stability = this.calculateStability();
        // 计算网络拥塞程度
        this.currentQuality.congestion = this.calculateCongestion();
        // 计算连接可靠性
        this.currentQuality.reliability = this.calculateReliability();
        // 计算网络质量分数
        this.currentQuality.qualityScore = this.calculateQualityScore();
        // 计算延迟趋势
        this.currentQuality.latencyTrend = this.calculateLatencyTrend();
        // 确定质量等级
        this.currentQuality.level = this.determineQualityLevel();
        // 更新时间戳
        this.currentQuality.timestamp = now;
        // 执行额外的网络检测和测量
        if (this.config.enableNetworkTypeDetection) {
            this.detectNetworkType();
        }
        if (this.config.enableDnsMonitoring) {
            this.measureDnsResolutionTime();
        }
        if (this.config.enableServerResponseTimeMonitoring) {
            this.measureServerResponseTime();
        }
        if (this.config.enablePathAnalysis) {
            this.analyzeNetworkPath();
        }
        // 计算数据传输速率
        this.currentQuality.dataTransferRate = this.calculateDataTransferRate();
        // 更新错误和警告计数
        if (this.config.enableErrorCounting) {
            this.updateErrorCount();
        }
        if (this.config.enableWarningCounting) {
            this.updateWarningCount();
        }
        // 如果启用网络预测，更新预测数据
        if (this.config.enableNetworkPrediction && this.qualityHistory.length >= this.config.predictionWindowSize) {
            this.currentQuality.prediction = this.predictNetworkQuality();
        }
        // 添加到历史记录
        this.qualityHistory.push(__assign({}, this.currentQuality));
        // 如果历史记录过长，删除最旧的记录
        var maxHistorySize = this.config.historySize || 100;
        if (this.qualityHistory.length > maxHistorySize) {
            this.qualityHistory.shift();
        }
        // 添加到预测数据集
        if (this.config.enableNetworkPrediction) {
            this.predictionData.push(__assign({}, this.currentQuality));
            if (this.predictionData.length > this.config.predictionWindowSize * 2) {
                this.predictionData.shift();
            }
        }
        // 记录网络事件
        if (this.config.enableNetworkEventLogging) {
            this.logNetworkEvent('qualityUpdate', {
                timestamp: now,
                quality: __assign({}, this.currentQuality)
            });
        }
        // 触发质量更新事件
        this.emit('qualityUpdate', this.currentQuality);
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "\u7F51\u7EDC\u8D28\u91CF\u66F4\u65B0: RTT=".concat(this.currentQuality.rtt.toFixed(2), "ms, \u4E22\u5305\u7387=").concat((this.currentQuality.packetLoss * 100).toFixed(2), "%, \u6296\u52A8=").concat(this.currentQuality.jitter.toFixed(2), "ms, \u5E26\u5BBD=").concat(this.currentQuality.bandwidth, "B/s, \u7B49\u7EA7=").concat(this.currentQuality.level));
        }
    };
    /**
     * 计算数据传输速率
     * @returns 数据传输速率（字节/秒）
     */
    NetworkQualityMonitor.prototype.calculateDataTransferRate = function () {
        // 这里使用一个简化的计算，实际应用中需要更准确的测量
        var uploadRate = this.currentQuality.uploadBandwidth || 0;
        var downloadRate = this.currentQuality.downloadBandwidth || 0;
        return uploadRate + downloadRate;
    };
    /**
     * 更新错误计数
     */
    NetworkQualityMonitor.prototype.updateErrorCount = function () {
        // 检查是否有严重的网络问题
        var hasErrors = this.currentQuality.level === NetworkQualityLevel.VERY_BAD ||
            this.currentQuality.packetLoss > this.config.packetLossThresholds.bad * 1.5 ||
            this.currentQuality.rtt > this.config.rttThresholds.bad * 1.5;
        if (hasErrors) {
            this.errorCount++;
            this.currentQuality.errorCount = this.errorCount;
        }
    };
    /**
     * 更新警告计数
     */
    NetworkQualityMonitor.prototype.updateWarningCount = function () {
        // 检查是否有中等严重的网络问题
        var hasWarnings = this.currentQuality.level === NetworkQualityLevel.BAD ||
            (this.currentQuality.packetLoss > this.config.packetLossThresholds.medium &&
                this.currentQuality.packetLoss <= this.config.packetLossThresholds.bad) ||
            (this.currentQuality.rtt > this.config.rttThresholds.medium &&
                this.currentQuality.rtt <= this.config.rttThresholds.bad);
        if (hasWarnings) {
            this.warningCount++;
            this.currentQuality.warningCount = this.warningCount;
        }
    };
    /**
     * 确定质量等级
     * @returns 质量等级
     */
    NetworkQualityMonitor.prototype.determineQualityLevel = function () {
        var _a = this.currentQuality, rtt = _a.rtt, packetLoss = _a.packetLoss;
        var _b = this.config, rttThresholds = _b.rttThresholds, packetLossThresholds = _b.packetLossThresholds;
        // 根据RTT和丢包率确定质量等级
        if (rtt <= rttThresholds.excellent && packetLoss <= packetLossThresholds.excellent) {
            return NetworkQualityLevel.EXCELLENT;
        }
        else if (rtt <= rttThresholds.good && packetLoss <= packetLossThresholds.good) {
            return NetworkQualityLevel.GOOD;
        }
        else if (rtt <= rttThresholds.medium && packetLoss <= packetLossThresholds.medium) {
            return NetworkQualityLevel.MEDIUM;
        }
        else if (rtt <= rttThresholds.bad && packetLoss <= packetLossThresholds.bad) {
            return NetworkQualityLevel.BAD;
        }
        else {
            return NetworkQualityLevel.VERY_BAD;
        }
    };
    /**
     * 清理过期的历史数据
     */
    NetworkQualityMonitor.prototype.cleanupHistory = function () {
        var now = Date.now();
        var cutoff = now - this.config.historyDuration;
        // 移除过期的历史数据
        this.qualityHistory = this.qualityHistory.filter(function (data) { return data.timestamp >= cutoff; });
    };
    /**
     * 获取当前网络质量
     * @returns 当前网络质量数据
     */
    NetworkQualityMonitor.prototype.getCurrentQuality = function () {
        return __assign({}, this.currentQuality);
    };
    /**
     * 获取历史网络质量数据
     * @param duration 时间范围（毫秒），如果为0则返回所有历史数据
     * @returns 历史网络质量数据
     */
    NetworkQualityMonitor.prototype.getQualityHistory = function (duration) {
        if (duration === void 0) { duration = 0; }
        if (duration <= 0) {
            return __spreadArray([], this.qualityHistory, true);
        }
        var now = Date.now();
        var cutoff = now - duration;
        return this.qualityHistory.filter(function (data) { return data.timestamp >= cutoff; });
    };
    /**
     * 导出网络质量数据
     * @param format 导出格式，支持'json'和'csv'
     * @param duration 时间范围（毫秒），如果为0则导出所有历史数据
     * @returns 导出的数据字符串
     */
    NetworkQualityMonitor.prototype.exportQualityData = function (format, duration) {
        if (format === void 0) { format = 'json'; }
        if (duration === void 0) { duration = 0; }
        var data = this.getQualityHistory(duration);
        if (format === 'json') {
            return JSON.stringify(data, null, 2);
        }
        else if (format === 'csv') {
            // CSV表头
            var headers = [
                'timestamp', 'rtt', 'packetLoss', 'jitter', 'bandwidth',
                'uploadBandwidth', 'downloadBandwidth', 'stability', 'congestion',
                'level', 'bandwidthUtilization', 'qualityScore', 'reliability', 'latencyTrend'
            ].join(',');
            // 转换数据为CSV行
            var rows = data.map(function (item) {
                var timestamp = new Date(item.timestamp).toISOString();
                var rtt = item.rtt.toFixed(2);
                var packetLoss = (item.packetLoss * 100).toFixed(2);
                var jitter = item.jitter.toFixed(2);
                var bandwidth = item.bandwidth.toFixed(0);
                var uploadBandwidth = (item.uploadBandwidth || 0).toFixed(0);
                var downloadBandwidth = (item.downloadBandwidth || 0).toFixed(0);
                var stability = (item.stability || 1).toFixed(2);
                var congestion = (item.congestion || 0).toFixed(2);
                var level = item.level;
                var bandwidthUtilization = (item.bandwidthUtilization || 0).toFixed(2);
                var qualityScore = (item.qualityScore || 0).toFixed(0);
                var reliability = (item.reliability || 1).toFixed(2);
                var latencyTrend = item.latencyTrend || 0;
                return [
                    timestamp, rtt, packetLoss, jitter, bandwidth,
                    uploadBandwidth, downloadBandwidth, stability, congestion,
                    level, bandwidthUtilization, qualityScore, reliability, latencyTrend
                ].join(',');
            });
            // 组合CSV内容
            return __spreadArray([headers], rows, true).join('\n');
        }
        return '';
    };
    /**
     * 重置监控器
     */
    NetworkQualityMonitor.prototype.reset = function () {
        this.probeSequence = 0;
        this.pendingProbes.clear();
        this.probeResponses = [];
        this.lostProbes = 0;
        this.totalProbes = 0;
        this.bandwidthMeasureStartTime = 0;
        this.bandwidthMeasureBytes = 0;
        this.uploadBandwidthBytes = 0;
        this.downloadBandwidthBytes = 0;
        this.activeIssues.clear();
        this.issueHistory = [];
        this.stabilityHistory = [];
        this.congestionHistory = [];
        this.rttHistory = [];
        this.bandwidthUtilizationHistory = [];
        this.reliabilityHistory = [];
        this.predictionData = [];
        this.networkEventLog = [];
        this.pathAnalysisData = [];
        this.networkType = 'unknown';
        this.connectionType = 'unknown';
        this.signalStrength = 0;
        this.dnsResolutionTime = 0;
        this.serverResponseTime = 0;
        this.interfaceStatus = 'unknown';
        this.networkAddress = '';
        this.errorCount = 0;
        this.warningCount = 0;
        this.currentQuality = {
            rtt: 0,
            packetLoss: 0,
            jitter: 0,
            bandwidth: 0,
            uploadBandwidth: 0,
            downloadBandwidth: 0,
            stability: 1.0,
            congestion: 0.0,
            level: NetworkQualityLevel.UNKNOWN,
            timestamp: Date.now(),
            issues: [],
            bandwidthUtilization: 0,
            qualityScore: 100,
            reliability: 1.0,
            latencyTrend: 0,
            networkType: this.networkType,
            connectionType: this.connectionType,
            signalStrength: this.signalStrength,
            hopCount: 0,
            serverResponseTime: this.serverResponseTime,
            connectionEstablishTime: 0,
            dataTransferRate: 0,
            interfaceStatus: this.interfaceStatus,
            dnsResolutionTime: this.dnsResolutionTime,
            errorCount: this.errorCount,
            warningCount: this.warningCount,
            prediction: {
                rtt: 0,
                packetLoss: 0,
                bandwidth: 0,
                stability: 1.0,
                confidence: 0.5
            }
        };
        this.qualityHistory = [];
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络质量监控器已重置');
        }
    };
    /**
     * 启动诊断
     */
    NetworkQualityMonitor.prototype.startDiagnostics = function () {
        var _this = this;
        if (this.diagnosticsTimerId !== null) {
            return;
        }
        this.diagnosticsTimerId = window.setInterval(function () {
            _this.diagnoseNetworkIssues();
        }, this.config.diagnosticsInterval);
        // 立即进行一次诊断
        this.diagnoseNetworkIssues();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络诊断已启动');
        }
    };
    /**
     * 停止诊断
     */
    NetworkQualityMonitor.prototype.stopDiagnostics = function () {
        if (this.diagnosticsTimerId !== null) {
            clearInterval(this.diagnosticsTimerId);
            this.diagnosticsTimerId = null;
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkQualityMonitor', '网络诊断已停止');
            }
        }
    };
    /**
     * 诊断网络问题
     * 检测各种网络问题并更新问题列表
     */
    NetworkQualityMonitor.prototype.diagnoseNetworkIssues = function () {
        var now = Date.now();
        var issues = [];
        var t = i18n_1.default.t.bind(i18n_1.default);
        // 检查高延迟问题
        if (this.currentQuality.rtt > this.config.rttThresholds.bad) {
            var severity = Math.min(1.0, (this.currentQuality.rtt - this.config.rttThresholds.bad) / 500);
            var issue = this.getOrCreateIssue(NetworkIssueType.HIGH_LATENCY, severity, now, t('network.issues.highLatencyDescription', { value: this.currentQuality.rtt.toFixed(0) }), t('network.issues.highLatencySolution'));
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.HIGH_LATENCY, now);
        }
        // 检查丢包问题
        if (this.currentQuality.packetLoss > this.config.packetLossThresholds.bad) {
            var severity = Math.min(1.0, (this.currentQuality.packetLoss - this.config.packetLossThresholds.bad) / 0.2);
            var issue = this.getOrCreateIssue(NetworkIssueType.PACKET_LOSS, severity, now, t('network.issues.packetLossDescription', { value: (this.currentQuality.packetLoss * 100).toFixed(1) }), t('network.issues.packetLossSolution'));
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.PACKET_LOSS, now);
        }
        // 检查高抖动问题
        if (this.currentQuality.jitter > this.config.jitterThresholds.bad) {
            var severity = Math.min(1.0, (this.currentQuality.jitter - this.config.jitterThresholds.bad) / 200);
            var issue = this.getOrCreateIssue(NetworkIssueType.HIGH_JITTER, severity, now, t('network.issues.highJitterDescription', { value: this.currentQuality.jitter.toFixed(1) }), t('network.issues.highJitterSolution'));
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.HIGH_JITTER, now);
        }
        // 检查低带宽问题
        if (this.currentQuality.bandwidth < this.config.bandwidthThresholds.bad) {
            var severity = Math.min(1.0, 1 - (this.currentQuality.bandwidth / this.config.bandwidthThresholds.bad));
            var issue = this.getOrCreateIssue(NetworkIssueType.LOW_BANDWIDTH, severity, now, t('network.issues.lowBandwidthDescription', { value: (this.currentQuality.bandwidth / 1024).toFixed(0) }), t('network.issues.lowBandwidthSolution'));
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.LOW_BANDWIDTH, now);
        }
        // 检查连接不稳定问题
        if (this.stabilityHistory.length >= 5) {
            var avgStability = this.stabilityHistory.reduce(function (sum, val) { return sum + val; }, 0) / this.stabilityHistory.length;
            if (avgStability < 0.7) {
                var severity = Math.min(1.0, 1 - avgStability);
                var issue = this.getOrCreateIssue(NetworkIssueType.UNSTABLE_CONNECTION, severity, now);
                issues.push(issue);
            }
            else {
                this.resolveIssue(NetworkIssueType.UNSTABLE_CONNECTION, now);
            }
        }
        // 检查网络拥塞问题
        if (this.currentQuality.congestion > 0.7) {
            var severity = Math.min(1.0, (this.currentQuality.congestion - 0.7) / 0.3);
            var issue = this.getOrCreateIssue(NetworkIssueType.NETWORK_CONGESTION, severity, now);
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.NETWORK_CONGESTION, now);
        }
        // 检查带宽波动问题
        if (this.bandwidthUtilizationHistory.length >= 5) {
            var bandwidthValues = this.qualityHistory.slice(-5).map(function (data) { return data.bandwidth; });
            var bandwidthVariation = this.calculateVariation(bandwidthValues);
            if (bandwidthVariation > 0.3) {
                var severity = Math.min(1.0, bandwidthVariation);
                var issue = this.getOrCreateIssue(NetworkIssueType.BANDWIDTH_FLUCTUATION, severity, now);
                issues.push(issue);
            }
            else {
                this.resolveIssue(NetworkIssueType.BANDWIDTH_FLUCTUATION, now);
            }
        }
        // 检查服务器响应慢问题
        // 这里假设服务器响应时间是RTT的一部分，如果RTT高但丢包率低，可能是服务器响应慢
        if (this.currentQuality.rtt > this.config.rttThresholds.bad && this.currentQuality.packetLoss < this.config.packetLossThresholds.medium) {
            var severity = Math.min(1.0, (this.currentQuality.rtt - this.config.rttThresholds.bad) / 500);
            var issue = this.getOrCreateIssue(NetworkIssueType.SLOW_SERVER_RESPONSE, severity, now);
            issues.push(issue);
        }
        else {
            this.resolveIssue(NetworkIssueType.SLOW_SERVER_RESPONSE, now);
        }
        // 更新当前质量数据中的问题列表
        this.currentQuality.issues = issues;
        // 如果有问题，触发问题检测事件
        if (issues.length > 0) {
            this.emit('issuesDetected', issues);
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkQualityMonitor', "\u68C0\u6D4B\u5230 ".concat(issues.length, " \u4E2A\u7F51\u7EDC\u95EE\u9898"));
                issues.forEach(function (issue) {
                    Debug_1.Debug.log('NetworkQualityMonitor', "\u95EE\u9898: ".concat(issue.type, ", \u4E25\u91CD\u7A0B\u5EA6: ").concat(issue.severity.toFixed(2), ", \u63CF\u8FF0: ").concat(issue.description));
                });
            }
            // 如果启用自动排障，尝试解决问题
            if (this.config.autoTroubleshoot) {
                this.troubleshootIssues(issues);
            }
        }
    };
    /**
     * 获取或创建网络问题
     * @param type 问题类型
     * @param severity 严重程度
     * @param now 当前时间
     * @param description 问题描述（可选）
     * @param solution 解决方案（可选）
     * @returns 网络问题
     */
    NetworkQualityMonitor.prototype.getOrCreateIssue = function (type, severity, now, description, solution) {
        // 如果已存在该类型的问题，则更新它
        if (this.activeIssues.has(type)) {
            var issue_1 = this.activeIssues.get(type);
            issue_1.severity = severity;
            issue_1.duration = now - issue_1.startTime;
            // 如果提供了新的描述和解决方案，则更新
            if (description)
                issue_1.description = description;
            if (solution)
                issue_1.solution = solution;
            return issue_1;
        }
        // 否则创建新问题
        var issue = {
            type: type,
            severity: severity,
            description: description || this.getIssueDescription(type),
            solution: solution || this.getIssueSolution(type),
            startTime: now,
            duration: 0,
            resolved: false,
        };
        // 添加到活动问题列表
        this.activeIssues.set(type, issue);
        return issue;
    };
    /**
     * 解决网络问题
     * @param type 问题类型
     * @param now 当前时间
     */
    NetworkQualityMonitor.prototype.resolveIssue = function (type, now) {
        if (this.activeIssues.has(type)) {
            var issue = this.activeIssues.get(type);
            issue.resolved = true;
            issue.duration = now - issue.startTime;
            // 添加到历史记录
            this.issueHistory.push(__assign({}, issue));
            // 从活动问题列表中移除
            this.activeIssues.delete(type);
            // 触发问题解决事件
            this.emit('issueResolved', issue);
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkQualityMonitor', "\u95EE\u9898\u5DF2\u89E3\u51B3: ".concat(issue.type, ", \u6301\u7EED\u65F6\u95F4: ").concat(issue.duration, "ms"));
            }
        }
    };
    /**
     * 获取问题描述
     * @param type 问题类型
     * @returns 问题描述
     */
    NetworkQualityMonitor.prototype.getIssueDescription = function (type) {
        switch (type) {
            case NetworkIssueType.HIGH_LATENCY:
                return '网络延迟过高，可能导致操作响应缓慢';
            case NetworkIssueType.PACKET_LOSS:
                return '网络丢包率过高，可能导致数据丢失或不完整';
            case NetworkIssueType.HIGH_JITTER:
                return '网络抖动过大，可能导致连接不稳定';
            case NetworkIssueType.LOW_BANDWIDTH:
                return '网络带宽不足，可能导致数据传输速度慢';
            case NetworkIssueType.UNSTABLE_CONNECTION:
                return '网络连接不稳定，可能导致频繁断线或重连';
            case NetworkIssueType.CONNECTION_INTERRUPTED:
                return '网络连接中断，需要重新连接';
            case NetworkIssueType.NETWORK_CONGESTION:
                return '网络拥塞，多个应用或用户同时占用带宽导致性能下降';
            case NetworkIssueType.BANDWIDTH_FLUCTUATION:
                return '带宽波动较大，网络传输速度不稳定';
            case NetworkIssueType.SLOW_SERVER_RESPONSE:
                return '服务器响应缓慢，可能是服务器负载过高或距离过远';
            case NetworkIssueType.DNS_RESOLUTION_ISSUE:
                return 'DNS解析问题，可能导致连接延迟或失败';
            default:
                return '未知网络问题';
        }
    };
    /**
     * 获取问题解决方案
     * @param type 问题类型
     * @returns 问题解决方案
     */
    NetworkQualityMonitor.prototype.getIssueSolution = function (type) {
        switch (type) {
            case NetworkIssueType.HIGH_LATENCY:
                return '尝试连接到更近的服务器，检查本地网络连接，关闭其他占用带宽的应用';
            case NetworkIssueType.PACKET_LOSS:
                return '检查网络连接稳定性，避免使用无线网络，减少网络拥塞';
            case NetworkIssueType.HIGH_JITTER:
                return '使用有线网络连接，减少网络干扰，关闭其他占用带宽的应用';
            case NetworkIssueType.LOW_BANDWIDTH:
                return '提高网络带宽，关闭其他占用带宽的应用，降低数据传输质量';
            case NetworkIssueType.UNSTABLE_CONNECTION:
                return '检查网络连接稳定性，使用有线网络连接，更换网络环境';
            case NetworkIssueType.CONNECTION_INTERRUPTED:
                return '检查网络连接，等待自动重连或手动重新连接';
            case NetworkIssueType.NETWORK_CONGESTION:
                return '关闭其他占用带宽的应用，避开网络高峰期，考虑升级网络带宽';
            case NetworkIssueType.BANDWIDTH_FLUCTUATION:
                return '使用有线网络连接，避免在网络高峰期使用，检查是否有其他应用占用带宽';
            case NetworkIssueType.SLOW_SERVER_RESPONSE:
                return '尝试连接到其他服务器，检查服务器状态，稍后再试';
            case NetworkIssueType.DNS_RESOLUTION_ISSUE:
                return '尝试使用其他DNS服务器，清除DNS缓存，联系网络管理员';
            default:
                return '尝试重新连接或联系网络管理员';
        }
    };
    /**
     * 尝试解决网络问题
     * @param issues 网络问题列表
     */
    NetworkQualityMonitor.prototype.troubleshootIssues = function (issues) {
        var _this = this;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试自动解决网络问题');
        }
        // 触发自动排障事件
        this.emit('troubleshooting', issues);
        // 对每个问题应用特定的解决策略
        issues.forEach(function (issue) {
            switch (issue.type) {
                case NetworkIssueType.HIGH_LATENCY:
                    _this.troubleshootHighLatency(issue);
                    break;
                case NetworkIssueType.PACKET_LOSS:
                    _this.troubleshootPacketLoss(issue);
                    break;
                case NetworkIssueType.NETWORK_CONGESTION:
                    _this.troubleshootNetworkCongestion(issue);
                    break;
                case NetworkIssueType.BANDWIDTH_FLUCTUATION:
                    _this.troubleshootBandwidthFluctuation(issue);
                    break;
                case NetworkIssueType.UNSTABLE_CONNECTION:
                    _this.troubleshootUnstableConnection(issue);
                    break;
                default:
                    // 对于其他问题，记录但不采取特定操作
                    if (_this.config.detailedLogging) {
                        Debug_1.Debug.log('NetworkQualityMonitor', "\u6CA1\u6709\u9488\u5BF9 ".concat(issue.type, " \u7684\u81EA\u52A8\u89E3\u51B3\u65B9\u6848"));
                    }
            }
        });
    };
    /**
     * 解决高延迟问题
     * @param issue 网络问题
     */
    NetworkQualityMonitor.prototype.troubleshootHighLatency = function (issue) {
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试解决高延迟问题');
        }
        // 可以实现的策略：
        // 1. 降低数据传输频率
        // 2. 减少每次传输的数据量
        // 3. 使用更高效的压缩算法
        // 触发高延迟问题解决事件
        this.emit('troubleshootHighLatency', {
            issue: issue,
            recommendations: [
                '降低数据传输频率',
                '减少每次传输的数据量',
                '使用更高效的压缩算法'
            ]
        });
    };
    /**
     * 解决丢包问题
     * @param issue 网络问题
     */
    NetworkQualityMonitor.prototype.troubleshootPacketLoss = function (issue) {
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试解决丢包问题');
        }
        // 可以实现的策略：
        // 1. 增加数据包重传次数
        // 2. 使用更可靠的传输协议
        // 3. 减小数据包大小
        // 触发丢包问题解决事件
        this.emit('troubleshootPacketLoss', {
            issue: issue,
            recommendations: [
                '增加数据包重传次数',
                '使用更可靠的传输协议',
                '减小数据包大小'
            ]
        });
    };
    /**
     * 解决网络拥塞问题
     * @param issue 网络问题
     */
    NetworkQualityMonitor.prototype.troubleshootNetworkCongestion = function (issue) {
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试解决网络拥塞问题');
        }
        // 可以实现的策略：
        // 1. 降低数据传输优先级
        // 2. 延迟非关键数据传输
        // 3. 使用更高效的数据压缩
        // 触发网络拥塞问题解决事件
        this.emit('troubleshootNetworkCongestion', {
            issue: issue,
            recommendations: [
                '降低数据传输优先级',
                '延迟非关键数据传输',
                '使用更高效的数据压缩'
            ]
        });
    };
    /**
     * 解决带宽波动问题
     * @param issue 网络问题
     */
    NetworkQualityMonitor.prototype.troubleshootBandwidthFluctuation = function (issue) {
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试解决带宽波动问题');
        }
        // 可以实现的策略：
        // 1. 动态调整数据传输速率
        // 2. 实现自适应质量控制
        // 3. 使用缓冲区平滑数据流
        // 触发带宽波动问题解决事件
        this.emit('troubleshootBandwidthFluctuation', {
            issue: issue,
            recommendations: [
                '动态调整数据传输速率',
                '实现自适应质量控制',
                '使用缓冲区平滑数据流'
            ]
        });
    };
    /**
     * 解决连接不稳定问题
     * @param issue 网络问题
     */
    NetworkQualityMonitor.prototype.troubleshootUnstableConnection = function (issue) {
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '尝试解决连接不稳定问题');
        }
        // 可以实现的策略：
        // 1. 增加心跳包频率
        // 2. 实现断线重连机制
        // 3. 使用更可靠的连接方式
        // 触发连接不稳定问题解决事件
        this.emit('troubleshootUnstableConnection', {
            issue: issue,
            recommendations: [
                '增加心跳包频率',
                '实现断线重连机制',
                '使用更可靠的连接方式'
            ]
        });
    };
    /**
     * 计算连接稳定性
     * 基于最近的网络质量数据计算稳定性指标（0-1）
     */
    NetworkQualityMonitor.prototype.calculateStability = function () {
        // 如果历史数据不足，返回默认值
        if (this.qualityHistory.length < 5) {
            return 1.0;
        }
        // 获取最近的5个质量数据
        var recentData = this.qualityHistory.slice(-5);
        // 计算RTT的变化率
        var rttVariation = this.calculateVariation(recentData.map(function (data) { return data.rtt; }));
        // 计算丢包率的变化率
        var packetLossVariation = this.calculateVariation(recentData.map(function (data) { return data.packetLoss; }));
        // 计算抖动的变化率
        var jitterVariation = this.calculateVariation(recentData.map(function (data) { return data.jitter; }));
        // 综合计算稳定性（1 - 平均变化率）
        var avgVariation = (rttVariation + packetLossVariation + jitterVariation) / 3;
        var stability = Math.max(0, Math.min(1, 1 - avgVariation));
        // 更新稳定性历史
        this.stabilityHistory.push(stability);
        if (this.stabilityHistory.length > 10) {
            this.stabilityHistory.shift();
        }
        return stability;
    };
    /**
     * 计算数据变化率
     * @param values 数值数组
     * @returns 变化率（0-1）
     */
    NetworkQualityMonitor.prototype.calculateVariation = function (values) {
        if (values.length < 2) {
            return 0;
        }
        // 计算相邻值之间的变化率
        var totalVariation = 0;
        for (var i = 1; i < values.length; i++) {
            var prev = values[i - 1];
            var curr = values[i];
            // 避免除以零
            if (prev === 0) {
                continue;
            }
            var variation = Math.abs(curr - prev) / prev;
            totalVariation += variation;
        }
        // 计算平均变化率，并限制在0-1范围内
        return Math.min(1, totalVariation / (values.length - 1));
    };
    /**
     * 计算网络拥塞程度
     * 基于RTT、丢包率和带宽计算拥塞指标（0-1）
     */
    NetworkQualityMonitor.prototype.calculateCongestion = function () {
        // 计算RTT拥塞因子
        var rttFactor = Math.min(1, this.currentQuality.rtt / (this.config.rttThresholds.bad * 2));
        // 计算丢包率拥塞因子
        var packetLossFactor = Math.min(1, this.currentQuality.packetLoss / (this.config.packetLossThresholds.bad * 2));
        // 计算带宽拥塞因子
        var bandwidthFactor = 0;
        if (this.currentQuality.bandwidth > 0) {
            bandwidthFactor = Math.min(1, 1 - (this.currentQuality.bandwidth / (this.config.bandwidthThresholds.excellent * 2)));
        }
        // 计算抖动拥塞因子
        var jitterFactor = Math.min(1, this.currentQuality.jitter / (this.config.jitterThresholds.bad * 2));
        // 综合计算拥塞程度
        var congestion = (rttFactor * 0.3 + packetLossFactor * 0.3 + bandwidthFactor * 0.2 + jitterFactor * 0.2);
        // 更新拥塞历史
        this.congestionHistory.push(congestion);
        if (this.congestionHistory.length > 10) {
            this.congestionHistory.shift();
        }
        return congestion;
    };
    /**
     * 计算连接可靠性
     * 基于丢包率、连接稳定性和网络拥塞程度计算可靠性指标（0-1）
     */
    NetworkQualityMonitor.prototype.calculateReliability = function () {
        // 丢包率因子（丢包率越低，可靠性越高）
        var packetLossFactor = 1 - Math.min(1, this.currentQuality.packetLoss / 0.2);
        // 稳定性因子
        var stabilityFactor = this.currentQuality.stability || 1.0;
        // 拥塞因子（拥塞程度越低，可靠性越高）
        var congestionFactor = 1 - (this.currentQuality.congestion || 0);
        // 综合计算可靠性
        var reliability = (packetLossFactor * 0.5 + stabilityFactor * 0.3 + congestionFactor * 0.2);
        // 更新可靠性历史
        this.reliabilityHistory.push(reliability);
        if (this.reliabilityHistory.length > 10) {
            this.reliabilityHistory.shift();
        }
        return reliability;
    };
    /**
     * 计算网络质量分数
     * 综合各项指标计算网络质量分数（0-100）
     */
    NetworkQualityMonitor.prototype.calculateQualityScore = function () {
        // RTT分数（RTT越低越好）
        var rttScore = Math.max(0, 100 - (this.currentQuality.rtt / 5));
        // 丢包率分数（丢包率越低越好）
        var packetLossScore = Math.max(0, 100 - (this.currentQuality.packetLoss * 500));
        // 抖动分数（抖动越低越好）
        var jitterScore = Math.max(0, 100 - (this.currentQuality.jitter / 2));
        // 带宽分数（带宽越高越好）
        var bandwidthScore = 0;
        if (this.currentQuality.bandwidth > 0) {
            bandwidthScore = Math.min(100, (this.currentQuality.bandwidth / this.config.bandwidthThresholds.excellent) * 100);
        }
        // 稳定性分数
        var stabilityScore = (this.currentQuality.stability || 1.0) * 100;
        // 拥塞分数（拥塞程度越低越好）
        var congestionScore = (1 - (this.currentQuality.congestion || 0)) * 100;
        // 可靠性分数
        var reliabilityScore = (this.currentQuality.reliability || 1.0) * 100;
        // 综合计算质量分数
        var qualityScore = Math.round(rttScore * 0.2 +
            packetLossScore * 0.2 +
            jitterScore * 0.1 +
            bandwidthScore * 0.2 +
            stabilityScore * 0.1 +
            congestionScore * 0.1 +
            reliabilityScore * 0.1);
        return Math.max(0, Math.min(100, qualityScore));
    };
    /**
     * 计算延迟趋势
     * 分析最近的RTT数据，确定延迟趋势（-1: 恶化, 0: 稳定, 1: 改善）
     */
    NetworkQualityMonitor.prototype.calculateLatencyTrend = function () {
        // 如果RTT历史数据不足，则认为趋势稳定
        if (this.rttHistory.length < 5) {
            // 添加当前RTT到历史数据
            this.rttHistory.push(this.currentQuality.rtt);
            if (this.rttHistory.length > 20) {
                this.rttHistory.shift();
            }
            return 0;
        }
        // 添加当前RTT到历史数据
        this.rttHistory.push(this.currentQuality.rtt);
        if (this.rttHistory.length > 20) {
            this.rttHistory.shift();
        }
        // 计算最近5个样本的平均RTT
        var recentAvg = this.rttHistory.slice(-5).reduce(function (sum, rtt) { return sum + rtt; }, 0) / 5;
        // 计算前5个样本的平均RTT
        var previousAvg = this.rttHistory.slice(-10, -5).reduce(function (sum, rtt) { return sum + rtt; }, 0) / 5;
        // 计算变化百分比
        var changePercent = (previousAvg - recentAvg) / previousAvg;
        // 确定趋势
        if (Math.abs(changePercent) < 0.1) {
            // 变化小于10%，认为趋势稳定
            return 0;
        }
        else if (changePercent > 0) {
            // 延迟减少，趋势改善
            return 1;
        }
        else {
            // 延迟增加，趋势恶化
            return -1;
        }
    };
    /**
     * 获取网络问题历史
     * @param count 获取的问题数量，默认为所有
     * @returns 网络问题历史
     */
    NetworkQualityMonitor.prototype.getIssueHistory = function (count) {
        if (count === void 0) { count = 0; }
        if (count <= 0 || count >= this.issueHistory.length) {
            return __spreadArray([], this.issueHistory, true);
        }
        return this.issueHistory.slice(-count);
    };
    /**
     * 获取当前活动的网络问题
     * @returns 当前活动的网络问题
     */
    NetworkQualityMonitor.prototype.getActiveIssues = function () {
        return Array.from(this.activeIssues.values());
    };
    /**
     * 获取网络稳定性历史
     * @returns 网络稳定性历史
     */
    NetworkQualityMonitor.prototype.getStabilityHistory = function () {
        return __spreadArray([], this.stabilityHistory, true);
    };
    /**
     * 获取网络拥塞历史
     * @returns 网络拥塞历史
     */
    NetworkQualityMonitor.prototype.getCongestionHistory = function () {
        return __spreadArray([], this.congestionHistory, true);
    };
    /**
     * 销毁监控器
     */
    NetworkQualityMonitor.prototype.dispose = function () {
        this.stopSampling();
        this.stopDiagnostics();
        this.stopReportGeneration();
        this.reset();
        this.removeAllListeners();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络质量监控器已销毁');
        }
    };
    /**
     * 启动网络报告生成
     */
    NetworkQualityMonitor.prototype.startReportGeneration = function () {
        var _this = this;
        if (this.reportTimerId !== null || !this.config.enableReportGeneration) {
            return;
        }
        this.reportTimerId = window.setInterval(function () {
            _this.generateNetworkReport();
        }, this.config.reportGenerationInterval);
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络报告生成已启动');
        }
    };
    /**
     * 停止网络报告生成
     */
    NetworkQualityMonitor.prototype.stopReportGeneration = function () {
        if (this.reportTimerId !== null) {
            clearInterval(this.reportTimerId);
            this.reportTimerId = null;
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkQualityMonitor', '网络报告生成已停止');
            }
        }
    };
    /**
     * 生成网络报告
     * @returns 网络报告
     */
    NetworkQualityMonitor.prototype.generateNetworkReport = function () {
        var now = Date.now();
        var report = {
            timestamp: now,
            timeRange: {
                start: now - this.config.historyDuration,
                end: now,
            },
            currentQuality: __assign({}, this.currentQuality),
            averageQuality: this.calculateAverageQuality(),
            activeIssues: Array.from(this.activeIssues.values()),
            resolvedIssues: this.issueHistory.slice(-10),
            networkType: this.networkType,
            connectionType: this.connectionType,
            prediction: this.predictNetworkQuality(),
            recommendations: this.generateRecommendations(),
        };
        // 触发报告生成事件
        this.emit('reportGenerated', report);
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', '网络报告已生成');
        }
        return report;
    };
    /**
     * 计算平均网络质量
     * @returns 平均网络质量数据
     */
    NetworkQualityMonitor.prototype.calculateAverageQuality = function () {
        if (this.qualityHistory.length === 0) {
            return __assign({}, this.currentQuality);
        }
        var sum = this.qualityHistory.reduce(function (acc, data) {
            acc.rtt += data.rtt;
            acc.packetLoss += data.packetLoss;
            acc.jitter += data.jitter;
            acc.bandwidth += data.bandwidth;
            acc.stability += data.stability || 1;
            acc.congestion += data.congestion || 0;
            acc.reliability += data.reliability || 1;
            acc.qualityScore += data.qualityScore || 100;
            return acc;
        }, {
            rtt: 0,
            packetLoss: 0,
            jitter: 0,
            bandwidth: 0,
            stability: 0,
            congestion: 0,
            reliability: 0,
            qualityScore: 0,
        });
        var count = this.qualityHistory.length;
        return {
            rtt: sum.rtt / count,
            packetLoss: sum.packetLoss / count,
            jitter: sum.jitter / count,
            bandwidth: sum.bandwidth / count,
            stability: sum.stability / count,
            congestion: sum.congestion / count,
            reliability: sum.reliability / count,
            qualityScore: sum.qualityScore / count,
        };
    };
    /**
     * 预测网络质量
     * @returns 预测的网络质量数据
     */
    NetworkQualityMonitor.prototype.predictNetworkQuality = function () {
        if (!this.config.enableNetworkPrediction || this.qualityHistory.length < this.config.predictionWindowSize) {
            return {
                rtt: this.currentQuality.rtt,
                packetLoss: this.currentQuality.packetLoss,
                bandwidth: this.currentQuality.bandwidth,
                stability: this.currentQuality.stability,
                confidence: 0.5,
            };
        }
        // 获取最近的数据
        var recentData = this.qualityHistory.slice(-this.config.predictionWindowSize);
        // 计算趋势
        var rttTrend = this.calculateTrend(recentData.map(function (data) { return data.rtt; }));
        var packetLossTrend = this.calculateTrend(recentData.map(function (data) { return data.packetLoss; }));
        var bandwidthTrend = this.calculateTrend(recentData.map(function (data) { return data.bandwidth; }));
        var stabilityTrend = this.calculateTrend(recentData.map(function (data) { return data.stability || 1; }));
        // 预测未来值
        var predictedRtt = Math.max(0, this.currentQuality.rtt + rttTrend);
        var predictedPacketLoss = Math.max(0, Math.min(1, this.currentQuality.packetLoss + packetLossTrend));
        var predictedBandwidth = Math.max(0, this.currentQuality.bandwidth + bandwidthTrend);
        var predictedStability = Math.max(0, Math.min(1, (this.currentQuality.stability || 1) + stabilityTrend));
        // 计算预测置信度
        var trendVariability = (this.calculateVariation(recentData.map(function (data) { return data.rtt; })) +
            this.calculateVariation(recentData.map(function (data) { return data.packetLoss; })) +
            this.calculateVariation(recentData.map(function (data) { return data.bandwidth; })) +
            this.calculateVariation(recentData.map(function (data) { return data.stability || 1; }))) / 4;
        var confidence = Math.max(0, Math.min(1, 1 - trendVariability));
        return {
            rtt: predictedRtt,
            packetLoss: predictedPacketLoss,
            bandwidth: predictedBandwidth,
            stability: predictedStability,
            confidence: confidence,
        };
    };
    /**
     * 计算数据趋势
     * @param values 数值数组
     * @returns 趋势值
     */
    NetworkQualityMonitor.prototype.calculateTrend = function (values) {
        if (values.length < 2) {
            return 0;
        }
        // 简单线性回归
        var n = values.length;
        var indices = Array.from({ length: n }, function (_, i) { return i; });
        var sumX = indices.reduce(function (sum, x) { return sum + x; }, 0);
        var sumY = values.reduce(function (sum, y) { return sum + y; }, 0);
        var sumXY = indices.reduce(function (sum, x, i) { return sum + x * values[i]; }, 0);
        var sumXX = indices.reduce(function (sum, x) { return sum + x * x; }, 0);
        var slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        return slope;
    };
    /**
     * 计算数据变异性
     * @param values 数值数组
     * @returns 变异性（0-1，越高表示数据越不稳定）
     */
    NetworkQualityMonitor.prototype.calculateVariation = function (values) {
        if (values.length < 2) {
            return 0;
        }
        // 计算平均值
        var mean = values.reduce(function (sum, val) { return sum + val; }, 0) / values.length;
        // 如果平均值为0，返回0以避免除以0
        if (mean === 0) {
            return 0;
        }
        // 计算变异系数（标准差/平均值）
        var squaredDiffs = values.map(function (val) { return Math.pow(val - mean, 2); });
        var variance = squaredDiffs.reduce(function (sum, diff) { return sum + diff; }, 0) / values.length;
        var stdDev = Math.sqrt(variance);
        // 变异系数，标准化到0-1范围
        var cv = stdDev / mean;
        return Math.min(1, Math.max(0, cv));
    };
    /**
     * 生成网络优化建议
     * @returns 建议列表
     */
    NetworkQualityMonitor.prototype.generateRecommendations = function () {
        var recommendations = [];
        // 基于当前网络质量和问题生成建议
        if (this.currentQuality.rtt > this.config.rttThresholds.bad) {
            recommendations.push('减少网络延迟：尝试连接到更近的服务器，检查本地网络连接，关闭其他占用带宽的应用');
        }
        if (this.currentQuality.packetLoss > this.config.packetLossThresholds.bad) {
            recommendations.push('减少丢包率：检查网络连接稳定性，避免使用无线网络，减少网络拥塞');
        }
        if (this.currentQuality.jitter > this.config.jitterThresholds.bad) {
            recommendations.push('减少网络抖动：使用有线网络连接，减少网络干扰，关闭其他占用带宽的应用');
        }
        if (this.currentQuality.bandwidth < this.config.bandwidthThresholds.bad) {
            recommendations.push('提高网络带宽：关闭其他占用带宽的应用，考虑升级网络带宽，降低数据传输质量');
        }
        if ((this.currentQuality.stability || 1) < 0.7) {
            recommendations.push('提高连接稳定性：检查网络连接稳定性，使用有线网络连接，更换网络环境');
        }
        if ((this.currentQuality.congestion || 0) > 0.7) {
            recommendations.push('减少网络拥塞：关闭其他占用带宽的应用，避开网络高峰期，考虑升级网络带宽');
        }
        // 如果没有特定问题，提供一般性建议
        if (recommendations.length === 0) {
            recommendations.push('当前网络状况良好，无需特别优化');
        }
        return recommendations;
    };
    /**
     * 记录网络事件
     * @param type 事件类型
     * @param data 事件数据
     */
    NetworkQualityMonitor.prototype.logNetworkEvent = function (type, data) {
        if (!this.config.enableNetworkEventLogging) {
            return;
        }
        this.networkEventLog.push({
            timestamp: Date.now(),
            type: type,
            data: data,
        });
        // 限制日志大小
        if (this.networkEventLog.length > 1000) {
            this.networkEventLog.shift();
        }
        // 触发事件记录事件
        this.emit('eventLogged', { type: type, data: data });
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "\u7F51\u7EDC\u4E8B\u4EF6\u5DF2\u8BB0\u5F55: ".concat(type));
        }
    };
    /**
     * 获取网络事件日志
     * @param count 获取的事件数量，默认为所有
     * @returns 网络事件日志
     */
    NetworkQualityMonitor.prototype.getNetworkEventLog = function (count) {
        if (count === void 0) { count = 0; }
        if (count <= 0 || count >= this.networkEventLog.length) {
            return __spreadArray([], this.networkEventLog, true);
        }
        return this.networkEventLog.slice(-count);
    };
    /**
     * 检测网络类型
     * 注意：这是一个模拟实现，实际应用中需要使用浏览器API或其他方法获取真实网络类型
     */
    NetworkQualityMonitor.prototype.detectNetworkType = function () {
        if (!this.config.enableNetworkTypeDetection) {
            return;
        }
        // 模拟网络类型检测
        // 实际应用中可以使用navigator.connection或其他API
        var networkTypes = ['wifi', 'ethernet', 'cellular', 'unknown'];
        var connectionTypes = ['4g', '5g', 'wifi', 'ethernet', 'unknown'];
        // 这里只是随机选择一个类型，实际应用中应该使用真实检测
        this.networkType = networkTypes[Math.floor(Math.random() * networkTypes.length)];
        this.connectionType = connectionTypes[Math.floor(Math.random() * connectionTypes.length)];
        // 更新当前质量数据
        this.currentQuality.networkType = this.networkType;
        this.currentQuality.connectionType = this.connectionType;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "\u68C0\u6D4B\u5230\u7F51\u7EDC\u7C7B\u578B: ".concat(this.networkType, ", \u8FDE\u63A5\u7C7B\u578B: ").concat(this.connectionType));
        }
    };
    /**
     * 测量DNS解析时间
     * 注意：这是一个模拟实现，实际应用中需要使用真实的DNS解析测量
     */
    NetworkQualityMonitor.prototype.measureDnsResolutionTime = function () {
        if (!this.config.enableDnsMonitoring) {
            return;
        }
        // 模拟DNS解析时间测量
        // 实际应用中可以使用Performance API或其他方法
        this.dnsResolutionTime = Math.random() * 100; // 0-100ms
        // 更新当前质量数据
        this.currentQuality.dnsResolutionTime = this.dnsResolutionTime;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "DNS\u89E3\u6790\u65F6\u95F4: ".concat(this.dnsResolutionTime.toFixed(2), "ms"));
        }
    };
    /**
     * 测量服务器响应时间
     * 注意：这是一个模拟实现，实际应用中需要使用真实的服务器响应时间测量
     */
    NetworkQualityMonitor.prototype.measureServerResponseTime = function () {
        if (!this.config.enableServerResponseTimeMonitoring) {
            return;
        }
        // 模拟服务器响应时间测量
        // 实际应用中可以使用真实的服务器请求
        this.serverResponseTime = this.currentQuality.rtt * 0.7; // 假设服务器响应时间占RTT的70%
        // 更新当前质量数据
        this.currentQuality.serverResponseTime = this.serverResponseTime;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "\u670D\u52A1\u5668\u54CD\u5E94\u65F6\u95F4: ".concat(this.serverResponseTime.toFixed(2), "ms"));
        }
    };
    /**
     * 分析网络路径
     * 注意：这是一个模拟实现，实际应用中需要使用真实的网络路径分析
     */
    NetworkQualityMonitor.prototype.analyzeNetworkPath = function () {
        if (!this.config.enablePathAnalysis) {
            return;
        }
        // 模拟网络路径分析
        // 实际应用中可以使用traceroute或其他方法
        var hopCount = Math.floor(Math.random() * 10) + 1; // 1-10跳
        this.pathAnalysisData = [];
        for (var i = 1; i <= hopCount; i++) {
            this.pathAnalysisData.push({
                hop: i,
                address: "192.168.".concat(i, ".1"),
                rtt: Math.random() * 50 * i, // 模拟RTT随跳数增加
            });
        }
        // 更新当前质量数据
        this.currentQuality.hopCount = hopCount;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkQualityMonitor', "\u7F51\u7EDC\u8DEF\u5F84\u5206\u6790\u5B8C\u6210\uFF0C\u8DF3\u6570: ".concat(hopCount));
        }
    };
    /**
     * 获取网络路径分析数据
     * @returns 网络路径分析数据
     */
    NetworkQualityMonitor.prototype.getNetworkPathData = function () {
        return __spreadArray([], this.pathAnalysisData, true);
    };
    return NetworkQualityMonitor;
}(EventEmitter_1.EventEmitter));
exports.NetworkQualityMonitor = NetworkQualityMonitor;
