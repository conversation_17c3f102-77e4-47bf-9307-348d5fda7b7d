"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ALBERTModel = void 0;
/**
 * ALBERT模型
 * 用于自然语言理解任务的轻量级BERT变体
 */
var AIModelType_1 = require("../AIModelType");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * ALBERT模型
 */
var ALBERTModel = exports.ALBERTModel = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    function ALBERTModel(config, globalConfig) {
        if (config === void 0) { config = {}; }
        if (globalConfig === void 0) { globalConfig = {}; }
        /** 模型类型 */
        this.modelType = AIModelType_1.AIModelType.ALBERT;
        /** 是否已初始化 */
        this.initialized = false;
        /** 是否正在初始化 */
        this.initializing = false;
        /** 模型（仅用于类型安全） */
        this.model = null;
        /** 分词器（仅用于类型安全） */
        this.tokenizer = null;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        this.config = __assign({ version: 'base', variant: 'base', emotionCategories: ALBERTModel.DEFAULT_EMOTION_CATEGORIES, confidenceThreshold: 0.5, maxSequenceLength: 128 }, config);
        this.globalConfig = globalConfig;
    }
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    ALBERTModel.prototype.getType = function () {
        return this.modelType;
    };
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    ALBERTModel.prototype.getConfig = function () {
        return this.config;
    };
    /**
     * 获取模型实例（仅用于内部使用）
     * @returns 模型实例
     * @internal
     */
    ALBERTModel.prototype.getModelInstance = function () {
        return this.model;
    };
    /**
     * 获取分词器实例（仅用于内部使用）
     * @returns 分词器实例
     * @internal
     */
    ALBERTModel.prototype.getTokenizerInstance = function () {
        return this.tokenizer;
    };
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    ALBERTModel.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var debug, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.initialized) {
                            return [2 /*return*/, true];
                        }
                        if (this.initializing) {
                            // 等待初始化完成
                            return [2 /*return*/, new Promise(function (resolve) {
                                    var checkInterval = setInterval(function () {
                                        if (_this.initialized) {
                                            clearInterval(checkInterval);
                                            resolve(true);
                                        }
                                    }, 100);
                                })];
                        }
                        this.initializing = true;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log('初始化ALBERT模型');
                        }
                        // 这里是初始化模型的占位代码
                        // 实际实现需要根据具体需求
                        // 模拟初始化延迟
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 300); })];
                    case 2:
                        // 这里是初始化模型的占位代码
                        // 实际实现需要根据具体需求
                        // 模拟初始化延迟
                        _a.sent();
                        // 创建模拟模型和分词器
                        this.model = {
                            predict: function (input) { return _this.mockPredict(input); }
                        };
                        this.tokenizer = {
                            encode: function (_text) { return ({ input_ids: [1, 2, 3], attention_mask: [1, 1, 1] }); }
                        };
                        this.initialized = true;
                        this.initializing = false;
                        if (debug) {
                            console.log('ALBERT模型初始化成功');
                        }
                        return [2 /*return*/, true];
                    case 3:
                        error_1 = _a.sent();
                        this.initializing = false;
                        console.error('初始化ALBERT模型失败:', error_1);
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    ALBERTModel.prototype.generateText = function (_prompt, _options) {
        if (_options === void 0) { _options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                throw new Error('ALBERT模型不支持文本生成');
            });
        });
    };
    /**
     * 分类文本
     * @param text 要分类的文本
     * @param categories 分类类别
     * @returns 分类结果
     */
    ALBERTModel.prototype.classifyText = function (text, _categories) {
        return __awaiter(this, void 0, void 0, function () {
            var debug, model, tokenizer, encoded, prediction, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        try {
                            debug = this.config.debug || this.globalConfig.debug;
                            if (debug) {
                                console.log("\u5206\u7C7B\u6587\u672C: \"".concat(text, "\""));
                            }
                            model = this.getModelInstance();
                            tokenizer = this.getTokenizerInstance();
                            // 检查模型和分词器是否可用
                            if (!model || !tokenizer) {
                                throw new Error('模型或分词器未初始化');
                            }
                            encoded = tokenizer.encode(text);
                            prediction = model.predict(encoded);
                            result = {
                                label: prediction.prediction || 'positive',
                                confidence: prediction.confidence || 0.82,
                                allLabels: prediction.scores || {
                                    'positive': 0.82,
                                    'neutral': 0.12,
                                    'negative': 0.06
                                }
                            };
                            return [2 /*return*/, result];
                        }
                        catch (error) {
                            console.error('分类文本失败:', error);
                            throw error;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 销毁模型
     */
    ALBERTModel.prototype.dispose = function () {
        // 清理资源
        this.model = null;
        this.tokenizer = null;
        this.initialized = false;
        this.eventEmitter.removeAllListeners();
    };
    /**
     * 模拟预测
     * @param input 输入
     * @returns 预测结果
     */
    ALBERTModel.prototype.mockPredict = function (_input) {
        // 模拟预测结果
        return {
            prediction: 'positive',
            confidence: 0.82,
            scores: {
                'positive': 0.82,
                'neutral': 0.12,
                'negative': 0.06
            }
        };
    };
    /** 默认情感类别 */
    ALBERTModel.DEFAULT_EMOTION_CATEGORIES = [
        'happy', 'sad', 'angry', 'surprised', 'fear', 'disgust', 'neutral'
    ];
    return ALBERTModel;
}());
