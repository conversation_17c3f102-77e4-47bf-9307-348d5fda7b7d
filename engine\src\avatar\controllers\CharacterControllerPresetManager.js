"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterControllerPresetManager = exports.ControllerPresetType = void 0;
/**
 * 角色控制器预设管理器
 * 用于管理角色控制器的预设和模板
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
var Debug_1 = require("../../utils/Debug");
/**
 * 控制器预设类型
 */
var ControllerPresetType;
(function (ControllerPresetType) {
    /** 基础预设 */
    ControllerPresetType["BASIC"] = "basic";
    /** 高级预设 */
    ControllerPresetType["ADVANCED"] = "advanced";
    /** 特殊预设 */
    ControllerPresetType["SPECIAL"] = "special";
    /** 自定义预设 */
    ControllerPresetType["CUSTOM"] = "custom";
    /** 第一人称预设 */
    ControllerPresetType["FIRST_PERSON"] = "first_person";
    /** 第三人称预设 */
    ControllerPresetType["THIRD_PERSON"] = "third_person";
    /** 飞行预设 */
    ControllerPresetType["FLYING"] = "flying";
    /** 游泳预设 */
    ControllerPresetType["SWIMMING"] = "swimming";
    /** 驾驶预设 */
    ControllerPresetType["DRIVING"] = "driving";
    /** 战斗预设 */
    ControllerPresetType["COMBAT"] = "combat";
    /** 潜行预设 */
    ControllerPresetType["STEALTH"] = "stealth";
    /** 攀爬预设 */
    ControllerPresetType["CLIMBING"] = "climbing";
    /** 跑酷预设 */
    ControllerPresetType["PARKOUR"] = "parkour";
    /** 舞蹈预设 */
    ControllerPresetType["DANCING"] = "dancing";
    /** 物理交互预设 */
    ControllerPresetType["PHYSICS_INTERACTION"] = "physics_interaction";
    /** 环境感知预设 */
    ControllerPresetType["ENVIRONMENT_AWARE"] = "environment_aware";
})(ControllerPresetType || (exports.ControllerPresetType = ControllerPresetType = {}));
/**
 * 角色控制器预设管理器
 */
var CharacterControllerPresetManager = exports.CharacterControllerPresetManager = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 配置
     */
    function CharacterControllerPresetManager(config) {
        if (config === void 0) { config = {}; }
        /** 预设映射 */
        this.presets = new Map();
        /** 模板映射 */
        this.templates = new Map();
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 是否已初始化 */
        this.initialized = false;
        this.config = __assign(__assign({}, CharacterControllerPresetManager.DEFAULT_CONFIG), config);
        // 初始化
        this.initialize();
    }
    /**
     * 获取单例实例
     * @param config 配置
     * @returns 预设管理器实例
     */
    CharacterControllerPresetManager.getInstance = function (config) {
        if (!CharacterControllerPresetManager.instance) {
            CharacterControllerPresetManager.instance = new CharacterControllerPresetManager(config);
        }
        return CharacterControllerPresetManager.instance;
    };
    /**
     * 初始化
     */
    CharacterControllerPresetManager.prototype.initialize = function () {
        if (this.initialized)
            return;
        if (this.config.debug) {
            Debug_1.Debug.log('角色控制器预设管理器初始化');
        }
        // 加载预设
        if (this.config.autoLoadPresets) {
            this.loadPresets();
        }
        this.initialized = true;
    };
    /**
     * 加载预设
     */
    CharacterControllerPresetManager.prototype.loadPresets = function () {
        // 加载内置预设
        this.loadBuiltInPresets();
        // 从本地存储加载
        if (this.config.useLocalStorage) {
            this.loadFromLocalStorage();
        }
        // 从服务器加载
        this.loadFromServer();
        if (this.config.debug) {
            Debug_1.Debug.log("\u5DF2\u52A0\u8F7D ".concat(this.presets.size, " \u4E2A\u9884\u8BBE\u548C ").concat(this.templates.size, " \u4E2A\u6A21\u677F"));
        }
    };
    /**
     * 加载内置预设
     */
    CharacterControllerPresetManager.prototype.loadBuiltInPresets = function () {
        // 添加基础角色预设
        this.addPreset({
            id: 'basic_character',
            name: '基础角色控制器',
            description: '标准的角色控制器预设，适用于大多数角色',
            type: ControllerPresetType.BASIC,
            tags: ['基础', '标准', '通用'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                useStateMachine: true,
                useBlendSpace: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加高级角色预设
        this.addPreset({
            id: 'advanced_character',
            name: '高级角色控制器',
            description: '高级角色控制器预设，包含更多功能和更好的物理交互',
            type: ControllerPresetType.ADVANCED,
            tags: ['高级', '物理', '交互'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                crouchSpeed: 1.0,
                jumpForce: 5.0,
                gravity: 9.8,
                turnSpeed: 2.0,
                airControl: 0.5,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加第一人称角色预设
        this.addPreset({
            id: 'first_person_character',
            name: '第一人称角色控制器',
            description: '第一人称角色控制器预设，适用于FPS游戏',
            type: ControllerPresetType.FIRST_PERSON,
            tags: ['第一人称', 'FPS', '射击'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: false,
                useIK: false,
                useEnvironmentAwareness: false
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加第三人称角色预设
        this.addPreset({
            id: 'third_person_character',
            name: '第三人称角色控制器',
            description: '第三人称角色控制器预设，适用于动作冒险游戏',
            type: ControllerPresetType.THIRD_PERSON,
            tags: ['第三人称', '动作', '冒险'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: false
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加飞行角色预设
        this.addPreset({
            id: 'flying_character',
            name: '飞行角色控制器',
            description: '飞行角色控制器预设，适用于飞行场景',
            type: ControllerPresetType.FLYING,
            tags: ['飞行', '空中', '翱翔'],
            config: {
                flySpeed: 10.0,
                turnSpeed: 2.0,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true,
                gravity: 0
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加物理交互角色预设
        this.addPreset({
            id: 'physics_interaction_character',
            name: '物理交互角色控制器',
            description: '物理交互角色控制器预设，适用于需要与物理对象交互的场景',
            type: ControllerPresetType.PHYSICS_INTERACTION,
            tags: ['物理', '交互', '抓取'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加环境感知角色预设
        this.addPreset({
            id: 'environment_aware_character',
            name: '环境感知角色控制器',
            description: '环境感知角色控制器预设，适用于需要对环境做出响应的场景',
            type: ControllerPresetType.ENVIRONMENT_AWARE,
            tags: ['环境', '感知', '响应'],
            config: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加基础模板
        this.addTemplate({
            id: 'basic_template',
            name: '基础角色模板',
            description: '可自定义的基础角色控制器模板',
            type: ControllerPresetType.BASIC,
            tags: ['基础', '模板', '通用'],
            baseConfig: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                useStateMachine: true,
                useBlendSpace: true
            },
            parameters: [
                {
                    id: 'walkSpeed',
                    name: '行走速度',
                    description: '角色行走的速度',
                    type: 'number',
                    defaultValue: 2.0,
                    min: 0.5,
                    max: 5.0,
                    step: 0.1,
                    required: true,
                    group: '移动'
                },
                {
                    id: 'runSpeed',
                    name: '跑步速度',
                    description: '角色跑步的速度',
                    type: 'number',
                    defaultValue: 5.0,
                    min: 1.0,
                    max: 10.0,
                    step: 0.1,
                    required: true,
                    group: '移动'
                },
                {
                    id: 'jumpForce',
                    name: '跳跃力量',
                    description: '角色跳跃的力量',
                    type: 'number',
                    defaultValue: 5.0,
                    min: 1.0,
                    max: 10.0,
                    step: 0.1,
                    required: true,
                    group: '移动'
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加物理交互模板
        this.addTemplate({
            id: 'physics_interaction_template',
            name: '物理交互角色模板',
            description: '可自定义的物理交互角色控制器模板',
            type: ControllerPresetType.PHYSICS_INTERACTION,
            tags: ['物理', '交互', '模板'],
            baseConfig: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true
            },
            parameters: [
                {
                    id: 'interactionRange',
                    name: '交互范围',
                    description: '角色可以与物体交互的最大距离',
                    type: 'number',
                    defaultValue: 2.0,
                    min: 0.5,
                    max: 5.0,
                    step: 0.1,
                    required: true,
                    group: '交互'
                },
                {
                    id: 'maxForce',
                    name: '最大力量',
                    description: '角色可以施加的最大力量',
                    type: 'number',
                    defaultValue: 1000,
                    min: 100,
                    max: 5000,
                    step: 100,
                    required: true,
                    group: '交互'
                },
                {
                    id: 'maxTorque',
                    name: '最大扭矩',
                    description: '角色可以施加的最大扭矩',
                    type: 'number',
                    defaultValue: 500,
                    min: 50,
                    max: 2000,
                    step: 50,
                    required: true,
                    group: '交互'
                },
                {
                    id: 'useRagdoll',
                    name: '使用布娃娃物理',
                    description: '是否启用布娃娃物理效果',
                    type: 'boolean',
                    defaultValue: true,
                    required: false,
                    group: '物理'
                },
                {
                    id: 'ragdollTransitionTime',
                    name: '布娃娃过渡时间',
                    description: '从动画到布娃娃物理的过渡时间',
                    type: 'number',
                    defaultValue: 0.5,
                    min: 0.1,
                    max: 2.0,
                    step: 0.1,
                    required: false,
                    group: '物理'
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 添加环境感知模板
        this.addTemplate({
            id: 'environment_aware_template',
            name: '环境感知角色模板',
            description: '可自定义的环境感知角色控制器模板',
            type: ControllerPresetType.ENVIRONMENT_AWARE,
            tags: ['环境', '感知', '模板'],
            baseConfig: {
                walkSpeed: 2.0,
                runSpeed: 5.0,
                jumpForce: 5.0,
                gravity: 9.8,
                usePhysics: true,
                useStateMachine: true,
                useBlendSpace: true,
                useIK: true,
                useEnvironmentAwareness: true
            },
            parameters: [
                {
                    id: 'awarenessRange',
                    name: '感知范围',
                    description: '角色可以感知环境的最大距离',
                    type: 'number',
                    defaultValue: 50,
                    min: 10,
                    max: 200,
                    step: 10,
                    required: true,
                    group: '感知'
                },
                {
                    id: 'updateFrequency',
                    name: '更新频率',
                    description: '环境感知系统更新的频率（毫秒）',
                    type: 'number',
                    defaultValue: 1000,
                    min: 100,
                    max: 5000,
                    step: 100,
                    required: true,
                    group: '感知'
                },
                {
                    id: 'autoDetect',
                    name: '自动检测',
                    description: '是否自动检测环境',
                    type: 'boolean',
                    defaultValue: true,
                    required: false,
                    group: '感知'
                },
                {
                    id: 'autoRespond',
                    name: '自动响应',
                    description: '是否自动响应环境变化',
                    type: 'boolean',
                    defaultValue: true,
                    required: false,
                    group: '响应'
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        });
    };
    /**
     * 从本地存储加载
     */
    CharacterControllerPresetManager.prototype.loadFromLocalStorage = function () {
        // 实现从本地存储加载预设和模板的逻辑
    };
    /**
     * 从服务器加载
     */
    CharacterControllerPresetManager.prototype.loadFromServer = function () {
        // 实现从服务器加载预设和模板的逻辑
    };
    /**
     * 添加预设
     * @param preset 预设数据
     */
    CharacterControllerPresetManager.prototype.addPreset = function (preset) {
        this.presets.set(preset.id, preset);
        // 发出预设添加事件
        this.eventEmitter.emit('presetAdded', preset);
        if (this.config.debug) {
            Debug_1.Debug.log("\u6DFB\u52A0\u9884\u8BBE: ".concat(preset.id), preset);
        }
    };
    /**
     * 添加模板
     * @param template 模板数据
     */
    CharacterControllerPresetManager.prototype.addTemplate = function (template) {
        this.templates.set(template.id, template);
        // 发出模板添加事件
        this.eventEmitter.emit('templateAdded', template);
        if (this.config.debug) {
            Debug_1.Debug.log("\u6DFB\u52A0\u6A21\u677F: ".concat(template.id), template);
        }
    };
    /**
     * 获取预设
     * @param id 预设ID
     * @returns 预设数据
     */
    CharacterControllerPresetManager.prototype.getPreset = function (id) {
        return this.presets.get(id);
    };
    /**
     * 获取模板
     * @param id 模板ID
     * @returns 模板数据
     */
    CharacterControllerPresetManager.prototype.getTemplate = function (id) {
        return this.templates.get(id);
    };
    /**
     * 获取所有预设
     * @returns 预设数据数组
     */
    CharacterControllerPresetManager.prototype.getAllPresets = function () {
        return Array.from(this.presets.values());
    };
    /**
     * 获取所有模板
     * @returns 模板数据数组
     */
    CharacterControllerPresetManager.prototype.getAllTemplates = function () {
        return Array.from(this.templates.values());
    };
    /**
     * 应用模板
     * @param templateId 模板ID
     * @param parameters 参数值
     * @returns 控制器配置
     */
    CharacterControllerPresetManager.prototype.applyTemplate = function (templateId, parameters) {
        if (parameters === void 0) { parameters = {}; }
        // 获取模板
        var template = this.templates.get(templateId);
        if (!template) {
            if (this.config.debug) {
                Debug_1.Debug.warn("\u6A21\u677F\u4E0D\u5B58\u5728: ".concat(templateId));
            }
            return null;
        }
        // 创建配置副本
        var config = __assign({}, template.baseConfig);
        // 应用参数
        for (var _i = 0, _a = template.parameters; _i < _a.length; _i++) {
            var param = _a[_i];
            var value = parameters[param.id];
            if (value !== undefined) {
                // 验证参数值
                if (param.type === 'number' && typeof value === 'number') {
                    if (param.min !== undefined && value < param.min)
                        continue;
                    if (param.max !== undefined && value > param.max)
                        continue;
                }
                // 设置参数值
                config[param.id] = value;
            }
        }
        return config;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    CharacterControllerPresetManager.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    CharacterControllerPresetManager.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /** 默认配置 */
    CharacterControllerPresetManager.DEFAULT_CONFIG = {
        debug: false,
        presetsPath: 'presets/controllers',
        autoLoadPresets: true,
        useLocalStorage: true
    };
    return CharacterControllerPresetManager;
}());
