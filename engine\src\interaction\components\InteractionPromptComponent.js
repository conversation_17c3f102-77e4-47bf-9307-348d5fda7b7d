"use strict";
/**
 * InteractionPromptComponent.ts
 *
 * 交互提示组件，用于显示交互提示
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionPromptComponent = exports.PromptPositionType = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
/**
 * 提示位置类型枚举
 */
var PromptPositionType;
(function (PromptPositionType) {
    /** 世界空间 */
    PromptPositionType["WORLD"] = "world";
    /** 屏幕空间 */
    PromptPositionType["SCREEN"] = "screen";
    /** 跟随对象 */
    PromptPositionType["FOLLOW"] = "follow";
})(PromptPositionType || (exports.PromptPositionType = PromptPositionType = {}));
/**
 * 交互提示组件
 * 用于显示交互提示
 */
var InteractionPromptComponent = /** @class */ (function (_super) {
    __extends(InteractionPromptComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    function InteractionPromptComponent(entity, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'InteractionPrompt') || this;
        /** 当前不透明度 */
        _this.opacity = 0;
        /** 是否正在淡入 */
        _this.isFadingIn = false;
        /** 是否正在淡出 */
        _this.isFadingOut = false;
        /** 淡入开始时间 */
        _this.fadeInStartTime = 0;
        /** 淡出开始时间 */
        _this.fadeOutStartTime = 0;
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化属性
        _this._text = config.text || '';
        _this._icon = config.icon;
        _this._positionType = config.positionType || PromptPositionType.FOLLOW;
        _this._offset = config.offset || new three_1.Vector3(0, 1, 0);
        _this._duration = config.duration !== undefined ? config.duration : 0;
        _this._fadeInTime = config.fadeInTime || 200;
        _this._fadeOutTime = config.fadeOutTime || 200;
        _this._backgroundColor = config.backgroundColor || 'rgba(0, 0, 0, 0.7)';
        _this._textColor = config.textColor || '#ffffff';
        _this._borderColor = config.borderColor || '#ffffff';
        _this._borderWidth = config.borderWidth !== undefined ? config.borderWidth : 1;
        _this._borderRadius = config.borderRadius !== undefined ? config.borderRadius : 5;
        _this._fontSize = config.fontSize || 14;
        _this._fontFamily = config.fontFamily || 'Arial, sans-serif';
        _this._padding = config.padding !== undefined ? config.padding : 10;
        _this._autoHide = config.autoHide !== undefined ? config.autoHide : true;
        _this._visible = config.visible !== undefined ? config.visible : false;
        // 创建HTML元素
        _this.createHTMLElement();
        // 如果初始可见，则显示
        if (_this._visible) {
            _this.show();
        }
        return _this;
    }
    Object.defineProperty(InteractionPromptComponent.prototype, "text", {
        /**
         * 获取提示文本
         */
        get: function () {
            return this._text;
        },
        /**
         * 设置提示文本
         */
        set: function (value) {
            this._text = value;
            this.updateText();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionPromptComponent.prototype, "icon", {
        /**
         * 获取提示图标
         */
        get: function () {
            return this._icon;
        },
        /**
         * 设置提示图标
         */
        set: function (value) {
            this._icon = value;
            this.updateIcon();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionPromptComponent.prototype, "positionType", {
        /**
         * 获取提示位置类型
         */
        get: function () {
            return this._positionType;
        },
        /**
         * 设置提示位置类型
         */
        set: function (value) {
            this._positionType = value;
            this.updatePosition();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionPromptComponent.prototype, "offset", {
        /**
         * 获取提示位置偏移
         */
        get: function () {
            return this._offset;
        },
        /**
         * 设置提示位置偏移
         */
        set: function (value) {
            this._offset = value;
            this.updatePosition();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionPromptComponent.prototype, "visible", {
        /**
         * 获取是否可见
         */
        get: function () {
            return this._visible;
        },
        /**
         * 设置是否可见
         */
        set: function (value) {
            if (this._visible === value)
                return;
            this._visible = value;
            if (value) {
                this.show();
            }
            else {
                this.hide();
            }
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 创建HTML元素
     */
    InteractionPromptComponent.prototype.createHTMLElement = function () {
        // 如果已经存在，则返回
        if (this.element)
            return;
        // 创建元素
        this.element = document.createElement('div');
        this.element.className = 'interaction-prompt';
        this.element.style.position = 'absolute';
        this.element.style.pointerEvents = 'none';
        this.element.style.opacity = '0';
        this.element.style.transition = "opacity ".concat(this._fadeInTime, "ms ease-in-out");
        this.element.style.backgroundColor = this._backgroundColor;
        this.element.style.color = this._textColor;
        this.element.style.border = "".concat(this._borderWidth, "px solid ").concat(this._borderColor);
        this.element.style.borderRadius = "".concat(this._borderRadius, "px");
        this.element.style.padding = "".concat(this._padding, "px");
        this.element.style.fontSize = "".concat(this._fontSize, "px");
        this.element.style.fontFamily = this._fontFamily;
        this.element.style.zIndex = '1000';
        this.element.style.display = 'none';
        // 创建图标元素（如果有）
        if (this._icon) {
            var iconElement = document.createElement('img');
            iconElement.src = this._icon;
            iconElement.style.marginRight = '5px';
            iconElement.style.verticalAlign = 'middle';
            this.element.appendChild(iconElement);
        }
        // 创建文本元素
        var textElement = document.createElement('span');
        textElement.textContent = this._text;
        this.element.appendChild(textElement);
        // 添加到文档
        document.body.appendChild(this.element);
    };
    /**
     * 更新文本
     */
    InteractionPromptComponent.prototype.updateText = function () {
        if (!this.element)
            return;
        // 查找文本元素
        var textElement = this.element.querySelector('span');
        if (textElement) {
            textElement.textContent = this._text;
        }
    };
    /**
     * 更新图标
     */
    InteractionPromptComponent.prototype.updateIcon = function () {
        var _a;
        if (!this.element)
            return;
        // 查找图标元素
        var iconElement = this.element.querySelector('img');
        // 如果有图标但没有图标元素，则创建
        if (this._icon && !iconElement) {
            iconElement = document.createElement('img');
            iconElement.style.marginRight = '5px';
            iconElement.style.verticalAlign = 'middle';
            this.element.insertBefore(iconElement, this.element.firstChild);
        }
        // 如果有图标元素，则更新
        if (iconElement) {
            if (this._icon) {
                iconElement.src = this._icon;
            }
            else {
                // 如果没有图标，则移除图标元素
                (_a = iconElement.parentElement) === null || _a === void 0 ? void 0 : _a.removeChild(iconElement);
            }
        }
    };
    /**
     * 更新位置
     */
    InteractionPromptComponent.prototype.updatePosition = function () {
        if (!this.element)
            return;
        // 根据位置类型更新位置
        switch (this._positionType) {
            case PromptPositionType.WORLD:
                // TODO: 实现世界空间位置
                break;
            case PromptPositionType.SCREEN:
                // 屏幕空间位置
                this.element.style.left = "".concat(this._offset.x, "px");
                this.element.style.top = "".concat(this._offset.y, "px");
                break;
            case PromptPositionType.FOLLOW:
                // TODO: 实现跟随对象位置
                break;
        }
    };
    /**
     * 显示提示
     */
    InteractionPromptComponent.prototype.show = function () {
        var _this = this;
        if (!this.element)
            return;
        // 清除之前的计时器
        if (this.showTimer !== undefined) {
            clearTimeout(this.showTimer);
            this.showTimer = undefined;
        }
        // 显示元素
        this.element.style.display = 'block';
        // 开始淡入
        this.startFadeIn();
        // 如果设置了持续时间且自动隐藏，则设置计时器
        if (this._duration > 0 && this._autoHide) {
            this.showTimer = window.setTimeout(function () {
                _this.hide();
            }, this._duration);
        }
    };
    /**
     * 隐藏提示
     */
    InteractionPromptComponent.prototype.hide = function () {
        if (!this.element)
            return;
        // 清除之前的计时器
        if (this.showTimer !== undefined) {
            clearTimeout(this.showTimer);
            this.showTimer = undefined;
        }
        // 开始淡出
        this.startFadeOut();
    };
    /**
     * 开始淡入
     */
    InteractionPromptComponent.prototype.startFadeIn = function () {
        this.isFadingIn = true;
        this.isFadingOut = false;
        this.fadeInStartTime = Date.now();
    };
    /**
     * 开始淡出
     */
    InteractionPromptComponent.prototype.startFadeOut = function () {
        this.isFadingIn = false;
        this.isFadingOut = true;
        this.fadeOutStartTime = Date.now();
    };
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    InteractionPromptComponent.prototype.update = function (deltaTime) {
        if (!this.element)
            return;
        // 更新位置
        this.updatePosition();
        // 处理淡入淡出
        var now = Date.now();
        if (this.isFadingIn) {
            var elapsed = now - this.fadeInStartTime;
            var progress = Math.min(elapsed / this._fadeInTime, 1);
            this.opacity = progress;
            if (progress >= 1) {
                this.isFadingIn = false;
            }
        }
        else if (this.isFadingOut) {
            var elapsed = now - this.fadeOutStartTime;
            var progress = Math.min(elapsed / this._fadeOutTime, 1);
            this.opacity = 1 - progress;
            if (progress >= 1) {
                this.isFadingOut = false;
                this.element.style.display = 'none';
            }
        }
        // 更新不透明度
        this.element.style.opacity = this.opacity.toString();
    };
    /**
     * 销毁组件
     */
    InteractionPromptComponent.prototype.dispose = function () {
        // 清除计时器
        if (this.showTimer !== undefined) {
            clearTimeout(this.showTimer);
            this.showTimer = undefined;
        }
        // 移除HTML元素
        if (this.element && this.element.parentElement) {
            this.element.parentElement.removeChild(this.element);
        }
        this.element = undefined;
        // 调用基类的销毁方法
        _super.prototype.dispose.call(this);
    };
    return InteractionPromptComponent;
}(Component_1.Component));
exports.InteractionPromptComponent = InteractionPromptComponent;
