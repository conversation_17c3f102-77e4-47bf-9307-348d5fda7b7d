"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VisualScriptEngine = void 0;
var EventEmitter_1 = require("../utils/EventEmitter");
var EventNode_1 = require("./nodes/EventNode");
var Fiber_1 = require("./execution/Fiber");
var ExecutionContext_1 = require("./execution/ExecutionContext");
var Graph_1 = require("./graph/Graph");
var Variable_1 = require("./values/Variable");
var CustomEvent_1 = require("./events/CustomEvent");
/**
 * 视觉脚本引擎
 * 负责执行视觉脚本
 */
var VisualScriptEngine = /** @class */ (function (_super) {
    __extends(VisualScriptEngine, _super);
    /**
     * 创建视觉脚本引擎
     * @param options 引擎选项
     */
    function VisualScriptEngine(options) {
        var _this = _super.call(this) || this;
        /** 节点实例映射 */
        _this.nodes = new Map();
        /** 事件节点列表 */
        _this.eventNodes = [];
        /** 执行纤程队列 */
        _this.fiberQueue = [];
        /** 变量映射 */
        _this.variables = new Map();
        /** 自定义事件映射 */
        _this.customEvents = new Map();
        /** 是否正在运行 */
        _this.running = false;
        /** 执行步数 */
        _this.executionSteps = 0;
        /** 最大执行步数限制 */
        _this.maxExecutionSteps = 1000000;
        /** 最大执行时间限制（毫秒） */
        _this.maxExecutionTime = 100;
        _this.script = options.script;
        _this.nodeRegistry = options.nodeRegistry;
        _this.valueTypeRegistry = options.valueTypeRegistry;
        _this.entity = options.entity;
        _this.world = options.world;
        // 创建图形实例
        _this.graph = new Graph_1.Graph({
            id: _this.entity.id,
            name: _this.entity.name
        });
        // 创建执行上下文
        _this.executionContext = new ExecutionContext_1.ExecutionContext({
            engine: _this,
            entity: _this.entity,
            world: _this.world
        });
        // 初始化引擎
        _this.initialize();
        return _this;
    }
    /**
     * 初始化引擎
     */
    VisualScriptEngine.prototype.initialize = function () {
        // 创建变量
        this.initializeVariables();
        // 创建自定义事件
        this.initializeCustomEvents();
        // 创建节点
        this.initializeNodes();
        // 连接节点
        this.connectNodes();
        // 初始化事件节点
        this.initializeEventNodes();
    };
    /**
     * 初始化变量
     */
    VisualScriptEngine.prototype.initializeVariables = function () {
        // 清空变量映射
        this.variables.clear();
        // 创建变量
        if (this.script.variables) {
            for (var _i = 0, _a = this.script.variables; _i < _a.length; _i++) {
                var variableData = _a[_i];
                var variable = new Variable_1.Variable({
                    id: variableData.id,
                    name: variableData.name,
                    type: variableData.type,
                    value: variableData.value,
                    description: variableData.description,
                    constant: variableData.constant,
                    global: variableData.global
                });
                this.variables.set(variable.id, variable);
                this.graph.addVariable(variable);
            }
        }
    };
    /**
     * 初始化自定义事件
     */
    VisualScriptEngine.prototype.initializeCustomEvents = function () {
        // 清空自定义事件映射
        this.customEvents.clear();
        // 创建自定义事件
        if (this.script.customEvents) {
            for (var _i = 0, _a = this.script.customEvents; _i < _a.length; _i++) {
                var eventData = _a[_i];
                var customEvent = new CustomEvent_1.CustomEvent({
                    id: eventData.id,
                    name: eventData.name,
                    parameterTypes: eventData.parameterTypes,
                    description: eventData.description
                });
                this.customEvents.set(customEvent.id, customEvent);
                this.graph.addCustomEvent(customEvent);
            }
        }
    };
    /**
     * 初始化节点
     */
    VisualScriptEngine.prototype.initializeNodes = function () {
        // 清空节点映射和事件节点列表
        this.nodes.clear();
        this.eventNodes = [];
        // 创建节点
        if (this.script.nodes) {
            for (var _i = 0, _a = this.script.nodes; _i < _a.length; _i++) {
                var nodeData = _a[_i];
                // 获取节点类型
                var nodeType = this.nodeRegistry.getNodeType(nodeData.type);
                if (!nodeType) {
                    console.warn("\u672A\u77E5\u8282\u70B9\u7C7B\u578B: ".concat(nodeData.type));
                    continue;
                }
                // 创建节点实例
                var node = new nodeType({
                    id: nodeData.id,
                    type: nodeData.type,
                    metadata: nodeData.metadata,
                    graph: this.graph,
                    context: this.executionContext
                });
                // 设置节点参数
                if (nodeData.parameters) {
                    for (var _b = 0, _c = Object.entries(nodeData.parameters); _b < _c.length; _b++) {
                        var _d = _c[_b], paramName = _d[0], paramValue = _d[1];
                        if (paramValue.value !== undefined) {
                            node.setParameterValue(paramName, paramValue.value);
                        }
                    }
                }
                // 添加到节点映射
                this.nodes.set(node.id, node);
                // 如果是事件节点，添加到事件节点列表
                if (node instanceof EventNode_1.EventNode) {
                    this.eventNodes.push(node);
                }
                // 添加到图形
                this.graph.addNode(node);
            }
        }
    };
    /**
     * 连接节点
     */
    VisualScriptEngine.prototype.connectNodes = function () {
        // 连接节点参数
        if (this.script.nodes) {
            for (var _i = 0, _a = this.script.nodes; _i < _a.length; _i++) {
                var nodeData = _a[_i];
                var node = this.nodes.get(nodeData.id);
                if (!node) {
                    continue;
                }
                // 连接参数链接
                if (nodeData.parameters) {
                    for (var _b = 0, _c = Object.entries(nodeData.parameters); _b < _c.length; _b++) {
                        var _d = _c[_b], paramName = _d[0], paramValue = _d[1];
                        if (paramValue.link) {
                            var sourceNode = this.nodes.get(paramValue.link.nodeId);
                            if (sourceNode) {
                                node.connectInput(paramName, sourceNode, paramValue.link.socket);
                            }
                        }
                    }
                }
                // 连接流程
                if (nodeData.flows) {
                    for (var _e = 0, _f = Object.entries(nodeData.flows); _e < _f.length; _e++) {
                        var _g = _f[_e], flowName = _g[0], flowConnection = _g[1];
                        var targetNode = this.nodes.get(flowConnection.nodeId);
                        if (targetNode) {
                            node.connectFlow(flowName, targetNode, flowConnection.socket);
                        }
                    }
                }
            }
        }
    };
    /**
     * 初始化事件节点
     */
    VisualScriptEngine.prototype.initializeEventNodes = function () {
        // 初始化所有事件节点
        for (var _i = 0, _a = this.eventNodes; _i < _a.length; _i++) {
            var eventNode = _a[_i];
            eventNode.initialize();
        }
    };
    /**
     * 开始执行
     */
    VisualScriptEngine.prototype.start = function () {
        if (this.running) {
            return;
        }
        this.running = true;
        // 触发开始事件
        this.emit('started');
        // 触发所有事件节点的开始事件
        for (var _i = 0, _a = this.eventNodes; _i < _a.length; _i++) {
            var eventNode = _a[_i];
            eventNode.onStart();
        }
    };
    /**
     * 停止执行
     */
    VisualScriptEngine.prototype.stop = function () {
        if (!this.running) {
            return;
        }
        this.running = false;
        // 清空纤程队列
        this.fiberQueue = [];
        // 触发所有事件节点的停止事件
        for (var _i = 0, _a = this.eventNodes; _i < _a.length; _i++) {
            var eventNode = _a[_i];
            eventNode.onStop();
        }
        // 触发停止事件
        this.emit('stopped');
    };
    /**
     * 更新引擎
     * @param deltaTime 帧间隔时间（秒）
     */
    VisualScriptEngine.prototype.update = function (deltaTime) {
        if (!this.running) {
            return;
        }
        // 触发所有事件节点的更新事件
        for (var _i = 0, _a = this.eventNodes; _i < _a.length; _i++) {
            var eventNode = _a[_i];
            eventNode.onUpdate(deltaTime);
        }
        // 执行所有纤程
        this.executeAllFibers();
    };
    /**
     * 执行所有纤程
     */
    VisualScriptEngine.prototype.executeAllFibers = function () {
        var startTime = performance.now();
        var executedSteps = 0;
        // 执行纤程队列中的所有纤程
        while (this.fiberQueue.length > 0 &&
            executedSteps < this.maxExecutionSteps &&
            performance.now() - startTime < this.maxExecutionTime) {
            // 获取第一个纤程
            var fiber = this.fiberQueue[0];
            // 执行一步
            var stepResult = fiber.executeStep();
            // 增加执行步数
            executedSteps++;
            this.executionSteps++;
            // 如果纤程已完成，从队列中移除
            if (stepResult.completed) {
                this.fiberQueue.shift();
            }
            // 如果需要暂停，跳出循环
            if (stepResult.pause) {
                break;
            }
        }
    };
    /**
     * 添加纤程到队列
     * @param fiber 纤程
     */
    VisualScriptEngine.prototype.addFiber = function (fiber) {
        this.fiberQueue.push(fiber);
    };
    /**
     * 创建新纤程
     * @param sourceNode 源节点
     * @param outputName 输出名称
     * @param callback 完成回调
     */
    VisualScriptEngine.prototype.createFiber = function (sourceNode, outputName, callback) {
        var fiber = new Fiber_1.Fiber({
            engine: this,
            sourceNode: sourceNode,
            outputName: outputName,
            callback: callback
        });
        return fiber;
    };
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点实例
     */
    VisualScriptEngine.prototype.getNode = function (id) {
        return this.nodes.get(id);
    };
    /**
     * 获取变量
     * @param id 变量ID
     * @returns 变量实例
     */
    VisualScriptEngine.prototype.getVariable = function (id) {
        return this.variables.get(id);
    };
    /**
     * 获取自定义事件
     * @param id 事件ID
     * @returns 自定义事件实例
     */
    VisualScriptEngine.prototype.getCustomEvent = function (id) {
        return this.customEvents.get(id);
    };
    /**
     * 获取执行上下文
     * @returns 执行上下文
     */
    VisualScriptEngine.prototype.getExecutionContext = function () {
        return this.executionContext;
    };
    /**
     * 获取图形实例
     * @returns 图形实例
     */
    VisualScriptEngine.prototype.getGraph = function () {
        return this.graph;
    };
    /**
     * 获取所属实体
     * @returns 所属实体
     */
    VisualScriptEngine.prototype.getEntity = function () {
        return this.entity;
    };
    /**
     * 获取所属世界
     * @returns 所属世界
     */
    VisualScriptEngine.prototype.getWorld = function () {
        return this.world;
    };
    /**
     * 获取是否正在运行
     * @returns 是否正在运行
     */
    VisualScriptEngine.prototype.isRunning = function () {
        return this.running;
    };
    /**
     * 获取执行步数
     * @returns 执行步数
     */
    VisualScriptEngine.prototype.getExecutionSteps = function () {
        return this.executionSteps;
    };
    /**
     * 销毁引擎
     */
    VisualScriptEngine.prototype.dispose = function () {
        // 停止执行
        this.stop();
        // 清空节点映射
        this.nodes.clear();
        // 清空事件节点列表
        this.eventNodes = [];
        // 清空变量映射
        this.variables.clear();
        // 清空自定义事件映射
        this.customEvents.clear();
        // 清空纤程队列
        this.fiberQueue = [];
        // 清空图形
        this.graph.clear();
        // 移除所有事件监听
        this.removeAllListeners();
    };
    return VisualScriptEngine;
}(EventEmitter_1.EventEmitter));
exports.VisualScriptEngine = VisualScriptEngine;
