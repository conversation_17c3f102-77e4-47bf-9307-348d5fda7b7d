"use strict";
/**
 * 面部表情类型枚举
 * 定义了各种面部表情的类型
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacialExpressionUtils = exports.FacialRegion = exports.FacialExpressionIntensity = exports.FacialExpressionType = void 0;
var FacialExpressionType;
(function (FacialExpressionType) {
    /** 中性表情 */
    FacialExpressionType["NEUTRAL"] = "neutral";
    /** 快乐 */
    FacialExpressionType["HAPPY"] = "happy";
    /** 悲伤 */
    FacialExpressionType["SAD"] = "sad";
    /** 愤怒 */
    FacialExpressionType["ANGRY"] = "angry";
    /** 惊讶 */
    FacialExpressionType["SURPRISED"] = "surprised";
    /** 恐惧 */
    FacialExpressionType["FEAR"] = "fear";
    /** 厌恶 */
    FacialExpressionType["DISGUSTED"] = "disgusted";
    /** 厌恶（别名） */
    FacialExpressionType["DISGUST"] = "disgusted";
    /** 困惑 */
    FacialExpressionType["CONFUSED"] = "confused";
    /** 兴奋 */
    FacialExpressionType["EXCITED"] = "excited";
    /** 疲惫 */
    FacialExpressionType["TIRED"] = "tired";
    /** 专注 */
    FacialExpressionType["FOCUSED"] = "focused";
    /** 放松 */
    FacialExpressionType["RELAXED"] = "relaxed";
    /** 紧张 */
    FacialExpressionType["NERVOUS"] = "nervous";
    /** 自信 */
    FacialExpressionType["CONFIDENT"] = "confident";
    /** 害羞 */
    FacialExpressionType["SHY"] = "shy";
    /** 骄傲 */
    FacialExpressionType["PROUD"] = "proud";
    /** 嫉妒 */
    FacialExpressionType["JEALOUS"] = "jealous";
    /** 爱意 */
    FacialExpressionType["LOVING"] = "loving";
    /** 痛苦 */
    FacialExpressionType["PAIN"] = "pain";
    /** 思考 */
    FacialExpressionType["THINKING"] = "thinking";
    /** 微笑 */
    FacialExpressionType["SMILE"] = "smile";
    /** 大笑 */
    FacialExpressionType["LAUGH"] = "laugh";
    /** 哭泣 */
    FacialExpressionType["CRY"] = "cry";
    /** 眨眼 */
    FacialExpressionType["BLINK"] = "blink";
    /** 眯眼 */
    FacialExpressionType["SQUINT"] = "squint";
    /** 皱眉 */
    FacialExpressionType["FROWN"] = "frown";
    /** 撅嘴 */
    FacialExpressionType["POUT"] = "pout";
    /** 张嘴 */
    FacialExpressionType["MOUTH_OPEN"] = "mouth_open";
    /** 闭嘴 */
    FacialExpressionType["MOUTH_CLOSED"] = "mouth_closed";
})(FacialExpressionType || (exports.FacialExpressionType = FacialExpressionType = {}));
/**
 * 面部表情强度枚举
 */
var FacialExpressionIntensity;
(function (FacialExpressionIntensity) {
    /** 无 */
    FacialExpressionIntensity[FacialExpressionIntensity["NONE"] = 0] = "NONE";
    /** 轻微 */
    FacialExpressionIntensity[FacialExpressionIntensity["LIGHT"] = 0.25] = "LIGHT";
    /** 中等 */
    FacialExpressionIntensity[FacialExpressionIntensity["MEDIUM"] = 0.5] = "MEDIUM";
    /** 强烈 */
    FacialExpressionIntensity[FacialExpressionIntensity["STRONG"] = 0.75] = "STRONG";
    /** 极强 */
    FacialExpressionIntensity[FacialExpressionIntensity["EXTREME"] = 1] = "EXTREME";
})(FacialExpressionIntensity || (exports.FacialExpressionIntensity = FacialExpressionIntensity = {}));
/**
 * 面部区域枚举
 */
var FacialRegion;
(function (FacialRegion) {
    /** 眼部 */
    FacialRegion["EYES"] = "eyes";
    /** 眉毛 */
    FacialRegion["EYEBROWS"] = "eyebrows";
    /** 嘴部 */
    FacialRegion["MOUTH"] = "mouth";
    /** 鼻子 */
    FacialRegion["NOSE"] = "nose";
    /** 脸颊 */
    FacialRegion["CHEEKS"] = "cheeks";
    /** 下巴 */
    FacialRegion["CHIN"] = "chin";
    /** 额头 */
    FacialRegion["FOREHEAD"] = "forehead";
    /** 全脸 */
    FacialRegion["FULL_FACE"] = "full_face";
})(FacialRegion || (exports.FacialRegion = FacialRegion = {}));
/**
 * 面部表情工具类
 */
var FacialExpressionUtils = /** @class */ (function () {
    function FacialExpressionUtils() {
    }
    /**
     * 获取表情的默认强度
     */
    FacialExpressionUtils.getDefaultIntensity = function (type) {
        switch (type) {
            case FacialExpressionType.NEUTRAL:
                return 0;
            case FacialExpressionType.BLINK:
                return 1.0;
            case FacialExpressionType.SMILE:
            case FacialExpressionType.HAPPY:
                return 0.7;
            case FacialExpressionType.LAUGH:
                return 0.9;
            case FacialExpressionType.SAD:
            case FacialExpressionType.CRY:
                return 0.8;
            case FacialExpressionType.ANGRY:
                return 0.8;
            case FacialExpressionType.SURPRISED:
                return 0.9;
            case FacialExpressionType.FEAR:
                return 0.7;
            case FacialExpressionType.DISGUSTED:
            case FacialExpressionType.DISGUST:
                return 0.6;
            default:
                return 0.5;
        }
    };
    /**
     * 获取表情影响的主要区域
     */
    FacialExpressionUtils.getPrimaryRegions = function (type) {
        switch (type) {
            case FacialExpressionType.BLINK:
            case FacialExpressionType.SQUINT:
                return [FacialRegion.EYES];
            case FacialExpressionType.SMILE:
            case FacialExpressionType.LAUGH:
            case FacialExpressionType.FROWN:
            case FacialExpressionType.POUT:
                return [FacialRegion.MOUTH];
            case FacialExpressionType.SURPRISED:
                return [FacialRegion.EYES, FacialRegion.EYEBROWS, FacialRegion.MOUTH];
            case FacialExpressionType.ANGRY:
                return [FacialRegion.EYEBROWS, FacialRegion.EYES, FacialRegion.MOUTH];
            case FacialExpressionType.HAPPY:
                return [FacialRegion.MOUTH, FacialRegion.EYES, FacialRegion.CHEEKS];
            case FacialExpressionType.SAD:
                return [FacialRegion.MOUTH, FacialRegion.EYEBROWS, FacialRegion.EYES];
            default:
                return [FacialRegion.FULL_FACE];
        }
    };
    /**
     * 检查两个表情是否兼容（可以同时播放）
     */
    FacialExpressionUtils.areCompatible = function (type1, type2) {
        var regions1 = this.getPrimaryRegions(type1);
        var regions2 = this.getPrimaryRegions(type2);
        // 检查是否有重叠的区域
        for (var _i = 0, regions1_1 = regions1; _i < regions1_1.length; _i++) {
            var region1 = regions1_1[_i];
            for (var _a = 0, regions2_1 = regions2; _a < regions2_1.length; _a++) {
                var region2 = regions2_1[_a];
                if (region1 === region2 || region1 === FacialRegion.FULL_FACE || region2 === FacialRegion.FULL_FACE) {
                    return false;
                }
            }
        }
        return true;
    };
    /**
     * 创建表情配置
     */
    FacialExpressionUtils.createConfig = function (type, intensity, duration) {
        return {
            type: type,
            intensity: intensity !== null && intensity !== void 0 ? intensity : this.getDefaultIntensity(type),
            regions: this.getPrimaryRegions(type),
            duration: duration,
            loop: false,
            blendWeight: 1.0
        };
    };
    return FacialExpressionUtils;
}());
exports.FacialExpressionUtils = FacialExpressionUtils;
