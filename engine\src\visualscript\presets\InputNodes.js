"use strict";
/**
 * 输入相关的可视化脚本节点
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerInputNodes = exports.GamepadInputNode = exports.TouchInputNode = exports.MouseInputNode = exports.KeyboardInputNode = void 0;
var VisualScriptNode_1 = require("../VisualScriptNode");
var NodeRegistry_1 = require("../NodeRegistry");
/**
 * 键盘输入节点
 */
var KeyboardInputNode = /** @class */ (function (_super) {
    __extends(KeyboardInputNode, _super);
    function KeyboardInputNode() {
        var _this = _super.call(this, 'KeyboardInput', '键盘输入') || this;
        _this.addInput('key', 'string', '按键');
        _this.addOutput('pressed', 'boolean', '是否按下');
        _this.addOutput('justPressed', 'boolean', '刚按下');
        _this.addOutput('justReleased', 'boolean', '刚释放');
        return _this;
    }
    KeyboardInputNode.prototype.execute = function (inputs) {
        var key = inputs.key;
        if (!key)
            return { pressed: false, justPressed: false, justReleased: false };
        // 这里应该连接到实际的输入系统
        // 暂时返回模拟数据
        return {
            pressed: false,
            justPressed: false,
            justReleased: false
        };
    };
    return KeyboardInputNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.KeyboardInputNode = KeyboardInputNode;
/**
 * 鼠标输入节点
 */
var MouseInputNode = /** @class */ (function (_super) {
    __extends(MouseInputNode, _super);
    function MouseInputNode() {
        var _this = _super.call(this, 'MouseInput', '鼠标输入') || this;
        _this.addInput('button', 'number', '鼠标按钮');
        _this.addOutput('pressed', 'boolean', '是否按下');
        _this.addOutput('position', 'vector2', '鼠标位置');
        _this.addOutput('delta', 'vector2', '移动增量');
        return _this;
    }
    MouseInputNode.prototype.execute = function (inputs) {
        // 这里应该连接到实际的输入系统
        // 暂时返回模拟数据
        return {
            pressed: false,
            position: { x: 0, y: 0 },
            delta: { x: 0, y: 0 }
        };
    };
    return MouseInputNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.MouseInputNode = MouseInputNode;
/**
 * 触摸输入节点
 */
var TouchInputNode = /** @class */ (function (_super) {
    __extends(TouchInputNode, _super);
    function TouchInputNode() {
        var _this = _super.call(this, 'TouchInput', '触摸输入') || this;
        _this.addOutput('touching', 'boolean', '是否触摸');
        _this.addOutput('position', 'vector2', '触摸位置');
        _this.addOutput('touchCount', 'number', '触摸点数量');
        return _this;
    }
    TouchInputNode.prototype.execute = function () {
        // 这里应该连接到实际的输入系统
        // 暂时返回模拟数据
        return {
            touching: false,
            position: { x: 0, y: 0 },
            touchCount: 0
        };
    };
    return TouchInputNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.TouchInputNode = TouchInputNode;
/**
 * 游戏手柄输入节点
 */
var GamepadInputNode = /** @class */ (function (_super) {
    __extends(GamepadInputNode, _super);
    function GamepadInputNode() {
        var _this = _super.call(this, 'GamepadInput', '游戏手柄输入') || this;
        _this.addInput('gamepadIndex', 'number', '手柄索引');
        _this.addInput('button', 'number', '按钮');
        _this.addOutput('pressed', 'boolean', '是否按下');
        _this.addOutput('leftStick', 'vector2', '左摇杆');
        _this.addOutput('rightStick', 'vector2', '右摇杆');
        return _this;
    }
    GamepadInputNode.prototype.execute = function (inputs) {
        // 这里应该连接到实际的输入系统
        // 暂时返回模拟数据
        return {
            pressed: false,
            leftStick: { x: 0, y: 0 },
            rightStick: { x: 0, y: 0 }
        };
    };
    return GamepadInputNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.GamepadInputNode = GamepadInputNode;
/**
 * 注册输入节点
 */
function registerInputNodes() {
    NodeRegistry_1.NodeRegistry.register('KeyboardInput', KeyboardInputNode);
    NodeRegistry_1.NodeRegistry.register('MouseInput', MouseInputNode);
    NodeRegistry_1.NodeRegistry.register('TouchInput', TouchInputNode);
    NodeRegistry_1.NodeRegistry.register('GamepadInput', GamepadInputNode);
}
exports.registerInputNodes = registerInputNodes;
