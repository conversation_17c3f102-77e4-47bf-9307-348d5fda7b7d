"use strict";
/**
 * InteractionHighlightComponent.ts
 *
 * 交互高亮组件，用于高亮可交互对象
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionHighlightComponent = exports.HighlightType = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
/**
 * 高亮类型枚举
 */
var HighlightType;
(function (HighlightType) {
    /** 轮廓高亮 */
    HighlightType["OUTLINE"] = "outline";
    /** 发光高亮 */
    HighlightType["GLOW"] = "glow";
    /** 颜色高亮 */
    HighlightType["COLOR"] = "color";
    /** 自定义高亮 */
    HighlightType["CUSTOM"] = "custom";
})(HighlightType || (exports.HighlightType = HighlightType = {}));
/**
 * 交互高亮组件
 * 用于高亮可交互对象
 */
var InteractionHighlightComponent = /** @class */ (function (_super) {
    __extends(InteractionHighlightComponent, _super);
    /**
     * 构造函数
     * @param config 组件配置
     */
    function InteractionHighlightComponent(config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'InteractionHighlight') || this;
        /** 原始材质映射 */
        _this.originalMaterials = new Map();
        /** 高亮材质映射 */
        _this.highlightMaterials = new Map();
        /** 脉冲计时器 */
        _this.pulseTime = 0;
        /** 当前脉冲值（0-1） */
        _this.pulseValue = 0;
        // 初始化属性
        _this._highlightType = config.highlightType || HighlightType.OUTLINE;
        _this._highlightColor = new three_1.Color(config.highlightColor || '#ffff00');
        _this._highlightIntensity = config.highlightIntensity !== undefined ? config.highlightIntensity : 1.0;
        _this._outlineWidth = config.outlineWidth !== undefined ? config.outlineWidth : 2.0;
        _this._pulse = config.pulse !== undefined ? config.pulse : true;
        _this._pulseSpeed = config.pulseSpeed !== undefined ? config.pulseSpeed : 1.0;
        _this._enabled = config.enabled !== undefined ? config.enabled : true;
        _this._highlighted = config.highlighted !== undefined ? config.highlighted : false;
        // 如果初始高亮，则应用高亮
        if (_this._enabled && _this._highlighted) {
            _this.applyHighlight();
        }
        return _this;
    }
    Object.defineProperty(InteractionHighlightComponent.prototype, "highlightType", {
        /**
         * 获取高亮类型
         */
        get: function () {
            return this._highlightType;
        },
        /**
         * 设置高亮类型
         */
        set: function (value) {
            // 如果类型相同，则返回
            if (this._highlightType === value)
                return;
            // 如果当前正在高亮，则先移除高亮
            var wasHighlighted = this._highlighted;
            if (wasHighlighted) {
                this.removeHighlight();
            }
            // 更新类型
            this._highlightType = value;
            // 如果之前在高亮，则重新应用高亮
            if (wasHighlighted) {
                this.applyHighlight();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionHighlightComponent.prototype, "highlightColor", {
        /**
         * 获取高亮颜色
         */
        get: function () {
            return this._highlightColor;
        },
        /**
         * 设置高亮颜色
         */
        set: function (value) {
            // 转换为Color对象
            var newColor = value instanceof three_1.Color ? value : new three_1.Color(value);
            // 如果颜色相同，则返回
            if (this._highlightColor.equals(newColor))
                return;
            // 更新颜色
            this._highlightColor = newColor;
            // 如果当前正在高亮，则更新高亮材质
            if (this._highlighted) {
                this.updateHighlightMaterials();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionHighlightComponent.prototype, "highlightIntensity", {
        /**
         * 获取高亮强度
         */
        get: function () {
            return this._highlightIntensity;
        },
        /**
         * 设置高亮强度
         */
        set: function (value) {
            // 如果强度相同，则返回
            if (this._highlightIntensity === value)
                return;
            // 更新强度
            this._highlightIntensity = value;
            // 如果当前正在高亮，则更新高亮材质
            if (this._highlighted) {
                this.updateHighlightMaterials();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionHighlightComponent.prototype, "enabled", {
        /**
         * 获取是否启用
         */
        get: function () {
            return this._enabled;
        },
        /**
         * 设置是否启用
         */
        set: function (value) {
            // 如果状态相同，则返回
            if (this._enabled === value)
                return;
            // 如果禁用且当前正在高亮，则移除高亮
            if (!value && this._highlighted) {
                this.removeHighlight();
            }
            // 更新状态
            this._enabled = value;
            // 如果启用且应该高亮，则应用高亮
            if (value && this._highlighted) {
                this.applyHighlight();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractionHighlightComponent.prototype, "highlighted", {
        /**
         * 获取是否高亮
         */
        get: function () {
            return this._highlighted;
        },
        /**
         * 设置是否高亮
         */
        set: function (value) {
            // 如果状态相同，则返回
            if (this._highlighted === value)
                return;
            // 更新状态
            this._highlighted = value;
            // 如果启用，则应用或移除高亮
            if (this._enabled) {
                if (value) {
                    this.applyHighlight();
                }
                else {
                    this.removeHighlight();
                }
            }
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 应用高亮
     */
    InteractionHighlightComponent.prototype.applyHighlight = function () {
        // 获取对象的3D表示
        var object3D = this.getObject3D();
        if (!object3D)
            return;
        // 根据高亮类型应用高亮
        switch (this._highlightType) {
            case HighlightType.OUTLINE:
                this.applyOutlineHighlight(object3D);
                break;
            case HighlightType.GLOW:
                this.applyGlowHighlight(object3D);
                break;
            case HighlightType.COLOR:
                this.applyColorHighlight(object3D);
                break;
            case HighlightType.CUSTOM:
                this.applyCustomHighlight(object3D);
                break;
        }
    };
    /**
     * 应用轮廓高亮
     * @param object3D 3D对象
     */
    InteractionHighlightComponent.prototype.applyOutlineHighlight = function (object3D) {
        var _this = this;
        // 遍历所有网格
        object3D.traverse(function (child) {
            if (child instanceof three_1.Mesh && child.material) {
                // 保存原始材质
                if (!_this.originalMaterials.has(child)) {
                    _this.originalMaterials.set(child, child.material);
                }
                // 创建轮廓材质
                // 这里使用简单的线框材质模拟轮廓效果
                // 在实际项目中，应该使用后处理效果实现更好的轮廓效果
                var outlineMaterial = new three_1.MeshBasicMaterial({
                    color: _this._highlightColor,
                    wireframe: true,
                    transparent: true,
                    opacity: _this._highlightIntensity,
                    depthTest: true
                });
                // 应用轮廓材质
                child.material = outlineMaterial;
                // 保存高亮材质
                _this.highlightMaterials.set(child, outlineMaterial);
            }
        });
        // 注意：这只是一个简单的实现
        // 在实际项目中，应该使用后处理效果实现更好的轮廓效果
        // 例如使用OutlinePass或自定义着色器
    };
    /**
     * 应用发光高亮
     * @param object3D 3D对象
     */
    InteractionHighlightComponent.prototype.applyGlowHighlight = function (object3D) {
        var _this = this;
        // 遍历所有网格
        object3D.traverse(function (child) {
            if (child instanceof three_1.Mesh && child.material) {
                // 保存原始材质
                if (!_this.originalMaterials.has(child)) {
                    _this.originalMaterials.set(child, child.material);
                }
                // 创建发光材质
                // 这里使用简单的自发光材质模拟发光效果
                // 在实际项目中，应该使用后处理效果实现更好的发光效果
                var glowMaterial = new three_1.MeshBasicMaterial({
                    color: _this._highlightColor,
                    transparent: true,
                    opacity: _this._highlightIntensity,
                    wireframe: false,
                    depthTest: true,
                    emissive: _this._highlightColor,
                    emissiveIntensity: _this._highlightIntensity
                });
                // 应用发光材质
                child.material = glowMaterial;
                // 保存高亮材质
                _this.highlightMaterials.set(child, glowMaterial);
            }
        });
        // 注意：这只是一个简单的实现
        // 在实际项目中，应该使用后处理效果实现更好的发光效果
        // 例如使用UnrealBloomPass或自定义着色器
    };
    /**
     * 应用颜色高亮
     * @param object3D 3D对象
     */
    InteractionHighlightComponent.prototype.applyColorHighlight = function (object3D) {
        var _this = this;
        // 遍历所有网格
        object3D.traverse(function (child) {
            if (child instanceof three_1.Mesh && child.material) {
                // 保存原始材质
                if (!_this.originalMaterials.has(child)) {
                    _this.originalMaterials.set(child, child.material);
                }
                // 创建高亮材质
                var highlightMaterial = new three_1.MeshBasicMaterial({
                    color: _this._highlightColor,
                    transparent: true,
                    opacity: _this._highlightIntensity,
                    wireframe: false,
                    depthTest: true
                });
                // 应用高亮材质
                child.material = highlightMaterial;
                // 保存高亮材质
                _this.highlightMaterials.set(child, highlightMaterial);
            }
        });
    };
    /**
     * 应用自定义高亮
     * @param object3D 3D对象
     */
    InteractionHighlightComponent.prototype.applyCustomHighlight = function (object3D) {
        // 这里提供一个简单的自定义高亮实现
        // 在实际项目中，可以根据需求实现更复杂的自定义高亮效果
        var _this = this;
        // 遍历所有网格
        object3D.traverse(function (child) {
            if (child instanceof three_1.Mesh && child.material) {
                // 保存原始材质
                if (!_this.originalMaterials.has(child)) {
                    _this.originalMaterials.set(child, child.material);
                }
                // 创建自定义高亮材质
                // 这里使用简单的着色器材质
                var customMaterial = new three_1.ShaderMaterial({
                    uniforms: {
                        highlightColor: { value: new three_1.Color(_this._highlightColor) },
                        highlightIntensity: { value: _this._highlightIntensity },
                        time: { value: 0.0 }
                    },
                    vertexShader: "\n            varying vec2 vUv;\n            varying vec3 vNormal;\n\n            void main() {\n              vUv = uv;\n              vNormal = normalize(normalMatrix * normal);\n              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n            }\n          ",
                    fragmentShader: "\n            uniform vec3 highlightColor;\n            uniform float highlightIntensity;\n            uniform float time;\n\n            varying vec2 vUv;\n            varying vec3 vNormal;\n\n            void main() {\n              // \u8BA1\u7B97\u8FB9\u7F18\u53D1\u5149\u6548\u679C\n              float rim = 1.0 - max(dot(vNormal, vec3(0.0, 0.0, 1.0)), 0.0);\n              rim = pow(rim, 2.0) * highlightIntensity;\n\n              // \u6DFB\u52A0\u8109\u51B2\u6548\u679C\n              float pulse = 0.5 + 0.5 * sin(time * 2.0);\n\n              // \u6700\u7EC8\u989C\u8272\n              vec3 finalColor = highlightColor * rim * pulse;\n\n              gl_FragColor = vec4(finalColor, rim * pulse);\n            }\n          ",
                    transparent: true,
                    depthTest: true
                });
                // 应用自定义高亮材质
                child.material = customMaterial;
                // 保存高亮材质
                _this.highlightMaterials.set(child, customMaterial);
            }
        });
    };
    /**
     * 移除高亮
     */
    InteractionHighlightComponent.prototype.removeHighlight = function () {
        // 恢复原始材质
        for (var _i = 0, _a = this.originalMaterials; _i < _a.length; _i++) {
            var _b = _a[_i], mesh = _b[0], material = _b[1];
            mesh.material = material;
        }
        // 清空高亮材质
        this.highlightMaterials.clear();
    };
    /**
     * 更新高亮材质
     */
    InteractionHighlightComponent.prototype.updateHighlightMaterials = function () {
        // 更新所有高亮材质
        for (var _i = 0, _a = this.highlightMaterials.values(); _i < _a.length; _i++) {
            var material = _a[_i];
            if (material instanceof three_1.MeshBasicMaterial) {
                material.color = this._highlightColor;
                material.opacity = this._highlightIntensity * this.pulseValue;
            }
            else if (material instanceof three_1.ShaderMaterial) {
                // 更新着色器材质的颜色和强度
                if (material.uniforms.highlightColor) {
                    material.uniforms.highlightColor.value = this._highlightColor;
                }
                if (material.uniforms.highlightIntensity) {
                    material.uniforms.highlightIntensity.value = this._highlightIntensity * this.pulseValue;
                }
            }
        }
    };
    /**
     * 获取对象的3D表示
     * @returns 3D对象
     */
    InteractionHighlightComponent.prototype.getObject3D = function () {
        var entity = this.getEntity();
        if (!entity)
            return null;
        // 从实体中获取Transform组件
        var transform = entity.getComponent('Transform');
        if (transform) {
            // 使用Transform组件的getObject3D方法获取3D对象
            return transform.getObject3D();
        }
        // 如果没有Transform组件，尝试从实体中获取mesh属性（用于示例代码兼容）
        if (entity.mesh instanceof three_1.Object3D) {
            return entity.mesh;
        }
        return null;
    };
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    InteractionHighlightComponent.prototype.update = function (deltaTime) {
        // 如果未启用或未高亮，则返回
        if (!this._enabled || !this._highlighted)
            return;
        // 更新脉冲效果
        if (this._pulse) {
            // 更新脉冲计时器
            this.pulseTime += deltaTime * this._pulseSpeed;
            // 计算脉冲值（0.5-1.0范围内的正弦波）
            this.pulseValue = 0.5 + 0.5 * Math.sin(this.pulseTime);
            // 更新高亮材质
            this.updateHighlightMaterials();
        }
        // 更新自定义着色器材质的时间参数
        for (var _i = 0, _a = this.highlightMaterials.values(); _i < _a.length; _i++) {
            var material = _a[_i];
            if (material instanceof three_1.ShaderMaterial && material.uniforms.time) {
                material.uniforms.time.value = this.pulseTime;
            }
        }
    };
    /**
     * 销毁组件
     */
    InteractionHighlightComponent.prototype.dispose = function () {
        // 移除高亮
        this.removeHighlight();
        // 清空材质映射
        this.originalMaterials.clear();
        this.highlightMaterials.clear();
        // 调用基类的销毁方法
        _super.prototype.dispose.call(this);
    };
    return InteractionHighlightComponent;
}(Component_1.Component));
exports.InteractionHighlightComponent = InteractionHighlightComponent;
