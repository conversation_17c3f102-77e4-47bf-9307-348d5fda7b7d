"use strict";
/**
 * UIModule.ts
 *
 * UI模块，导出所有UI相关的组件和系统
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
// 导出接口
__exportStar(require("./interfaces/IUIElement"), exports);
// 导出组件
__exportStar(require("./components/UIComponent"), exports);
__exportStar(require("./components/UI2DComponent"), exports);
__exportStar(require("./components/UI3DComponent"), exports);
__exportStar(require("./components/UIAnimationComponent"), exports);
__exportStar(require("./components/UIEventComponent"), exports);
__exportStar(require("./components/UILayoutComponent"), exports);
// 导出系统
__exportStar(require("./systems/UISystem"), exports);
__exportStar(require("./systems/UI2DSystem"), exports);
__exportStar(require("./systems/UI3DSystem"), exports);
__exportStar(require("./systems/UIAnimationSystem"), exports);
__exportStar(require("./systems/UILayoutSystem"), exports);
// 导出工具函数和常量
// 这里可以添加其他工具函数和常量的导出
