"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkTransformComponent = void 0;
/**
 * 网络变换组件
 * 用于同步实体的变换（位置、旋转、缩放）
 */
var Component_1 = require("../../core/Component");
var Debug_1 = require("../../utils/Debug");
/**
 * 网络变换组件
 * 用于同步实体的变换（位置、旋转、缩放）
 */
var NetworkTransformComponent = exports.NetworkTransformComponent = /** @class */ (function (_super) {
    __extends(NetworkTransformComponent, _super);
    /**
     * 创建网络变换组件
     * @param entity 实体
     * @param props 组件属性
     */
    function NetworkTransformComponent(entity, props) {
        if (props === void 0) { props = {}; }
        var _this = _super.call(this, entity) || this;
        /** 上次同步时间 */
        _this.lastSyncTime = 0;
        /** 上次同步位置 */
        _this.lastSyncedPosition = new THREE.Vector3();
        /** 上次同步旋转 */
        _this.lastSyncedRotation = new THREE.Quaternion();
        /** 上次同步缩放 */
        _this.lastSyncedScale = new THREE.Vector3(1, 1, 1);
        /** 目标位置 */
        _this.targetPosition = new THREE.Vector3();
        /** 目标旋转 */
        _this.targetRotation = new THREE.Quaternion();
        /** 目标缩放 */
        _this.targetScale = new THREE.Vector3(1, 1, 1);
        /** 是否正在插值 */
        _this.isLerping = false;
        /** 插值开始时间 */
        _this.lerpStartTime = 0;
        /** 插值持续时间 */
        _this.lerpDuration = 0;
        /** 是否有待同步的更改 */
        _this.hasPendingChanges = false;
        _this.syncInterval = props.syncInterval || 100;
        _this.autoSync = props.autoSync !== undefined ? props.autoSync : true;
        _this.syncPosition = props.syncPosition !== undefined ? props.syncPosition : true;
        _this.syncRotation = props.syncRotation !== undefined ? props.syncRotation : true;
        _this.syncScale = props.syncScale !== undefined ? props.syncScale : true;
        _this.positionLerpSpeed = props.positionLerpSpeed || 10;
        _this.rotationLerpSpeed = props.rotationLerpSpeed || 10;
        _this.scaleLerpSpeed = props.scaleLerpSpeed || 10;
        _this.positionThreshold = props.positionThreshold || 0.001;
        _this.rotationThreshold = props.rotationThreshold || 0.001;
        _this.scaleThreshold = props.scaleThreshold || 0.001;
        _this.useCompression = props.useCompression !== undefined ? props.useCompression : true;
        _this.usePrediction = props.usePrediction !== undefined ? props.usePrediction : true;
        _this.useSmoothing = props.useSmoothing !== undefined ? props.useSmoothing : true;
        _this.useExtrapolation = props.useExtrapolation !== undefined ? props.useExtrapolation : false;
        _this.extrapolationTime = props.extrapolationTime || 0.1;
        _this.maxExtrapolationDistance = props.maxExtrapolationDistance || 5;
        _this.syncPriority = props.syncPriority || 0;
        return _this;
    }
    /**
     * 初始化组件
     */
    NetworkTransformComponent.prototype.initialize = function () {
        // 获取网络实体组件
        this.networkEntity = this.entity.getComponent('NetworkEntity');
        if (!this.networkEntity) {
            Debug_1.Debug.warn('NetworkTransformComponent', 'Entity does not have a NetworkEntity component');
            return;
        }
        // 获取变换组件
        this.transform = this.entity.getComponent('Transform');
        if (!this.transform) {
            Debug_1.Debug.warn('NetworkTransformComponent', 'Entity does not have a Transform component');
            return;
        }
        // 保存初始变换
        this.lastSyncedPosition.copy(this.transform.position);
        this.lastSyncedRotation.copy(this.transform.rotation);
        this.lastSyncedScale.copy(this.transform.scale);
        // 设置目标变换
        this.targetPosition.copy(this.transform.position);
        this.targetRotation.copy(this.transform.rotation);
        this.targetScale.copy(this.transform.scale);
        // 监听变换变化事件
        this.entity.on('transformChanged', this.onTransformChanged.bind(this));
        // 监听网络同步事件
        this.entity.on('networkSync', this.onNetworkSync.bind(this));
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkTransformComponent.prototype.update = function (deltaTime) {
        // 如果没有网络实体组件或变换组件，则不更新
        if (!this.networkEntity || !this.transform) {
            return;
        }
        // 如果是本地拥有的实体，则检查是否需要同步
        if (this.networkEntity.isLocallyOwned) {
            // 如果启用了自动同步，则检查是否需要同步
            if (this.autoSync && this.hasPendingChanges) {
                var now = Date.now();
                // 检查是否达到同步间隔
                if (now - this.lastSyncTime >= this.syncInterval) {
                    this.sync();
                }
            }
        }
        // 如果不是本地拥有的实体，则进行插值
        else if (this.isLerping) {
            this.interpolate(deltaTime);
        }
    };
    /**
     * 同步变换
     */
    NetworkTransformComponent.prototype.sync = function () {
        if (!this.networkEntity || !this.transform || !this.hasPendingChanges) {
            return;
        }
        // 获取当前变换
        var position = this.transform.position;
        var rotation = this.transform.rotation;
        var scale = this.transform.scale;
        // 检查是否有足够的变化需要同步
        var needSync = false;
        if (this.syncPosition && position.distanceToSquared(this.lastSyncedPosition) > this.positionThreshold * this.positionThreshold) {
            needSync = true;
        }
        if (this.syncRotation && 1 - rotation.dot(this.lastSyncedRotation) > this.rotationThreshold) {
            needSync = true;
        }
        if (this.syncScale && scale.distanceToSquared(this.lastSyncedScale) > this.scaleThreshold * this.scaleThreshold) {
            needSync = true;
        }
        if (!needSync) {
            return;
        }
        // 构建同步数据
        var syncData = {};
        if (this.syncPosition) {
            syncData.position = {
                x: position.x,
                y: position.y,
                z: position.z,
            };
            // 更新上次同步位置
            this.lastSyncedPosition.copy(position);
        }
        if (this.syncRotation) {
            syncData.rotation = {
                x: rotation.x,
                y: rotation.y,
                z: rotation.z,
                w: rotation.w,
            };
            // 更新上次同步旋转
            this.lastSyncedRotation.copy(rotation);
        }
        if (this.syncScale) {
            syncData.scale = {
                x: scale.x,
                y: scale.y,
                z: scale.z,
            };
            // 更新上次同步缩放
            this.lastSyncedScale.copy(scale);
        }
        // 触发网络同步事件
        this.entity.emit('networkSync', {
            entityId: this.networkEntity.entityId,
            ownerId: this.networkEntity.ownerId,
            transform: syncData,
        });
        // 更新同步时间
        this.lastSyncTime = Date.now();
        // 清除待同步标记
        this.hasPendingChanges = false;
    };
    /**
     * 应用同步数据
     * @param data 同步数据
     */
    NetworkTransformComponent.prototype.applySyncData = function (data) {
        if (!this.transform || this.networkEntity.isLocallyOwned) {
            return;
        }
        // 如果没有变换数据，则不应用
        if (!data.transform) {
            return;
        }
        var transform = data.transform;
        // 设置目标变换
        if (transform.position && this.syncPosition) {
            this.targetPosition.set(transform.getPosition().x, transform.getPosition().y, transform.getPosition().z);
        }
        if (transform.rotation && this.syncRotation) {
            this.targetRotation.set(transform.rotation.x, transform.rotation.y, transform.rotation.z, transform.rotation.w);
        }
        if (transform.scale && this.syncScale) {
            this.targetScale.set(transform.scale.x, transform.scale.y, transform.scale.z);
        }
        // 如果使用平滑，则开始插值
        if (this.useSmoothing) {
            this.startLerping();
        }
        // 否则直接设置变换
        else {
            if (transform.position && this.syncPosition) {
                this.transform.position.copy(this.targetPosition);
            }
            if (transform.rotation && this.syncRotation) {
                this.transform.rotation.copy(this.targetRotation);
            }
            if (transform.scale && this.syncScale) {
                this.transform.scale.copy(this.targetScale);
            }
        }
    };
    /**
     * 开始插值
     */
    NetworkTransformComponent.prototype.startLerping = function () {
        this.isLerping = true;
        this.lerpStartTime = Date.now();
        this.lerpDuration = this.syncInterval;
    };
    /**
     * 插值更新
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkTransformComponent.prototype.interpolate = function (deltaTime) {
        if (!this.isLerping || !this.transform) {
            return;
        }
        // 计算插值因子
        var elapsed = (Date.now() - this.lerpStartTime) / this.lerpDuration;
        var t = Math.min(1, elapsed);
        // 插值位置
        if (this.syncPosition) {
            this.transform.position.lerp(this.targetPosition, t * this.positionLerpSpeed * deltaTime);
        }
        // 插值旋转
        if (this.syncRotation) {
            this.transform.rotation.slerp(this.targetRotation, t * this.rotationLerpSpeed * deltaTime);
        }
        // 插值缩放
        if (this.syncScale) {
            this.transform.scale.lerp(this.targetScale, t * this.scaleLerpSpeed * deltaTime);
        }
        // 如果插值完成，则停止插值
        if (t >= 1) {
            this.isLerping = false;
        }
    };
    /**
     * 变换变化事件处理
     */
    NetworkTransformComponent.prototype.onTransformChanged = function () {
        if (!this.networkEntity || !this.networkEntity.isLocallyOwned) {
            return;
        }
        this.hasPendingChanges = true;
    };
    /**
     * 网络同步事件处理
     * @param data 同步数据
     */
    NetworkTransformComponent.prototype.onNetworkSync = function (data) {
        this.applySyncData(data);
    };
    /**
     * 销毁组件
     */
    NetworkTransformComponent.prototype.dispose = function () {
        // 移除事件监听器
        this.entity.off('transformChanged', this.onTransformChanged);
        this.entity.off('networkSync', this.onNetworkSync);
    };
    /** 组件类型 */
    NetworkTransformComponent.type = 'NetworkTransform';
    return NetworkTransformComponent;
}(Component_1.Component));
