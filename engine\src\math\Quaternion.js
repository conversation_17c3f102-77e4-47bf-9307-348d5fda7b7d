"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Quaternion = void 0;
/**
 * 四元数类
 * 表示三维空间中的旋转
 */
var Quaternion = /** @class */ (function () {
    /**
     * 创建四元数
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    function Quaternion(x, y, z, w) {
        if (x === void 0) { x = 0; }
        if (y === void 0) { y = 0; }
        if (z === void 0) { z = 0; }
        if (w === void 0) { w = 1; }
        this.x = x;
        this.y = y;
        this.z = z;
        this.w = w;
    }
    /**
     * 设置四元数分量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     * @returns 当前四元数
     */
    Quaternion.prototype.set = function (x, y, z, w) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.w = w;
        return this;
    };
    /**
     * 复制另一个四元数的值
     * @param q 要复制的四元数
     * @returns 当前四元数
     */
    Quaternion.prototype.copy = function (q) {
        this.x = q.x;
        this.y = q.y;
        this.z = q.z;
        this.w = q.w;
        return this;
    };
    /**
     * 克隆四元数
     * @returns 新的四元数实例
     */
    Quaternion.prototype.clone = function () {
        return new Quaternion(this.x, this.y, this.z, this.w);
    };
    /**
     * 四元数乘法
     * @param q 要乘的四元数
     * @returns 当前四元数
     */
    Quaternion.prototype.multiply = function (q) {
        var qax = this.x, qay = this.y, qaz = this.z, qaw = this.w;
        var qbx = q.x, qby = q.y, qbz = q.z, qbw = q.w;
        this.x = qax * qbw + qaw * qbx + qay * qbz - qaz * qby;
        this.y = qay * qbw + qaw * qby + qaz * qbx - qax * qbz;
        this.z = qaz * qbw + qaw * qbz + qax * qby - qay * qbx;
        this.w = qaw * qbw - qax * qbx - qay * qby - qaz * qbz;
        return this;
    };
    /**
     * 四元数点积
     * @param q 另一个四元数
     * @returns 点积结果
     */
    Quaternion.prototype.dot = function (q) {
        return this.x * q.x + this.y * q.y + this.z * q.z + this.w * q.w;
    };
    /**
     * 计算四元数长度
     * @returns 四元数长度
     */
    Quaternion.prototype.length = function () {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w);
    };
    /**
     * 归一化四元数
     * @returns 当前四元数
     */
    Quaternion.prototype.normalize = function () {
        var l = this.length();
        if (l === 0) {
            this.x = 0;
            this.y = 0;
            this.z = 0;
            this.w = 1;
        }
        else {
            l = 1 / l;
            this.x *= l;
            this.y *= l;
            this.z *= l;
            this.w *= l;
        }
        return this;
    };
    /**
     * 四元数求逆
     * @returns 当前四元数
     */
    Quaternion.prototype.inverse = function () {
        // 共轭除以模长的平方
        this.conjugate();
        var dot = this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w;
        if (dot !== 0) {
            var invDot = 1.0 / dot;
            this.x *= invDot;
            this.y *= invDot;
            this.z *= invDot;
            this.w *= invDot;
        }
        return this;
    };
    /**
     * 四元数共轭
     * @returns 当前四元数
     */
    Quaternion.prototype.conjugate = function () {
        this.x = -this.x;
        this.y = -this.y;
        this.z = -this.z;
        return this;
    };
    /**
     * 从欧拉角设置四元数
     * @param x X轴旋转（弧度）
     * @param y Y轴旋转（弧度）
     * @param z Z轴旋转（弧度）
     * @returns 当前四元数
     */
    Quaternion.prototype.setFromEuler = function (x, y, z) {
        var c1 = Math.cos(x / 2);
        var c2 = Math.cos(y / 2);
        var c3 = Math.cos(z / 2);
        var s1 = Math.sin(x / 2);
        var s2 = Math.sin(y / 2);
        var s3 = Math.sin(z / 2);
        this.x = s1 * c2 * c3 + c1 * s2 * s3;
        this.y = c1 * s2 * c3 - s1 * c2 * s3;
        this.z = c1 * c2 * s3 + s1 * s2 * c3;
        this.w = c1 * c2 * c3 - s1 * s2 * s3;
        return this;
    };
    /**
     * 从轴角设置四元数
     * @param axis 旋转轴
     * @param angle 旋转角度（弧度）
     * @returns 当前四元数
     */
    Quaternion.prototype.setFromAxisAngle = function (axis, angle) {
        // 归一化轴
        var length = Math.sqrt(axis.x * axis.x + axis.y * axis.y + axis.z * axis.z);
        if (length === 0) {
            this.set(0, 0, 0, 1);
            return this;
        }
        var halfAngle = angle / 2;
        var s = Math.sin(halfAngle) / length;
        this.x = axis.x * s;
        this.y = axis.y * s;
        this.z = axis.z * s;
        this.w = Math.cos(halfAngle);
        return this;
    };
    /**
     * 判断四元数是否约等于另一个四元数
     * @param q 另一个四元数
     * @param epsilon 误差范围
     * @returns 是否约等于
     */
    Quaternion.prototype.equals = function (q, epsilon) {
        if (epsilon === void 0) { epsilon = 0.0001; }
        return (Math.abs(this.x - q.x) < epsilon &&
            Math.abs(this.y - q.y) < epsilon &&
            Math.abs(this.z - q.z) < epsilon &&
            Math.abs(this.w - q.w) < epsilon);
    };
    return Quaternion;
}());
exports.Quaternion = Quaternion;
