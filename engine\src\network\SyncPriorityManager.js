"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncPriorityManager = exports.SyncPriorityType = void 0;
/**
 * 同步优先级管理器
 * 负责管理网络实体的同步优先级
 */
var Debug_1 = require("../utils/Debug");
var Vector3_1 = require("../math/Vector3");
var NetworkEntityComponent_1 = require("./components/NetworkEntityComponent");
var NetworkTransformComponent_1 = require("./components/NetworkTransformComponent");
/**
 * 同步优先级类型
 */
var SyncPriorityType;
(function (SyncPriorityType) {
    /** 距离优先级 */
    SyncPriorityType["DISTANCE"] = "distance";
    /** 视野优先级 */
    SyncPriorityType["VISIBILITY"] = "visibility";
    /** 重要性优先级 */
    SyncPriorityType["IMPORTANCE"] = "importance";
    /** 活动优先级 */
    SyncPriorityType["ACTIVITY"] = "activity";
    /** 自定义优先级 */
    SyncPriorityType["CUSTOM"] = "custom";
})(SyncPriorityType || (exports.SyncPriorityType = SyncPriorityType = {}));
/**
 * 同步优先级管理器
 */
var SyncPriorityManager = /** @class */ (function () {
    /**
     * 创建同步优先级管理器
     * @param config 配置
     */
    function SyncPriorityManager(config) {
        if (config === void 0) { config = {}; }
        /** 本地用户ID */
        this.localUserId = '';
        /** 观察者位置 */
        this.observerPosition = new Vector3_1.Vector3();
        /** 观察者方向 */
        this.observerDirection = new Vector3_1.Vector3(0, 0, 1);
        /** 实体优先级状态映射表 */
        this.entityPriorityStates = new Map();
        /** 优先级更新计时器 */
        this.priorityUpdateTimer = 0;
        /** 是否正在更新优先级 */
        this.isUpdatingPriority = false;
        // 默认配置
        this.config = __assign({ useDistancePriority: true, distancePriorityWeight: 0.5, maxDistance: 100, useVisibilityPriority: true, visibilityPriorityWeight: 0.3, visibilityAngle: 120, useImportancePriority: true, importancePriorityWeight: 0.2, useActivityPriority: true, activityPriorityWeight: 0.4, activityThreshold: 0.1, activityDecayTime: 5000, useCustomPriority: false, customPriorityWeight: 0.1, customPriorityFunction: null, priorityUpdateInterval: 1000, priorityRange: [0, 1], useAdaptiveSync: true, minSyncInterval: 50, maxSyncInterval: 1000, baseSyncInterval: 100, priorityToIntervalFactor: 0.8 }, config);
    }
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     */
    SyncPriorityManager.prototype.initialize = function (localUserId) {
        this.localUserId = localUserId;
    };
    /**
     * 设置观察者位置和方向
     * @param position 位置
     * @param direction 方向
     */
    SyncPriorityManager.prototype.setObserverTransform = function (position, direction) {
        this.observerPosition.copy(position);
        this.observerDirection.copy(direction).normalize();
    };
    /**
     * 注册实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    SyncPriorityManager.prototype.registerEntity = function (entityId, entity) {
        // 获取网络组件
        var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
        if (!networkEntity) {
            Debug_1.Debug.warn('SyncPriorityManager', "Entity ".concat(entityId, " does not have NetworkEntityComponent"));
            return;
        }
        // 获取变换组件
        var transform = entity.getComponent(NetworkTransformComponent_1.NetworkTransformComponent);
        var position = transform ? transform.getPosition() : new Vector3_1.Vector3();
        // 创建优先级状态
        var priorityState = {
            entityId: entityId,
            priority: 0,
            distancePriority: 0,
            visibilityPriority: 0,
            importancePriority: networkEntity.getImportance(),
            activityPriority: 0,
            customPriority: 0,
            lastActivityTime: Date.now(),
            lastPosition: position.clone(),
            lastUpdateTime: Date.now(),
            recommendedSyncInterval: this.config.baseSyncInterval,
            lastSyncIntervalUpdateTime: Date.now()
        };
        // 添加到映射表
        this.entityPriorityStates.set(entityId, priorityState);
        // 立即计算初始优先级
        this.updateEntityPriority(entityId, entity);
    };
    /**
     * 注销实体
     * @param entityId 实体ID
     */
    SyncPriorityManager.prototype.unregisterEntity = function (entityId) {
        this.entityPriorityStates.delete(entityId);
    };
    /**
     * 更新
     * @param deltaTime 时间增量（毫秒）
     * @param entities 实体映射表
     */
    SyncPriorityManager.prototype.update = function (deltaTime, entities) {
        // 更新优先级更新计时器
        this.priorityUpdateTimer += deltaTime;
        // 如果达到更新间隔，则更新所有实体的优先级
        if (this.priorityUpdateTimer >= this.config.priorityUpdateInterval) {
            this.updateAllPriorities(entities);
            this.priorityUpdateTimer = 0;
        }
    };
    /**
     * 更新所有实体的优先级
     * @param entities 实体映射表
     */
    SyncPriorityManager.prototype.updateAllPriorities = function (entities) {
        if (this.isUpdatingPriority) {
            return;
        }
        this.isUpdatingPriority = true;
        try {
            // 更新每个实体的优先级
            for (var _i = 0, _a = entities.entries(); _i < _a.length; _i++) {
                var _b = _a[_i], entityId = _b[0], entity = _b[1];
                // 跳过本地用户实体
                if (entityId === this.localUserId) {
                    continue;
                }
                // 更新实体优先级
                this.updateEntityPriority(entityId, entity);
            }
        }
        finally {
            this.isUpdatingPriority = false;
        }
    };
    /**
     * 更新实体优先级
     * @param entityId 实体ID
     * @param entity 实体
     */
    SyncPriorityManager.prototype.updateEntityPriority = function (entityId, entity) {
        var priorityState = this.entityPriorityStates.get(entityId);
        if (!priorityState) {
            return;
        }
        // 获取网络组件
        var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
        if (!networkEntity) {
            return;
        }
        // 获取变换组件
        var transform = entity.getComponent(NetworkTransformComponent_1.NetworkTransformComponent);
        var position = transform ? transform.getPosition() : new Vector3_1.Vector3();
        // 计算各种优先级
        if (this.config.useDistancePriority) {
            priorityState.distancePriority = this.calculateDistancePriority(position);
        }
        if (this.config.useVisibilityPriority) {
            priorityState.visibilityPriority = this.calculateVisibilityPriority(position);
        }
        if (this.config.useImportancePriority) {
            priorityState.importancePriority = networkEntity.getImportance();
        }
        if (this.config.useActivityPriority) {
            priorityState.activityPriority = this.calculateActivityPriority(position, priorityState);
        }
        if (this.config.useCustomPriority && this.config.customPriorityFunction) {
            priorityState.customPriority = this.config.customPriorityFunction(entity, this.observerPosition);
        }
        // 计算总优先级
        priorityState.priority = this.calculateTotalPriority(priorityState);
        // 如果启用自适应同步，则计算推荐同步间隔
        if (this.config.useAdaptiveSync) {
            this.calculateRecommendedSyncInterval(priorityState);
        }
        // 更新状态
        priorityState.lastPosition.copy(position);
        priorityState.lastUpdateTime = Date.now();
    };
    /**
     * 计算推荐同步间隔
     * @param priorityState 优先级状态
     */
    SyncPriorityManager.prototype.calculateRecommendedSyncInterval = function (priorityState) {
        // 检查是否需要更新同步间隔
        var now = Date.now();
        var timeSinceLastUpdate = now - priorityState.lastSyncIntervalUpdateTime;
        // 每秒最多更新一次同步间隔
        if (timeSinceLastUpdate < 1000) {
            return;
        }
        // 根据优先级计算同步间隔
        // 优先级越高，同步间隔越短
        var priority = priorityState.priority;
        var factor = this.config.priorityToIntervalFactor;
        var minInterval = this.config.minSyncInterval;
        var maxInterval = this.config.maxSyncInterval;
        var baseInterval = this.config.baseSyncInterval;
        // 计算同步间隔
        // 当优先级为0时，使用最大间隔
        // 当优先级为1时，使用最小间隔
        // 中间值使用线性插值
        var intervalRange = maxInterval - minInterval;
        var priorityEffect = priority * factor;
        var interval = Math.round(maxInterval - (intervalRange * priorityEffect));
        // 限制在配置范围内
        var finalInterval = Math.max(minInterval, Math.min(interval, maxInterval));
        // 平滑过渡：与上次推荐间隔进行加权平均
        var smoothFactor = 0.3; // 平滑因子
        var smoothedInterval = Math.round((finalInterval * smoothFactor) + (priorityState.recommendedSyncInterval * (1 - smoothFactor)));
        // 更新推荐同步间隔
        priorityState.recommendedSyncInterval = smoothedInterval;
        priorityState.lastSyncIntervalUpdateTime = now;
    };
    /**
     * 计算距离优先级
     * @param position 实体位置
     * @returns 距离优先级（0-1）
     */
    SyncPriorityManager.prototype.calculateDistancePriority = function (position) {
        // 计算与观察者的距离
        var distance = position.distanceTo(this.observerPosition);
        // 距离越近，优先级越高
        var priority = 1 - Math.min(1, distance / this.config.maxDistance);
        return priority;
    };
    /**
     * 计算视野优先级
     * @param position 实体位置
     * @returns 视野优先级（0-1）
     */
    SyncPriorityManager.prototype.calculateVisibilityPriority = function (position) {
        // 计算方向向量
        var directionToEntity = position.clone().sub(this.observerPosition).normalize();
        // 计算与观察者方向的夹角（弧度）
        var angle = Math.acos(Math.max(-1, Math.min(1, this.observerDirection.dot(directionToEntity))));
        // 转换为角度
        var angleDegrees = angle * (180 / Math.PI);
        // 如果在视野角度内，则优先级为1，否则为0
        var halfAngle = this.config.visibilityAngle / 2;
        var priority = angleDegrees <= halfAngle ? 1 : Math.max(0, 1 - (angleDegrees - halfAngle) / (180 - halfAngle));
        return priority;
    };
    /**
     * 计算活动优先级
     * @param position 实体位置
     * @param priorityState 优先级状态
     * @returns 活动优先级（0-1）
     */
    SyncPriorityManager.prototype.calculateActivityPriority = function (position, priorityState) {
        // 计算位置变化
        var positionChange = position.distanceTo(priorityState.lastPosition);
        // 如果位置变化超过阈值，则更新活动时间
        if (positionChange > this.config.activityThreshold) {
            priorityState.lastActivityTime = Date.now();
        }
        // 计算活动衰减
        var timeSinceLastActivity = Date.now() - priorityState.lastActivityTime;
        var activityDecay = Math.max(0, 1 - timeSinceLastActivity / this.config.activityDecayTime);
        // 活动优先级 = 位置变化 + 活动衰减
        var priority = Math.min(1, positionChange / this.config.activityThreshold + activityDecay);
        return priority;
    };
    /**
     * 计算总优先级
     * @param priorityState 优先级状态
     * @returns 总优先级（0-1）
     */
    SyncPriorityManager.prototype.calculateTotalPriority = function (priorityState) {
        // 加权平均
        var totalWeight = 0;
        var weightedSum = 0;
        if (this.config.useDistancePriority) {
            weightedSum += priorityState.distancePriority * this.config.distancePriorityWeight;
            totalWeight += this.config.distancePriorityWeight;
        }
        if (this.config.useVisibilityPriority) {
            weightedSum += priorityState.visibilityPriority * this.config.visibilityPriorityWeight;
            totalWeight += this.config.visibilityPriorityWeight;
        }
        if (this.config.useImportancePriority) {
            weightedSum += priorityState.importancePriority * this.config.importancePriorityWeight;
            totalWeight += this.config.importancePriorityWeight;
        }
        if (this.config.useActivityPriority) {
            weightedSum += priorityState.activityPriority * this.config.activityPriorityWeight;
            totalWeight += this.config.activityPriorityWeight;
        }
        if (this.config.useCustomPriority) {
            weightedSum += priorityState.customPriority * this.config.customPriorityWeight;
            totalWeight += this.config.customPriorityWeight;
        }
        // 如果没有权重，则返回默认优先级
        if (totalWeight === 0) {
            return 0.5;
        }
        // 计算加权平均
        var priority = weightedSum / totalWeight;
        // 限制在配置的范围内
        return Math.max(this.config.priorityRange[0], Math.min(priority, this.config.priorityRange[1]));
    };
    /**
     * 获取实体优先级
     * @param entityId 实体ID
     * @returns 优先级（0-1）
     */
    SyncPriorityManager.prototype.getEntityPriority = function (entityId) {
        var priorityState = this.entityPriorityStates.get(entityId);
        return priorityState ? priorityState.priority : 0;
    };
    /**
     * 获取实体详细优先级状态
     * @param entityId 实体ID
     * @returns 优先级状态
     */
    SyncPriorityManager.prototype.getEntityPriorityState = function (entityId) {
        var priorityState = this.entityPriorityStates.get(entityId);
        if (!priorityState) {
            return null;
        }
        return {
            entityId: priorityState.entityId,
            priority: priorityState.priority,
            distancePriority: priorityState.distancePriority,
            visibilityPriority: priorityState.visibilityPriority,
            importancePriority: priorityState.importancePriority,
            activityPriority: priorityState.activityPriority,
            customPriority: priorityState.customPriority,
            lastActivityTime: priorityState.lastActivityTime,
            lastUpdateTime: priorityState.lastUpdateTime,
            recommendedSyncInterval: priorityState.recommendedSyncInterval,
            lastSyncIntervalUpdateTime: priorityState.lastSyncIntervalUpdateTime
        };
    };
    /**
     * 获取实体推荐同步间隔
     * @param entityId 实体ID
     * @returns 推荐同步间隔（毫秒）
     */
    SyncPriorityManager.prototype.getEntityRecommendedSyncInterval = function (entityId) {
        var priorityState = this.entityPriorityStates.get(entityId);
        if (!priorityState) {
            return this.config.baseSyncInterval;
        }
        return priorityState.recommendedSyncInterval;
    };
    /**
     * 获取所有实体的优先级
     * @returns 实体优先级映射表
     */
    SyncPriorityManager.prototype.getAllEntityPriorities = function () {
        var priorities = new Map();
        for (var _i = 0, _a = this.entityPriorityStates.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], priorityState = _b[1];
            priorities.set(entityId, priorityState.priority);
        }
        return priorities;
    };
    /**
     * 销毁管理器
     */
    SyncPriorityManager.prototype.dispose = function () {
        this.entityPriorityStates.clear();
        this.priorityUpdateTimer = 0;
        this.isUpdatingPriority = false;
    };
    return SyncPriorityManager;
}());
exports.SyncPriorityManager = SyncPriorityManager;
