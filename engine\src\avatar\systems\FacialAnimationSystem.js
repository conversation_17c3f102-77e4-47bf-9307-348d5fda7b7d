"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacialAnimationSystem = void 0;
var System_1 = require("../../core/System");
var EventEmitter_1 = require("../../utils/EventEmitter");
var FacialAnimationComponent_1 = require("../components/FacialAnimationComponent");
/**
 * 面部动画系统
 */
var FacialAnimationSystem = exports.FacialAnimationSystem = /** @class */ (function (_super) {
    __extends(FacialAnimationSystem, _super);
    /**
     * 构造函数
     * @param config 配置
     */
    function FacialAnimationSystem(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, 250) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 面部动画组件映射 */
        _this.components = new Map();
        /** 模型适配器系统 */
        _this.modelAdapterSystem = null;
        /** 音频上下文 */
        _this.audioContext = null;
        /** 音频分析器 */
        _this.audioAnalyser = null;
        /** 音频源 */
        _this.audioSource = null;
        /** 摄像头视频元素 */
        _this.videoElement = null;
        /** 摄像头流 */
        _this.webcamStream = null;
        _this.config = __assign({ debug: false, autoDetectAudio: false, useWebcam: false, updateFrequency: 1 / 30 }, config);
        if (_this.config.debug) {
            console.log('面部动画系统初始化');
        }
        // 如果启用自动检测音频，初始化音频上下文
        if (_this.config.autoDetectAudio) {
            _this.initAudioContext();
        }
        // 如果启用摄像头，初始化摄像头
        if (_this.config.useWebcam) {
            _this.initWebcam();
        }
        return _this;
    }
    /**
     * 初始化音频上下文
     */
    FacialAnimationSystem.prototype.initAudioContext = function () {
        var _this = this;
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.audioAnalyser = this.audioContext.createAnalyser();
            this.audioAnalyser.fftSize = 1024;
            this.audioAnalyser.smoothingTimeConstant = 0.8;
            // 获取麦克风
            navigator.mediaDevices.getUserMedia({ audio: true, video: false })
                .then(function (stream) {
                if (_this.audioContext) {
                    _this.audioSource = _this.audioContext.createMediaStreamSource(stream);
                    _this.audioSource.connect(_this.audioAnalyser);
                    if (_this.config.debug) {
                        console.log('音频上下文初始化成功');
                    }
                }
            })
                .catch(function (error) {
                console.error('获取麦克风失败:', error);
            });
        }
        catch (error) {
            console.error('初始化音频上下文失败:', error);
        }
    };
    /**
     * 初始化摄像头
     */
    FacialAnimationSystem.prototype.initWebcam = function () {
        var _this = this;
        try {
            this.videoElement = document.createElement('video');
            this.videoElement.style.display = 'none';
            document.body.appendChild(this.videoElement);
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function (stream) {
                if (_this.videoElement) {
                    _this.webcamStream = stream;
                    _this.videoElement.srcObject = stream;
                    _this.videoElement.play();
                    if (_this.config.debug) {
                        console.log('摄像头初始化成功');
                    }
                }
            })
                .catch(function (error) {
                console.error('获取摄像头失败:', error);
            });
        }
        catch (error) {
            console.error('初始化摄像头失败:', error);
        }
    };
    /**
     * 设置模型适配器系统
     * @param system 模型适配器系统
     */
    FacialAnimationSystem.prototype.setModelAdapterSystem = function (system) {
        this.modelAdapterSystem = system;
    };
    /**
     * 创建面部动画组件
     * @param entity 实体
     * @returns 面部动画组件
     */
    FacialAnimationSystem.prototype.createFacialAnimation = function (entity) {
        // 检查实体是否已有面部动画组件
        if (this.components.has(entity)) {
            return this.components.get(entity);
        }
        // 创建面部动画组件
        var component = new FacialAnimationComponent_1.FacialAnimationComponent(entity);
        // 添加组件到实体
        entity.addComponent(component);
        // 添加组件到映射
        this.components.set(entity, component);
        if (this.config.debug) {
            console.log('创建面部动画组件', entity);
        }
        return component;
    };
    /**
     * 获取面部动画组件
     * @param entity 实体
     * @returns 面部动画组件
     */
    FacialAnimationSystem.prototype.getFacialAnimation = function (entity) {
        return this.components.get(entity) || null;
    };
    /**
     * 移除面部动画组件
     * @param entity 实体
     */
    FacialAnimationSystem.prototype.removeFacialAnimation = function (entity) {
        var component = this.components.get(entity);
        if (component) {
            entity.removeComponent(component);
            this.components.delete(entity);
            if (this.config.debug) {
                console.log('移除面部动画组件', entity);
            }
        }
    };
    /**
     * 将面部动画组件与模型绑定
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 是否成功绑定
     */
    FacialAnimationSystem.prototype.linkToModel = function (entity, mesh) {
        if (!this.modelAdapterSystem) {
            console.warn('未设置模型适配器系统');
            return false;
        }
        return this.modelAdapterSystem.createModelAdapter(entity, mesh);
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationSystem.prototype.update = function (deltaTime) {
        // 更新所有面部动画组件
        for (var _i = 0, _a = this.components.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], component = _b[1];
            // 更新组件
            component.update(deltaTime);
            // 如果有模型适配器系统，应用表情和口型到模型
            if (this.modelAdapterSystem) {
                var adapter = this.modelAdapterSystem.getModelAdapter(entity);
                if (adapter) {
                    // 获取当前表情
                    var expressionType = component.getCurrentExpression().expression;
                    var expressionWeight = component.getCurrentExpression().weight;
                    // 获取当前口型
                    var visemeType = component.getCurrentViseme().viseme;
                    var visemeWeight = component.getCurrentViseme().weight;
                    // 应用表情和口型
                    adapter.applyExpression(expressionType, expressionWeight);
                    adapter.applyViseme(visemeType, visemeWeight);
                }
            }
        }
    };
    /**
     * 销毁系统
     */
    FacialAnimationSystem.prototype.dispose = function () {
        // 关闭音频上下文
        if (this.audioSource) {
            this.audioSource.disconnect();
            this.audioSource = null;
        }
        if (this.audioAnalyser) {
            this.audioAnalyser = null;
        }
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        // 关闭摄像头
        if (this.webcamStream) {
            this.webcamStream.getTracks().forEach(function (track) { return track.stop(); });
            this.webcamStream = null;
        }
        if (this.videoElement) {
            document.body.removeChild(this.videoElement);
            this.videoElement = null;
        }
        // 清除组件映射
        this.components.clear();
        if (this.config.debug) {
            console.log('面部动画系统销毁');
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationSystem.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationSystem.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /** 系统名称 */
    FacialAnimationSystem.NAME = 'FacialAnimationSystem';
    return FacialAnimationSystem;
}(System_1.System));
