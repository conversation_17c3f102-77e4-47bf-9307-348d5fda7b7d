"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebRTCConnection = exports.WebRTCConnectionState = void 0;
/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var MessageSerializer_1 = require("./MessageSerializer");
/**
 * WebRTC连接状态
 */
var WebRTCConnectionState;
(function (WebRTCConnectionState) {
    /** 新建 */
    WebRTCConnectionState["NEW"] = "new";
    /** 正在连接 */
    WebRTCConnectionState["CONNECTING"] = "connecting";
    /** 已连接 */
    WebRTCConnectionState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    WebRTCConnectionState["DISCONNECTING"] = "disconnecting";
    /** 已断开连接 */
    WebRTCConnectionState["DISCONNECTED"] = "disconnected";
    /** 连接失败 */
    WebRTCConnectionState["FAILED"] = "failed";
    /** 连接关闭 */
    WebRTCConnectionState["CLOSED"] = "closed";
})(WebRTCConnectionState || (exports.WebRTCConnectionState = WebRTCConnectionState = {}));
/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
var WebRTCConnection = /** @class */ (function (_super) {
    __extends(WebRTCConnection, _super);
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @param iceServers ICE服务器配置
     * @param config 连接配置
     */
    function WebRTCConnection(userId, iceServers, config) {
        if (iceServers === void 0) { iceServers = []; }
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 对等连接 */
        _this.peerConnection = null;
        /** 数据通道 */
        _this.dataChannel = null;
        /** 连接状态 */
        _this.state = WebRTCConnectionState.NEW;
        /** 本地媒体流 */
        _this.localStream = null;
        /** 远程媒体流 */
        _this.remoteStream = null;
        /** 是否是发起方 */
        _this.isInitiator = false;
        /** 是否已交换SDP */
        _this.sdpExchanged = false;
        /** 待处理的ICE候选 */
        _this.pendingCandidates = [];
        /** 心跳间隔（毫秒） */
        _this.heartbeatInterval = 10000;
        /** 心跳定时器ID */
        _this.heartbeatTimerId = null;
        /** 最后一次接收消息的时间戳 */
        _this.lastReceivedTime = 0;
        /** 消息队列 */
        _this.messageQueue = [];
        _this.userId = userId;
        _this.iceServers = iceServers;
        // 默认配置
        _this.config = __assign({ enableDataChannel: true, enableAudio: false, enableVideo: false, enableScreenShare: false, dataChannelConfig: {
                ordered: true,
            }, audioConstraints: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
            }, videoConstraints: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                frameRate: { ideal: 30 },
            }, useCompression: true }, config);
        _this.messageSerializer = new MessageSerializer_1.MessageSerializer(_this.config.useCompression);
        return _this;
    }
    /**
     * 创建对等连接
     */
    WebRTCConnection.prototype.createConnection = function () {
        if (this.peerConnection) {
            return;
        }
        try {
            // 创建RTCPeerConnection
            this.peerConnection = new RTCPeerConnection({
                iceServers: this.iceServers,
            });
            // 设置事件监听器
            this.setupPeerConnectionListeners();
            // 如果启用数据通道，则创建数据通道
            if (this.config.enableDataChannel) {
                this.createDataChannel();
            }
            // 如果启用音频或视频，则获取媒体流
            if (this.config.enableAudio || this.config.enableVideo) {
                this.getLocalMedia();
            }
            this.state = WebRTCConnectionState.NEW;
            Debug_1.Debug.log('WebRTCConnection', "Created peer connection to user: ".concat(this.userId));
        }
        catch (error) {
            Debug_1.Debug.error('WebRTCConnection', 'Failed to create peer connection:', error);
            this.state = WebRTCConnectionState.FAILED;
            this.emit('error', error);
        }
    };
    /**
     * 创建提议
     */
    WebRTCConnection.prototype.createOffer = function () {
        return __awaiter(this, void 0, void 0, function () {
            var offer, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.peerConnection || this.state !== WebRTCConnectionState.NEW) {
                            return [2 /*return*/];
                        }
                        this.isInitiator = true;
                        this.state = WebRTCConnectionState.CONNECTING;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        return [4 /*yield*/, this.peerConnection.createOffer()];
                    case 2:
                        offer = _a.sent();
                        // 设置本地描述
                        return [4 /*yield*/, this.peerConnection.setLocalDescription(offer)];
                    case 3:
                        // 设置本地描述
                        _a.sent();
                        // 触发提议事件
                        this.emit('offer', offer);
                        Debug_1.Debug.log('WebRTCConnection', "Created offer to user: ".concat(this.userId));
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to create offer:', error_1);
                        this.state = WebRTCConnectionState.FAILED;
                        this.emit('error', error_1);
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 处理提议
     * @param offer 提议
     */
    WebRTCConnection.prototype.handleOffer = function (offer) {
        return __awaiter(this, void 0, void 0, function () {
            var answer, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.peerConnection) {
                            return [2 /*return*/];
                        }
                        this.isInitiator = false;
                        this.state = WebRTCConnectionState.CONNECTING;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 5, , 6]);
                        // 设置远程描述
                        return [4 /*yield*/, this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))];
                    case 2:
                        // 设置远程描述
                        _a.sent();
                        return [4 /*yield*/, this.peerConnection.createAnswer()];
                    case 3:
                        answer = _a.sent();
                        // 设置本地描述
                        return [4 /*yield*/, this.peerConnection.setLocalDescription(answer)];
                    case 4:
                        // 设置本地描述
                        _a.sent();
                        // 触发应答事件
                        this.emit('answer', answer);
                        this.sdpExchanged = true;
                        // 处理待处理的ICE候选
                        this.processPendingCandidates();
                        Debug_1.Debug.log('WebRTCConnection', "Handled offer from user: ".concat(this.userId));
                        return [3 /*break*/, 6];
                    case 5:
                        error_2 = _a.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to handle offer:', error_2);
                        this.state = WebRTCConnectionState.FAILED;
                        this.emit('error', error_2);
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 处理应答
     * @param answer 应答
     */
    WebRTCConnection.prototype.handleAnswer = function (answer) {
        return __awaiter(this, void 0, void 0, function () {
            var error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.peerConnection || !this.isInitiator) {
                            return [2 /*return*/];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 设置远程描述
                        return [4 /*yield*/, this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer))];
                    case 2:
                        // 设置远程描述
                        _a.sent();
                        this.sdpExchanged = true;
                        // 处理待处理的ICE候选
                        this.processPendingCandidates();
                        Debug_1.Debug.log('WebRTCConnection', "Handled answer from user: ".concat(this.userId));
                        return [3 /*break*/, 4];
                    case 3:
                        error_3 = _a.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to handle answer:', error_3);
                        this.state = WebRTCConnectionState.FAILED;
                        this.emit('error', error_3);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 处理ICE候选
     * @param candidate ICE候选
     */
    WebRTCConnection.prototype.handleIceCandidate = function (candidate) {
        return __awaiter(this, void 0, void 0, function () {
            var error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.peerConnection) {
                            return [2 /*return*/];
                        }
                        // 如果SDP尚未交换，则将候选添加到待处理列表
                        if (!this.sdpExchanged) {
                            this.pendingCandidates.push(candidate);
                            return [2 /*return*/];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 添加ICE候选
                        return [4 /*yield*/, this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate))];
                    case 2:
                        // 添加ICE候选
                        _a.sent();
                        Debug_1.Debug.log('WebRTCConnection', "Added ICE candidate from user: ".concat(this.userId));
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to add ICE candidate:', error_4);
                        this.emit('error', error_4);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 发送消息
     * @param message 消息对象
     */
    WebRTCConnection.prototype.send = function (message) {
        if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
            // 添加到消息队列
            this.messageQueue.push(message);
            return;
        }
        try {
            // 序列化消息
            var data = this.messageSerializer.serialize(message);
            // 发送消息
            this.dataChannel.send(data);
        }
        catch (error) {
            Debug_1.Debug.error('WebRTCConnection', 'Failed to send message:', error);
            this.emit('error', error);
            // 添加到消息队列
            this.messageQueue.push(message);
        }
    };
    /**
     * 断开连接
     */
    WebRTCConnection.prototype.disconnect = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.state = WebRTCConnectionState.DISCONNECTING;
                // 停止心跳
                this.stopHeartbeat();
                // 关闭数据通道
                if (this.dataChannel) {
                    this.dataChannel.close();
                    this.dataChannel = null;
                }
                // 停止本地媒体流
                if (this.localStream) {
                    this.localStream.getTracks().forEach(function (track) { return track.stop(); });
                    this.localStream = null;
                }
                // 关闭对等连接
                if (this.peerConnection) {
                    this.peerConnection.close();
                    this.peerConnection = null;
                }
                this.state = WebRTCConnectionState.DISCONNECTED;
                this.emit('disconnected');
                Debug_1.Debug.log('WebRTCConnection', "Disconnected from user: ".concat(this.userId));
                return [2 /*return*/];
            });
        });
    };
    /**
     * 更新连接
     * @param deltaTime 帧间隔时间（秒）
     */
    WebRTCConnection.prototype.update = function (deltaTime) {
        // 检查连接状态
        if (this.state === WebRTCConnectionState.CONNECTED) {
            // 检查是否长时间未收到消息
            var now = Date.now();
            var elapsed = now - this.lastReceivedTime;
            if (elapsed > this.heartbeatInterval * 3) {
                Debug_1.Debug.warn('WebRTCConnection', "No message received for ".concat(elapsed, "ms, connection may be lost"));
                // 尝试重新连接
                this.reconnect();
            }
        }
    };
    /**
     * 获取远程用户ID
     * @returns 远程用户ID
     */
    WebRTCConnection.prototype.getUserId = function () {
        return this.userId;
    };
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    WebRTCConnection.prototype.getState = function () {
        return this.state;
    };
    /**
     * 获取本地媒体流
     * @returns 本地媒体流
     */
    WebRTCConnection.prototype.getLocalStream = function () {
        return this.localStream;
    };
    /**
     * 获取远程媒体流
     * @returns 远程媒体流
     */
    WebRTCConnection.prototype.getRemoteStream = function () {
        return this.remoteStream;
    };
    /**
     * 设置心跳间隔
     * @param interval 心跳间隔（毫秒）
     */
    WebRTCConnection.prototype.setHeartbeatInterval = function (interval) {
        this.heartbeatInterval = interval;
        // 如果已连接，则重新启动心跳
        if (this.state === WebRTCConnectionState.CONNECTED) {
            this.stopHeartbeat();
            this.startHeartbeat();
        }
    };
    /**
     * 设置对等连接事件监听器
     */
    WebRTCConnection.prototype.setupPeerConnectionListeners = function () {
        var _this = this;
        if (!this.peerConnection) {
            return;
        }
        // ICE候选事件
        this.peerConnection.onicecandidate = function (event) {
            if (event.candidate) {
                // 触发ICE候选事件
                _this.emit('iceCandidate', event.candidate.toJSON());
            }
        };
        // ICE连接状态变化事件
        this.peerConnection.oniceconnectionstatechange = function () {
            Debug_1.Debug.log('WebRTCConnection', "ICE connection state changed to: ".concat(_this.peerConnection.iceConnectionState));
            switch (_this.peerConnection.iceConnectionState) {
                case 'connected':
                case 'completed':
                    if (_this.state !== WebRTCConnectionState.CONNECTED) {
                        _this.state = WebRTCConnectionState.CONNECTED;
                        _this.lastReceivedTime = Date.now();
                        // 启动心跳
                        _this.startHeartbeat();
                        // 处理消息队列
                        _this.processMessageQueue();
                        _this.emit('connected');
                    }
                    break;
                case 'failed':
                    _this.state = WebRTCConnectionState.FAILED;
                    _this.emit('error', new Error('ICE connection failed'));
                    // 尝试重新连接
                    _this.reconnect();
                    break;
                case 'disconnected':
                    // ICE连接可能暂时断开，等待重新连接
                    Debug_1.Debug.warn('WebRTCConnection', 'ICE connection disconnected, waiting for reconnection');
                    break;
                case 'closed':
                    _this.state = WebRTCConnectionState.CLOSED;
                    _this.emit('disconnected');
                    break;
            }
        };
        // 连接状态变化事件
        this.peerConnection.onconnectionstatechange = function () {
            Debug_1.Debug.log('WebRTCConnection', "Connection state changed to: ".concat(_this.peerConnection.connectionState));
            switch (_this.peerConnection.connectionState) {
                case 'connected':
                    if (_this.state !== WebRTCConnectionState.CONNECTED) {
                        _this.state = WebRTCConnectionState.CONNECTED;
                        _this.lastReceivedTime = Date.now();
                        // 启动心跳
                        _this.startHeartbeat();
                        // 处理消息队列
                        _this.processMessageQueue();
                        _this.emit('connected');
                    }
                    break;
                case 'failed':
                    _this.state = WebRTCConnectionState.FAILED;
                    _this.emit('error', new Error('Connection failed'));
                    // 尝试重新连接
                    _this.reconnect();
                    break;
                case 'closed':
                    _this.state = WebRTCConnectionState.CLOSED;
                    _this.emit('disconnected');
                    break;
            }
        };
        // 数据通道事件
        this.peerConnection.ondatachannel = function (event) {
            _this.dataChannel = event.channel;
            _this.setupDataChannelListeners();
            Debug_1.Debug.log('WebRTCConnection', "Received data channel from user: ".concat(_this.userId));
        };
        // 媒体流事件
        this.peerConnection.ontrack = function (event) {
            if (!_this.remoteStream) {
                _this.remoteStream = new MediaStream();
            }
            // 添加轨道到远程媒体流
            event.streams[0].getTracks().forEach(function (track) {
                _this.remoteStream.addTrack(track);
            });
            _this.emit('track', event.track, _this.remoteStream);
            Debug_1.Debug.log('WebRTCConnection', "Received ".concat(event.track.kind, " track from user: ").concat(_this.userId));
        };
    };
    /**
     * 设置数据通道事件监听器
     */
    WebRTCConnection.prototype.setupDataChannelListeners = function () {
        var _this = this;
        if (!this.dataChannel) {
            return;
        }
        // 打开事件
        this.dataChannel.onopen = function () {
            Debug_1.Debug.log('WebRTCConnection', "Data channel opened with user: ".concat(_this.userId));
            // 处理消息队列
            _this.processMessageQueue();
        };
        // 关闭事件
        this.dataChannel.onclose = function () {
            Debug_1.Debug.log('WebRTCConnection', "Data channel closed with user: ".concat(_this.userId));
        };
        // 错误事件
        this.dataChannel.onerror = function (event) {
            Debug_1.Debug.error('WebRTCConnection', 'Data channel error:', event);
            _this.emit('error', new Error('Data channel error'));
        };
        // 消息事件
        this.dataChannel.onmessage = function (event) {
            _this.lastReceivedTime = Date.now();
            try {
                // 反序列化消息
                var message = _this.messageSerializer.deserialize(event.data);
                // 如果是心跳消息，则不触发消息事件
                if (message.type === 'heartbeat') {
                    return;
                }
                _this.emit('message', message);
            }
            catch (error) {
                Debug_1.Debug.error('WebRTCConnection', 'Failed to deserialize message:', error);
                _this.emit('error', error);
            }
        };
    };
    /**
     * 创建数据通道
     */
    WebRTCConnection.prototype.createDataChannel = function () {
        if (!this.peerConnection || this.dataChannel) {
            return;
        }
        try {
            // 创建数据通道
            this.dataChannel = this.peerConnection.createDataChannel('data', this.config.dataChannelConfig);
            // 设置数据通道事件监听器
            this.setupDataChannelListeners();
            Debug_1.Debug.log('WebRTCConnection', "Created data channel with user: ".concat(this.userId));
        }
        catch (error) {
            Debug_1.Debug.error('WebRTCConnection', 'Failed to create data channel:', error);
            this.emit('error', error);
        }
    };
    /**
     * 获取本地媒体
     */
    WebRTCConnection.prototype.getLocalMedia = function () {
        return __awaiter(this, void 0, void 0, function () {
            var constraints, _a, error_5;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        constraints = {
                            audio: this.config.enableAudio ? this.config.audioConstraints : false,
                            video: this.config.enableVideo ? this.config.videoConstraints : false,
                        };
                        // 获取媒体流
                        _a = this;
                        return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];
                    case 1:
                        // 获取媒体流
                        _a.localStream = _b.sent();
                        // 添加轨道到对等连接
                        this.localStream.getTracks().forEach(function (track) {
                            _this.peerConnection.addTrack(track, _this.localStream);
                        });
                        this.emit('localStream', this.localStream);
                        Debug_1.Debug.log('WebRTCConnection', "Got local media stream with user: ".concat(this.userId));
                        return [3 /*break*/, 3];
                    case 2:
                        error_5 = _b.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to get local media:', error_5);
                        this.emit('error', error_5);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 处理待处理的ICE候选
     */
    WebRTCConnection.prototype.processPendingCandidates = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _i, _a, candidate, error_6;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.peerConnection || !this.sdpExchanged) {
                            return [2 /*return*/];
                        }
                        _i = 0, _a = this.pendingCandidates;
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 6];
                        candidate = _a[_i];
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate))];
                    case 3:
                        _b.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        error_6 = _b.sent();
                        Debug_1.Debug.error('WebRTCConnection', 'Failed to add ICE candidate:', error_6);
                        return [3 /*break*/, 5];
                    case 5:
                        _i++;
                        return [3 /*break*/, 1];
                    case 6:
                        // 清空待处理列表
                        this.pendingCandidates = [];
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 重新连接
     */
    WebRTCConnection.prototype.reconnect = function () {
        // 断开连接
        this.disconnect();
        // 创建新的连接
        this.createConnection();
        // 如果是发起方，则创建提议
        if (this.isInitiator) {
            this.createOffer();
        }
    };
    /**
     * 启动心跳
     */
    WebRTCConnection.prototype.startHeartbeat = function () {
        var _this = this;
        if (this.heartbeatTimerId !== null) {
            return;
        }
        this.heartbeatTimerId = window.setInterval(function () {
            _this.sendHeartbeat();
        }, this.heartbeatInterval);
    };
    /**
     * 停止心跳
     */
    WebRTCConnection.prototype.stopHeartbeat = function () {
        if (this.heartbeatTimerId !== null) {
            clearInterval(this.heartbeatTimerId);
            this.heartbeatTimerId = null;
        }
    };
    /**
     * 发送心跳消息
     */
    WebRTCConnection.prototype.sendHeartbeat = function () {
        if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
            return;
        }
        var message = {
            type: 'heartbeat',
            data: {
                timestamp: Date.now(),
            },
            timestamp: Date.now(),
        };
        try {
            // 序列化消息
            var data = this.messageSerializer.serialize(message);
            // 发送消息
            this.dataChannel.send(data);
        }
        catch (error) {
            Debug_1.Debug.error('WebRTCConnection', 'Failed to send heartbeat:', error);
        }
    };
    /**
     * 处理消息队列
     */
    WebRTCConnection.prototype.processMessageQueue = function () {
        if (!this.dataChannel || this.dataChannel.readyState !== 'open' || this.messageQueue.length === 0) {
            return;
        }
        // 复制消息队列
        var queue = __spreadArray([], this.messageQueue, true);
        this.messageQueue = [];
        // 发送队列中的消息
        for (var _i = 0, queue_1 = queue; _i < queue_1.length; _i++) {
            var message = queue_1[_i];
            this.send(message);
        }
    };
    return WebRTCConnection;
}(EventEmitter_1.EventEmitter));
exports.WebRTCConnection = WebRTCConnection;
