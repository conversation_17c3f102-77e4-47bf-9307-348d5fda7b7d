"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseInputDevice = void 0;
/**
 * 输入设备接口
 * 定义输入设备的基本功能
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 输入设备基类
 * 实现输入设备接口的基本功能
 */
var BaseInputDevice = /** @class */ (function () {
    /**
     * 创建输入设备
     * @param name 设备名称
     */
    function BaseInputDevice(name) {
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 设备值映射 */
        this.values = new Map();
        /** 是否已初始化 */
        this.initialized = false;
        /** 是否已销毁 */
        this.destroyed = false;
        this.name = name;
    }
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    BaseInputDevice.prototype.getName = function () {
        return this.name;
    };
    /**
     * 初始化设备
     */
    BaseInputDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        this.initialized = true;
    };
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    BaseInputDevice.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
    };
    /**
     * 销毁设备
     */
    BaseInputDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        this.destroyed = true;
    };
    /**
     * 获取设备值
     * @param key 键名
     * @returns 设备值
     */
    BaseInputDevice.prototype.getValue = function (key) {
        return this.values.get(key);
    };
    /**
     * 设置设备值
     * @param key 键名
     * @param value 设备值
     */
    BaseInputDevice.prototype.setValue = function (key, value) {
        var oldValue = this.values.get(key);
        if (oldValue !== value) {
            this.values.set(key, value);
            this.eventEmitter.emit("".concat(key, "Changed"), { key: key, value: value, oldValue: oldValue });
        }
    };
    /**
     * 检查设备是否支持指定键
     * @param key 键名
     * @returns 是否支持
     */
    BaseInputDevice.prototype.hasKey = function (key) {
        return this.values.has(key);
    };
    /**
     * 获取所有键名
     * @returns 键名列表
     */
    BaseInputDevice.prototype.getKeys = function () {
        return Array.from(this.values.keys());
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    BaseInputDevice.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    BaseInputDevice.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    return BaseInputDevice;
}());
exports.BaseInputDevice = BaseInputDevice;
