"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlowNode = void 0;
/**
 * 视觉脚本流程节点
 * 流程节点用于控制执行流程
 */
var Node_1 = require("./Node");
/**
 * 流程节点基类
 */
var FlowNode = /** @class */ (function (_super) {
    __extends(FlowNode, _super);
    /**
     * 创建流程节点
     * @param options 节点选项
     */
    function FlowNode(options) {
        var _this = _super.call(this, options) || this;
        /** 节点类型 */
        _this.nodeType = Node_1.NodeType.NORMAL;
        /** 节点类别 */
        _this.category = Node_1.NodeCategory.FLOW;
        _this.inputFlowName = options.inputFlowName || 'flow';
        _this.outputFlowNames = options.outputFlowNames || ['flow'];
        return _this;
    }
    /**
     * 初始化插槽
     */
    FlowNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: this.inputFlowName,
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加输出流程插槽
        for (var _i = 0, _a = this.outputFlowNames; _i < _a.length; _i++) {
            var name_1 = _a[_i];
            this.addOutput({
                name: name_1,
                type: Node_1.SocketType.FLOW,
                direction: Node_1.SocketDirection.OUTPUT,
                description: '执行输出'
            });
        }
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    FlowNode.prototype.execute = function () {
        // 获取所有输入值
        var inputs = {};
        for (var _i = 0, _a = this.inputs.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], name_2 = _b[0], socket = _b[1];
            if (socket.type === Node_1.SocketType.DATA) {
                inputs[name_2] = this.getInputValue(name_2);
            }
        }
        // 处理输入并确定输出流程
        var outputFlowName = this.process(inputs);
        // 如果有输出流程，触发它
        if (outputFlowName && this.outputs.has(outputFlowName)) {
            this.triggerFlow(outputFlowName);
        }
        return outputFlowName;
    };
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    FlowNode.prototype.process = function (inputs) {
        // 默认返回第一个输出流程
        return this.outputFlowNames.length > 0 ? this.outputFlowNames[0] : null;
    };
    return FlowNode;
}(Node_1.Node));
exports.FlowNode = FlowNode;
