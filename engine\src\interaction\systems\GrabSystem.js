"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabSystem = exports.GrabType = void 0;
/**
 * 抓取系统
 * 用于处理实体之间的抓取交互
 */
var System_1 = require("../../core/System");
var three_1 = require("three");
var Debug_1 = require("../../utils/Debug");
var PhysicsGrabComponent_1 = require("../components/PhysicsGrabComponent");
// 抓取类型枚举
var GrabType;
(function (GrabType) {
    GrabType["DIRECT"] = "direct";
    GrabType["DISTANCE"] = "distance";
    GrabType["SPRING"] = "spring";
})(GrabType || (exports.GrabType = GrabType = {}));
/**
 * 抓取系统
 */
var GrabSystem = exports.GrabSystem = /** @class */ (function (_super) {
    __extends(GrabSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    function GrabSystem(world, config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, 100) || this;
        /** 可抓取组件列表 */
        _this.grabbableComponents = new Map();
        /** 抓取者组件列表 */
        _this.grabberComponents = new Map();
        /** 被抓取组件列表 */
        _this.grabbedComponents = new Map();
        /** 临时向量 - 用于计算 */
        _this.tempVector = new three_1.Vector3();
        /** 临时四元数 - 用于计算 */
        _this.tempQuaternion = new three_1.Quaternion();
        /** 临时矩阵 - 用于计算 */
        _this.tempMatrix = new three_1.Matrix4();
        _this.world = world;
        _this.config = {
            debug: config.debug !== undefined ? config.debug : false,
            enablePhysicsGrab: config.enablePhysicsGrab !== undefined ? config.enablePhysicsGrab : true,
            enableNetworkSync: config.enableNetworkSync !== undefined ? config.enableNetworkSync : true,
            enableGestureGrab: config.enableGestureGrab !== undefined ? config.enableGestureGrab : true,
            defaultGrabType: config.defaultGrabType || GrabType.DIRECT
        };
        return _this;
    }
    /**
     * 初始化系统
     */
    GrabSystem.prototype.initialize = function () {
        if (this.config.debug) {
            Debug_1.Debug.log('GrabSystem', 'Initializing GrabSystem');
        }
        // 获取输入系统引用
        this.inputSystem = this.world.getSystem('InputSystem');
        if (!this.inputSystem && this.config.debug) {
            Debug_1.Debug.warn('GrabSystem', 'InputSystem not found, some features may not work properly');
        }
        // 获取物理系统引用
        this.physicsSystem = this.world.getSystem('PhysicsSystem');
        if (!this.physicsSystem && this.config.enablePhysicsGrab && this.config.debug) {
            Debug_1.Debug.warn('GrabSystem', 'PhysicsSystem not found, physics grab will not work');
        }
        // 监听实体添加和移除事件
        this.world.on('entityAdded', this.onEntityAdded.bind(this));
        this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
        // 初始化现有实体
        var entities = this.world.getEntities();
        for (var _i = 0, entities_1 = entities; _i < entities_1.length; _i++) {
            var entity = entities_1[_i];
            this.setupEntityComponents(entity);
        }
    };
    /**
     * 处理实体添加事件
     * @param entity 添加的实体
     */
    GrabSystem.prototype.onEntityAdded = function (entity) {
        this.setupEntityComponents(entity);
    };
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    GrabSystem.prototype.onEntityRemoved = function (entity) {
        // 移除组件引用
        this.grabbableComponents.delete(entity);
        this.grabberComponents.delete(entity);
        this.grabbedComponents.delete(entity);
    };
    /**
     * 设置实体组件
     * @param entity 实体
     */
    GrabSystem.prototype.setupEntityComponents = function (entity) {
        // 检查并注册可抓取组件
        var grabbableComponent = entity.getComponent('GrabbableComponent');
        if (grabbableComponent) {
            this.registerGrabbableComponent(entity, grabbableComponent);
        }
        // 检查并注册抓取者组件
        var grabberComponent = entity.getComponent('GrabberComponent');
        if (grabberComponent) {
            this.registerGrabberComponent(entity, grabberComponent);
        }
        // 检查并注册被抓取组件
        var grabbedComponent = entity.getComponent('GrabbedComponent');
        if (grabbedComponent) {
            this.registerGrabbedComponent(entity, grabbedComponent);
        }
    };
    /**
     * 注册可抓取组件
     * @param entity 实体
     * @param component 可抓取组件
     */
    GrabSystem.prototype.registerGrabbableComponent = function (entity, component) {
        this.grabbableComponents.set(entity, component);
        if (this.config.debug) {
            Debug_1.Debug.log('GrabSystem', "Registered grabbable component for entity ".concat(entity.id));
        }
    };
    /**
     * 注册抓取者组件
     * @param entity 实体
     * @param component 抓取者组件
     */
    GrabSystem.prototype.registerGrabberComponent = function (entity, component) {
        this.grabberComponents.set(entity, component);
        if (this.config.debug) {
            Debug_1.Debug.log('GrabSystem', "Registered grabber component for entity ".concat(entity.id));
        }
    };
    /**
     * 注册被抓取组件
     * @param entity 实体
     * @param component 被抓取组件
     */
    GrabSystem.prototype.registerGrabbedComponent = function (entity, component) {
        this.grabbedComponents.set(entity, component);
        if (this.config.debug) {
            Debug_1.Debug.log('GrabSystem', "Registered grabbed component for entity ".concat(entity.id));
        }
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    GrabSystem.prototype.update = function (deltaTime) {
        // 处理输入
        this.handleInput();
        // 更新被抓取对象的位置和旋转
        this.updateGrabbedEntities();
    };
    /**
     * 处理输入
     */
    GrabSystem.prototype.handleInput = function () {
        // 如果没有输入系统，则返回
        if (!this.inputSystem)
            return;
        // 这里可以添加输入处理逻辑
        // 例如，检测按键或手势来触发抓取和释放
    };
    /**
     * 更新被抓取实体
     */
    GrabSystem.prototype.updateGrabbedEntities = function () {
        for (var _i = 0, _a = this.grabbedComponents; _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], grabbedComponent = _b[1];
            var grabber = grabbedComponent.grabber;
            var hand = grabbedComponent.hand;
            // 获取抓取者的变换组件
            var grabberTransform = grabber.getComponent('Transform');
            if (!grabberTransform)
                continue;
            // 获取被抓取实体的变换组件
            var entityTransform = entity.getComponent('Transform');
            if (!entityTransform)
                continue;
            // 获取可抓取组件
            var grabbableComponent = this.grabbableComponents.get(entity);
            if (!grabbableComponent)
                continue;
            // 根据抓取类型更新位置和旋转
            switch (grabbableComponent.grabType) {
                case GrabType.DIRECT:
                    this.updateDirectGrab(entity, grabber, hand);
                    break;
                case GrabType.DISTANCE:
                    this.updateDistanceGrab(entity, grabber, hand);
                    break;
                case GrabType.SPRING:
                    this.updateSpringGrab(entity, grabber, hand);
                    break;
            }
        }
    };
    /**
     * 更新直接抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    GrabSystem.prototype.updateDirectGrab = function (entity, grabber, hand) {
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform)
            return;
        // 获取被抓取实体的变换组件
        var entityTransform = entity.getComponent('Transform');
        if (!entityTransform)
            return;
        // 获取被抓取组件
        var grabbedComponent = this.grabbedComponents.get(entity);
        if (!grabbedComponent)
            return;
        // 计算手的位置和旋转
        var handPosition = this.getHandPosition(grabber, hand);
        var handRotation = this.getHandRotation(grabber, hand);
        // 应用偏移
        var offset = grabbedComponent.offset;
        this.tempVector.set(offset.x, offset.y, offset.z);
        this.tempVector.applyQuaternion(handRotation);
        // 更新位置和旋转
        entityTransform.setPosition(handPosition.x + this.tempVector.x, handPosition.y + this.tempVector.y, handPosition.z + this.tempVector.z);
        entityTransform.setRotation(handRotation.x, handRotation.y, handRotation.z, handRotation.w);
    };
    /**
     * 更新距离抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    GrabSystem.prototype.updateDistanceGrab = function (entity, grabber, hand) {
        // 获取可抓取组件
        var grabbableComponent = this.grabbableComponents.get(entity);
        if (!grabbableComponent)
            return;
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform)
            return;
        // 获取被抓取实体的变换组件
        var entityTransform = entity.getComponent('Transform');
        if (!entityTransform)
            return;
        // 计算手的位置和旋转
        var handPosition = this.getHandPosition(grabber, hand);
        var handRotation = this.getHandRotation(grabber, hand);
        // 计算方向向量
        this.tempVector.set(0, 0, -1);
        this.tempVector.applyQuaternion(handRotation);
        // 计算目标位置
        var distance = grabbableComponent.grabDistance;
        var targetPosition = new three_1.Vector3(handPosition.x + this.tempVector.x * distance, handPosition.y + this.tempVector.y * distance, handPosition.z + this.tempVector.z * distance);
        // 更新位置和旋转
        entityTransform.setPosition(targetPosition.x, targetPosition.y, targetPosition.z);
        entityTransform.setRotation(handRotation.x, handRotation.y, handRotation.z, handRotation.w);
    };
    /**
     * 更新弹簧抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    GrabSystem.prototype.updateSpringGrab = function (entity, grabber, hand) {
        // 如果没有物理系统，则使用直接抓取
        if (!this.physicsSystem) {
            this.updateDirectGrab(entity, grabber, hand);
            return;
        }
        // 获取物理体组件
        var physicsBody = entity.getComponent('PhysicsBodyComponent');
        if (!physicsBody) {
            this.updateDirectGrab(entity, grabber, hand);
            return;
        }
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform)
            return;
        // 计算手的位置
        var handPosition = this.getHandPosition(grabber, hand);
        // 获取当前位置
        var entityTransform = entity.getComponent('Transform');
        if (!entityTransform)
            return;
        var currentPosition = new three_1.Vector3(entityTransform.getPosition().x, entityTransform.getPosition().y, entityTransform.getPosition().z);
        // 计算方向和距离
        var direction = new three_1.Vector3().subVectors(handPosition, currentPosition);
        var distance = direction.length();
        direction.normalize();
        // 应用弹簧力
        var springStrength = 10.0; // 弹簧强度
        var force = direction.multiplyScalar(distance * springStrength);
        physicsBody.applyForce(force.x, force.y, force.z);
    };
    /**
     * 获取手的位置
     * @param grabber 抓取者
     * @param hand 手
     * @returns 手的位置
     */
    GrabSystem.prototype.getHandPosition = function (grabber, hand) {
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform) {
            return new three_1.Vector3();
        }
        // 获取抓取者的位置
        var position = new three_1.Vector3(grabberTransform.getPosition().x, grabberTransform.getPosition().y, grabberTransform.getPosition().z);
        // 根据手的类型计算偏移
        var handOffset = new three_1.Vector3();
        if (hand === PhysicsGrabComponent_1.Hand.LEFT) {
            handOffset.set(-0.2, -0.1, 0.1);
        }
        else if (hand === PhysicsGrabComponent_1.Hand.RIGHT) {
            handOffset.set(0.2, -0.1, 0.1);
        }
        // 应用旋转
        var rotation = new three_1.Quaternion(grabberTransform.rotation.x, grabberTransform.rotation.y, grabberTransform.rotation.z, grabberTransform.rotation.w);
        handOffset.applyQuaternion(rotation);
        // 返回最终位置
        return position.add(handOffset);
    };
    /**
     * 获取手的旋转
     * @param grabber 抓取者
     * @param hand 手
     * @returns 手的旋转
     */
    GrabSystem.prototype.getHandRotation = function (grabber, hand) {
        // 获取抓取者的变换组件
        var grabberTransform = grabber.getComponent('Transform');
        if (!grabberTransform) {
            return new three_1.Quaternion();
        }
        // 获取抓取者的旋转
        var rotation = new three_1.Quaternion(grabberTransform.rotation.x, grabberTransform.rotation.y, grabberTransform.rotation.z, grabberTransform.rotation.w);
        // 根据手的类型计算额外旋转
        var handRotation = new three_1.Quaternion();
        if (hand === PhysicsGrabComponent_1.Hand.LEFT) {
            handRotation.setFromEuler({ x: 0, y: 0, z: -Math.PI / 8 });
        }
        else if (hand === PhysicsGrabComponent_1.Hand.RIGHT) {
            handRotation.setFromEuler({ x: 0, y: 0, z: Math.PI / 8 });
        }
        // 应用额外旋转
        rotation.multiply(handRotation);
        return rotation;
    };
    /** 系统名称 */
    GrabSystem.NAME = 'GrabSystem';
    return GrabSystem;
}(System_1.System));
