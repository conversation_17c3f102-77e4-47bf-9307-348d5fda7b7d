"use strict";
/**
 * 网络事件
 * 定义网络事件的结构和类型
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkEventType = void 0;
/**
 * 网络事件类型
 */
var NetworkEventType;
(function (NetworkEventType) {
    // 连接事件
    /** 连接成功 */
    NetworkEventType["CONNECTED"] = "connected";
    /** 连接失败 */
    NetworkEventType["CONNECTION_FAILED"] = "connection_failed";
    /** 断开连接 */
    NetworkEventType["DISCONNECTED"] = "disconnected";
    /** 重新连接 */
    NetworkEventType["RECONNECTED"] = "reconnected";
    /** 重新连接失败 */
    NetworkEventType["RECONNECTION_FAILED"] = "reconnection_failed";
    /** 连接错误 */
    NetworkEventType["CONNECTION_ERROR"] = "connection_error";
    // 房间事件
    /** 加入房间 */
    NetworkEventType["ROOM_JOINED"] = "room_joined";
    /** 离开房间 */
    NetworkEventType["ROOM_LEFT"] = "room_left";
    /** 房间创建 */
    NetworkEventType["ROOM_CREATED"] = "room_created";
    /** 房间关闭 */
    NetworkEventType["ROOM_CLOSED"] = "room_closed";
    /** 房间更新 */
    NetworkEventType["ROOM_UPDATED"] = "room_updated";
    // 用户事件
    /** 用户加入 */
    NetworkEventType["USER_JOINED"] = "user_joined";
    /** 用户离开 */
    NetworkEventType["USER_LEFT"] = "user_left";
    /** 用户更新 */
    NetworkEventType["USER_UPDATED"] = "user_updated";
    // 实体事件
    /** 实体创建 */
    NetworkEventType["ENTITY_CREATED"] = "entity_created";
    /** 实体更新 */
    NetworkEventType["ENTITY_UPDATED"] = "entity_updated";
    /** 实体删除 */
    NetworkEventType["ENTITY_DELETED"] = "entity_deleted";
    /** 实体所有权变更 */
    NetworkEventType["ENTITY_OWNERSHIP_CHANGED"] = "entity_ownership_changed";
    // 消息事件
    /** 消息接收 */
    NetworkEventType["MESSAGE_RECEIVED"] = "message_received";
    /** 消息发送 */
    NetworkEventType["MESSAGE_SENT"] = "message_sent";
    /** 消息确认 */
    NetworkEventType["MESSAGE_ACKNOWLEDGED"] = "message_acknowledged";
    /** 消息错误 */
    NetworkEventType["MESSAGE_ERROR"] = "message_error";
    // WebRTC事件
    /** WebRTC连接成功 */
    NetworkEventType["WEBRTC_CONNECTED"] = "webrtc_connected";
    /** WebRTC连接失败 */
    NetworkEventType["WEBRTC_CONNECTION_FAILED"] = "webrtc_connection_failed";
    /** WebRTC断开连接 */
    NetworkEventType["WEBRTC_DISCONNECTED"] = "webrtc_disconnected";
    /** WebRTC连接错误 */
    NetworkEventType["WEBRTC_ERROR"] = "webrtc_error";
    /** WebRTC媒体流添加 */
    NetworkEventType["WEBRTC_STREAM_ADDED"] = "webrtc_stream_added";
    /** WebRTC媒体流移除 */
    NetworkEventType["WEBRTC_STREAM_REMOVED"] = "webrtc_stream_removed";
    // 系统事件
    /** 系统错误 */
    NetworkEventType["SYSTEM_ERROR"] = "system_error";
    /** 系统警告 */
    NetworkEventType["SYSTEM_WARNING"] = "system_warning";
    /** 系统信息 */
    NetworkEventType["SYSTEM_INFO"] = "system_info";
    // 自定义事件
    /** 自定义事件 */
    NetworkEventType["CUSTOM"] = "custom";
})(NetworkEventType || (exports.NetworkEventType = NetworkEventType = {}));
