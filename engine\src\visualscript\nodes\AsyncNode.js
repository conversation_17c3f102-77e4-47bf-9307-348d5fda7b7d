"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AsyncNode = exports.AsyncNodeState = void 0;
/**
 * 视觉脚本异步节点
 * 异步节点用于执行异步操作，如定时器、网络请求等
 */
var FlowNode_1 = require("./FlowNode");
var Node_1 = require("./Node");
/**
 * 异步节点状态
 */
var AsyncNodeState;
(function (AsyncNodeState) {
    /** 空闲 */
    AsyncNodeState["IDLE"] = "idle";
    /** 运行中 */
    AsyncNodeState["RUNNING"] = "running";
    /** 已完成 */
    AsyncNodeState["COMPLETED"] = "completed";
    /** 已取消 */
    AsyncNodeState["CANCELED"] = "canceled";
    /** 出错 */
    AsyncNodeState["ERROR"] = "error";
})(AsyncNodeState || (exports.AsyncNodeState = AsyncNodeState = {}));
/**
 * 异步节点基类
 */
var AsyncNode = /** @class */ (function (_super) {
    __extends(AsyncNode, _super);
    /**
     * 创建异步节点
     * @param options 节点选项
     */
    function AsyncNode(options) {
        var _this = _super.call(this, options) || this;
        /** 节点类型 */
        _this.nodeType = Node_1.NodeType.ASYNC;
        /** 节点类别 */
        _this.category = Node_1.NodeCategory.FLOW;
        /** 当前状态 */
        _this.state = AsyncNodeState.IDLE;
        /** 开始时间 */
        _this.startTime = 0;
        /** 完成回调 */
        _this.completeCallback = null;
        /** 错误回调 */
        _this.errorCallback = null;
        /** 取消回调 */
        _this.cancelCallback = null;
        /** 超时定时器ID */
        _this.timeoutId = null;
        _this.timeout = options.timeout || 0;
        // 添加完成和错误输出流程
        if (!_this.outputFlowNames.includes('complete')) {
            _this.outputFlowNames.push('complete');
        }
        if (!_this.outputFlowNames.includes('error')) {
            _this.outputFlowNames.push('error');
        }
        return _this;
    }
    /**
     * 初始化插槽
     */
    AsyncNode.prototype.initializeSockets = function () {
        _super.prototype.initializeSockets.call(this);
        // 添加状态输出
        this.addOutput({
            name: 'state',
            type: Node_1.SocketType.DATA,
            direction: SocketDirection.OUTPUT,
            dataType: 'string',
            description: '当前状态'
        });
        // 添加错误输出
        this.addOutput({
            name: 'error',
            type: Node_1.SocketType.DATA,
            direction: SocketDirection.OUTPUT,
            dataType: 'object',
            description: '错误信息'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    AsyncNode.prototype.execute = function () {
        // 如果已经在运行，不重复执行
        if (this.state === AsyncNodeState.RUNNING) {
            return null;
        }
        // 获取所有输入值
        var inputs = {};
        for (var _i = 0, _a = this.inputs.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], name_1 = _b[0], socket = _b[1];
            if (socket.type === Node_1.SocketType.DATA) {
                inputs[name_1] = this.getInputValue(name_1);
            }
        }
        // 开始异步操作
        this.start(inputs);
        // 不触发任何输出流程，等待异步操作完成
        return null;
    };
    /**
     * 开始异步操作
     * @param inputs 输入值
     */
    AsyncNode.prototype.start = function (inputs) {
        var _this = this;
        // 设置状态为运行中
        this.state = AsyncNodeState.RUNNING;
        this.setOutputValue('state', this.state);
        // 记录开始时间
        this.startTime = Date.now();
        // 设置超时定时器
        if (this.timeout > 0) {
            this.timeoutId = setTimeout(function () {
                _this.handleError(new Error('操作超时'));
            }, this.timeout);
        }
        // 执行异步操作
        try {
            this.executeAsync(inputs)
                .then(function (result) { return _this.handleComplete(result); })
                .catch(function (error) { return _this.handleError(error); });
        }
        catch (error) {
            this.handleError(error);
        }
    };
    /**
     * 执行异步操作
     * @param inputs 输入值
     * @returns Promise对象
     */
    AsyncNode.prototype.executeAsync = function (inputs) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // 子类实现
                return [2 /*return*/, null];
            });
        });
    };
    /**
     * 处理完成
     * @param result 结果
     */
    AsyncNode.prototype.handleComplete = function (result) {
        // 清除超时定时器
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        // 设置状态为已完成
        this.state = AsyncNodeState.COMPLETED;
        this.setOutputValue('state', this.state);
        // 设置输出值
        if (result !== null && result !== undefined) {
            // 如果结果是对象，分别设置各个输出
            if (typeof result === 'object' && !Array.isArray(result)) {
                for (var _i = 0, _a = Object.entries(result); _i < _a.length; _i++) {
                    var _b = _a[_i], key = _b[0], value = _b[1];
                    if (this.outputs.has(key)) {
                        this.setOutputValue(key, value);
                    }
                }
            }
            // 如果只有一个数据输出，直接设置
            else {
                var dataOutputs = Array.from(this.outputs.entries())
                    .filter(function (_a) {
                    var name = _a[0], socket = _a[1];
                    return socket.type === Node_1.SocketType.DATA && name !== 'state' && name !== 'error';
                });
                if (dataOutputs.length === 1) {
                    this.setOutputValue(dataOutputs[0][0], result);
                }
            }
        }
        // 触发完成流程
        this.triggerFlow('complete');
        // 调用完成回调
        if (this.completeCallback) {
            this.completeCallback();
            this.completeCallback = null;
        }
    };
    /**
     * 处理错误
     * @param error 错误
     */
    AsyncNode.prototype.handleError = function (error) {
        // 清除超时定时器
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        // 设置状态为出错
        this.state = AsyncNodeState.ERROR;
        this.setOutputValue('state', this.state);
        // 设置错误输出
        this.setOutputValue('error', error);
        // 触发错误流程
        this.triggerFlow('error');
        // 调用错误回调
        if (this.errorCallback) {
            this.errorCallback(error);
            this.errorCallback = null;
        }
    };
    /**
     * 取消操作
     */
    AsyncNode.prototype.cancel = function () {
        // 如果不在运行状态，不需要取消
        if (this.state !== AsyncNodeState.RUNNING) {
            return;
        }
        // 清除超时定时器
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        // 设置状态为已取消
        this.state = AsyncNodeState.CANCELED;
        this.setOutputValue('state', this.state);
        // 调用取消回调
        if (this.cancelCallback) {
            this.cancelCallback();
            this.cancelCallback = null;
        }
    };
    /**
     * 重置状态
     */
    AsyncNode.prototype.reset = function () {
        // 如果在运行状态，先取消
        if (this.state === AsyncNodeState.RUNNING) {
            this.cancel();
        }
        // 重置状态
        this.state = AsyncNodeState.IDLE;
        this.setOutputValue('state', this.state);
        this.setOutputValue('error', null);
        // 清除回调
        this.completeCallback = null;
        this.errorCallback = null;
        this.cancelCallback = null;
    };
    /**
     * 设置完成回调
     * @param callback 回调函数
     */
    AsyncNode.prototype.onComplete = function (callback) {
        this.completeCallback = callback;
    };
    /**
     * 设置错误回调
     * @param callback 回调函数
     */
    AsyncNode.prototype.onError = function (callback) {
        this.errorCallback = callback;
    };
    /**
     * 设置取消回调
     * @param callback 回调函数
     */
    AsyncNode.prototype.onCancel = function (callback) {
        this.cancelCallback = callback;
    };
    /**
     * 获取当前状态
     * @returns 当前状态
     */
    AsyncNode.prototype.getState = function () {
        return this.state;
    };
    /**
     * 获取运行时间（毫秒）
     * @returns 运行时间
     */
    AsyncNode.prototype.getRunningTime = function () {
        if (this.state !== AsyncNodeState.RUNNING || this.startTime === 0) {
            return 0;
        }
        return Date.now() - this.startTime;
    };
    return AsyncNode;
}(FlowNode_1.FlowNode));
exports.AsyncNode = AsyncNode;
