"use strict";
/**
 * WebRTC数据通道
 * 用于WebRTC点对点数据传输
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebRTCDataChannel = void 0;
var WebRTCDataChannel = /** @class */ (function () {
    function WebRTCDataChannel(channel, options) {
        this.channel = channel;
        this.options = options;
        this.setupEventHandlers();
    }
    WebRTCDataChannel.prototype.setupEventHandlers = function () {
        var _this = this;
        this.channel.onopen = function () {
            console.log("\u6570\u636E\u901A\u9053 ".concat(_this.options.label, " \u5DF2\u6253\u5F00"));
        };
        this.channel.onclose = function () {
            console.log("\u6570\u636E\u901A\u9053 ".concat(_this.options.label, " \u5DF2\u5173\u95ED"));
        };
        this.channel.onerror = function (error) {
            console.error("\u6570\u636E\u901A\u9053 ".concat(_this.options.label, " \u9519\u8BEF:"), error);
        };
        this.channel.onmessage = function (event) {
            _this.handleMessage(event.data);
        };
    };
    WebRTCDataChannel.prototype.handleMessage = function (data) {
        // 处理接收到的消息
        console.log("\u6536\u5230\u6D88\u606F:", data);
    };
    WebRTCDataChannel.prototype.send = function (data) {
        if (this.channel.readyState === 'open') {
            this.channel.send(data);
        }
        else {
            console.warn('数据通道未打开，无法发送消息');
        }
    };
    WebRTCDataChannel.prototype.close = function () {
        this.channel.close();
    };
    Object.defineProperty(WebRTCDataChannel.prototype, "readyState", {
        get: function () {
            return this.channel.readyState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(WebRTCDataChannel.prototype, "label", {
        get: function () {
            return this.channel.label;
        },
        enumerable: false,
        configurable: true
    });
    return WebRTCDataChannel;
}());
exports.WebRTCDataChannel = WebRTCDataChannel;
