"use strict";
/**
 * UI2DComponent.ts
 *
 * 2D UI元素组件，用于创建和管理2D界面元素
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI2DComponent = void 0;
var three_1 = require("three");
var UIComponent_1 = require("./UIComponent");
/**
 * 2D UI元素组件
 * 用于创建和管理2D界面元素
 */
var UI2DComponent = /** @class */ (function (_super) {
    __extends(UI2DComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param props 2D UI元素属性
     */
    function UI2DComponent(entity, props) {
        if (props === void 0) { props = {}; }
        var _this = _super.call(this, entity, __assign(__assign({}, props), { is3D: false })) || this;
        // CSS类和样式
        _this.cssClass = '';
        _this.cssStyle = {};
        // 内容
        _this.innerHTML = '';
        _this.textContent = '';
        // 设置HTML元素
        _this.htmlElement = props.htmlElement;
        // 设置CSS类和样式
        _this.cssClass = props.cssClass || '';
        _this.cssStyle = props.cssStyle || {};
        // 设置内容
        _this.innerHTML = props.innerHTML || '';
        _this.textContent = props.textContent || '';
        // 设置文本属性
        _this.fontSize = props.fontSize;
        _this.fontFamily = props.fontFamily;
        _this.fontWeight = props.fontWeight;
        _this.textAlign = props.textAlign;
        _this.textColor = props.textColor;
        _this.lineHeight = props.lineHeight;
        // 设置事件处理器
        if (props.onFocus)
            _this.addEventListener('focus', props.onFocus);
        if (props.onBlur)
            _this.addEventListener('blur', props.onBlur);
        if (props.onKeyDown)
            _this.addEventListener('keydown', props.onKeyDown);
        if (props.onKeyUp)
            _this.addEventListener('keyup', props.onKeyUp);
        if (props.onChange)
            _this.addEventListener('change', props.onChange);
        // 创建HTML元素（如果没有提供）
        if (!_this.htmlElement) {
            _this.createHTMLElement();
        }
        // 应用样式
        _this.applyStyles();
        return _this;
    }
    /**
     * 创建HTML元素
     */
    UI2DComponent.prototype.createHTMLElement = function () {
        // 根据组件类型创建相应的HTML元素
        switch (this.uiType) {
            case UIComponent_1.UIComponentType.BUTTON:
                this.htmlElement = document.createElement('button');
                break;
            case UIComponent_1.UIComponentType.TEXT:
                this.htmlElement = document.createElement('span');
                break;
            case UIComponent_1.UIComponentType.IMAGE:
                this.htmlElement = document.createElement('img');
                break;
            case UIComponent_1.UIComponentType.INPUT:
                this.htmlElement = document.createElement('input');
                break;
            case UIComponent_1.UIComponentType.CHECKBOX:
                this.htmlElement = document.createElement('input');
                this.htmlElement.type = 'checkbox';
                break;
            case UIComponent_1.UIComponentType.SLIDER:
                this.htmlElement = document.createElement('input');
                this.htmlElement.type = 'range';
                break;
            case UIComponent_1.UIComponentType.DROPDOWN:
                this.htmlElement = document.createElement('select');
                break;
            case UIComponent_1.UIComponentType.PANEL:
            case UIComponent_1.UIComponentType.CONTAINER:
            case UIComponent_1.UIComponentType.WINDOW:
            default:
                this.htmlElement = document.createElement('div');
                break;
        }
        // 设置ID
        this.htmlElement.id = this.id;
        // 设置内容
        if (this.innerHTML) {
            this.htmlElement.innerHTML = this.innerHTML;
        }
        else if (this.textContent) {
            this.htmlElement.textContent = this.textContent;
        }
        // 添加事件监听器
        this.addHTMLEventListeners();
    };
    /**
     * 应用样式
     */
    UI2DComponent.prototype.applyStyles = function () {
        if (!this.htmlElement)
            return;
        // 应用CSS类
        if (this.cssClass) {
            this.htmlElement.className = this.cssClass;
        }
        // 应用基本样式
        var style = this.htmlElement.style;
        // 位置和尺寸
        style.position = 'absolute';
        style.left = this.position instanceof three_1.Vector2 ? "".concat(this.getPosition().x, "px") : "".concat(this.position.x, "px");
        style.top = this.position instanceof three_1.Vector2 ? "".concat(this.getPosition().y, "px") : "".concat(this.position.y, "px");
        style.width = "".concat(this.size.x, "px");
        style.height = "".concat(this.size.y, "px");
        // 可见性和透明度
        style.visibility = this.visible ? 'visible' : 'hidden';
        style.opacity = this.opacity.toString();
        style.zIndex = this.zIndex.toString();
        // 背景和边框
        if (this.backgroundColor)
            style.backgroundColor = this.backgroundColor;
        if (this.borderColor)
            style.borderColor = this.borderColor;
        if (this.borderWidth > 0)
            style.borderWidth = "".concat(this.borderWidth, "px");
        if (this.borderRadius > 0)
            style.borderRadius = "".concat(this.borderRadius, "px");
        // 内边距和外边距
        style.paddingTop = "".concat(this.padding.top, "px");
        style.paddingRight = "".concat(this.padding.right, "px");
        style.paddingBottom = "".concat(this.padding.bottom, "px");
        style.paddingLeft = "".concat(this.padding.left, "px");
        style.marginTop = "".concat(this.margin.top, "px");
        style.marginRight = "".concat(this.margin.right, "px");
        style.marginBottom = "".concat(this.margin.bottom, "px");
        style.marginLeft = "".concat(this.margin.left, "px");
        // 文本样式
        if (this.fontSize)
            style.fontSize = typeof this.fontSize === 'number' ? "".concat(this.fontSize, "px") : this.fontSize;
        if (this.fontFamily)
            style.fontFamily = this.fontFamily;
        if (this.fontWeight)
            style.fontWeight = this.fontWeight.toString();
        if (this.textAlign)
            style.textAlign = this.textAlign;
        if (this.textColor)
            style.color = this.textColor;
        if (this.lineHeight)
            style.lineHeight = typeof this.lineHeight === 'number' ? "".concat(this.lineHeight, "px") : this.lineHeight;
        // 交互性
        style.pointerEvents = this.interactive ? 'auto' : 'none';
        // 应用自定义CSS样式
        for (var key in this.cssStyle) {
            style[key] = this.cssStyle[key];
        }
    };
    /**
     * 添加HTML事件监听器
     */
    UI2DComponent.prototype.addHTMLEventListeners = function () {
        var _this = this;
        if (!this.htmlElement)
            return;
        // 点击事件
        this.htmlElement.addEventListener('click', function (event) {
            _this.triggerEvent('click', event);
        });
        // 悬停事件
        this.htmlElement.addEventListener('mouseenter', function (event) {
            _this.triggerEvent('hover', { type: 'enter', event: event });
        });
        this.htmlElement.addEventListener('mouseleave', function (event) {
            _this.triggerEvent('hover', { type: 'leave', event: event });
        });
        // 拖拽事件
        this.htmlElement.addEventListener('dragstart', function (event) {
            _this.triggerEvent('dragstart', event);
        });
        this.htmlElement.addEventListener('drag', function (event) {
            _this.triggerEvent('drag', event);
        });
        this.htmlElement.addEventListener('dragend', function (event) {
            _this.triggerEvent('dragend', event);
        });
        // 焦点事件
        this.htmlElement.addEventListener('focus', function (event) {
            _this.triggerEvent('focus', event);
        });
        this.htmlElement.addEventListener('blur', function (event) {
            _this.triggerEvent('blur', event);
        });
        // 键盘事件
        this.htmlElement.addEventListener('keydown', function (event) {
            _this.triggerEvent('keydown', event);
        });
        this.htmlElement.addEventListener('keyup', function (event) {
            _this.triggerEvent('keyup', event);
        });
        // 变更事件（针对输入元素）
        if (this.uiType === UIComponent_1.UIComponentType.INPUT || this.uiType === UIComponent_1.UIComponentType.CHECKBOX ||
            this.uiType === UIComponent_1.UIComponentType.SLIDER || this.uiType === UIComponent_1.UIComponentType.DROPDOWN) {
            this.htmlElement.addEventListener('change', function (event) {
                _this.triggerEvent('change', event);
            });
        }
    };
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     */
    UI2DComponent.prototype.update = function (deltaTime) {
        _super.prototype.update.call(this, deltaTime);
        // 更新HTML元素样式
        this.applyStyles();
    };
    /**
     * 渲染UI元素
     */
    UI2DComponent.prototype.render = function () {
        _super.prototype.render.call(this);
        // 2D UI元素不需要额外的渲染逻辑，因为它们由浏览器渲染
    };
    /**
     * 销毁UI元素
     */
    UI2DComponent.prototype.dispose = function () {
        _super.prototype.dispose.call(this);
        // 移除HTML元素
        if (this.htmlElement && this.htmlElement.parentNode) {
            this.htmlElement.parentNode.removeChild(this.htmlElement);
        }
        this.htmlElement = undefined;
    };
    /**
     * 设置HTML内容
     * @param html HTML内容
     */
    UI2DComponent.prototype.setHTML = function (html) {
        this.innerHTML = html;
        if (this.htmlElement) {
            this.htmlElement.innerHTML = html;
        }
    };
    /**
     * 设置文本内容
     * @param text 文本内容
     */
    UI2DComponent.prototype.setText = function (text) {
        this.textContent = text;
        if (this.htmlElement) {
            this.htmlElement.textContent = text;
        }
    };
    /**
     * 设置CSS类
     * @param cssClass CSS类名
     */
    UI2DComponent.prototype.setCSSClass = function (cssClass) {
        this.cssClass = cssClass;
        if (this.htmlElement) {
            this.htmlElement.className = cssClass;
        }
    };
    /**
     * 添加CSS类
     * @param cssClass CSS类名
     */
    UI2DComponent.prototype.addCSSClass = function (cssClass) {
        if (this.htmlElement) {
            this.htmlElement.classList.add(cssClass);
        }
    };
    /**
     * 移除CSS类
     * @param cssClass CSS类名
     */
    UI2DComponent.prototype.removeCSSClass = function (cssClass) {
        if (this.htmlElement) {
            this.htmlElement.classList.remove(cssClass);
        }
    };
    return UI2DComponent;
}(UIComponent_1.UIComponent));
exports.UI2DComponent = UI2DComponent;
