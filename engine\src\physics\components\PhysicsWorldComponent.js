"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsWorldComponent = void 0;
/**
 * 物理世界组件
 * 为场景提供物理世界
 */
var CANNON = require("cannon-es");
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * 物理世界组件
 */
var PhysicsWorldComponent = exports.PhysicsWorldComponent = /** @class */ (function (_super) {
    __extends(PhysicsWorldComponent, _super);
    /**
     * 创建物理世界组件
     * @param options 物理世界选项
     */
    function PhysicsWorldComponent(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsWorldComponent.type) || this;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        _this.gravity = options.gravity ? options.gravity.clone() : new THREE.Vector3(0, -9.82, 0);
        _this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
        _this.iterations = options.iterations || 10;
        _this.broadphase = options.broadphase || 'naive';
        _this.gridBroadphaseSize = options.gridBroadphaseSize || 5;
        _this.defaultContactMaterial = options.defaultContactMaterial || null;
        _this.defaultFriction = options.defaultFriction || 0.3;
        _this.defaultRestitution = options.defaultRestitution || 0.3;
        // 创建物理世界
        _this.world = new CANNON.World();
        // 设置物理世界属性
        _this.world.gravity.set(_this.gravity.x, _this.gravity.y, _this.gravity.z);
        _this.world.allowSleep = _this.allowSleep;
        // 设置求解器迭代次数（使用类型断言）
        _this.world.solver.iterations = _this.iterations;
        // 设置宽相检测算法
        _this.setBroadphase(_this.broadphase);
        // 设置默认接触材质
        if (_this.defaultContactMaterial) {
            _this.world.defaultContactMaterial = _this.defaultContactMaterial;
        }
        else {
            // 创建默认材质
            var defaultMaterial = new CANNON.Material('default');
            var defaultContactMaterial = new CANNON.ContactMaterial(defaultMaterial, defaultMaterial, {
                friction: _this.defaultFriction,
                restitution: _this.defaultRestitution
            });
            _this.world.defaultContactMaterial = defaultContactMaterial;
        }
        return _this;
    }
    /**
     * 初始化组件
     */
    PhysicsWorldComponent.prototype.initialize = function () {
        if (this.initialized || this.destroyed)
            return;
        this.initialized = true;
    };
    /**
     * 设置宽相检测算法
     * @param broadphase 宽相检测算法
     */
    PhysicsWorldComponent.prototype.setBroadphase = function (broadphase) {
        switch (broadphase) {
            case 'naive':
                this.world.broadphase = new CANNON.NaiveBroadphase();
                break;
            case 'sap':
                this.world.broadphase = new CANNON.SAPBroadphase(this.world);
                break;
            case 'grid':
                this.world.broadphase = new CANNON.GridBroadphase(new CANNON.Vec3(-this.gridBroadphaseSize, -this.gridBroadphaseSize, -this.gridBroadphaseSize), new CANNON.Vec3(this.gridBroadphaseSize, this.gridBroadphaseSize, this.gridBroadphaseSize), 10, 10, 10);
                break;
            default:
                this.world.broadphase = new CANNON.NaiveBroadphase();
                break;
        }
    };
    /**
     * 更新物理世界
     * @param deltaTime 帧间隔时间（秒）
     * @param maxSubSteps 最大子步长
     */
    PhysicsWorldComponent.prototype.update = function (deltaTime, maxSubSteps) {
        if (maxSubSteps === void 0) { maxSubSteps = 10; }
        if (!this.initialized || this.destroyed)
            return;
        // 更新物理世界
        this.world.step(deltaTime, deltaTime, maxSubSteps);
    };
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    PhysicsWorldComponent.prototype.getWorld = function () {
        return this.world;
    };
    /**
     * 设置重力
     * @param gravity 重力向量
     */
    PhysicsWorldComponent.prototype.setGravity = function (gravity) {
        this.gravity = gravity.clone();
        this.world.gravity.set(gravity.x, gravity.y, gravity.z);
    };
    /**
     * 获取重力
     * @returns 重力向量
     */
    PhysicsWorldComponent.prototype.getGravity = function () {
        return this.gravity.clone();
    };
    /**
     * 设置是否允许休眠
     * @param allowSleep 是否允许休眠
     */
    PhysicsWorldComponent.prototype.setAllowSleep = function (allowSleep) {
        this.allowSleep = allowSleep;
        this.world.allowSleep = allowSleep;
    };
    /**
     * 获取是否允许休眠
     * @returns 是否允许休眠
     */
    PhysicsWorldComponent.prototype.getAllowSleep = function () {
        return this.allowSleep;
    };
    /**
     * 设置迭代次数
     * @param iterations 迭代次数
     */
    PhysicsWorldComponent.prototype.setIterations = function (iterations) {
        this.iterations = iterations;
        // 设置求解器迭代次数（使用类型断言）
        this.world.solver.iterations = iterations;
    };
    /**
     * 获取迭代次数
     * @returns 迭代次数
     */
    PhysicsWorldComponent.prototype.getIterations = function () {
        return this.iterations;
    };
    /**
     * 设置宽相检测算法
     * @param broadphase 宽相检测算法
     * @param gridSize 网格宽相检测的单元格大小（仅适用于grid算法）
     */
    PhysicsWorldComponent.prototype.setBroadphaseAlgorithm = function (broadphase, gridSize) {
        this.broadphase = broadphase;
        if (gridSize !== undefined) {
            this.gridBroadphaseSize = gridSize;
        }
        this.setBroadphase(broadphase);
    };
    /**
     * 获取宽相检测算法
     * @returns 宽相检测算法
     */
    PhysicsWorldComponent.prototype.getBroadphaseAlgorithm = function () {
        return this.broadphase;
    };
    /**
     * 创建材质
     * @param name 材质名称
     * @returns 材质
     */
    PhysicsWorldComponent.prototype.createMaterial = function (name) {
        return new CANNON.Material(name);
    };
    /**
     * 创建接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @param options 接触材质选项
     * @returns 接触材质
     */
    PhysicsWorldComponent.prototype.createContactMaterial = function (materialA, materialB, options) {
        if (options === void 0) { options = {}; }
        var contactMaterial = new CANNON.ContactMaterial(materialA, materialB, options);
        this.world.addContactMaterial(contactMaterial);
        return contactMaterial;
    };
    /**
     * 射线检测
     * @param from 起点
     * @param to 终点
     * @param options 射线检测选项
     * @returns 射线检测结果
     */
    PhysicsWorldComponent.prototype.raycast = function (from, to, options) {
        if (options === void 0) { options = {}; }
        var rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
        var rayTo = new CANNON.Vec3(to.x, to.y, to.z);
        var result = new CANNON.RaycastResult();
        this.world.raycastClosest(rayFrom, rayTo, options, result);
        return result.hasHit ? result : null;
    };
    /**
     * 射线检测（多个结果）
     * @param from 起点
     * @param to 终点
     * @param options 射线检测选项
     * @returns 射线检测结果数组
     */
    PhysicsWorldComponent.prototype.raycastAll = function (from, to, options) {
        if (options === void 0) { options = {}; }
        var rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
        var rayTo = new CANNON.Vec3(to.x, to.y, to.z);
        var results = [];
        this.world.raycastAll(rayFrom, rayTo, options, function (result) {
            results.push(result);
        });
        return results;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    PhysicsWorldComponent.prototype.addEventListener = function (event, callback) {
        this.world.addEventListener(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    PhysicsWorldComponent.prototype.removeEventListener = function (event, callback) {
        this.world.removeEventListener(event, callback);
    };
    /**
     * 销毁组件
     */
    PhysicsWorldComponent.prototype.dispose = function () {
        if (this.destroyed)
            return;
        // 清空物理世界
        while (this.world.bodies.length > 0) {
            this.world.removeBody(this.world.bodies[0]);
        }
        while (this.world.constraints.length > 0) {
            this.world.removeConstraint(this.world.constraints[0]);
        }
        this.initialized = false;
        this.destroyed = true;
        // 调用基类的dispose方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsWorldComponent.type = 'PhysicsWorldComponent';
    return PhysicsWorldComponent;
}(Component_1.Component));
