"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkEventBuffer = exports.EventPriority = void 0;
/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
var NetworkEvent_1 = require("./NetworkEvent");
var Debug_1 = require("../utils/Debug");
/**
 * 事件优先级
 */
var EventPriority;
(function (EventPriority) {
    /** 最高优先级 */
    EventPriority[EventPriority["HIGHEST"] = 0] = "HIGHEST";
    /** 高优先级 */
    EventPriority[EventPriority["HIGH"] = 1] = "HIGH";
    /** 中等优先级 */
    EventPriority[EventPriority["MEDIUM"] = 2] = "MEDIUM";
    /** 低优先级 */
    EventPriority[EventPriority["LOW"] = 3] = "LOW";
    /** 最低优先级 */
    EventPriority[EventPriority["LOWEST"] = 4] = "LOWEST";
})(EventPriority || (exports.EventPriority = EventPriority = {}));
/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
var NetworkEventBuffer = /** @class */ (function () {
    /**
     * 创建网络事件缓冲器
     * @param config 配置
     */
    function NetworkEventBuffer(config) {
        if (config === void 0) { config = {}; }
        /** 事件缓冲区 */
        this.eventBuffer = [];
        /** 处理定时器ID */
        this.processTimerId = null;
        /** 事件处理回调 */
        this.eventHandler = null;
        /** 是否正在处理事件 */
        this.isProcessing = false;
        /** 事件类型优先级映射 */
        this.eventTypePriorities = new Map();
        // 默认配置
        this.config = __assign({ maxBufferSize: 1000, processInterval: 16, autoProcess: true, maxEventsPerProcess: 10, eventTypePriorities: {} }, config);
        // 初始化事件类型优先级映射
        this.initEventTypePriorities();
        // 如果启用自动处理，则启动处理定时器
        if (this.config.autoProcess) {
            this.startProcessing();
        }
    }
    /**
     * 初始化事件类型优先级映射
     */
    NetworkEventBuffer.prototype.initEventTypePriorities = function () {
        var _a;
        // 默认优先级
        var defaultPriorities = (_a = {},
            // 系统事件（最高优先级）
            _a[NetworkEvent_1.NetworkEventType.SYSTEM_ERROR] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.SYSTEM_WARNING] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.SYSTEM_INFO] = EventPriority.HIGH,
            // 连接事件（高优先级）
            _a[NetworkEvent_1.NetworkEventType.CONNECTED] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.CONNECTION_FAILED] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.DISCONNECTED] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.RECONNECTED] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.RECONNECTION_FAILED] = EventPriority.HIGHEST,
            _a[NetworkEvent_1.NetworkEventType.CONNECTION_ERROR] = EventPriority.HIGHEST,
            // 房间事件（高优先级）
            _a[NetworkEvent_1.NetworkEventType.ROOM_JOINED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.ROOM_LEFT] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.ROOM_CREATED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.ROOM_CLOSED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.ROOM_UPDATED] = EventPriority.MEDIUM,
            // 用户事件（中等优先级）
            _a[NetworkEvent_1.NetworkEventType.USER_JOINED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.USER_LEFT] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.USER_UPDATED] = EventPriority.MEDIUM,
            // 实体事件（中等优先级）
            _a[NetworkEvent_1.NetworkEventType.ENTITY_CREATED] = EventPriority.MEDIUM,
            _a[NetworkEvent_1.NetworkEventType.ENTITY_UPDATED] = EventPriority.MEDIUM,
            _a[NetworkEvent_1.NetworkEventType.ENTITY_DELETED] = EventPriority.MEDIUM,
            _a[NetworkEvent_1.NetworkEventType.ENTITY_OWNERSHIP_CHANGED] = EventPriority.MEDIUM,
            // 消息事件（低优先级）
            _a[NetworkEvent_1.NetworkEventType.MESSAGE_RECEIVED] = EventPriority.LOW,
            _a[NetworkEvent_1.NetworkEventType.MESSAGE_SENT] = EventPriority.LOW,
            _a[NetworkEvent_1.NetworkEventType.MESSAGE_ACKNOWLEDGED] = EventPriority.LOW,
            _a[NetworkEvent_1.NetworkEventType.MESSAGE_ERROR] = EventPriority.MEDIUM,
            // WebRTC事件（中等优先级）
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_CONNECTED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_CONNECTION_FAILED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_DISCONNECTED] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_ERROR] = EventPriority.HIGH,
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_STREAM_ADDED] = EventPriority.MEDIUM,
            _a[NetworkEvent_1.NetworkEventType.WEBRTC_STREAM_REMOVED] = EventPriority.MEDIUM,
            // 自定义事件（最低优先级）
            _a[NetworkEvent_1.NetworkEventType.CUSTOM] = EventPriority.LOWEST,
            _a);
        // 合并默认优先级和用户配置的优先级
        var mergedPriorities = __assign(__assign({}, defaultPriorities), this.config.eventTypePriorities);
        // 填充优先级映射
        for (var _i = 0, _b = Object.entries(mergedPriorities); _i < _b.length; _i++) {
            var _c = _b[_i], type = _c[0], priority = _c[1];
            this.eventTypePriorities.set(type, priority);
        }
    };
    /**
     * 启动事件处理
     */
    NetworkEventBuffer.prototype.startProcessing = function () {
        var _this = this;
        if (this.processTimerId !== null) {
            return;
        }
        this.processTimerId = window.setInterval(function () {
            _this.processEvents();
        }, this.config.processInterval);
    };
    /**
     * 停止事件处理
     */
    NetworkEventBuffer.prototype.stopProcessing = function () {
        if (this.processTimerId !== null) {
            clearInterval(this.processTimerId);
            this.processTimerId = null;
        }
    };
    /**
     * 设置事件处理回调
     * @param handler 事件处理回调
     */
    NetworkEventBuffer.prototype.setEventHandler = function (handler) {
        this.eventHandler = handler;
    };
    /**
     * 添加事件到缓冲区
     * @param event 网络事件
     * @returns 是否添加成功
     */
    NetworkEventBuffer.prototype.addEvent = function (event) {
        // 检查缓冲区是否已满
        if (this.eventBuffer.length >= this.config.maxBufferSize) {
            Debug_1.Debug.warn('NetworkEventBuffer', 'Event buffer is full, dropping event:', event);
            return false;
        }
        // 设置事件优先级（如果未设置）
        if (event.priority === undefined) {
            event.priority = this.getEventPriority(event.type);
        }
        // 添加到缓冲区
        this.eventBuffer.push(event);
        // 按优先级排序
        this.sortEventBuffer();
        return true;
    };
    /**
     * 处理事件
     */
    NetworkEventBuffer.prototype.processEvents = function () {
        // 如果正在处理事件或者没有事件处理回调，则跳过
        if (this.isProcessing || !this.eventHandler) {
            return;
        }
        this.isProcessing = true;
        try {
            // 处理指定数量的事件
            var count = Math.min(this.config.maxEventsPerProcess, this.eventBuffer.length);
            for (var i = 0; i < count; i++) {
                var event_1 = this.eventBuffer.shift();
                if (event_1) {
                    try {
                        // 调用事件处理回调
                        this.eventHandler(event_1);
                        // 标记事件为已处理
                        event_1.handled = true;
                    }
                    catch (error) {
                        Debug_1.Debug.error('NetworkEventBuffer', 'Error processing event:', error);
                        // 标记事件为未处理
                        event_1.handled = false;
                        // 如果是高优先级事件，则重新添加到缓冲区
                        if (event_1.priority !== undefined && event_1.priority <= EventPriority.HIGH) {
                            this.eventBuffer.unshift(event_1);
                        }
                    }
                }
            }
        }
        finally {
            this.isProcessing = false;
        }
    };
    /**
     * 获取事件优先级
     * @param eventType 事件类型
     * @returns 事件优先级
     */
    NetworkEventBuffer.prototype.getEventPriority = function (eventType) {
        return this.eventTypePriorities.get(eventType) || EventPriority.MEDIUM;
    };
    /**
     * 按优先级排序事件缓冲区
     */
    NetworkEventBuffer.prototype.sortEventBuffer = function () {
        var _this = this;
        this.eventBuffer.sort(function (a, b) {
            // 首先按优先级排序（数字越小优先级越高）
            var priorityA = a.priority !== undefined ? a.priority : _this.getEventPriority(a.type);
            var priorityB = b.priority !== undefined ? b.priority : _this.getEventPriority(b.type);
            if (priorityA !== priorityB) {
                return priorityA - priorityB;
            }
            // 其次按时间戳排序（较早的事件先处理）
            return a.timestamp - b.timestamp;
        });
    };
    /**
     * 清空事件缓冲区
     */
    NetworkEventBuffer.prototype.clearBuffer = function () {
        this.eventBuffer = [];
    };
    /**
     * 获取缓冲区中的事件数量
     * @returns 事件数量
     */
    NetworkEventBuffer.prototype.getBufferSize = function () {
        return this.eventBuffer.length;
    };
    /**
     * 设置事件类型的优先级
     * @param eventType 事件类型
     * @param priority 优先级
     */
    NetworkEventBuffer.prototype.setEventTypePriority = function (eventType, priority) {
        this.eventTypePriorities.set(eventType, priority);
        // 重新排序缓冲区
        this.sortEventBuffer();
    };
    /**
     * 获取事件类型的优先级
     * @param eventType 事件类型
     * @returns 优先级
     */
    NetworkEventBuffer.prototype.getEventTypePriority = function (eventType) {
        return this.getEventPriority(eventType);
    };
    /**
     * 销毁缓冲器
     */
    NetworkEventBuffer.prototype.dispose = function () {
        this.stopProcessing();
        this.clearBuffer();
        this.eventHandler = null;
    };
    return NetworkEventBuffer;
}());
exports.NetworkEventBuffer = NetworkEventBuffer;
