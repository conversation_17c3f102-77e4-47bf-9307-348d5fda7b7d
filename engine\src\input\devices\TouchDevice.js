"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TouchDevice = void 0;
/**
 * 触摸输入设备
 */
var InputDevice_1 = require("../InputDevice");
/**
 * 触摸输入设备
 */
var TouchDevice = /** @class */ (function (_super) {
    __extends(TouchDevice, _super);
    /**
     * 创建触摸输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    function TouchDevice(element, preventDefault, stopPropagation) {
        if (element === void 0) { element = document.body; }
        if (preventDefault === void 0) { preventDefault = true; }
        if (stopPropagation === void 0) { stopPropagation = false; }
        var _this = _super.call(this, 'touch') || this;
        /** 触摸点映射 */
        _this.touchPoints = new Map();
        /** 触摸事件处理器 */
        _this.touchEventHandlers = {};
        _this.element = element;
        _this.preventDefault = preventDefault;
        _this.stopPropagation = stopPropagation;
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    TouchDevice.prototype.initEventHandlers = function () {
        // 触摸开始事件
        this.touchEventHandlers.touchstart = this.handleTouchStart.bind(this);
        // 触摸移动事件
        this.touchEventHandlers.touchmove = this.handleTouchMove.bind(this);
        // 触摸结束事件
        this.touchEventHandlers.touchend = this.handleTouchEnd.bind(this);
        // 触摸取消事件
        this.touchEventHandlers.touchcancel = this.handleTouchCancel.bind(this);
    };
    /**
     * 初始化设备
     */
    TouchDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    TouchDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        // 清空触摸点映射
        this.touchPoints.clear();
        _super.prototype.destroy.call(this);
    };
    /**
     * 添加事件监听器
     */
    TouchDevice.prototype.addEventListeners = function () {
        // 添加触摸事件监听器
        for (var _i = 0, _a = Object.entries(this.touchEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            this.element.addEventListener(event_1, handler, { passive: !this.preventDefault });
        }
    };
    /**
     * 移除事件监听器
     */
    TouchDevice.prototype.removeEventListeners = function () {
        // 移除触摸事件监听器
        for (var _i = 0, _a = Object.entries(this.touchEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_2 = _b[0], handler = _b[1];
            this.element.removeEventListener(event_2, handler);
        }
    };
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    TouchDevice.prototype.handleTouchStart = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.set(touch.identifier, touch);
            this.setValue("touch:".concat(touch.identifier, ":active"), true);
            this.setValue("touch:".concat(touch.identifier, ":x"), touch.clientX);
            this.setValue("touch:".concat(touch.identifier, ":y"), touch.clientY);
        }
        // 触发触摸开始事件
        this.eventEmitter.emit('touchstart', {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    TouchDevice.prototype.handleTouchMove = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            var oldTouch = this.touchPoints.get(touch.identifier);
            this.touchPoints.set(touch.identifier, touch);
            this.setValue("touch:".concat(touch.identifier, ":x"), touch.clientX);
            this.setValue("touch:".concat(touch.identifier, ":y"), touch.clientY);
            if (oldTouch) {
                this.setValue("touch:".concat(touch.identifier, ":deltaX"), touch.clientX - oldTouch.clientX);
                this.setValue("touch:".concat(touch.identifier, ":deltaY"), touch.clientY - oldTouch.clientY);
            }
        }
        // 触发触摸移动事件
        this.eventEmitter.emit('touchmove', {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    TouchDevice.prototype.handleTouchEnd = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.delete(touch.identifier);
            this.setValue("touch:".concat(touch.identifier, ":active"), false);
        }
        // 触发触摸结束事件
        this.eventEmitter.emit('touchend', {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    TouchDevice.prototype.handleTouchCancel = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.delete(touch.identifier);
            this.setValue("touch:".concat(touch.identifier, ":active"), false);
        }
        // 触发触摸取消事件
        this.eventEmitter.emit('touchcancel', {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 获取触摸点
     * @param identifier 触摸点标识符
     * @returns 触摸点
     */
    TouchDevice.prototype.getTouchPoint = function (identifier) {
        return this.touchPoints.get(identifier);
    };
    /**
     * 获取所有触摸点
     * @returns 触摸点列表
     */
    TouchDevice.prototype.getTouchPoints = function () {
        return Array.from(this.touchPoints.values());
    };
    /**
     * 获取触摸点数量
     * @returns 触摸点数量
     */
    TouchDevice.prototype.getTouchCount = function () {
        return this.touchPoints.size;
    };
    /**
     * 检查触摸点是否活跃
     * @param identifier 触摸点标识符
     * @returns 是否活跃
     */
    TouchDevice.prototype.isTouchActive = function (identifier) {
        return !!this.getValue("touch:".concat(identifier, ":active"));
    };
    /**
     * 获取触摸点位置
     * @param identifier 触摸点标识符
     * @returns 触摸点位置
     */
    TouchDevice.prototype.getTouchPosition = function (identifier) {
        var x = this.getValue("touch:".concat(identifier, ":x"));
        var y = this.getValue("touch:".concat(identifier, ":y"));
        if (x !== undefined && y !== undefined) {
            return { x: x, y: y };
        }
        return undefined;
    };
    /**
     * 获取触摸点移动
     * @param identifier 触摸点标识符
     * @returns 触摸点移动
     */
    TouchDevice.prototype.getTouchDelta = function (identifier) {
        var deltaX = this.getValue("touch:".concat(identifier, ":deltaX"));
        var deltaY = this.getValue("touch:".concat(identifier, ":deltaY"));
        if (deltaX !== undefined && deltaY !== undefined) {
            return { x: deltaX, y: deltaY };
        }
        return undefined;
    };
    return TouchDevice;
}(InputDevice_1.BaseInputDevice));
exports.TouchDevice = TouchDevice;
