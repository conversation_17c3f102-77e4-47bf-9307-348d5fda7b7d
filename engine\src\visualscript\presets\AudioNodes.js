"use strict";
/**
 * 音频相关的可视化脚本节点
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAudioNodes = exports.Audio3DNode = exports.AudioAnalyzerNode = exports.SetVolumeNode = exports.StopAudioNode = exports.PlayAudioNode = void 0;
var VisualScriptNode_1 = require("../VisualScriptNode");
var NodeRegistry_1 = require("../NodeRegistry");
/**
 * 播放音频节点
 */
var PlayAudioNode = /** @class */ (function (_super) {
    __extends(PlayAudioNode, _super);
    function PlayAudioNode() {
        var _this = _super.call(this, 'PlayAudio', '播放音频') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('audioClip', 'string', '音频片段');
        _this.addInput('volume', 'number', '音量');
        _this.addInput('loop', 'boolean', '循环');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    PlayAudioNode.prototype.execute = function (inputs) {
        if (inputs.trigger && inputs.audioClip) {
            // 这里应该连接到实际的音频系统
            console.log("\u64AD\u653E\u97F3\u9891: ".concat(inputs.audioClip, ", \u97F3\u91CF: ").concat(inputs.volume || 1, ", \u5FAA\u73AF: ").concat(inputs.loop || false));
            return { completed: true };
        }
        return {};
    };
    return PlayAudioNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.PlayAudioNode = PlayAudioNode;
/**
 * 停止音频节点
 */
var StopAudioNode = /** @class */ (function (_super) {
    __extends(StopAudioNode, _super);
    function StopAudioNode() {
        var _this = _super.call(this, 'StopAudio', '停止音频') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('audioClip', 'string', '音频片段');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    StopAudioNode.prototype.execute = function (inputs) {
        if (inputs.trigger) {
            // 这里应该连接到实际的音频系统
            console.log("\u505C\u6B62\u97F3\u9891: ".concat(inputs.audioClip || '全部'));
            return { completed: true };
        }
        return {};
    };
    return StopAudioNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.StopAudioNode = StopAudioNode;
/**
 * 设置音量节点
 */
var SetVolumeNode = /** @class */ (function (_super) {
    __extends(SetVolumeNode, _super);
    function SetVolumeNode() {
        var _this = _super.call(this, 'SetVolume', '设置音量') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('volume', 'number', '音量');
        _this.addInput('fadeTime', 'number', '淡入淡出时间');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    SetVolumeNode.prototype.execute = function (inputs) {
        if (inputs.trigger && typeof inputs.volume === 'number') {
            var volume = Math.max(0, Math.min(1, inputs.volume));
            var fadeTime = inputs.fadeTime || 0;
            // 这里应该连接到实际的音频系统
            console.log("\u8BBE\u7F6E\u97F3\u91CF: ".concat(volume, ", \u6DE1\u5165\u6DE1\u51FA\u65F6\u95F4: ").concat(fadeTime));
            return { completed: true };
        }
        return {};
    };
    return SetVolumeNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.SetVolumeNode = SetVolumeNode;
/**
 * 音频分析节点
 */
var AudioAnalyzerNode = /** @class */ (function (_super) {
    __extends(AudioAnalyzerNode, _super);
    function AudioAnalyzerNode() {
        var _this = _super.call(this, 'AudioAnalyzer', '音频分析') || this;
        _this.addInput('audioSource', 'string', '音频源');
        _this.addOutput('volume', 'number', '音量');
        _this.addOutput('frequency', 'array', '频率数据');
        _this.addOutput('waveform', 'array', '波形数据');
        return _this;
    }
    AudioAnalyzerNode.prototype.execute = function (inputs) {
        // 这里应该连接到实际的音频分析系统
        // 暂时返回模拟数据
        return {
            volume: 0.5,
            frequency: new Array(256).fill(0),
            waveform: new Array(1024).fill(0)
        };
    };
    return AudioAnalyzerNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.AudioAnalyzerNode = AudioAnalyzerNode;
/**
 * 3D音频节点
 */
var Audio3DNode = /** @class */ (function (_super) {
    __extends(Audio3DNode, _super);
    function Audio3DNode() {
        var _this = _super.call(this, 'Audio3D', '3D音频') || this;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('audioClip', 'string', '音频片段');
        _this.addInput('position', 'vector3', '位置');
        _this.addInput('maxDistance', 'number', '最大距离');
        _this.addInput('rolloffFactor', 'number', '衰减因子');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    Audio3DNode.prototype.execute = function (inputs) {
        if (inputs.trigger && inputs.audioClip && inputs.position) {
            // 这里应该连接到实际的3D音频系统
            console.log("\u64AD\u653E3D\u97F3\u9891: ".concat(inputs.audioClip, " \u5728\u4F4D\u7F6E (").concat(inputs.getPosition().x, ", ").concat(inputs.getPosition().y, ", ").concat(inputs.getPosition().z, ")"));
            return { completed: true };
        }
        return {};
    };
    return Audio3DNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.Audio3DNode = Audio3DNode;
/**
 * 注册音频节点
 */
function registerAudioNodes() {
    NodeRegistry_1.NodeRegistry.register('PlayAudio', PlayAudioNode);
    NodeRegistry_1.NodeRegistry.register('StopAudio', StopAudioNode);
    NodeRegistry_1.NodeRegistry.register('SetVolume', SetVolumeNode);
    NodeRegistry_1.NodeRegistry.register('AudioAnalyzer', AudioAnalyzerNode);
    NodeRegistry_1.NodeRegistry.register('Audio3D', Audio3DNode);
}
exports.registerAudioNodes = registerAudioNodes;
