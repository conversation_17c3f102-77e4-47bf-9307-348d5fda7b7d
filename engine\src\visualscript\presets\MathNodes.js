"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerMathNodes = exports.TrigonometricNode = exports.SquareRootNode = exports.PowerNode = exports.ModuloNode = exports.DivideNode = exports.MultiplyNode = exports.SubtractNode = exports.AddNode = void 0;
/**
 * 视觉脚本数学节点
 * 提供数学运算相关的节点
 */
var FunctionNode_1 = require("../nodes/FunctionNode");
var Node_1 = require("../nodes/Node");
/**
 * 三角函数节点类型
 */
var TrigonometricType;
(function (TrigonometricType) {
    TrigonometricType["SIN"] = "sin";
    TrigonometricType["COS"] = "cos";
    TrigonometricType["TAN"] = "tan";
    TrigonometricType["ASIN"] = "asin";
    TrigonometricType["ACOS"] = "acos";
    TrigonometricType["ATAN"] = "atan";
})(TrigonometricType || (TrigonometricType = {}));
/**
 * 加法节点
 * 计算两个数的和
 */
var AddNode = /** @class */ (function (_super) {
    __extends(AddNode, _super);
    function AddNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    AddNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个数输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第一个数',
            defaultValue: 0
        });
        // 添加第二个数输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第二个数',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    AddNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 计算结果
        var result = a + b;
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return AddNode;
}(FunctionNode_1.FunctionNode));
exports.AddNode = AddNode;
/**
 * 减法节点
 * 计算两个数的差
 */
var SubtractNode = /** @class */ (function (_super) {
    __extends(SubtractNode, _super);
    function SubtractNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    SubtractNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个数输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第一个数',
            defaultValue: 0
        });
        // 添加第二个数输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第二个数',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    SubtractNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 计算结果
        var result = a - b;
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return SubtractNode;
}(FunctionNode_1.FunctionNode));
exports.SubtractNode = SubtractNode;
/**
 * 乘法节点
 * 计算两个数的积
 */
var MultiplyNode = /** @class */ (function (_super) {
    __extends(MultiplyNode, _super);
    function MultiplyNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    MultiplyNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个数输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第一个数',
            defaultValue: 0
        });
        // 添加第二个数输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '第二个数',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    MultiplyNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 计算结果
        var result = a * b;
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return MultiplyNode;
}(FunctionNode_1.FunctionNode));
exports.MultiplyNode = MultiplyNode;
/**
 * 除法节点
 * 计算两个数的商
 */
var DivideNode = /** @class */ (function (_super) {
    __extends(DivideNode, _super);
    function DivideNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    DivideNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个数输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '被除数',
            defaultValue: 0
        });
        // 添加第二个数输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '除数',
            defaultValue: 1
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    DivideNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 检查除数是否为0
        if (b === 0) {
            console.error('除数不能为0');
            this.triggerFlow('flow');
            return NaN;
        }
        // 计算结果
        var result = a / b;
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return DivideNode;
}(FunctionNode_1.FunctionNode));
exports.DivideNode = DivideNode;
/**
 * 取模节点
 * 计算两个数的模
 */
var ModuloNode = /** @class */ (function (_super) {
    __extends(ModuloNode, _super);
    function ModuloNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ModuloNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个数输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '被除数',
            defaultValue: 0
        });
        // 添加第二个数输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '除数',
            defaultValue: 1
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ModuloNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 检查除数是否为0
        if (b === 0) {
            console.error('除数不能为0');
            this.triggerFlow('flow');
            return NaN;
        }
        // 计算结果
        var result = a % b;
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return ModuloNode;
}(FunctionNode_1.FunctionNode));
exports.ModuloNode = ModuloNode;
/**
 * 幂运算节点
 * 计算一个数的幂
 */
var PowerNode = /** @class */ (function (_super) {
    __extends(PowerNode, _super);
    function PowerNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    PowerNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加底数输入
        this.addInput({
            name: 'base',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '底数',
            defaultValue: 0
        });
        // 添加指数输入
        this.addInput({
            name: 'exponent',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '指数',
            defaultValue: 1
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    PowerNode.prototype.execute = function () {
        // 获取输入值
        var base = this.getInputValue('base');
        var exponent = this.getInputValue('exponent');
        // 计算结果
        var result = Math.pow(base, exponent);
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return PowerNode;
}(FunctionNode_1.FunctionNode));
exports.PowerNode = PowerNode;
/**
 * 平方根节点
 * 计算一个数的平方根
 */
var SquareRootNode = /** @class */ (function (_super) {
    __extends(SquareRootNode, _super);
    function SquareRootNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    SquareRootNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加数值输入
        this.addInput({
            name: 'value',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '数值',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    SquareRootNode.prototype.execute = function () {
        // 获取输入值
        var value = this.getInputValue('value');
        // 检查输入值是否为负数
        if (value < 0) {
            console.error('不能计算负数的平方根');
            this.triggerFlow('flow');
            return NaN;
        }
        // 计算结果
        var result = Math.sqrt(value);
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return SquareRootNode;
}(FunctionNode_1.FunctionNode));
exports.SquareRootNode = SquareRootNode;
/**
 * 三角函数节点
 * 计算三角函数值
 */
var TrigonometricNode = /** @class */ (function (_super) {
    __extends(TrigonometricNode, _super);
    /**
     * 创建三角函数节点
     * @param options 节点选项
     */
    function TrigonometricNode(options) {
        var _this = this;
        var _a;
        _this = _super.call(this, options) || this;
        // 设置三角函数类型
        _this.trigType = ((_a = options.metadata) === null || _a === void 0 ? void 0 : _a.trigType) || TrigonometricType.SIN;
        return _this;
    }
    /**
     * 初始化插槽
     */
    TrigonometricNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加角度输入
        this.addInput({
            name: 'angle',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '角度（弧度）',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    TrigonometricNode.prototype.execute = function () {
        // 获取输入值
        var angle = this.getInputValue('angle');
        // 根据三角函数类型计算结果
        var result;
        switch (this.trigType) {
            case TrigonometricType.SIN:
                result = Math.sin(angle);
                break;
            case TrigonometricType.COS:
                result = Math.cos(angle);
                break;
            case TrigonometricType.TAN:
                result = Math.tan(angle);
                break;
            case TrigonometricType.ASIN:
                result = Math.asin(angle);
                break;
            case TrigonometricType.ACOS:
                result = Math.acos(angle);
                break;
            case TrigonometricType.ATAN:
                result = Math.atan(angle);
                break;
            default:
                result = 0;
        }
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return TrigonometricNode;
}(FunctionNode_1.FunctionNode));
exports.TrigonometricNode = TrigonometricNode;
/**
 * 注册数学节点
 * @param registry 节点注册表
 */
function registerMathNodes(registry) {
    // 注册加法节点
    registry.registerNodeType({
        type: 'math/basic/add',
        category: Node_1.NodeCategory.MATH,
        constructor: AddNode,
        label: '加法',
        description: '计算两个数的和',
        icon: 'plus',
        color: '#2196F3',
        tags: ['math', 'basic', 'add']
    });
    // 注册减法节点
    registry.registerNodeType({
        type: 'math/basic/subtract',
        category: Node_1.NodeCategory.MATH,
        constructor: SubtractNode,
        label: '减法',
        description: '计算两个数的差',
        icon: 'minus',
        color: '#2196F3',
        tags: ['math', 'basic', 'subtract']
    });
    // 注册乘法节点
    registry.registerNodeType({
        type: 'math/basic/multiply',
        category: Node_1.NodeCategory.MATH,
        constructor: MultiplyNode,
        label: '乘法',
        description: '计算两个数的积',
        icon: 'multiply',
        color: '#2196F3',
        tags: ['math', 'basic', 'multiply']
    });
    // 注册除法节点
    registry.registerNodeType({
        type: 'math/basic/divide',
        category: Node_1.NodeCategory.MATH,
        constructor: DivideNode,
        label: '除法',
        description: '计算两个数的商',
        icon: 'divide',
        color: '#2196F3',
        tags: ['math', 'basic', 'divide']
    });
    // 注册取模节点
    registry.registerNodeType({
        type: 'math/basic/modulo',
        category: Node_1.NodeCategory.MATH,
        constructor: ModuloNode,
        label: '取模',
        description: '计算两个数的模',
        icon: 'modulo',
        color: '#2196F3',
        tags: ['math', 'basic', 'modulo']
    });
    // 注册幂运算节点
    registry.registerNodeType({
        type: 'math/advanced/power',
        category: Node_1.NodeCategory.MATH,
        constructor: PowerNode,
        label: '幂运算',
        description: '计算一个数的幂',
        icon: 'power',
        color: '#2196F3',
        tags: ['math', 'advanced', 'power']
    });
    // 注册平方根节点
    registry.registerNodeType({
        type: 'math/advanced/sqrt',
        category: Node_1.NodeCategory.MATH,
        constructor: SquareRootNode,
        label: '平方根',
        description: '计算一个数的平方根',
        icon: 'sqrt',
        color: '#2196F3',
        tags: ['math', 'advanced', 'sqrt']
    });
    // 注册正弦函数节点
    registry.registerNodeType({
        type: 'math/trigonometric/sin',
        category: Node_1.NodeCategory.MATH,
        constructor: TrigonometricNode,
        label: '正弦',
        description: '计算正弦值',
        icon: 'sin',
        color: '#2196F3',
        tags: ['math', 'trigonometric', 'sin'],
        metadata: {
            trigType: TrigonometricType.SIN
        }
    });
    // 注册余弦函数节点
    registry.registerNodeType({
        type: 'math/trigonometric/cos',
        category: Node_1.NodeCategory.MATH,
        constructor: TrigonometricNode,
        label: '余弦',
        description: '计算余弦值',
        icon: 'cos',
        color: '#2196F3',
        tags: ['math', 'trigonometric', 'cos'],
        metadata: {
            trigType: TrigonometricType.COS
        }
    });
    // 注册正切函数节点
    registry.registerNodeType({
        type: 'math/trigonometric/tan',
        category: Node_1.NodeCategory.MATH,
        constructor: TrigonometricNode,
        label: '正切',
        description: '计算正切值',
        icon: 'tan',
        color: '#2196F3',
        tags: ['math', 'trigonometric', 'tan'],
        metadata: {
            trigType: TrigonometricType.TAN
        }
    });
}
exports.registerMathNodes = registerMathNodes;
