"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventNode = void 0;
/**
 * 视觉脚本事件节点
 * 事件节点是视觉脚本的入口点，用于响应各种事件
 */
var Node_1 = require("./Node");
/**
 * 事件节点基类
 */
var EventNode = /** @class */ (function (_super) {
    __extends(EventNode, _super);
    /**
     * 创建事件节点
     * @param options 节点选项
     */
    function EventNode(options) {
        var _this = _super.call(this, options) || this;
        /** 节点类型 */
        _this.nodeType = Node_1.NodeType.EVENT;
        /** 节点类别 */
        _this.category = Node_1.NodeCategory.EVENT;
        _this.eventName = options.eventName || '';
        return _this;
    }
    /**
     * 初始化插槽
     */
    EventNode.prototype.initializeSockets = function () {
        // 事件节点只有输出流程插槽，没有输入流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: SocketDirection.OUTPUT,
            description: '当事件触发时执行'
        });
    };
    /**
     * 初始化节点
     * 在视觉脚本引擎启动时调用
     */
    EventNode.prototype.initialize = function () {
        // 子类实现
    };
    /**
     * 当视觉脚本开始执行时调用
     */
    EventNode.prototype.onStart = function () {
        // 子类实现
    };
    /**
     * 当视觉脚本停止执行时调用
     */
    EventNode.prototype.onStop = function () {
        // 子类实现
    };
    /**
     * 当视觉脚本更新时调用
     * @param deltaTime 帧间隔时间（秒）
     */
    EventNode.prototype.onUpdate = function (deltaTime) {
        // 子类实现
    };
    /**
     * 触发事件
     * @param args 事件参数
     */
    EventNode.prototype.trigger = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        // 设置输出参数
        if (args.length > 0 && this.outputs.size > 1) {
            var outputNames = Array.from(this.outputs.keys()).filter(function (name) { return name !== 'flow'; });
            for (var i = 0; i < Math.min(args.length, outputNames.length); i++) {
                this.setOutputValue(outputNames[i], args[i]);
            }
        }
        // 触发流程
        this.triggerFlow('flow');
    };
    return EventNode;
}(Node_1.Node));
exports.EventNode = EventNode;
