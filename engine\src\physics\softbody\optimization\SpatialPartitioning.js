"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpatialPartitioning = exports.SpatialCell = void 0;
/**
 * 空间分区系统
 * 用于加速软体物理的碰撞检测
 */
var THREE = require("three");
/**
 * 空间分区单元格
 */
var SpatialCell = /** @class */ (function () {
    /**
     * 创建空间分区单元格
     * @param x X索引
     * @param y Y索引
     * @param z Z索引
     */
    function SpatialCell(x, y, z) {
        /** 单元格中的粒子 */
        this.particles = [];
        this.index = new THREE.Vector3(x, y, z);
    }
    /**
     * 添加粒子
     * @param particle 粒子
     */
    SpatialCell.prototype.addParticle = function (particle) {
        if (!this.particles.includes(particle)) {
            this.particles.push(particle);
        }
    };
    /**
     * 移除粒子
     * @param particle 粒子
     */
    SpatialCell.prototype.removeParticle = function (particle) {
        var index = this.particles.indexOf(particle);
        if (index !== -1) {
            this.particles.splice(index, 1);
        }
    };
    /**
     * 清空单元格
     */
    SpatialCell.prototype.clear = function () {
        this.particles.length = 0;
    };
    return SpatialCell;
}());
exports.SpatialCell = SpatialCell;
/**
 * 空间分区系统
 * 使用均匀网格进行空间分区
 */
var SpatialPartitioning = /** @class */ (function () {
    /**
     * 创建空间分区系统
     * @param options 空间分区系统选项
     */
    function SpatialPartitioning(options) {
        if (options === void 0) { options = {}; }
        /** 单元格映射 */
        this.cells = new Map();
        /** 粒子到单元格的映射 */
        this.particleCells = new Map();
        this.cellSize = options.cellSize || 1.0;
        this.worldMin = options.worldMin || new THREE.Vector3(-100, -100, -100);
        this.worldMax = options.worldMax || new THREE.Vector3(100, 100, 100);
        // 计算网格尺寸
        this.gridSize = new THREE.Vector3(Math.ceil((this.worldMax.x - this.worldMin.x) / this.cellSize), Math.ceil((this.worldMax.y - this.worldMin.y) / this.cellSize), Math.ceil((this.worldMax.z - this.worldMin.z) / this.cellSize));
    }
    /**
     * 获取单元格键
     * @param x X索引
     * @param y Y索引
     * @param z Z索引
     * @returns 单元格键
     */
    SpatialPartitioning.prototype.getCellKey = function (x, y, z) {
        return "".concat(x, ",").concat(y, ",").concat(z);
    };
    /**
     * 获取位置所在的单元格索引
     * @param position 位置
     * @returns 单元格索引
     */
    SpatialPartitioning.prototype.getCellIndex = function (position) {
        var x = Math.floor((position.x - this.worldMin.x) / this.cellSize);
        var y = Math.floor((position.y - this.worldMin.y) / this.cellSize);
        var z = Math.floor((position.z - this.worldMin.z) / this.cellSize);
        // 确保索引在有效范围内
        return new THREE.Vector3(THREE.MathUtils.clamp(x, 0, this.gridSize.x - 1), THREE.MathUtils.clamp(y, 0, this.gridSize.y - 1), THREE.MathUtils.clamp(z, 0, this.gridSize.z - 1));
    };
    /**
     * 获取或创建单元格
     * @param index 单元格索引
     * @returns 单元格
     */
    SpatialPartitioning.prototype.getOrCreateCell = function (index) {
        var key = this.getCellKey(index.x, index.y, index.z);
        var cell = this.cells.get(key);
        if (!cell) {
            cell = new SpatialCell(index.x, index.y, index.z);
            this.cells.set(key, cell);
        }
        return cell;
    };
    /**
     * 更新粒子位置
     * @param particle 粒子
     */
    SpatialPartitioning.prototype.updateParticle = function (particle) {
        // 获取粒子当前所在的单元格
        var currentCell = this.particleCells.get(particle);
        // 获取粒子应该在的单元格
        var newCellIndex = this.getCellIndex(particle.position);
        var newCellKey = this.getCellKey(newCellIndex.x, newCellIndex.y, newCellIndex.z);
        var newCell = this.getOrCreateCell(newCellIndex);
        // 如果粒子所在单元格发生变化，更新单元格
        if (!currentCell || this.getCellKey(currentCell.index.x, currentCell.index.y, currentCell.index.z) !== newCellKey) {
            // 从旧单元格中移除
            if (currentCell) {
                currentCell.removeParticle(particle);
            }
            // 添加到新单元格
            newCell.addParticle(particle);
            this.particleCells.set(particle, newCell);
        }
    };
    /**
     * 添加粒子
     * @param particle 粒子
     */
    SpatialPartitioning.prototype.addParticle = function (particle) {
        var cellIndex = this.getCellIndex(particle.position);
        var cell = this.getOrCreateCell(cellIndex);
        cell.addParticle(particle);
        this.particleCells.set(particle, cell);
    };
    /**
     * 移除粒子
     * @param particle 粒子
     */
    SpatialPartitioning.prototype.removeParticle = function (particle) {
        var cell = this.particleCells.get(particle);
        if (cell) {
            cell.removeParticle(particle);
            this.particleCells.delete(particle);
        }
    };
    /**
     * 更新所有粒子
     * @param particles 粒子数组
     */
    SpatialPartitioning.prototype.updateAll = function (particles) {
        for (var _i = 0, particles_1 = particles; _i < particles_1.length; _i++) {
            var particle = particles_1[_i];
            this.updateParticle(particle);
        }
    };
    /**
     * 获取附近的粒子
     * @param position 位置
     * @param radius 半径
     * @returns 附近的粒子
     */
    SpatialPartitioning.prototype.getNearbyParticles = function (position, radius) {
        // 计算搜索范围（单元格索引）
        var cellRadius = Math.ceil(radius / this.cellSize);
        var centerIndex = this.getCellIndex(position);
        var nearbyParticles = [];
        var processedParticles = new Set();
        // 遍历搜索范围内的所有单元格
        for (var x = centerIndex.x - cellRadius; x <= centerIndex.x + cellRadius; x++) {
            for (var y = centerIndex.y - cellRadius; y <= centerIndex.y + cellRadius; y++) {
                for (var z = centerIndex.z - cellRadius; z <= centerIndex.z + cellRadius; z++) {
                    // 确保索引在有效范围内
                    if (x < 0 || y < 0 || z < 0 || x >= this.gridSize.x || y >= this.gridSize.y || z >= this.gridSize.z) {
                        continue;
                    }
                    var cellKey = this.getCellKey(x, y, z);
                    var cell = this.cells.get(cellKey);
                    if (cell) {
                        // 检查单元格中的每个粒子
                        for (var _i = 0, _a = cell.particles; _i < _a.length; _i++) {
                            var particle = _a[_i];
                            // 避免重复处理
                            if (processedParticles.has(particle)) {
                                continue;
                            }
                            processedParticles.add(particle);
                            // 计算距离
                            var dx = particle.getPosition().x - position.x;
                            var dy = particle.getPosition().y - position.y;
                            var dz = particle.getPosition().z - position.z;
                            var distanceSquared = dx * dx + dy * dy + dz * dz;
                            // 如果在半径内，添加到结果
                            if (distanceSquared <= radius * radius) {
                                nearbyParticles.push(particle);
                            }
                        }
                    }
                }
            }
        }
        return nearbyParticles;
    };
    /**
     * 清空所有单元格
     */
    SpatialPartitioning.prototype.clear = function () {
        this.cells.clear();
        this.particleCells.clear();
    };
    return SpatialPartitioning;
}());
exports.SpatialPartitioning = SpatialPartitioning;
