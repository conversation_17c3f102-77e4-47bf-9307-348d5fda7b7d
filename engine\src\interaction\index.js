"use strict";
/**
 * interaction/index.ts
 *
 * 导出交互系统的所有组件和类
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabEventType = exports.GrabState = exports.PhysicsGrabComponent = exports.GrabbedComponent = exports.GrabberComponent = exports.Hand = exports.GrabType = exports.GrabbableComponent = exports.GrabSystem = exports.HighlightType = exports.InteractionHighlightComponent = exports.PromptPositionType = exports.InteractionPromptComponent = exports.InteractionEvent = exports.InteractionEventType = exports.InteractionEventComponent = exports.InteractionType = exports.InteractableComponent = exports.InteractionSystem = void 0;
// 导出交互系统
var InteractionSystem_1 = require("./InteractionSystem");
Object.defineProperty(exports, "InteractionSystem", { enumerable: true, get: function () { return InteractionSystem_1.InteractionSystem; } });
// 导出可交互组件
var InteractableComponent_1 = require("./components/InteractableComponent");
Object.defineProperty(exports, "InteractableComponent", { enumerable: true, get: function () { return InteractableComponent_1.InteractableComponent; } });
Object.defineProperty(exports, "InteractionType", { enumerable: true, get: function () { return InteractableComponent_1.InteractionType; } });
// 导出交互事件组件
var InteractionEventComponent_1 = require("./components/InteractionEventComponent");
Object.defineProperty(exports, "InteractionEventComponent", { enumerable: true, get: function () { return InteractionEventComponent_1.InteractionEventComponent; } });
Object.defineProperty(exports, "InteractionEventType", { enumerable: true, get: function () { return InteractionEventComponent_1.InteractionEventType; } });
Object.defineProperty(exports, "InteractionEvent", { enumerable: true, get: function () { return InteractionEventComponent_1.InteractionEvent; } });
// 导出交互提示组件
var InteractionPromptComponent_1 = require("./components/InteractionPromptComponent");
Object.defineProperty(exports, "InteractionPromptComponent", { enumerable: true, get: function () { return InteractionPromptComponent_1.InteractionPromptComponent; } });
Object.defineProperty(exports, "PromptPositionType", { enumerable: true, get: function () { return InteractionPromptComponent_1.PromptPositionType; } });
// 导出交互高亮组件
var InteractionHighlightComponent_1 = require("./components/InteractionHighlightComponent");
Object.defineProperty(exports, "InteractionHighlightComponent", { enumerable: true, get: function () { return InteractionHighlightComponent_1.InteractionHighlightComponent; } });
Object.defineProperty(exports, "HighlightType", { enumerable: true, get: function () { return InteractionHighlightComponent_1.HighlightType; } });
// 导出抓取系统
var GrabSystem_1 = require("./systems/GrabSystem");
Object.defineProperty(exports, "GrabSystem", { enumerable: true, get: function () { return GrabSystem_1.GrabSystem; } });
// 导出可抓取组件
var GrabbableComponent_1 = require("./components/GrabbableComponent");
Object.defineProperty(exports, "GrabbableComponent", { enumerable: true, get: function () { return GrabbableComponent_1.GrabbableComponent; } });
Object.defineProperty(exports, "GrabType", { enumerable: true, get: function () { return GrabbableComponent_1.GrabType; } });
Object.defineProperty(exports, "Hand", { enumerable: true, get: function () { return GrabbableComponent_1.Hand; } });
// 导出抓取者组件
var GrabberComponent_1 = require("./components/GrabberComponent");
Object.defineProperty(exports, "GrabberComponent", { enumerable: true, get: function () { return GrabberComponent_1.GrabberComponent; } });
// 导出被抓取组件
var GrabbedComponent_1 = require("./components/GrabbedComponent");
Object.defineProperty(exports, "GrabbedComponent", { enumerable: true, get: function () { return GrabbedComponent_1.GrabbedComponent; } });
// 导出物理抓取组件
var PhysicsGrabComponent_1 = require("./components/PhysicsGrabComponent");
Object.defineProperty(exports, "PhysicsGrabComponent", { enumerable: true, get: function () { return PhysicsGrabComponent_1.PhysicsGrabComponent; } });
// 导出抓取状态
var GrabState_1 = require("./state/GrabState");
Object.defineProperty(exports, "GrabState", { enumerable: true, get: function () { return GrabState_1.GrabState; } });
Object.defineProperty(exports, "GrabEventType", { enumerable: true, get: function () { return GrabState_1.GrabEventType; } });
