"use strict";
/**
 * 媒体流约束定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaStreamConstraintsBuilder = void 0;
var MediaStreamConstraintsBuilder = /** @class */ (function () {
    function MediaStreamConstraintsBuilder() {
        this.constraints = {};
    }
    MediaStreamConstraintsBuilder.prototype.setAudio = function (enabled, constraints) {
        this.constraints.audio = enabled ? (constraints || true) : false;
        return this;
    };
    MediaStreamConstraintsBuilder.prototype.setVideo = function (enabled, constraints) {
        this.constraints.video = enabled ? (constraints || true) : false;
        return this;
    };
    MediaStreamConstraintsBuilder.prototype.setScreenShare = function (enabled, options) {
        this.constraints.screen = enabled ? (options || { video: true, audio: false }) : false;
        return this;
    };
    MediaStreamConstraintsBuilder.prototype.build = function () {
        var result = {};
        if (this.constraints.audio) {
            result.audio = this.constraints.audio;
        }
        if (this.constraints.video) {
            result.video = this.constraints.video;
        }
        return result;
    };
    MediaStreamConstraintsBuilder.createDefault = function () {
        return new MediaStreamConstraintsBuilder()
            .setAudio(true, {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        })
            .setVideo(true, {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
        });
    };
    MediaStreamConstraintsBuilder.createAudioOnly = function () {
        return new MediaStreamConstraintsBuilder()
            .setAudio(true, {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        })
            .setVideo(false);
    };
    MediaStreamConstraintsBuilder.createVideoOnly = function () {
        return new MediaStreamConstraintsBuilder()
            .setAudio(false)
            .setVideo(true, {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
        });
    };
    return MediaStreamConstraintsBuilder;
}());
exports.MediaStreamConstraintsBuilder = MediaStreamConstraintsBuilder;
