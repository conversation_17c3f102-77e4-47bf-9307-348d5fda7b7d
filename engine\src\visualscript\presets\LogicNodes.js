"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerLogicNodes = exports.ToggleNode = exports.LogicalOperationNode = exports.ComparisonNode = exports.BranchNode = void 0;
/**
 * 视觉脚本逻辑节点
 * 提供逻辑运算相关的节点
 */
var FlowNode_1 = require("../nodes/FlowNode");
var FunctionNode_1 = require("../nodes/FunctionNode");
var Node_1 = require("../nodes/Node");
/**
 * 比较运算符类型
 */
var ComparisonOperator;
(function (ComparisonOperator) {
    ComparisonOperator["EQUAL"] = "equal";
    ComparisonOperator["NOT_EQUAL"] = "notEqual";
    ComparisonOperator["GREATER"] = "greater";
    ComparisonOperator["GREATER_EQUAL"] = "greaterEqual";
    ComparisonOperator["LESS"] = "less";
    ComparisonOperator["LESS_EQUAL"] = "lessEqual";
})(ComparisonOperator || (ComparisonOperator = {}));
/**
 * 逻辑运算符类型
 */
var LogicalOperator;
(function (LogicalOperator) {
    LogicalOperator["AND"] = "and";
    LogicalOperator["OR"] = "or";
    LogicalOperator["NOT"] = "not";
})(LogicalOperator || (LogicalOperator = {}));
/**
 * 分支节点
 * 根据条件选择执行路径
 */
var BranchNode = /** @class */ (function (_super) {
    __extends(BranchNode, _super);
    function BranchNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    BranchNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加条件输入
        this.addInput({
            name: 'condition',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'boolean',
            description: '条件',
            defaultValue: false
        });
        // 添加真值输出流程插槽
        this.addOutput({
            name: 'true',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '条件为真时执行'
        });
        // 添加假值输出流程插槽
        this.addOutput({
            name: 'false',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '条件为假时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    BranchNode.prototype.execute = function () {
        // 获取条件值
        var condition = this.getInputValue('condition');
        // 根据条件选择执行路径
        if (condition) {
            this.triggerFlow('true');
            return true;
        }
        else {
            this.triggerFlow('false');
            return false;
        }
    };
    return BranchNode;
}(FlowNode_1.FlowNode));
exports.BranchNode = BranchNode;
/**
 * 比较节点
 * 比较两个值
 */
var ComparisonNode = /** @class */ (function (_super) {
    __extends(ComparisonNode, _super);
    /**
     * 创建比较节点
     * @param options 节点选项
     */
    function ComparisonNode(options) {
        var _this = this;
        var _a;
        _this = _super.call(this, options) || this;
        // 设置比较运算符
        _this.operator = ((_a = options.metadata) === null || _a === void 0 ? void 0 : _a.operator) || ComparisonOperator.EQUAL;
        return _this;
    }
    /**
     * 初始化插槽
     */
    ComparisonNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加第一个值输入
        this.addInput({
            name: 'a',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'any',
            description: '第一个值',
            defaultValue: 0
        });
        // 添加第二个值输入
        this.addInput({
            name: 'b',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'any',
            description: '第二个值',
            defaultValue: 0
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'boolean',
            description: '比较结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ComparisonNode.prototype.execute = function () {
        // 获取输入值
        var a = this.getInputValue('a');
        var b = this.getInputValue('b');
        // 根据运算符比较值
        var result;
        switch (this.operator) {
            case ComparisonOperator.EQUAL:
                result = a === b;
                break;
            case ComparisonOperator.NOT_EQUAL:
                result = a !== b;
                break;
            case ComparisonOperator.GREATER:
                result = a > b;
                break;
            case ComparisonOperator.GREATER_EQUAL:
                result = a >= b;
                break;
            case ComparisonOperator.LESS:
                result = a < b;
                break;
            case ComparisonOperator.LESS_EQUAL:
                result = a <= b;
                break;
            default:
                result = false;
        }
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return ComparisonNode;
}(FunctionNode_1.FunctionNode));
exports.ComparisonNode = ComparisonNode;
/**
 * 逻辑运算节点
 * 执行逻辑运算
 */
var LogicalOperationNode = /** @class */ (function (_super) {
    __extends(LogicalOperationNode, _super);
    /**
     * 创建逻辑运算节点
     * @param options 节点选项
     */
    function LogicalOperationNode(options) {
        var _this = this;
        var _a;
        _this = _super.call(this, options) || this;
        // 设置逻辑运算符
        _this.operator = ((_a = options.metadata) === null || _a === void 0 ? void 0 : _a.operator) || LogicalOperator.AND;
        return _this;
    }
    /**
     * 初始化插槽
     */
    LogicalOperationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 根据运算符添加输入
        if (this.operator !== LogicalOperator.NOT) {
            // 添加第一个值输入
            this.addInput({
                name: 'a',
                type: Node_1.SocketType.DATA,
                direction: Node_1.SocketDirection.INPUT,
                dataType: 'boolean',
                description: '第一个值',
                defaultValue: false
            });
            // 添加第二个值输入
            this.addInput({
                name: 'b',
                type: Node_1.SocketType.DATA,
                direction: Node_1.SocketDirection.INPUT,
                dataType: 'boolean',
                description: '第二个值',
                defaultValue: false
            });
        }
        else {
            // 添加值输入
            this.addInput({
                name: 'value',
                type: Node_1.SocketType.DATA,
                direction: Node_1.SocketDirection.INPUT,
                dataType: 'boolean',
                description: '值',
                defaultValue: false
            });
        }
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'boolean',
            description: '运算结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    LogicalOperationNode.prototype.execute = function () {
        var result;
        // 根据运算符执行逻辑运算
        switch (this.operator) {
            case LogicalOperator.AND:
                var a = this.getInputValue('a');
                var b = this.getInputValue('b');
                result = a && b;
                break;
            case LogicalOperator.OR:
                var c = this.getInputValue('a');
                var d = this.getInputValue('b');
                result = c || d;
                break;
            case LogicalOperator.NOT:
                var value = this.getInputValue('value');
                result = !value;
                break;
            default:
                result = false;
        }
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    return LogicalOperationNode;
}(FunctionNode_1.FunctionNode));
exports.LogicalOperationNode = LogicalOperationNode;
/**
 * 开关节点
 * 在两个状态之间切换
 */
var ToggleNode = /** @class */ (function (_super) {
    __extends(ToggleNode, _super);
    function ToggleNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 当前状态 */
        _this.state = false;
        return _this;
    }
    /**
     * 初始化插槽
     */
    ToggleNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加重置输入
        this.addInput({
            name: 'reset',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'boolean',
            description: '重置状态',
            defaultValue: false
        });
        // 添加初始状态输入
        this.addInput({
            name: 'initialState',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'boolean',
            description: '初始状态',
            defaultValue: false
        });
        // 添加当前状态输出
        this.addOutput({
            name: 'state',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'boolean',
            description: '当前状态'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ToggleNode.prototype.execute = function () {
        // 获取输入值
        var reset = this.getInputValue('reset');
        var initialState = this.getInputValue('initialState');
        // 如果需要重置，则设置为初始状态
        if (reset) {
            this.state = initialState;
        }
        else {
            // 否则切换状态
            this.state = !this.state;
        }
        // 设置输出值
        this.setOutputValue('state', this.state);
        // 触发输出流程
        this.triggerFlow('flow');
        return this.state;
    };
    return ToggleNode;
}(FunctionNode_1.FunctionNode));
exports.ToggleNode = ToggleNode;
/**
 * 注册逻辑节点
 * @param registry 节点注册表
 */
function registerLogicNodes(registry) {
    // 注册分支节点
    registry.registerNodeType({
        type: 'logic/flow/branch',
        category: Node_1.NodeCategory.LOGIC,
        constructor: BranchNode,
        label: '分支',
        description: '根据条件选择执行路径',
        icon: 'branch',
        color: '#FF9800',
        tags: ['logic', 'flow', 'branch']
    });
    // 注册相等比较节点
    registry.registerNodeType({
        type: 'logic/comparison/equal',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '相等',
        description: '比较两个值是否相等',
        icon: 'equal',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'equal'],
        metadata: {
            operator: ComparisonOperator.EQUAL
        }
    });
    // 注册不相等比较节点
    registry.registerNodeType({
        type: 'logic/comparison/notEqual',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '不相等',
        description: '比较两个值是否不相等',
        icon: 'notEqual',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'notEqual'],
        metadata: {
            operator: ComparisonOperator.NOT_EQUAL
        }
    });
    // 注册大于比较节点
    registry.registerNodeType({
        type: 'logic/comparison/greater',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '大于',
        description: '比较第一个值是否大于第二个值',
        icon: 'greater',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'greater'],
        metadata: {
            operator: ComparisonOperator.GREATER
        }
    });
    // 注册大于等于比较节点
    registry.registerNodeType({
        type: 'logic/comparison/greaterEqual',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '大于等于',
        description: '比较第一个值是否大于等于第二个值',
        icon: 'greaterEqual',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'greaterEqual'],
        metadata: {
            operator: ComparisonOperator.GREATER_EQUAL
        }
    });
    // 注册小于比较节点
    registry.registerNodeType({
        type: 'logic/comparison/less',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '小于',
        description: '比较第一个值是否小于第二个值',
        icon: 'less',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'less'],
        metadata: {
            operator: ComparisonOperator.LESS
        }
    });
    // 注册小于等于比较节点
    registry.registerNodeType({
        type: 'logic/comparison/lessEqual',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ComparisonNode,
        label: '小于等于',
        description: '比较第一个值是否小于等于第二个值',
        icon: 'lessEqual',
        color: '#FF9800',
        tags: ['logic', 'comparison', 'lessEqual'],
        metadata: {
            operator: ComparisonOperator.LESS_EQUAL
        }
    });
    // 注册与运算节点
    registry.registerNodeType({
        type: 'logic/operation/and',
        category: Node_1.NodeCategory.LOGIC,
        constructor: LogicalOperationNode,
        label: '与',
        description: '执行逻辑与运算',
        icon: 'and',
        color: '#FF9800',
        tags: ['logic', 'operation', 'and'],
        metadata: {
            operator: LogicalOperator.AND
        }
    });
    // 注册或运算节点
    registry.registerNodeType({
        type: 'logic/operation/or',
        category: Node_1.NodeCategory.LOGIC,
        constructor: LogicalOperationNode,
        label: '或',
        description: '执行逻辑或运算',
        icon: 'or',
        color: '#FF9800',
        tags: ['logic', 'operation', 'or'],
        metadata: {
            operator: LogicalOperator.OR
        }
    });
    // 注册非运算节点
    registry.registerNodeType({
        type: 'logic/operation/not',
        category: Node_1.NodeCategory.LOGIC,
        constructor: LogicalOperationNode,
        label: '非',
        description: '执行逻辑非运算',
        icon: 'not',
        color: '#FF9800',
        tags: ['logic', 'operation', 'not'],
        metadata: {
            operator: LogicalOperator.NOT
        }
    });
    // 注册开关节点
    registry.registerNodeType({
        type: 'logic/flow/toggle',
        category: Node_1.NodeCategory.LOGIC,
        constructor: ToggleNode,
        label: '开关',
        description: '在两个状态之间切换',
        icon: 'toggle',
        color: '#FF9800',
        tags: ['logic', 'flow', 'toggle']
    });
}
exports.registerLogicNodes = registerLogicNodes;
