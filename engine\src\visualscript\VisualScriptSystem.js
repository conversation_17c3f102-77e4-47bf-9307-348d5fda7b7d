"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VisualScriptSystem = void 0;
/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
var System_1 = require("../core/System");
var VisualScriptComponent_1 = require("./VisualScriptComponent");
var VisualScriptEngine_1 = require("./VisualScriptEngine");
var NodeRegistry_1 = require("./nodes/NodeRegistry");
var ValueTypeRegistry_1 = require("./values/ValueTypeRegistry");
var EventEmitter_1 = require("../utils/EventEmitter");
// 导入预设节点注册函数
var CoreNodes_1 = require("./presets/CoreNodes");
var LogicNodes_1 = require("./presets/LogicNodes");
var EntityNodes_1 = require("./presets/EntityNodes");
var MathNodes_1 = require("./presets/MathNodes");
var TimeNodes_1 = require("./presets/TimeNodes");
var PhysicsNodes_1 = require("./presets/PhysicsNodes");
var SoftBodyNodes_1 = require("./presets/SoftBodyNodes");
var AnimationNodes_1 = require("./presets/AnimationNodes");
var InputNodes_1 = require("./presets/InputNodes");
var AudioNodes_1 = require("./presets/AudioNodes");
var NetworkNodes_1 = require("./presets/NetworkNodes");
var AINodes_1 = require("./presets/AINodes");
/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
var VisualScriptSystem = exports.VisualScriptSystem = /** @class */ (function (_super) {
    __extends(VisualScriptSystem, _super);
    /**
     * 创建视觉脚本系统
     * @param options 系统选项
     */
    function VisualScriptSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, VisualScriptSystem.PRIORITY) || this;
        /** 视觉脚本引擎实例映射 */
        _this.engineInstances = new Map();
        /** 脚本域注册表 */
        _this.domainRegistries = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 世界引用 */
        _this.world = null;
        // 创建默认节点注册表和值类型注册表
        _this.nodeRegistry = new NodeRegistry_1.NodeRegistry();
        _this.valueTypeRegistry = new ValueTypeRegistry_1.ValueTypeRegistry();
        // 设置默认脚本域
        _this.defaultDomain = options.defaultDomain || 'default';
        // 注册默认脚本域
        _this.registerDomain(_this.defaultDomain, _this.nodeRegistry, _this.valueTypeRegistry);
        // 如果设置了自动初始化，则初始化系统
        if (options.autoInit !== false) {
            _this.initialize();
        }
        return _this;
    }
    /**
     * 初始化系统
     * @param world 世界实例
     */
    VisualScriptSystem.prototype.initialize = function (world) {
        var _this = this;
        _super.prototype.initialize.call(this);
        // 设置世界引用
        if (world) {
            this.world = world;
        }
        if (!this.world) {
            console.error('VisualScriptSystem: 世界实例未设置');
            return;
        }
        // 注册核心节点和值类型
        this.registerCoreNodesAndValueTypes();
        // 监听实体添加和移除事件
        this.world.on('entityAdded', this.onEntityAdded.bind(this));
        this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
        // 初始化现有实体
        this.world.getEntities().forEach(function (entity) {
            if (entity.hasComponent(VisualScriptComponent_1.VisualScriptComponent.TYPE)) {
                _this.initializeEntityScript(entity);
            }
        });
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    VisualScriptSystem.prototype.update = function (deltaTime) {
        // 更新所有视觉脚本引擎实例
        for (var _i = 0, _a = this.engineInstances.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], engine = _b[1];
            var component = entity.getComponent(VisualScriptComponent_1.VisualScriptComponent.TYPE);
            // 如果组件正在运行，则执行引擎
            if (component && component.running) {
                engine.update(deltaTime);
            }
        }
    };
    /**
     * 注册脚本域
     * @param domain 脚本域名称
     * @param nodeRegistry 节点注册表
     * @param valueTypeRegistry 值类型注册表
     */
    VisualScriptSystem.prototype.registerDomain = function (domain, nodeRegistry, valueTypeRegistry) {
        this.domainRegistries.set(domain, { nodes: nodeRegistry, values: valueTypeRegistry });
        this.eventEmitter.emit('domainRegistered', domain, nodeRegistry, valueTypeRegistry);
    };
    /**
     * 获取脚本域注册表
     * @param domain 脚本域名称
     * @returns 脚本域注册表
     */
    VisualScriptSystem.prototype.getDomainRegistry = function (domain) {
        return this.domainRegistries.get(domain);
    };
    /**
     * 获取默认脚本域
     * @returns 默认脚本域名称
     */
    VisualScriptSystem.prototype.getDefaultDomain = function () {
        return this.defaultDomain;
    };
    /**
     * 设置默认脚本域
     * @param domain 默认脚本域名称
     */
    VisualScriptSystem.prototype.setDefaultDomain = function (domain) {
        this.defaultDomain = domain;
    };
    /**
     * 注册核心节点和值类型
     */
    VisualScriptSystem.prototype.registerCoreNodesAndValueTypes = function () {
        // 获取默认域的注册表
        var defaultRegistry = this.getDomainRegistry(this.defaultDomain);
        if (defaultRegistry) {
            // 注册核心节点
            (0, CoreNodes_1.registerCoreNodes)(defaultRegistry.nodes);
            // 注册逻辑节点
            (0, LogicNodes_1.registerLogicNodes)(defaultRegistry.nodes);
            // 注册实体节点
            (0, EntityNodes_1.registerEntityNodes)(defaultRegistry.nodes);
            // 注册数学节点
            (0, MathNodes_1.registerMathNodes)(defaultRegistry.nodes);
            // 注册时间节点
            (0, TimeNodes_1.registerTimeNodes)(defaultRegistry.nodes);
            // 注册物理节点
            (0, PhysicsNodes_1.registerPhysicsNodes)(defaultRegistry.nodes);
            // 注册软体物理节点
            (0, SoftBodyNodes_1.registerSoftBodyNodes)(defaultRegistry.nodes);
            // 注册动画节点
            (0, AnimationNodes_1.registerAnimationNodes)(defaultRegistry.nodes);
            // 注册输入节点
            (0, InputNodes_1.registerInputNodes)(defaultRegistry.nodes);
            // 注册音频节点
            (0, AudioNodes_1.registerAudioNodes)(defaultRegistry.nodes);
            // 注册网络节点
            (0, NetworkNodes_1.registerNetworkNodes)(defaultRegistry.nodes);
            // 注册AI节点
            (0, AINodes_1.registerAINodes)(defaultRegistry.nodes);
            console.log('已注册所有预设节点类型');
        }
    };
    /**
     * 实体添加事件处理
     * @param entity 添加的实体
     */
    VisualScriptSystem.prototype.onEntityAdded = function (entity) {
        var _this = this;
        // 如果实体有视觉脚本组件，则初始化脚本
        if (entity.hasComponent(VisualScriptComponent_1.VisualScriptComponent.TYPE)) {
            this.initializeEntityScript(entity);
        }
        // 监听实体组件添加事件
        entity.on('componentAdded', function (component) {
            if (component.type === VisualScriptComponent_1.VisualScriptComponent.TYPE) {
                _this.initializeEntityScript(entity);
            }
        });
        // 监听实体组件移除事件
        entity.on('componentRemoved', function (component) {
            if (component.type === VisualScriptComponent_1.VisualScriptComponent.TYPE) {
                _this.cleanupEntityScript(entity);
            }
        });
    };
    /**
     * 实体移除事件处理
     * @param entity 移除的实体
     */
    VisualScriptSystem.prototype.onEntityRemoved = function (entity) {
        // 清理实体脚本
        this.cleanupEntityScript(entity);
    };
    /**
     * 初始化实体脚本
     * @param entity 实体
     */
    VisualScriptSystem.prototype.initializeEntityScript = function (entity) {
        var _this = this;
        // 获取视觉脚本组件
        var component = entity.getComponent(VisualScriptComponent_1.VisualScriptComponent.TYPE);
        // 如果已经有引擎实例，则先清理
        if (this.engineInstances.has(entity)) {
            this.cleanupEntityScript(entity);
        }
        // 如果没有脚本数据，则不创建引擎实例
        if (!component || !component.script) {
            return;
        }
        // 获取脚本域注册表
        var domainRegistry = this.getDomainRegistry(component.domain) ||
            this.getDomainRegistry(this.defaultDomain);
        if (!domainRegistry) {
            console.error("\u627E\u4E0D\u5230\u811A\u672C\u57DF: ".concat(component.domain));
            return;
        }
        // 创建视觉脚本引擎实例
        var engine = new VisualScriptEngine_1.VisualScriptEngine({
            script: component.script,
            nodeRegistry: domainRegistry.nodes,
            valueTypeRegistry: domainRegistry.values,
            entity: entity,
            world: this.world
        });
        // 存储引擎实例
        this.engineInstances.set(entity, engine);
        // 监听组件事件
        component.on('scriptChanged', function () {
            _this.initializeEntityScript(entity);
        });
        component.on('runningChanged', function (running) {
            if (running) {
                engine.start();
            }
            else {
                engine.stop();
            }
        });
        component.on('domainChanged', function () {
            _this.initializeEntityScript(entity);
        });
        // 如果组件设置为自动运行，则启动引擎
        if (component.running) {
            engine.start();
        }
    };
    /**
     * 清理实体脚本
     * @param entity 实体
     */
    VisualScriptSystem.prototype.cleanupEntityScript = function (entity) {
        // 获取引擎实例
        var engine = this.engineInstances.get(entity);
        if (engine) {
            // 停止引擎
            engine.stop();
            // 销毁引擎
            engine.dispose();
            // 移除引擎实例
            this.engineInstances.delete(entity);
        }
    };
    /**
     * 销毁系统
     */
    VisualScriptSystem.prototype.dispose = function () {
        // 清理所有引擎实例
        for (var _i = 0, _a = this.engineInstances.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], engine = _b[1];
            engine.stop();
            engine.dispose();
        }
        // 清空引擎实例映射
        this.engineInstances.clear();
        // 移除事件监听
        if (this.world) {
            this.world.off('entityAdded', this.onEntityAdded);
            this.world.off('entityRemoved', this.onEntityRemoved);
        }
        _super.prototype.dispose.call(this);
    };
    /** 系统优先级 */
    VisualScriptSystem.PRIORITY = 200;
    return VisualScriptSystem;
}(System_1.System));
