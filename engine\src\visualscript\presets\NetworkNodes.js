"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerNetworkNodes = exports.OnNetworkMessageNode = exports.SendNetworkMessageNode = exports.ConnectToServerNode = void 0;
var FlowNode_1 = require("../nodes/FlowNode");
var EventNode_1 = require("../nodes/EventNode");
var Node_1 = require("../nodes/Node");
var NetworkSystem_1 = require("../../network/NetworkSystem");
var MessageType_1 = require("../../network/MessageType");
// 导入其他网络节点模块
var NetworkProtocolNodes_1 = require("./NetworkProtocolNodes");
var WebRTCNodes_1 = require("./WebRTCNodes");
var NetworkSecurityNodes_1 = require("./NetworkSecurityNodes");
/**
 * 网络连接节点
 * 连接到网络服务器
 */
var ConnectToServerNode = /** @class */ (function (_super) {
    __extends(ConnectToServerNode, _super);
    function ConnectToServerNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ConnectToServerNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'serverUrl',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '服务器URL',
            defaultValue: 'ws://localhost:8080'
        });
        this.addInput({
            name: 'roomId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '房间ID',
            defaultValue: '',
            optional: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '连接成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '连接失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'connected',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否已连接'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ConnectToServerNode.prototype.execute = function () {
        var _this = this;
        // 获取输入值
        var serverUrl = this.getInputValue('serverUrl');
        var roomId = this.getInputValue('roomId');
        // 获取网络系统
        var networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
        if (!networkSystem) {
            this.setOutputValue('connected', false);
            this.triggerFlow('fail');
            return false;
        }
        try {
            // 连接到服务器
            networkSystem.connect(serverUrl, roomId || undefined);
            // 监听连接事件
            networkSystem.once('connected', function () {
                _this.setOutputValue('connected', true);
                _this.triggerFlow('success');
            });
            networkSystem.once('error', function () {
                _this.setOutputValue('connected', false);
                _this.triggerFlow('fail');
            });
            return true;
        }
        catch (error) {
            this.setOutputValue('connected', false);
            this.triggerFlow('fail');
            return false;
        }
    };
    return ConnectToServerNode;
}(FlowNode_1.FlowNode));
exports.ConnectToServerNode = ConnectToServerNode;
/**
 * 发送网络消息节点
 * 向其他用户发送网络消息
 */
var SendNetworkMessageNode = /** @class */ (function (_super) {
    __extends(SendNetworkMessageNode, _super);
    function SendNetworkMessageNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    SendNetworkMessageNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'message',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '消息内容'
        });
        this.addInput({
            name: 'targetUserId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标用户ID（为空则发送给所有用户）',
            defaultValue: '',
            optional: true
        });
        this.addInput({
            name: 'reliable',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否可靠传输',
            defaultValue: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否发送成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    SendNetworkMessageNode.prototype.execute = function () {
        // 获取输入值
        var message = this.getInputValue('message');
        var targetUserId = this.getInputValue('targetUserId');
        var reliable = this.getInputValue('reliable');
        // 获取网络系统
        var networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
        if (!networkSystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        try {
            // 创建网络消息
            var networkMessage = new NetworkMessage_1.NetworkMessage({
                type: MessageType_1.MessageType.CUSTOM,
                data: message,
                reliable: reliable
            });
            // 发送消息
            if (targetUserId) {
                // 发送给特定用户
                networkSystem.sendMessageToUser(targetUserId, networkMessage);
            }
            else {
                // 发送给所有用户
                networkSystem.broadcastMessage(networkMessage);
            }
            this.setOutputValue('success', true);
        }
        catch (error) {
            this.setOutputValue('success', false);
        }
        // 触发输出流程
        this.triggerFlow('flow');
        return true;
    };
    return SendNetworkMessageNode;
}(FlowNode_1.FlowNode));
exports.SendNetworkMessageNode = SendNetworkMessageNode;
/**
 * 网络消息接收事件节点
 * 当接收到网络消息时触发
 */
var OnNetworkMessageNode = /** @class */ (function (_super) {
    __extends(OnNetworkMessageNode, _super);
    function OnNetworkMessageNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    OnNetworkMessageNode.prototype.initializeSockets = function () {
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'message',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '消息内容'
        });
        this.addOutput({
            name: 'senderId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '发送者ID'
        });
    };
    /**
     * 初始化事件
     */
    OnNetworkMessageNode.prototype.initialize = function () {
        // 获取网络系统
        var networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
        if (!networkSystem) {
            return;
        }
        // 监听网络消息事件
        networkSystem.on('message', this.onNetworkMessage.bind(this));
    };
    /**
     * 网络消息处理
     * @param message 网络消息
     * @param senderId 发送者ID
     */
    OnNetworkMessageNode.prototype.onNetworkMessage = function (message, senderId) {
        // 设置输出值
        this.setOutputValue('message', message.data);
        this.setOutputValue('senderId', senderId);
        // 触发输出流程
        this.triggerFlow('flow');
    };
    /**
     * 清理事件
     */
    OnNetworkMessageNode.prototype.cleanup = function () {
        // 获取网络系统
        var networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
        if (!networkSystem) {
            return;
        }
        // 移除事件监听
        networkSystem.off('message', this.onNetworkMessage.bind(this));
    };
    return OnNetworkMessageNode;
}(EventNode_1.EventNode));
exports.OnNetworkMessageNode = OnNetworkMessageNode;
/**
 * 注册网络节点
 * @param registry 节点注册表
 */
function registerNetworkNodes(registry) {
    // 注册连接到服务器节点
    registry.registerNodeType({
        type: 'network/connectToServer',
        category: Node_1.NodeCategory.NETWORK,
        constructor: ConnectToServerNode,
        label: '连接到服务器',
        description: '连接到网络服务器',
        icon: 'connect',
        color: '#00BCD4',
        tags: ['network', 'connect', 'server']
    });
    // 注册发送网络消息节点
    registry.registerNodeType({
        type: 'network/sendMessage',
        category: Node_1.NodeCategory.NETWORK,
        constructor: SendNetworkMessageNode,
        label: '发送网络消息',
        description: '向其他用户发送网络消息',
        icon: 'send',
        color: '#00BCD4',
        tags: ['network', 'message', 'send']
    });
    // 注册网络消息接收事件节点
    registry.registerNodeType({
        type: 'network/events/onMessage',
        category: Node_1.NodeCategory.NETWORK,
        constructor: OnNetworkMessageNode,
        label: '接收网络消息',
        description: '当接收到网络消息时触发',
        icon: 'receive',
        color: '#00BCD4',
        tags: ['network', 'message', 'receive', 'event']
    });
    // 注册网络协议节点
    (0, NetworkProtocolNodes_1.registerNetworkProtocolNodes)(registry);
    // 注册WebRTC节点
    (0, WebRTCNodes_1.registerWebRTCNodes)(registry);
    // 注册网络安全节点
    (0, NetworkSecurityNodes_1.registerNetworkSecurityNodes)(registry);
    console.log('已注册所有网络节点类型');
}
exports.registerNetworkNodes = registerNetworkNodes;
