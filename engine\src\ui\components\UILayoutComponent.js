"use strict";
/**
 * UILayoutComponent.ts
 *
 * UI布局组件，用于管理UI元素的布局
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UILayoutComponent = exports.RelativeLayout = exports.AbsoluteLayout = exports.FlexLayout = exports.GridLayout = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var IUIElement_1 = require("../interfaces/IUIElement");
var UIComponent_1 = require("./UIComponent");
/**
 * 网格布局
 */
var GridLayout = /** @class */ (function () {
    /**
     * 构造函数
     * @param params 网格布局参数
     */
    function GridLayout(params) {
        this.type = IUIElement_1.UILayoutType.GRID;
        this.params = {
            columns: params.columns,
            rows: params.rows,
            cellWidth: params.cellWidth,
            cellHeight: params.cellHeight,
            columnGap: params.columnGap || 0,
            rowGap: params.rowGap || 0,
            justifyItems: params.justifyItems || 'center',
            alignItems: params.alignItems || 'center',
            autoFlow: params.autoFlow || 'row'
        };
    }
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    GridLayout.prototype.apply = function (container) {
        var children = container.children.filter(function (child) { return child instanceof UIComponent_1.UIComponent; });
        if (children.length === 0)
            return;
        var _a = this.params, columns = _a.columns, rows = _a.rows, cellWidth = _a.cellWidth, cellHeight = _a.cellHeight, columnGap = _a.columnGap, rowGap = _a.rowGap, autoFlow = _a.autoFlow;
        // 计算实际行数（如果没有指定）
        var actualRows = rows || Math.ceil(children.length / columns);
        // 计算总宽度和总高度
        var totalWidth = columns * cellWidth + (columns - 1) * columnGap;
        var totalHeight = actualRows * cellHeight + (actualRows - 1) * rowGap;
        // 更新容器尺寸（如果需要）
        if (container.size.x < totalWidth) {
            container.size.x = totalWidth;
        }
        if (container.size.y < totalHeight) {
            container.size.y = totalHeight;
        }
        // 计算起始位置（左上角）
        var startX = -totalWidth / 2 + cellWidth / 2;
        var startY = totalHeight / 2 - cellHeight / 2;
        // 应用布局到子元素
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 计算行和列索引
            var row = void 0, col = void 0;
            if (autoFlow === 'row') {
                row = Math.floor(i / columns);
                col = i % columns;
            }
            else {
                col = Math.floor(i / actualRows);
                row = i % actualRows;
            }
            // 计算位置
            var x = startX + col * (cellWidth + columnGap);
            var y = startY - row * (cellHeight + rowGap);
            // 设置子元素位置和尺寸
            child.setPosition(new three_1.Vector2(x, y));
            child.setSize(new three_1.Vector2(cellWidth, cellHeight));
            // 应用布局项参数（如果有）
            var layoutParams = child.layoutParams;
            if (layoutParams) {
                // 应用网格特定参数
                // 这里可以添加更多的网格布局项参数处理
            }
        }
    };
    return GridLayout;
}());
exports.GridLayout = GridLayout;
/**
 * 弹性布局
 */
var FlexLayout = /** @class */ (function () {
    /**
     * 构造函数
     * @param params 弹性布局参数
     */
    function FlexLayout(params) {
        this.type = IUIElement_1.UILayoutType.FLEX;
        this.params = {
            direction: params.direction,
            wrap: params.wrap || 'nowrap',
            justifyContent: params.justifyContent || 'flex-start',
            alignItems: params.alignItems || 'center',
            alignContent: params.alignContent || 'flex-start',
            gap: params.gap || 0
        };
    }
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    FlexLayout.prototype.apply = function (container) {
        var children = container.children.filter(function (child) { return child instanceof UIComponent_1.UIComponent; });
        if (children.length === 0)
            return;
        var _a = this.params, direction = _a.direction, justifyContent = _a.justifyContent, alignItems = _a.alignItems, gap = _a.gap;
        // 注意：wrap 参数目前未使用，但保留在参数中以便将来实现换行功能
        var isRow = direction === 'row' || direction === 'row-reverse';
        var isReverse = direction === 'row-reverse' || direction === 'column-reverse';
        // 计算容器尺寸
        var containerWidth = container.size.x;
        var containerHeight = container.size.y;
        // 计算子元素的总尺寸和间隙
        var totalMainSize = 0;
        // 注意：totalCrossSize 目前未使用，但保留以便将来实现多行布局
        // let totalCrossSize = 0;
        var maxCrossSize = 0;
        for (var _i = 0, children_1 = children; _i < children_1.length; _i++) {
            var child = children_1[_i];
            if (isRow) {
                totalMainSize += child.size.x;
                maxCrossSize = Math.max(maxCrossSize, child.size.y);
            }
            else {
                totalMainSize += child.size.y;
                maxCrossSize = Math.max(maxCrossSize, child.size.x);
            }
        }
        // 添加间隙
        totalMainSize += gap * (children.length - 1);
        // 计算主轴上的起始位置和步长
        var mainStart, mainStep;
        var mainSpace = (isRow ? containerWidth : containerHeight) - totalMainSize;
        switch (justifyContent) {
            case 'flex-start':
                mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
                mainStep = gap;
                break;
            case 'flex-end':
                mainStart = isRow ?
                    containerWidth / 2 - totalMainSize + children[0].size.x / 2 :
                    -containerHeight / 2 + totalMainSize - children[0].size.y / 2;
                mainStep = gap;
                break;
            case 'center':
                mainStart = isRow ?
                    -totalMainSize / 2 + children[0].size.x / 2 :
                    totalMainSize / 2 - children[0].size.y / 2;
                mainStep = gap;
                break;
            case 'space-between':
                mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
                mainStep = mainSpace / (children.length - 1);
                break;
            case 'space-around':
                var spaceAround = mainSpace / (children.length * 2);
                mainStart = isRow ?
                    -containerWidth / 2 + spaceAround + children[0].size.x / 2 :
                    containerHeight / 2 - spaceAround - children[0].size.y / 2;
                mainStep = spaceAround * 2;
                break;
            case 'space-evenly':
                var spaceEvenly = mainSpace / (children.length + 1);
                mainStart = isRow ?
                    -containerWidth / 2 + spaceEvenly + children[0].size.x / 2 :
                    containerHeight / 2 - spaceEvenly - children[0].size.y / 2;
                mainStep = spaceEvenly;
                break;
            default:
                mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
                mainStep = gap;
        }
        // 如果是反向，调整起始位置
        if (isReverse) {
            mainStart = isRow ?
                containerWidth / 2 - children[0].size.x / 2 :
                -containerHeight / 2 + children[0].size.y / 2;
        }
        // 计算交叉轴上的位置
        var crossPos;
        switch (alignItems) {
            case 'flex-start':
                crossPos = isRow ? containerHeight / 2 - children[0].size.y / 2 : -containerWidth / 2 + children[0].size.x / 2;
                break;
            case 'flex-end':
                crossPos = isRow ? -containerHeight / 2 + children[0].size.y / 2 : containerWidth / 2 - children[0].size.x / 2;
                break;
            case 'center':
                crossPos = 0;
                break;
            case 'stretch':
                // 拉伸子元素以填充交叉轴
                for (var _b = 0, children_2 = children; _b < children_2.length; _b++) {
                    var child = children_2[_b];
                    if (isRow) {
                        child.setSize(new three_1.Vector2(child.size.x, containerHeight));
                    }
                    else {
                        child.setSize(new three_1.Vector2(containerWidth, child.size.y));
                    }
                }
                crossPos = 0;
                break;
            default:
                crossPos = 0;
        }
        // 应用布局到子元素
        var mainPos = mainStart;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 计算位置
            var x = void 0, y = void 0;
            if (isRow) {
                x = isReverse ? mainPos - child.size.x * (i === 0 ? 0 : 1) : mainPos;
                y = crossPos;
            }
            else {
                x = crossPos;
                y = isReverse ? mainPos - child.size.y * (i === 0 ? 0 : 1) : mainPos;
            }
            // 设置子元素位置
            child.setPosition(new three_1.Vector2(x, y));
            // 更新主轴位置
            if (isRow) {
                mainPos += isReverse ? -(child.size.x + mainStep) : (child.size.x + mainStep);
            }
            else {
                mainPos += isReverse ? -(child.size.y + mainStep) : (child.size.y + mainStep);
            }
            // 应用布局项参数（如果有）
            var layoutParams = child.layoutParams;
            if (layoutParams) {
                // 应用弹性布局特定参数
                // 这里可以添加更多的弹性布局项参数处理
            }
        }
    };
    return FlexLayout;
}());
exports.FlexLayout = FlexLayout;
/**
 * 绝对布局
 */
var AbsoluteLayout = /** @class */ (function () {
    /**
     * 构造函数
     * @param params 绝对布局参数
     */
    function AbsoluteLayout(params) {
        if (params === void 0) { params = {}; }
        this.type = IUIElement_1.UILayoutType.ABSOLUTE;
        this.params = params;
    }
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    AbsoluteLayout.prototype.apply = function (container) {
        var children = container.children.filter(function (child) { return child instanceof UIComponent_1.UIComponent; });
        if (children.length === 0)
            return;
        // 绝对布局不改变子元素的位置，除非有特定的布局项参数
        for (var _i = 0, children_3 = children; _i < children_3.length; _i++) {
            var child = children_3[_i];
            var layoutParams = child.layoutParams;
            if (layoutParams) {
                // 应用绝对布局特定参数
                if (layoutParams.left !== undefined || layoutParams.right !== undefined ||
                    layoutParams.top !== undefined || layoutParams.bottom !== undefined) {
                    var containerWidth = container.size.x;
                    var containerHeight = container.size.y;
                    var x = child.position instanceof three_1.Vector2 ? child.getPosition().x : child.position.x;
                    var y = child.position instanceof three_1.Vector2 ? child.getPosition().y : child.position.y;
                    if (layoutParams.left !== undefined) {
                        x = -containerWidth / 2 + layoutParams.left + child.size.x / 2;
                    }
                    else if (layoutParams.right !== undefined) {
                        x = containerWidth / 2 - layoutParams.right - child.size.x / 2;
                    }
                    if (layoutParams.top !== undefined) {
                        y = containerHeight / 2 - layoutParams.top - child.size.y / 2;
                    }
                    else if (layoutParams.bottom !== undefined) {
                        y = -containerHeight / 2 + layoutParams.bottom + child.size.y / 2;
                    }
                    child.setPosition(new three_1.Vector2(x, y));
                }
                // 设置z-index
                if (layoutParams.zIndex !== undefined) {
                    child.zIndex = layoutParams.zIndex;
                }
            }
        }
    };
    return AbsoluteLayout;
}());
exports.AbsoluteLayout = AbsoluteLayout;
/**
 * 相对布局
 */
var RelativeLayout = /** @class */ (function () {
    /**
     * 构造函数
     * @param params 相对布局参数
     */
    function RelativeLayout(params) {
        if (params === void 0) { params = {}; }
        this.type = IUIElement_1.UILayoutType.RELATIVE;
        this.params = {
            spacing: params.spacing || 0,
            padding: params.padding || 0
        };
    }
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    RelativeLayout.prototype.apply = function (container) {
        var children = container.children.filter(function (child) { return child instanceof UIComponent_1.UIComponent; });
        if (children.length === 0)
            return;
        var _a = this.params, spacing = _a.spacing, padding = _a.padding;
        // 计算内边距
        var paddingTop = 0, paddingLeft = 0;
        if (typeof padding === 'number') {
            paddingTop = paddingLeft = padding;
            // 右边距和底部边距目前未使用
            // let paddingRight = padding, paddingBottom = padding;
        }
        else if (padding) {
            paddingTop = padding.top || 0;
            paddingLeft = padding.left || 0;
            // 右边距和底部边距目前未使用
            // let paddingRight = padding.right || 0;
            // let paddingBottom = padding.bottom || 0;
        }
        // 计算容器的内部尺寸 - 目前未使用，但保留以便将来实现更复杂的布局
        // const innerWidth = container.size.x - paddingLeft - paddingRight;
        // const innerHeight = container.size.y - paddingTop - paddingBottom;
        // 计算起始位置
        var startX = -container.size.x / 2 + paddingLeft;
        var startY = container.size.y / 2 - paddingTop;
        // 应用布局到子元素
        var currentY = startY;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 应用布局项参数（如果有）
            var layoutParams = child.layoutParams;
            var marginTop = 0, marginBottom = 0, marginLeft = 0;
            // marginRight 目前在垂直布局中未使用，但保留在代码注释中以便将来实现水平布局
            // let marginRight = 0;
            if (layoutParams && layoutParams.margin) {
                if (typeof layoutParams.margin === 'number') {
                    marginTop = marginBottom = marginLeft = layoutParams.margin;
                    // 右边距目前未使用
                    // let marginRight = layoutParams.margin;
                }
                else {
                    marginTop = layoutParams.margin.top || 0;
                    // 右边距目前未使用
                    // let marginRight = layoutParams.margin.right || 0;
                    marginBottom = layoutParams.margin.bottom || 0;
                    marginLeft = layoutParams.margin.left || 0;
                }
            }
            // 计算位置
            var x = startX + marginLeft + child.size.x / 2;
            var y = currentY - marginTop - child.size.y / 2;
            // 设置子元素位置
            child.setPosition(new three_1.Vector2(x, y));
            // 更新当前Y位置
            currentY = y - child.size.y / 2 - marginBottom - spacing;
        }
    };
    return RelativeLayout;
}());
exports.RelativeLayout = RelativeLayout;
/**
 * UI布局组件
 * 用于管理实体的UI布局
 */
var UILayoutComponent = /** @class */ (function (_super) {
    __extends(UILayoutComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param layout 布局
     * @param layoutItemParams 布局项参数
     */
    function UILayoutComponent(entity, layout, layoutItemParams) {
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'UILayout') || this;
        // 设置实体引用
        _this.setEntity(entity);
        _this.layout = layout;
        _this.layoutItemParams = layoutItemParams;
        return _this;
    }
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    UILayoutComponent.prototype.applyLayout = function (container) {
        this.layout.apply(container);
    };
    /**
     * 设置布局
     * @param layout 新布局
     */
    UILayoutComponent.prototype.setLayout = function (layout) {
        this.layout = layout;
    };
    /**
     * 设置布局项参数
     * @param params 布局项参数
     */
    UILayoutComponent.prototype.setLayoutItemParams = function (params) {
        this.layoutItemParams = params;
    };
    return UILayoutComponent;
}(Component_1.Component));
exports.UILayoutComponent = UILayoutComponent;
