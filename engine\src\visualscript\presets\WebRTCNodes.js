"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerWebRTCNodes = exports.DataChannelMessageEventNode = exports.CreateDataChannelNode = exports.CreateWebRTCConnectionNode = void 0;
var AsyncNode_1 = require("../nodes/AsyncNode");
var EventNode_1 = require("../nodes/EventNode");
var Node_1 = require("../nodes/Node");
var NetworkSystem_1 = require("../../network/NetworkSystem");
/**
 * 创建WebRTC连接节点
 * 创建与远程对等方的WebRTC连接
 */
var CreateWebRTCConnectionNode = /** @class */ (function (_super) {
    __extends(CreateWebRTCConnectionNode, _super);
    function CreateWebRTCConnectionNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateWebRTCConnectionNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'peerId',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '对等方ID'
        });
        this.addInput({
            name: 'config',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: 'WebRTC配置',
            defaultValue: {},
            optional: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'connection',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: 'WebRTC连接'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateWebRTCConnectionNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var peerId, config, networkSystem, connection, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        peerId = this.getInputValue('peerId');
                        config = this.getInputValue('config');
                        // 检查输入值是否有效
                        if (!peerId) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
                        if (!networkSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, networkSystem.createWebRTCConnection(peerId, config)];
                    case 2:
                        connection = _a.sent();
                        if (connection) {
                            // 设置输出值
                            this.setOutputValue('connection', connection);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('创建WebRTC连接失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return CreateWebRTCConnectionNode;
}(AsyncNode_1.AsyncNode));
exports.CreateWebRTCConnectionNode = CreateWebRTCConnectionNode;
/**
 * 创建数据通道节点
 * 在WebRTC连接上创建数据通道
 */
var CreateDataChannelNode = /** @class */ (function (_super) {
    __extends(CreateDataChannelNode, _super);
    function CreateDataChannelNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateDataChannelNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'connection',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: 'WebRTC连接'
        });
        this.addInput({
            name: 'label',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '通道标签',
            defaultValue: 'data'
        });
        this.addInput({
            name: 'options',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '通道选项',
            defaultValue: {},
            optional: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'dataChannel',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '数据通道'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateDataChannelNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var connection, label, options, dataChannel, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        connection = this.getInputValue('connection');
                        label = this.getInputValue('label');
                        options = this.getInputValue('options');
                        // 检查输入值是否有效
                        if (!connection) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, connection.createDataChannel(label, options)];
                    case 2:
                        dataChannel = _a.sent();
                        if (dataChannel) {
                            // 设置输出值
                            this.setOutputValue('dataChannel', dataChannel);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('创建数据通道失败:', error_2);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return CreateDataChannelNode;
}(AsyncNode_1.AsyncNode));
exports.CreateDataChannelNode = CreateDataChannelNode;
/**
 * 数据通道消息事件节点
 * 监听数据通道消息事件
 */
var DataChannelMessageEventNode = /** @class */ (function (_super) {
    __extends(DataChannelMessageEventNode, _super);
    function DataChannelMessageEventNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 数据通道 */
        _this.dataChannel = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    DataChannelMessageEventNode.prototype.initializeSockets = function () {
        // 添加输入数据插槽
        this.addInput({
            name: 'dataChannel',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '数据通道'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '收到消息'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'message',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '消息数据'
        });
    };
    /**
     * 初始化事件
     */
    DataChannelMessageEventNode.prototype.initialize = function () {
        // 获取数据通道
        this.dataChannel = this.getInputValue('dataChannel');
        if (this.dataChannel) {
            // 监听消息事件
            this.dataChannel.on('message', this.onMessage.bind(this));
        }
    };
    /**
     * 消息事件处理
     * @param message 消息数据
     */
    DataChannelMessageEventNode.prototype.onMessage = function (message) {
        // 设置输出值
        this.setOutputValue('message', message);
        // 触发输出流程
        this.triggerFlow('flow');
    };
    /**
     * 清理事件
     */
    DataChannelMessageEventNode.prototype.cleanup = function () {
        if (this.dataChannel) {
            // 移除事件监听
            this.dataChannel.off('message', this.onMessage.bind(this));
            this.dataChannel = null;
        }
    };
    return DataChannelMessageEventNode;
}(EventNode_1.EventNode));
exports.DataChannelMessageEventNode = DataChannelMessageEventNode;
/**
 * 注册WebRTC节点
 * @param registry 节点注册表
 */
function registerWebRTCNodes(registry) {
    // 注册创建WebRTC连接节点
    registry.registerNodeType({
        type: 'network/webrtc/createConnection',
        category: Node_1.NodeCategory.NETWORK,
        constructor: CreateWebRTCConnectionNode,
        label: '创建WebRTC连接',
        description: '创建与远程对等方的WebRTC连接',
        icon: 'webrtc',
        color: '#00BCD4',
        tags: ['network', 'webrtc', 'connection']
    });
    // 注册创建数据通道节点
    registry.registerNodeType({
        type: 'network/webrtc/createDataChannel',
        category: Node_1.NodeCategory.NETWORK,
        constructor: CreateDataChannelNode,
        label: '创建数据通道',
        description: '在WebRTC连接上创建数据通道',
        icon: 'datachannel',
        color: '#00BCD4',
        tags: ['network', 'webrtc', 'datachannel']
    });
    // 注册数据通道消息事件节点
    registry.registerNodeType({
        type: 'network/webrtc/onDataChannelMessage',
        category: Node_1.NodeCategory.NETWORK,
        constructor: DataChannelMessageEventNode,
        label: '数据通道消息事件',
        description: '监听数据通道消息事件',
        icon: 'message',
        color: '#00BCD4',
        tags: ['network', 'webrtc', 'datachannel', 'message', 'event']
    });
}
exports.registerWebRTCNodes = registerWebRTCNodes;
