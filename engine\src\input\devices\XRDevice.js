"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.XRDevice = void 0;
/**
 * XR输入设备
 */
var InputDevice_1 = require("../InputDevice");
/**
 * XR输入设备
 */
var XRDevice = /** @class */ (function (_super) {
    __extends(XRDevice, _super);
    /**
     * 创建XR输入设备
     */
    function XRDevice() {
        var _this = _super.call(this, 'xr') || this;
        /** XR会话 */
        _this.session = null;
        /** 输入源映射 */
        _this.inputSources = new Map();
        /** XR事件处理器 */
        _this.xrEventHandlers = {};
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    XRDevice.prototype.initEventHandlers = function () {
        // 输入源变化事件
        this.xrEventHandlers.inputsourceschange = this.handleInputSourcesChange.bind(this);
        // 选择开始事件
        this.xrEventHandlers.selectstart = this.handleSelectStart.bind(this);
        // 选择结束事件
        this.xrEventHandlers.selectend = this.handleSelectEnd.bind(this);
        // 选择事件
        this.xrEventHandlers.select = this.handleSelect.bind(this);
        // 挤压开始事件
        this.xrEventHandlers.squeezestart = this.handleSqueezeStart.bind(this);
        // 挤压结束事件
        this.xrEventHandlers.squeezeend = this.handleSqueezeEnd.bind(this);
        // 挤压事件
        this.xrEventHandlers.squeeze = this.handleSqueeze.bind(this);
    };
    /**
     * 初始化设备
     */
    XRDevice.prototype.initialize = function () {
        var _this = this;
        if (this.initialized)
            return;
        // 检查XR支持
        if (navigator.xr) {
            navigator.xr.isSessionSupported('immersive-vr').then(function (supported) {
                if (supported) {
                    _this.eventEmitter.emit('supported', { type: 'immersive-vr' });
                }
            });
            navigator.xr.isSessionSupported('immersive-ar').then(function (supported) {
                if (supported) {
                    _this.eventEmitter.emit('supported', { type: 'immersive-ar' });
                }
            });
        }
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    XRDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 结束XR会话
        this.endSession();
        _super.prototype.destroy.call(this);
    };
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    XRDevice.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
        // 更新输入源状态
        this.updateInputSources();
        _super.prototype.update.call(this, deltaTime);
    };
    /**
     * 更新输入源状态
     */
    XRDevice.prototype.updateInputSources = function () {
        if (!this.session)
            return;
        // 更新输入源状态
        for (var _i = 0, _a = this.session.inputSources; _i < _a.length; _i++) {
            var inputSource = _a[_i];
            // 更新输入源映射
            this.inputSources.set(this.getInputSourceId(inputSource), inputSource);
            // 更新游戏手柄状态
            if (inputSource.gamepad) {
                var gamepad = inputSource.gamepad;
                var inputSourceId = this.getInputSourceId(inputSource);
                // 更新按钮状态
                for (var i = 0; i < gamepad.buttons.length; i++) {
                    var button = gamepad.buttons[i];
                    var buttonKey = "".concat(inputSourceId, ":button:").concat(i);
                    var pressed = button.pressed || button.value > 0.5;
                    var wasPressed = this.getValue(buttonKey) || false;
                    // 更新按钮状态
                    this.setValue(buttonKey, pressed);
                    this.setValue("".concat(inputSourceId, ":button:").concat(i, ":value"), button.value);
                    // 触发按钮事件
                    if (pressed && !wasPressed) {
                        this.eventEmitter.emit("".concat(buttonKey, ":down"), {
                            inputSource: inputSource,
                            button: i,
                            value: button.value
                        });
                    }
                    else if (!pressed && wasPressed) {
                        this.eventEmitter.emit("".concat(buttonKey, ":up"), {
                            inputSource: inputSource,
                            button: i,
                            value: button.value
                        });
                    }
                }
                // 更新轴状态
                for (var i = 0; i < gamepad.axes.length; i++) {
                    var axisKey = "".concat(inputSourceId, ":axis:").concat(i);
                    var value = gamepad.axes[i];
                    var oldValue = this.getValue(axisKey) || 0;
                    // 更新轴状态
                    this.setValue(axisKey, value);
                    // 触发轴事件
                    if (value !== oldValue) {
                        this.eventEmitter.emit("".concat(axisKey, ":change"), {
                            inputSource: inputSource,
                            axis: i,
                            value: value
                        });
                    }
                }
            }
        }
    };
    /**
     * 开始XR会话
     * @param sessionType 会话类型
     * @param sessionOptions 会话选项
     * @returns 是否成功
     */
    XRDevice.prototype.startSession = function (sessionType, sessionOptions) {
        if (sessionType === void 0) { sessionType = 'immersive-vr'; }
        if (sessionOptions === void 0) { sessionOptions = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var supported, _a, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!navigator.xr)
                            return [2 /*return*/, false];
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 4, , 5]);
                        return [4 /*yield*/, navigator.xr.isSessionSupported(sessionType)];
                    case 2:
                        supported = _b.sent();
                        if (!supported)
                            return [2 /*return*/, false];
                        // 请求会话
                        _a = this;
                        return [4 /*yield*/, navigator.xr.requestSession(sessionType, sessionOptions)];
                    case 3:
                        // 请求会话
                        _a.session = _b.sent();
                        // 添加事件监听器
                        this.addEventListeners();
                        // 触发会话开始事件
                        this.eventEmitter.emit('sessionstart', { session: this.session });
                        return [2 /*return*/, true];
                    case 4:
                        error_1 = _b.sent();
                        console.error('Failed to start XR session:', error_1);
                        return [2 /*return*/, false];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 结束XR会话
     */
    XRDevice.prototype.endSession = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.session)
                            return [2 /*return*/];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 移除事件监听器
                        this.removeEventListeners();
                        // 结束会话
                        return [4 /*yield*/, this.session.end()];
                    case 2:
                        // 结束会话
                        _a.sent();
                        // 清空输入源映射
                        this.inputSources.clear();
                        // 触发会话结束事件
                        this.eventEmitter.emit('sessionend', { session: this.session });
                        this.session = null;
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('Failed to end XR session:', error_2);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 添加事件监听器
     */
    XRDevice.prototype.addEventListeners = function () {
        if (!this.session)
            return;
        // 添加XR事件监听器
        for (var _i = 0, _a = Object.entries(this.xrEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            this.session.addEventListener(event_1, handler);
        }
    };
    /**
     * 移除事件监听器
     */
    XRDevice.prototype.removeEventListeners = function () {
        if (!this.session)
            return;
        // 移除XR事件监听器
        for (var _i = 0, _a = Object.entries(this.xrEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_2 = _b[0], handler = _b[1];
            this.session.removeEventListener(event_2, handler);
        }
    };
    /**
     * 处理输入源变化事件
     * @param event 输入源变化事件
     */
    XRDevice.prototype.handleInputSourcesChange = function (event) {
        // 添加新输入源
        for (var _i = 0, _a = event.added; _i < _a.length; _i++) {
            var inputSource = _a[_i];
            var inputSourceId = this.getInputSourceId(inputSource);
            this.inputSources.set(inputSourceId, inputSource);
            this.setValue("".concat(inputSourceId, ":active"), true);
            this.setValue("".concat(inputSourceId, ":handedness"), inputSource.handedness);
            this.setValue("".concat(inputSourceId, ":targetRayMode"), inputSource.targetRayMode);
            // 触发输入源添加事件
            this.eventEmitter.emit('inputsourceadded', { inputSource: inputSource });
        }
        // 移除旧输入源
        for (var _b = 0, _c = event.removed; _b < _c.length; _b++) {
            var inputSource = _c[_b];
            var inputSourceId = this.getInputSourceId(inputSource);
            this.inputSources.delete(inputSourceId);
            this.setValue("".concat(inputSourceId, ":active"), false);
            // 触发输入源移除事件
            this.eventEmitter.emit('inputsourceremoved', { inputSource: inputSource });
        }
    };
    /**
     * 处理选择开始事件
     * @param event 选择事件
     */
    XRDevice.prototype.handleSelectStart = function (event) {
        var inputSource = event.inputSource;
        var inputSourceId = this.getInputSourceId(inputSource);
        this.setValue("".concat(inputSourceId, ":select"), true);
        // 触发选择开始事件
        this.eventEmitter.emit('selectstart', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 处理选择结束事件
     * @param event 选择事件
     */
    XRDevice.prototype.handleSelectEnd = function (event) {
        var inputSource = event.inputSource;
        var inputSourceId = this.getInputSourceId(inputSource);
        this.setValue("".concat(inputSourceId, ":select"), false);
        // 触发选择结束事件
        this.eventEmitter.emit('selectend', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 处理选择事件
     * @param event 选择事件
     */
    XRDevice.prototype.handleSelect = function (event) {
        var inputSource = event.inputSource;
        // 触发选择事件
        this.eventEmitter.emit('select', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 处理挤压开始事件
     * @param event 挤压事件
     */
    XRDevice.prototype.handleSqueezeStart = function (event) {
        var inputSource = event.inputSource;
        var inputSourceId = this.getInputSourceId(inputSource);
        this.setValue("".concat(inputSourceId, ":squeeze"), true);
        // 触发挤压开始事件
        this.eventEmitter.emit('squeezestart', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 处理挤压结束事件
     * @param event 挤压事件
     */
    XRDevice.prototype.handleSqueezeEnd = function (event) {
        var inputSource = event.inputSource;
        var inputSourceId = this.getInputSourceId(inputSource);
        this.setValue("".concat(inputSourceId, ":squeeze"), false);
        // 触发挤压结束事件
        this.eventEmitter.emit('squeezeend', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 处理挤压事件
     * @param event 挤压事件
     */
    XRDevice.prototype.handleSqueeze = function (event) {
        var inputSource = event.inputSource;
        // 触发挤压事件
        this.eventEmitter.emit('squeeze', { inputSource: inputSource, frame: event.frame });
    };
    /**
     * 获取输入源ID
     * @param inputSource 输入源
     * @returns 输入源ID
     */
    XRDevice.prototype.getInputSourceId = function (inputSource) {
        // 使用输入源的索引作为ID
        return Array.from(this.session.inputSources).indexOf(inputSource);
    };
    /**
     * 获取XR会话
     * @returns XR会话
     */
    XRDevice.prototype.getSession = function () {
        return this.session;
    };
    /**
     * 获取输入源
     * @param id 输入源ID
     * @returns 输入源
     */
    XRDevice.prototype.getInputSource = function (id) {
        return this.inputSources.get(id);
    };
    /**
     * 获取所有输入源
     * @returns 输入源列表
     */
    XRDevice.prototype.getInputSources = function () {
        return Array.from(this.inputSources.values());
    };
    /**
     * 检查输入源是否活跃
     * @param id 输入源ID
     * @returns 是否活跃
     */
    XRDevice.prototype.isInputSourceActive = function (id) {
        return !!this.getValue("".concat(id, ":active"));
    };
    /**
     * 检查输入源是否选择中
     * @param id 输入源ID
     * @returns 是否选择中
     */
    XRDevice.prototype.isInputSourceSelecting = function (id) {
        return !!this.getValue("".concat(id, ":select"));
    };
    /**
     * 检查输入源是否挤压中
     * @param id 输入源ID
     * @returns 是否挤压中
     */
    XRDevice.prototype.isInputSourceSqueezing = function (id) {
        return !!this.getValue("".concat(id, ":squeeze"));
    };
    /**
     * 检查按钮是否按下
     * @param inputSourceId 输入源ID
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    XRDevice.prototype.isButtonPressed = function (inputSourceId, buttonIndex) {
        return !!this.getValue("".concat(inputSourceId, ":button:").concat(buttonIndex));
    };
    /**
     * 获取按钮值
     * @param inputSourceId 输入源ID
     * @param buttonIndex 按钮索引
     * @returns 按钮值
     */
    XRDevice.prototype.getButtonValue = function (inputSourceId, buttonIndex) {
        return this.getValue("".concat(inputSourceId, ":button:").concat(buttonIndex, ":value")) || 0;
    };
    /**
     * 获取轴值
     * @param inputSourceId 输入源ID
     * @param axisIndex 轴索引
     * @returns 轴值
     */
    XRDevice.prototype.getAxisValue = function (inputSourceId, axisIndex) {
        return this.getValue("".concat(inputSourceId, ":axis:").concat(axisIndex)) || 0;
    };
    return XRDevice;
}(InputDevice_1.BaseInputDevice));
exports.XRDevice = XRDevice;
