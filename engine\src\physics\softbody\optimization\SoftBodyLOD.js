"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftBodyLOD = exports.LODLevel = void 0;
var SoftBodyComponent_1 = require("../SoftBodyComponent");
/**
 * LOD级别
 */
var LODLevel;
(function (LODLevel) {
    /** 高细节 */
    LODLevel[LODLevel["HIGH"] = 0] = "HIGH";
    /** 中细节 */
    LODLevel[LODLevel["MEDIUM"] = 1] = "MEDIUM";
    /** 低细节 */
    LODLevel[LODLevel["LOW"] = 2] = "LOW";
    /** 极低细节 */
    LODLevel[LODLevel["VERY_LOW"] = 3] = "VERY_LOW";
})(LODLevel || (exports.LODLevel = LODLevel = {}));
/**
 * 软体物理LOD系统
 * 根据与相机的距离动态调整软体物理的细节层次
 */
var SoftBodyLOD = exports.SoftBodyLOD = /** @class */ (function () {
    /**
     * 创建软体物理LOD系统
     * @param options 软体物理LOD系统选项
     */
    function SoftBodyLOD(options) {
        if (options === void 0) { options = {}; }
        /** 相机实体 */
        this.cameraEntity = null;
        /** 软体组件映射 */
        this.softBodies = new Map();
        this.enabled = options.enabled !== undefined ? options.enabled : true;
        this.config = __assign(__assign({}, SoftBodyLOD.DEFAULT_CONFIG), options.config);
        this.cameraEntity = options.cameraEntity || null;
    }
    /**
     * 设置相机实体
     * @param cameraEntity 相机实体
     */
    SoftBodyLOD.prototype.setCameraEntity = function (cameraEntity) {
        this.cameraEntity = cameraEntity;
    };
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    SoftBodyLOD.prototype.addSoftBody = function (softBody) {
        // 初始化为高细节
        this.softBodies.set(softBody, LODLevel.HIGH);
    };
    /**
     * 移除软体组件
     * @param softBody 软体组件
     */
    SoftBodyLOD.prototype.removeSoftBody = function (softBody) {
        this.softBodies.delete(softBody);
    };
    /**
     * 获取软体组件的LOD级别
     * @param softBody 软体组件
     * @returns LOD级别
     */
    SoftBodyLOD.prototype.getLODLevel = function (softBody) {
        return this.softBodies.get(softBody) || LODLevel.HIGH;
    };
    /**
     * 获取LOD级别的迭代次数
     * @param level LOD级别
     * @returns 迭代次数
     */
    SoftBodyLOD.prototype.getIterationsForLevel = function (level) {
        switch (level) {
            case LODLevel.HIGH:
                return this.config.highDetailIterations;
            case LODLevel.MEDIUM:
                return this.config.mediumDetailIterations;
            case LODLevel.LOW:
                return this.config.lowDetailIterations;
            case LODLevel.VERY_LOW:
                return this.config.veryLowDetailIterations;
            default:
                return this.config.highDetailIterations;
        }
    };
    /**
     * 获取LOD级别的网格分辨率
     * @param level LOD级别
     * @returns 网格分辨率
     */
    SoftBodyLOD.prototype.getResolutionForLevel = function (level) {
        switch (level) {
            case LODLevel.HIGH:
                return this.config.highDetailResolution;
            case LODLevel.MEDIUM:
                return this.config.mediumDetailResolution;
            case LODLevel.LOW:
                return this.config.lowDetailResolution;
            case LODLevel.VERY_LOW:
                return this.config.veryLowDetailResolution;
            default:
                return this.config.highDetailResolution;
        }
    };
    /**
     * 更新软体组件的LOD级别
     * @param softBody 软体组件
     */
    SoftBodyLOD.prototype.updateSoftBodyLOD = function (softBody) {
        if (!this.enabled || !this.cameraEntity || !softBody.entity)
            return;
        // 获取相机位置
        var cameraTransform = this.cameraEntity.getTransform();
        if (!cameraTransform)
            return;
        var cameraPosition = cameraTransform.getPosition();
        // 获取软体位置
        var softBodyTransform = softBody.entity.getTransform();
        if (!softBodyTransform)
            return;
        var softBodyPosition = softBodyTransform.getPosition();
        // 计算距离
        var distance = cameraPosition.distanceTo(softBodyPosition);
        // 确定LOD级别
        var newLevel;
        if (distance <= this.config.highDetailDistance) {
            newLevel = LODLevel.HIGH;
        }
        else if (distance <= this.config.mediumDetailDistance) {
            newLevel = LODLevel.MEDIUM;
        }
        else if (distance <= this.config.lowDetailDistance) {
            newLevel = LODLevel.LOW;
        }
        else {
            newLevel = LODLevel.VERY_LOW;
        }
        // 获取当前LOD级别
        var currentLevel = this.softBodies.get(softBody) || LODLevel.HIGH;
        // 如果LOD级别发生变化，更新软体
        if (newLevel !== currentLevel) {
            this.softBodies.set(softBody, newLevel);
            this.applySoftBodyLOD(softBody, newLevel);
        }
    };
    /**
     * 应用软体组件的LOD级别
     * @param softBody 软体组件
     * @param level LOD级别
     */
    SoftBodyLOD.prototype.applySoftBodyLOD = function (softBody, level) {
        // 根据软体类型应用不同的LOD策略
        switch (softBody.getType()) {
            case SoftBodyComponent_1.SoftBodyType.CLOTH:
                this.applyClothLOD(softBody, level);
                break;
            case SoftBodyComponent_1.SoftBodyType.ROPE:
                this.applyRopeLOD(softBody, level);
                break;
            case SoftBodyComponent_1.SoftBodyType.VOLUME:
                this.applyVolumeLOD(softBody, level);
                break;
        }
    };
    /**
     * 应用布料的LOD级别
     * @param softBody 软体组件
     * @param level LOD级别
     */
    SoftBodyLOD.prototype.applyClothLOD = function (softBody, level) {
        // 获取新的网格分辨率
        var resolution = this.getResolutionForLevel(level);
        // 更新布料参数
        // 注意：这需要在SoftBodyComponent中添加updateClothResolution方法
        // softBody.updateClothResolution(resolution.x, resolution.y);
    };
    /**
     * 应用绳索的LOD级别
     * @param softBody 软体组件
     * @param level LOD级别
     */
    SoftBodyLOD.prototype.applyRopeLOD = function (softBody, level) {
        // 获取新的分段数
        var segments = this.getResolutionForLevel(level).x;
        // 更新绳索参数
        // 注意：这需要在SoftBodyComponent中添加updateRopeSegments方法
        // softBody.updateRopeSegments(segments);
    };
    /**
     * 应用体积软体的LOD级别
     * @param softBody 软体组件
     * @param level LOD级别
     */
    SoftBodyLOD.prototype.applyVolumeLOD = function (softBody, level) {
        // 获取新的网格分辨率
        var resolution = this.getResolutionForLevel(level);
        // 更新体积软体参数
        // 注意：这需要在SoftBodyComponent中添加updateVolumeResolution方法
        // softBody.updateVolumeResolution(resolution.x, resolution.y, resolution.x);
    };
    /**
     * 更新所有软体组件
     */
    SoftBodyLOD.prototype.update = function () {
        if (!this.enabled || !this.cameraEntity)
            return;
        for (var _i = 0, _a = this.softBodies.keys(); _i < _a.length; _i++) {
            var softBody = _a[_i];
            this.updateSoftBodyLOD(softBody);
        }
    };
    /**
     * 启用LOD系统
     */
    SoftBodyLOD.prototype.enable = function () {
        this.enabled = true;
    };
    /**
     * 禁用LOD系统
     */
    SoftBodyLOD.prototype.disable = function () {
        this.enabled = false;
    };
    /**
     * 设置LOD配置
     * @param config LOD配置
     */
    SoftBodyLOD.prototype.setConfig = function (config) {
        this.config = __assign(__assign({}, this.config), config);
    };
    /** 默认LOD配置 */
    SoftBodyLOD.DEFAULT_CONFIG = {
        highDetailDistance: 10,
        mediumDetailDistance: 20,
        lowDetailDistance: 30,
        highDetailResolution: { x: 20, y: 20 },
        mediumDetailResolution: { x: 10, y: 10 },
        lowDetailResolution: { x: 5, y: 5 },
        veryLowDetailResolution: { x: 3, y: 3 },
        highDetailIterations: 10,
        mediumDetailIterations: 5,
        lowDetailIterations: 3,
        veryLowDetailIterations: 1
    };
    return SoftBodyLOD;
}());
