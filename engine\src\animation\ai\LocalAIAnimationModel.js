"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalAIAnimationModel = void 0;
/**
 * 本地AI动画模型
 * 使用本地模型生成动画，适用于离线环境
 */
var AnimationClip_1 = require("../AnimationClip");
var EmotionBasedAnimationGenerator_1 = require("./EmotionBasedAnimationGenerator");
/**
 * 本地AI动画模型
 */
var LocalAIAnimationModel = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 配置
     */
    function LocalAIAnimationModel(config) {
        if (config === void 0) { config = {}; }
        /** 是否已初始化 */
        this.initialized = false;
        /** 是否正在初始化 */
        this.initializing = false;
        /** 情感词典 */
        this.emotionDictionary = new Map();
        /** 请求映射 */
        this.requests = new Map();
        this.config = {
            debug: config.debug || false,
            modelPath: config.modelPath || 'models/ai/animation_model.onnx',
            vocabPath: config.vocabPath || 'models/ai/vocab.json',
            batchSize: config.batchSize || 1
        };
        // 创建情感动画生成器
        this.emotionGenerator = new EmotionBasedAnimationGenerator_1.EmotionBasedAnimationGenerator(this, this.config.debug);
        // 初始化情感词典
        this.initEmotionDictionary();
    }
    /**
     * 初始化情感词典
     */
    LocalAIAnimationModel.prototype.initEmotionDictionary = function () {
        // 简单的情感词典
        var emotions = [
            { word: 'happy', emotion: 'happy', score: 0.9 },
            { word: 'glad', emotion: 'happy', score: 0.8 },
            { word: 'joyful', emotion: 'happy', score: 0.9 },
            { word: 'excited', emotion: 'happy', score: 0.8 },
            { word: 'sad', emotion: 'sad', score: 0.9 },
            { word: 'unhappy', emotion: 'sad', score: 0.8 },
            { word: 'depressed', emotion: 'sad', score: 0.9 },
            { word: 'miserable', emotion: 'sad', score: 0.9 },
            { word: 'angry', emotion: 'angry', score: 0.9 },
            { word: 'furious', emotion: 'angry', score: 0.9 },
            { word: 'mad', emotion: 'angry', score: 0.8 },
            { word: 'annoyed', emotion: 'angry', score: 0.7 },
            { word: 'surprised', emotion: 'surprised', score: 0.9 },
            { word: 'shocked', emotion: 'surprised', score: 0.9 },
            { word: 'amazed', emotion: 'surprised', score: 0.8 },
            { word: 'astonished', emotion: 'surprised', score: 0.9 },
            { word: 'afraid', emotion: 'fear', score: 0.9 },
            { word: 'scared', emotion: 'fear', score: 0.9 },
            { word: 'fearful', emotion: 'fear', score: 0.9 },
            { word: 'terrified', emotion: 'fear', score: 0.9 },
            { word: 'disgusted', emotion: 'disgust', score: 0.9 },
            { word: 'repulsed', emotion: 'disgust', score: 0.9 },
            { word: 'neutral', emotion: 'neutral', score: 0.9 },
            { word: 'calm', emotion: 'neutral', score: 0.8 },
            { word: 'relaxed', emotion: 'neutral', score: 0.7 }
        ];
        // 添加到词典
        for (var _i = 0, emotions_1 = emotions; _i < emotions_1.length; _i++) {
            var item = emotions_1[_i];
            this.emotionDictionary.set(item.word, { emotion: item.emotion, score: item.score });
        }
    };
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    LocalAIAnimationModel.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.initialized)
                            return [2 /*return*/, true];
                        if (this.initializing)
                            return [2 /*return*/, false];
                        this.initializing = true;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 模拟模型加载
                        if (this.config.debug) {
                            console.log('正在加载本地AI动画模型...');
                        }
                        // 模拟加载延迟
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 1000); })];
                    case 2:
                        // 模拟加载延迟
                        _a.sent();
                        this.initialized = true;
                        this.initializing = false;
                        if (this.config.debug) {
                            console.log('本地AI动画模型加载完成');
                        }
                        return [2 /*return*/, true];
                    case 3:
                        error_1 = _a.sent();
                        this.initializing = false;
                        if (this.config.debug) {
                            console.error('加载本地AI动画模型失败:', error_1);
                        }
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成身体动画
     * @param request 生成请求
     * @returns 生成结果
     */
    LocalAIAnimationModel.prototype.generateBodyAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var clip, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 存储请求
                        this.requests.set(request.id, request);
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        // 模拟生成过程
                        if (this.config.debug) {
                            console.log('正在生成身体动画:', request);
                        }
                        // 模拟处理延迟
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 500); })];
                    case 4:
                        // 模拟处理延迟
                        _a.sent();
                        clip = new AnimationClip_1.AnimationClip({
                            name: request.prompt,
                            duration: request.duration,
                            loopMode: request.loop ? AnimationClip_1.LoopMode.REPEAT : AnimationClip_1.LoopMode.NONE
                        });
                        // 移除请求
                        this.requests.delete(request.id);
                        return [2 /*return*/, {
                                id: request.id,
                                success: true,
                                clip: clip,
                                generationTime: 500,
                                userData: request.userData
                            }];
                    case 5:
                        error_2 = _a.sent();
                        // 移除请求
                        this.requests.delete(request.id);
                        if (this.config.debug) {
                            console.error('生成身体动画失败:', error_2);
                        }
                        return [2 /*return*/, {
                                id: request.id,
                                success: false,
                                error: error_2 instanceof Error ? error_2.message : String(error_2),
                                userData: request.userData
                            }];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    LocalAIAnimationModel.prototype.generateFacialAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var result, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 存储请求
                        this.requests.set(request.id, request);
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        return [4 /*yield*/, this.emotionGenerator.generateFacialAnimation(request)];
                    case 4:
                        result = _a.sent();
                        // 移除请求
                        this.requests.delete(request.id);
                        return [2 /*return*/, result];
                    case 5:
                        error_3 = _a.sent();
                        // 移除请求
                        this.requests.delete(request.id);
                        if (this.config.debug) {
                            console.error('生成面部动画失败:', error_3);
                        }
                        return [2 /*return*/, {
                                id: request.id,
                                success: false,
                                error: error_3 instanceof Error ? error_3.message : String(error_3),
                                userData: request.userData
                            }];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成组合动画
     * @param request 生成请求
     * @returns 生成结果
     */
    LocalAIAnimationModel.prototype.generateCombinedAnimation = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // 组合动画生成逻辑
                // 目前简单地调用面部动画生成
                return [2 /*return*/, this.generateFacialAnimation(request)];
            });
        });
    };
    /**
     * 分析文本情感
     * @param text 文本
     * @returns 情感分析结果
     */
    LocalAIAnimationModel.prototype.analyzeEmotion = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var words, emotionScores, _i, words_1, word, entry, primaryEmotion, maxScore, _a, _b, _c, emotion, score, totalScore, intensity;
            return __generator(this, function (_d) {
                words = text.toLowerCase().split(/\s+/);
                emotionScores = {
                    'happy': 0,
                    'sad': 0,
                    'angry': 0,
                    'surprised': 0,
                    'fear': 0,
                    'disgust': 0,
                    'neutral': 0.1 // 默认有一点中性情感
                };
                // 计算每个情感的分数
                for (_i = 0, words_1 = words; _i < words_1.length; _i++) {
                    word = words_1[_i];
                    entry = this.emotionDictionary.get(word);
                    if (entry) {
                        emotionScores[entry.emotion] += entry.score;
                    }
                }
                primaryEmotion = 'neutral';
                maxScore = 0;
                for (_a = 0, _b = Object.entries(emotionScores); _a < _b.length; _a++) {
                    _c = _b[_a], emotion = _c[0], score = _c[1];
                    if (score > maxScore) {
                        maxScore = score;
                        primaryEmotion = emotion;
                    }
                }
                totalScore = Object.values(emotionScores).reduce(function (sum, score) { return sum + score; }, 0);
                intensity = totalScore > 0 ? maxScore / totalScore : 0;
                return [2 /*return*/, {
                        primaryEmotion: primaryEmotion,
                        intensity: intensity,
                        scores: emotionScores
                    }];
            });
        });
    };
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    LocalAIAnimationModel.prototype.cancelRequest = function (id) {
        return this.requests.delete(id);
    };
    /**
     * 释放资源
     */
    LocalAIAnimationModel.prototype.dispose = function () {
        this.requests.clear();
        this.emotionDictionary.clear();
        this.initialized = false;
    };
    return LocalAIAnimationModel;
}());
exports.LocalAIAnimationModel = LocalAIAnimationModel;
