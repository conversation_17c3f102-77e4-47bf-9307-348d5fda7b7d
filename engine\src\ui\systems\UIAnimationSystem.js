"use strict";
/**
 * UIAnimationSystem.ts
 *
 * UI动画系统，管理UI元素的动画效果
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIAnimationSystem = void 0;
var System_1 = require("../../core/System");
var UIAnimationComponent_1 = require("../components/UIAnimationComponent");
var IUIElement_1 = require("../interfaces/IUIElement");
/**
 * UI动画系统
 * 管理UI元素的动画效果
 */
var UIAnimationSystem = /** @class */ (function (_super) {
    __extends(UIAnimationSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config UI动画系统配置
     */
    function UIAnimationSystem(world, uiSystem, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级（默认为0）
        _super.call(this, 0) || this;
        // 动画组件列表
        _this.animationComponents = new Map();
        // 保存世界引用
        _this.world = world;
        _this.uiSystem = uiSystem;
        _this.config = {
            debug: config.debug || false,
            defaultDuration: config.defaultDuration || 500,
            defaultDelay: config.defaultDelay || 0,
            defaultEasing: config.defaultEasing || UIAnimationComponent_1.UIEasing.cubicInOut
        };
        return _this;
    }
    /**
     * 注册动画组件
     * @param entity 实体
     * @param component 动画组件
     */
    UIAnimationSystem.prototype.registerAnimationComponent = function (entity, component) {
        this.animationComponents.set(entity, component);
        this.uiSystem.registerUIAnimationComponent(entity, component);
    };
    /**
     * 注销动画组件
     * @param entity 实体
     */
    UIAnimationSystem.prototype.unregisterAnimationComponent = function (entity) {
        this.animationComponents.delete(entity);
        this.uiSystem.unregisterUIAnimationComponent(entity);
    };
    /**
     * 获取或创建动画组件
     * @param entity 实体
     * @returns 动画组件
     */
    UIAnimationSystem.prototype.getOrCreateAnimationComponent = function (entity) {
        var component = this.animationComponents.get(entity);
        if (!component) {
            component = new UIAnimationComponent_1.UIAnimationComponent(entity);
            this.registerAnimationComponent(entity, component);
        }
        return component;
    };
    /**
     * 创建淡入动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    UIAnimationSystem.prototype.createFadeInAnimation = function (entity, uiComponent, duration, delay, easing) {
        var _this = this;
        if (duration === void 0) { duration = this.config.defaultDuration; }
        if (delay === void 0) { delay = this.config.defaultDelay; }
        if (easing === void 0) { easing = this.config.defaultEasing; }
        var animationComponent = this.getOrCreateAnimationComponent(entity);
        var animation = animationComponent.createAnimation(IUIElement_1.UIAnimationType.FADE, uiComponent, 'opacity', 0, 1, duration, {
            delay: delay,
            easing: easing,
            onComplete: function () {
                if (_this.config.debug) {
                    console.log("Fade in animation completed for entity ".concat(entity.id));
                }
            }
        });
        animation.start();
        return animation;
    };
    /**
     * 创建淡出动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    UIAnimationSystem.prototype.createFadeOutAnimation = function (entity, uiComponent, duration, delay, easing) {
        var _this = this;
        if (duration === void 0) { duration = this.config.defaultDuration; }
        if (delay === void 0) { delay = this.config.defaultDelay; }
        if (easing === void 0) { easing = this.config.defaultEasing; }
        var animationComponent = this.getOrCreateAnimationComponent(entity);
        var animation = animationComponent.createAnimation(IUIElement_1.UIAnimationType.FADE, uiComponent, 'opacity', uiComponent.opacity, 0, duration, {
            delay: delay,
            easing: easing,
            onComplete: function () {
                if (_this.config.debug) {
                    console.log("Fade out animation completed for entity ".concat(entity.id));
                }
            }
        });
        animation.start();
        return animation;
    };
    /**
     * 创建移动动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param to 目标位置
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    UIAnimationSystem.prototype.createMoveAnimation = function (entity, uiComponent, to, duration, delay, easing) {
        var _this = this;
        if (duration === void 0) { duration = this.config.defaultDuration; }
        if (delay === void 0) { delay = this.config.defaultDelay; }
        if (easing === void 0) { easing = this.config.defaultEasing; }
        var animationComponent = this.getOrCreateAnimationComponent(entity);
        var animation = animationComponent.createAnimation(IUIElement_1.UIAnimationType.MOVE, uiComponent, 'position', uiComponent.position.clone(), to, duration, {
            delay: delay,
            easing: easing,
            onComplete: function () {
                if (_this.config.debug) {
                    console.log("Move animation completed for entity ".concat(entity.id));
                }
            }
        });
        animation.start();
        return animation;
    };
    /**
     * 创建缩放动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param to 目标尺寸
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    UIAnimationSystem.prototype.createScaleAnimation = function (entity, uiComponent, to, duration, delay, easing) {
        var _this = this;
        if (duration === void 0) { duration = this.config.defaultDuration; }
        if (delay === void 0) { delay = this.config.defaultDelay; }
        if (easing === void 0) { easing = this.config.defaultEasing; }
        var animationComponent = this.getOrCreateAnimationComponent(entity);
        var animation = animationComponent.createAnimation(IUIElement_1.UIAnimationType.SCALE, uiComponent, 'size', uiComponent.size.clone(), to, duration, {
            delay: delay,
            easing: easing,
            onComplete: function () {
                if (_this.config.debug) {
                    console.log("Scale animation completed for entity ".concat(entity.id));
                }
            }
        });
        animation.start();
        return animation;
    };
    /**
     * 创建颜色动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param property 目标属性
     * @param to 目标颜色
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    UIAnimationSystem.prototype.createColorAnimation = function (entity, uiComponent, property, to, duration, delay, easing) {
        var _this = this;
        if (duration === void 0) { duration = this.config.defaultDuration; }
        if (delay === void 0) { delay = this.config.defaultDelay; }
        if (easing === void 0) { easing = this.config.defaultEasing; }
        var animationComponent = this.getOrCreateAnimationComponent(entity);
        // 获取当前颜色
        var from;
        switch (property) {
            case 'backgroundColor':
                from = uiComponent.backgroundColor || '#000000';
                break;
            case 'borderColor':
                from = uiComponent.borderColor || '#000000';
                break;
            case 'fontColor':
                from = uiComponent.textColor || uiComponent.fontColor || '#000000';
                break;
        }
        var animation = animationComponent.createAnimation(IUIElement_1.UIAnimationType.COLOR, uiComponent, property, from, to, duration, {
            delay: delay,
            easing: easing,
            onComplete: function () {
                if (_this.config.debug) {
                    console.log("Color animation completed for entity ".concat(entity.id));
                }
            }
        });
        animation.start();
        return animation;
    };
    /**
     * 创建序列动画
     * @param _entity 实体 - 未使用，但保留以保持API一致性
     * @param animations 动画列表
     * @returns 动画列表
     */
    UIAnimationSystem.prototype.createSequenceAnimation = function (_entity, animations) {
        if (animations.length === 0)
            return [];
        var _loop_1 = function (i) {
            var currentAnimation = animations[i];
            var nextAnimation = animations[i + 1];
            currentAnimation.onComplete = function () {
                nextAnimation.start();
            };
        };
        // 设置动画序列
        for (var i = 0; i < animations.length - 1; i++) {
            _loop_1(i);
        }
        // 启动第一个动画
        animations[0].start();
        return animations;
    };
    /**
     * 创建并行动画
     * @param _entity 实体 - 未使用，但保留以保持API一致性
     * @param animations 动画列表
     * @returns 动画列表
     */
    UIAnimationSystem.prototype.createParallelAnimation = function (_entity, animations) {
        // 启动所有动画
        for (var _i = 0, animations_1 = animations; _i < animations_1.length; _i++) {
            var animation = animations_1[_i];
            animation.start();
        }
        return animations;
    };
    /**
     * 停止所有动画
     * @param entity 实体
     */
    UIAnimationSystem.prototype.stopAllAnimations = function (entity) {
        var animationComponent = this.animationComponents.get(entity);
        if (animationComponent) {
            animationComponent.clearAnimations();
        }
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    UIAnimationSystem.prototype.update = function (deltaTime) {
        // 更新所有动画组件
        for (var _i = 0, _a = this.animationComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], component = _b[1];
            component.update(deltaTime);
        }
    };
    /**
     * 销毁系统
     */
    UIAnimationSystem.prototype.dispose = function () {
        // 清空动画组件列表
        this.animationComponents.clear();
    };
    return UIAnimationSystem;
}(System_1.System));
exports.UIAnimationSystem = UIAnimationSystem;
