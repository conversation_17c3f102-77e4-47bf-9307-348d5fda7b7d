"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeRegistry = void 0;
/**
 * 视觉脚本节点注册表
 * 用于注册和管理节点类型
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
var Node_1 = require("./Node");
/**
 * 节点注册表
 * 用于注册和管理节点类型
 */
var NodeRegistry = /** @class */ (function (_super) {
    __extends(NodeRegistry, _super);
    /**
     * 创建节点注册表
     */
    function NodeRegistry() {
        var _this = _super.call(this) || this;
        /** 节点类型映射 */
        _this.nodeTypes = new Map();
        /** 节点类别映射 */
        _this.categories = new Map();
        /** 节点标签映射 */
        _this.tags = new Map();
        // 初始化类别映射
        for (var _i = 0, _a = Object.values(Node_1.NodeCategory); _i < _a.length; _i++) {
            var category = _a[_i];
            _this.categories.set(category, new Set());
        }
        return _this;
    }
    /**
     * 注册节点类型
     * @param info 节点类型信息
     * @returns 是否注册成功
     */
    NodeRegistry.prototype.registerNodeType = function (info) {
        // 检查是否已存在
        if (this.nodeTypes.has(info.type)) {
            console.warn("\u8282\u70B9\u7C7B\u578B\u5DF2\u5B58\u5728: ".concat(info.type));
            return false;
        }
        // 注册节点类型
        this.nodeTypes.set(info.type, info);
        // 添加到类别映射
        var categorySet = this.categories.get(info.category) || new Set();
        categorySet.add(info.type);
        this.categories.set(info.category, categorySet);
        // 添加到标签映射
        if (info.tags) {
            for (var _i = 0, _a = info.tags; _i < _a.length; _i++) {
                var tag = _a[_i];
                var tagSet = this.tags.get(tag) || new Set();
                tagSet.add(info.type);
                this.tags.set(tag, tagSet);
            }
        }
        // 触发注册事件
        this.emit('nodeTypeRegistered', info);
        return true;
    };
    /**
     * 注销节点类型
     * @param type 节点类型名称
     * @returns 是否注销成功
     */
    NodeRegistry.prototype.unregisterNodeType = function (type) {
        // 检查是否存在
        var info = this.nodeTypes.get(type);
        if (!info) {
            return false;
        }
        // 从类别映射中移除
        var categorySet = this.categories.get(info.category);
        if (categorySet) {
            categorySet.delete(type);
        }
        // 从标签映射中移除
        if (info.tags) {
            for (var _i = 0, _a = info.tags; _i < _a.length; _i++) {
                var tag = _a[_i];
                var tagSet = this.tags.get(tag);
                if (tagSet) {
                    tagSet.delete(type);
                    // 如果标签集合为空，移除标签
                    if (tagSet.size === 0) {
                        this.tags.delete(tag);
                    }
                }
            }
        }
        // 从节点类型映射中移除
        this.nodeTypes.delete(type);
        // 触发注销事件
        this.emit('nodeTypeUnregistered', info);
        return true;
    };
    /**
     * 获取节点类型
     * @param type 节点类型名称
     * @returns 节点构造函数
     */
    NodeRegistry.prototype.getNodeType = function (type) {
        var info = this.nodeTypes.get(type);
        return info ? info.constructor : undefined;
    };
    /**
     * 获取节点类型信息
     * @param type 节点类型名称
     * @returns 节点类型信息
     */
    NodeRegistry.prototype.getNodeTypeInfo = function (type) {
        return this.nodeTypes.get(type);
    };
    /**
     * 获取所有节点类型
     * @returns 节点类型信息列表
     */
    NodeRegistry.prototype.getAllNodeTypes = function () {
        return Array.from(this.nodeTypes.values());
    };
    /**
     * 获取指定类别的节点类型
     * @param category 节点类别
     * @returns 节点类型信息列表
     */
    NodeRegistry.prototype.getNodeTypesByCategory = function (category) {
        var _this = this;
        var categorySet = this.categories.get(category);
        if (!categorySet) {
            return [];
        }
        return Array.from(categorySet).map(function (type) { return _this.nodeTypes.get(type); });
    };
    /**
     * 获取指定标签的节点类型
     * @param tag 标签
     * @returns 节点类型信息列表
     */
    NodeRegistry.prototype.getNodeTypesByTag = function (tag) {
        var _this = this;
        var tagSet = this.tags.get(tag);
        if (!tagSet) {
            return [];
        }
        return Array.from(tagSet).map(function (type) { return _this.nodeTypes.get(type); });
    };
    /**
     * 获取所有类别
     * @returns 类别列表
     */
    NodeRegistry.prototype.getAllCategories = function () {
        return Array.from(this.categories.keys());
    };
    /**
     * 获取所有标签
     * @returns 标签列表
     */
    NodeRegistry.prototype.getAllTags = function () {
        return Array.from(this.tags.keys());
    };
    /**
     * 搜索节点类型
     * @param query 搜索查询
     * @returns 节点类型信息列表
     */
    NodeRegistry.prototype.searchNodeTypes = function (query) {
        if (!query) {
            return this.getAllNodeTypes();
        }
        var lowerQuery = query.toLowerCase();
        return this.getAllNodeTypes().filter(function (info) {
            // 匹配类型名称
            if (info.type.toLowerCase().includes(lowerQuery)) {
                return true;
            }
            // 匹配标签
            if (info.label && info.label.toLowerCase().includes(lowerQuery)) {
                return true;
            }
            // 匹配描述
            if (info.description && info.description.toLowerCase().includes(lowerQuery)) {
                return true;
            }
            // 匹配标签
            if (info.tags && info.tags.some(function (tag) { return tag.toLowerCase().includes(lowerQuery); })) {
                return true;
            }
            return false;
        });
    };
    /**
     * 清空注册表
     */
    NodeRegistry.prototype.clear = function () {
        // 清空节点类型映射
        this.nodeTypes.clear();
        // 清空类别映射
        for (var _i = 0, _a = Object.values(Node_1.NodeCategory); _i < _a.length; _i++) {
            var category = _a[_i];
            this.categories.set(category, new Set());
        }
        // 清空标签映射
        this.tags.clear();
        // 触发清空事件
        this.emit('cleared');
    };
    return NodeRegistry;
}(EventEmitter_1.EventEmitter));
exports.NodeRegistry = NodeRegistry;
