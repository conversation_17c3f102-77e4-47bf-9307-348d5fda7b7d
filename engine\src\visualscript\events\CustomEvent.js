"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomEvent = void 0;
/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
var CustomEvent = /** @class */ (function (_super) {
    __extends(CustomEvent, _super);
    /**
     * 创建自定义事件
     * @param options 事件选项
     */
    function CustomEvent(options) {
        var _this = _super.call(this) || this;
        _this.id = options.id;
        _this._name = options.name;
        _this._parameterTypes = options.parameterTypes || [];
        _this._description = options.description || '';
        return _this;
    }
    Object.defineProperty(CustomEvent.prototype, "name", {
        /**
         * 获取事件名称
         * @returns 事件名称
         */
        get: function () {
            return this._name;
        },
        /**
         * 设置事件名称
         * @param value 事件名称
         */
        set: function (value) {
            if (this._name !== value) {
                var oldValue = this._name;
                this._name = value;
                this.emit('nameChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CustomEvent.prototype, "parameterTypes", {
        /**
         * 获取事件参数类型
         * @returns 事件参数类型
         */
        get: function () {
            return __spreadArray([], this._parameterTypes, true);
        },
        /**
         * 设置事件参数类型
         * @param value 事件参数类型
         */
        set: function (value) {
            var oldValue = __spreadArray([], this._parameterTypes, true);
            this._parameterTypes = __spreadArray([], value, true);
            this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CustomEvent.prototype, "description", {
        /**
         * 获取事件描述
         * @returns 事件描述
         */
        get: function () {
            return this._description;
        },
        /**
         * 设置事件描述
         * @param value 事件描述
         */
        set: function (value) {
            if (this._description !== value) {
                var oldValue = this._description;
                this._description = value;
                this.emit('descriptionChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 添加参数类型
     * @param type 参数类型
     * @returns 是否添加成功
     */
    CustomEvent.prototype.addParameterType = function (type) {
        if (this._parameterTypes.includes(type)) {
            return false;
        }
        var oldValue = __spreadArray([], this._parameterTypes, true);
        this._parameterTypes.push(type);
        this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
        return true;
    };
    /**
     * 移除参数类型
     * @param type 参数类型
     * @returns 是否移除成功
     */
    CustomEvent.prototype.removeParameterType = function (type) {
        var index = this._parameterTypes.indexOf(type);
        if (index === -1) {
            return false;
        }
        var oldValue = __spreadArray([], this._parameterTypes, true);
        this._parameterTypes.splice(index, 1);
        this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
        return true;
    };
    /**
     * 触发事件
     * @param args 事件参数
     */
    CustomEvent.prototype.trigger = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        // 检查参数类型
        if (args.length !== this._parameterTypes.length) {
            console.warn("\u53C2\u6570\u6570\u91CF\u4E0D\u5339\u914D: \u671F\u671B ".concat(this._parameterTypes.length, " \u4E2A\u53C2\u6570\uFF0C\u5B9E\u9645 ").concat(args.length, " \u4E2A\u53C2\u6570"));
        }
        // 触发事件
        this.emit.apply(this, __spreadArray(['triggered'], args, false));
    };
    /**
     * 克隆事件
     * @returns 克隆的事件
     */
    CustomEvent.prototype.clone = function () {
        return new CustomEvent({
            id: this.id,
            name: this._name,
            parameterTypes: __spreadArray([], this._parameterTypes, true),
            description: this._description
        });
    };
    /**
     * 序列化事件
     * @returns 序列化数据
     */
    CustomEvent.prototype.serialize = function () {
        return {
            id: this.id,
            name: this._name,
            parameterTypes: __spreadArray([], this._parameterTypes, true),
            description: this._description
        };
    };
    /**
     * 反序列化事件
     * @param data 序列化数据
     */
    CustomEvent.prototype.deserialize = function (data) {
        if (data.name !== undefined) {
            this._name = data.name;
        }
        if (data.parameterTypes !== undefined) {
            this._parameterTypes = __spreadArray([], data.parameterTypes, true);
        }
        if (data.description !== undefined) {
            this._description = data.description;
        }
    };
    return CustomEvent;
}(EventEmitter_1.EventEmitter));
exports.CustomEvent = CustomEvent;
