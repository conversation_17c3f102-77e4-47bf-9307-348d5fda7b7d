"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionRecorder = void 0;
var EventEmitter_1 = require("../../utils/EventEmitter");
var Debug_1 = require("../../utils/Debug");
/**
 * 动作录制器
 */
var ActionRecorder = /** @class */ (function () {
    /**
     * 构造函数
     * @param entity 实体
     * @param actionControlSystem 动作控制系统
     * @param config 配置
     * @param characterController 角色控制器（可选）
     */
    function ActionRecorder(entity, actionControlSystem, config, characterController) {
        if (config === void 0) { config = {}; }
        /** 角色控制器 */
        this.characterController = null;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 是否正在录制 */
        this.isRecording = false;
        /** 当前录制 */
        this.currentRecording = null;
        /** 变换记录定时器 */
        this.transformRecordTimer = null;
        /** 动作开始事件处理器 */
        this.actionStartHandler = null;
        /** 动作结束事件处理器 */
        this.actionEndHandler = null;
        /** 输入事件处理器 */
        this.inputHandler = null;
        this.entity = entity;
        this.actionControlSystem = actionControlSystem;
        this.config = __assign({ debug: false, recordInput: true, recordTransform: true, transformRecordFrequency: 100, autoStart: false }, config);
        // 设置角色控制器
        this.characterController = characterController || null;
        // 如果配置为自动开始，则开始录制
        if (this.config.autoStart) {
            this.startRecording();
        }
    }
    /**
     * 设置角色控制器
     * @param characterController 角色控制器
     */
    ActionRecorder.prototype.setCharacterController = function (characterController) {
        this.characterController = characterController;
    };
    /**
     * 获取角色控制器
     * @returns 角色控制器
     */
    ActionRecorder.prototype.getCharacterController = function () {
        return this.characterController;
    };
    /**
     * 开始录制
     * @param name 录制名称
     * @returns 是否成功开始录制
     */
    ActionRecorder.prototype.startRecording = function (name) {
        if (name === void 0) { name = '未命名录制'; }
        if (this.isRecording) {
            if (this.config.debug) {
                Debug_1.Debug.warn('动作录制器已经在录制中');
            }
            return false;
        }
        this.isRecording = true;
        this.currentRecording = {
            id: "recording_".concat(Date.now()),
            name: name,
            startTimestamp: Date.now(),
            endTimestamp: 0,
            actionEvents: [],
            inputEvents: [],
            transformEvents: []
        };
        // 添加事件监听器
        this.addEventListeners();
        // 开始记录变换
        if (this.config.recordTransform) {
            this.startRecordingTransform();
        }
        if (this.config.debug) {
            Debug_1.Debug.log("\u5F00\u59CB\u5F55\u5236\u52A8\u4F5C: ".concat(name));
        }
        // 触发录制开始事件
        this.eventEmitter.emit('recordingStart', this.currentRecording);
        return true;
    };
    /**
     * 停止录制
     * @returns 录制数据
     */
    ActionRecorder.prototype.stopRecording = function () {
        if (!this.isRecording || !this.currentRecording) {
            if (this.config.debug) {
                Debug_1.Debug.warn('没有正在进行的录制');
            }
            return null;
        }
        this.isRecording = false;
        this.currentRecording.endTimestamp = Date.now();
        // 移除事件监听器
        this.removeEventListeners();
        // 停止记录变换
        if (this.transformRecordTimer !== null) {
            clearInterval(this.transformRecordTimer);
            this.transformRecordTimer = null;
        }
        var recording = this.currentRecording;
        this.currentRecording = null;
        if (this.config.debug) {
            Debug_1.Debug.log("\u505C\u6B62\u5F55\u5236\u52A8\u4F5C: ".concat(recording.name), recording);
        }
        // 触发录制结束事件
        this.eventEmitter.emit('recordingEnd', recording);
        return recording;
    };
    /**
     * 添加事件监听器
     */
    ActionRecorder.prototype.addEventListeners = function () {
        var _this = this;
        // 监听动作开始事件
        this.actionStartHandler = function (data) {
            if (!_this.isRecording || !_this.currentRecording || data.entity !== _this.entity)
                return;
            var event = {
                actionId: data.action.id,
                actionData: __assign({}, data.action),
                eventType: 'start',
                timestamp: Date.now(),
                params: data.action.params
            };
            _this.currentRecording.actionEvents.push(event);
            if (_this.config.debug) {
                Debug_1.Debug.log("\u8BB0\u5F55\u52A8\u4F5C\u5F00\u59CB: ".concat(data.action.id));
            }
        };
        // 监听动作结束事件
        this.actionEndHandler = function (data) {
            if (!_this.isRecording || !_this.currentRecording || data.entity !== _this.entity)
                return;
            var event = {
                actionId: data.action.id,
                actionData: __assign({}, data.action),
                eventType: 'stop',
                timestamp: Date.now(),
                params: data.action.params
            };
            _this.currentRecording.actionEvents.push(event);
            if (_this.config.debug) {
                Debug_1.Debug.log("\u8BB0\u5F55\u52A8\u4F5C\u7ED3\u675F: ".concat(data.action.id));
            }
        };
        // 添加事件监听器
        this.actionControlSystem.on('actionStart', this.actionStartHandler);
        this.actionControlSystem.on('actionEnd', this.actionEndHandler);
        // 如果需要记录输入，添加输入事件监听器
        if (this.config.recordInput && this.characterController) {
            // 实现输入事件监听
        }
    };
    /**
     * 移除事件监听器
     */
    ActionRecorder.prototype.removeEventListeners = function () {
        if (this.actionStartHandler) {
            this.actionControlSystem.off('actionStart', this.actionStartHandler);
            this.actionStartHandler = null;
        }
        if (this.actionEndHandler) {
            this.actionControlSystem.off('actionEnd', this.actionEndHandler);
            this.actionEndHandler = null;
        }
        if (this.inputHandler && this.characterController) {
            // 移除输入事件监听器
            this.inputHandler = null;
        }
    };
    /**
     * 开始记录变换
     */
    ActionRecorder.prototype.startRecordingTransform = function () {
        var _this = this;
        if (this.transformRecordTimer !== null) {
            clearInterval(this.transformRecordTimer);
        }
        this.transformRecordTimer = window.setInterval(function () {
            _this.recordTransform();
        }, this.config.transformRecordFrequency);
    };
    /**
     * 记录变换
     */
    ActionRecorder.prototype.recordTransform = function () {
        if (!this.isRecording || !this.currentRecording)
            return;
        var transform = this.entity.getTransform();
        if (!transform)
            return;
        var position = transform.getPosition();
        var rotation = transform.getRotationQuaternion();
        var event = {
            position: {
                x: position.x,
                y: position.y,
                z: position.z
            },
            rotation: {
                x: rotation.x,
                y: rotation.y,
                z: rotation.z,
                w: rotation.w
            },
            timestamp: Date.now()
        };
        this.currentRecording.transformEvents.push(event);
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionRecorder.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionRecorder.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 保存录制到文件
     * @param recording 录制数据
     * @param filename 文件名
     */
    ActionRecorder.saveToFile = function (recording, filename) {
        if (filename === void 0) { filename = 'action-recording.json'; }
        var json = JSON.stringify(recording, null, 2);
        var blob = new Blob([json], { type: 'application/json' });
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };
    /**
     * 从文件加载录制
     * @param file 文件
     * @returns Promise<ActionRecording>
     */
    ActionRecorder.loadFromFile = function (file) {
        return new Promise(function (resolve, reject) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var _a;
                try {
                    var json = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;
                    var recording = JSON.parse(json);
                    resolve(recording);
                }
                catch (error) {
                    reject(error);
                }
            };
            reader.onerror = function (error) { return reject(error); };
            reader.readAsText(file);
        });
    };
    return ActionRecorder;
}());
exports.ActionRecorder = ActionRecorder;
