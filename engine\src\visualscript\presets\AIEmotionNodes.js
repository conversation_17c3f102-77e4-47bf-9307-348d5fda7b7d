"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAIEmotionNodes = exports.EmotionDrivenAnimationNode = exports.EmotionAnalysisNode = void 0;
var FlowNode_1 = require("../nodes/FlowNode");
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var AIEmotionAnalysisSystem_1 = require("../../ai/AIEmotionAnalysisSystem");
var EmotionType_1 = require("../../ai/EmotionType");
var AnimatorComponent_1 = require("../../animation/AnimatorComponent");
var FacialAnimationComponent_1 = require("../../animation/FacialAnimationComponent");
/**
 * 情感分析节点
 * 分析文本的情感
 */
var EmotionAnalysisNode = /** @class */ (function (_super) {
    __extends(EmotionAnalysisNode, _super);
    function EmotionAnalysisNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 请求ID */
        _this.requestId = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    EmotionAnalysisNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要分析的文本',
            defaultValue: ''
        });
        this.addInput({
            name: 'detailed',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否返回详细结果',
            defaultValue: false
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分析成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分析失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'emotion',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '主要情感'
        });
        this.addOutput({
            name: 'intensity',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '情感强度'
        });
        this.addOutput({
            name: 'detailedResult',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '详细分析结果'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    EmotionAnalysisNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var text, detailed, emotionSystem, result, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        text = this.getInputValue('text');
                        detailed = this.getInputValue('detailed');
                        // 检查输入值是否有效
                        if (!text) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        emotionSystem = this.graph.getWorld().getSystem(AIEmotionAnalysisSystem_1.AIEmotionAnalysisSystem);
                        if (!emotionSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, emotionSystem.analyzeEmotion(text, { detailed: detailed })];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('emotion', result.primaryEmotion);
                            this.setOutputValue('intensity', result.intensity);
                            if (detailed) {
                                this.setOutputValue('detailedResult', result.detailedEmotions);
                            }
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('情感分析失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return EmotionAnalysisNode;
}(AsyncNode_1.AsyncNode));
exports.EmotionAnalysisNode = EmotionAnalysisNode;
/**
 * 情感驱动动画节点
 * 根据情感分析结果驱动角色动画
 */
var EmotionDrivenAnimationNode = /** @class */ (function (_super) {
    __extends(EmotionDrivenAnimationNode, _super);
    function EmotionDrivenAnimationNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    EmotionDrivenAnimationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标实体'
        });
        this.addInput({
            name: 'emotion',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '情感类型'
        });
        this.addInput({
            name: 'intensity',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '情感强度',
            defaultValue: 1.0
        });
        this.addInput({
            name: 'duration',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '持续时间（秒）',
            defaultValue: 3.0
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    EmotionDrivenAnimationNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var emotion = this.getInputValue('emotion');
        var intensity = this.getInputValue('intensity');
        var duration = this.getInputValue('duration');
        // 检查输入值是否有效
        if (!entity || !emotion) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        try {
            // 获取面部动画组件
            var facialComponent = entity.getComponent(FacialAnimationComponent_1.FacialAnimationComponent.TYPE);
            // 获取动画器组件
            var animatorComponent = entity.getComponent(AnimatorComponent_1.AnimatorComponent.TYPE);
            var success = false;
            // 应用面部表情
            if (facialComponent) {
                facialComponent.setEmotion(emotion, intensity, duration);
                success = true;
            }
            // 应用身体动画
            if (animatorComponent) {
                // 根据情感类型选择合适的动画
                var animationName = this.getAnimationForEmotion(emotion, intensity);
                if (animationName) {
                    animatorComponent.play(animationName, {
                        duration: duration,
                        weight: intensity,
                        fadeIn: 0.3,
                        fadeOut: 0.3
                    });
                    success = true;
                }
            }
            // 设置输出值
            this.setOutputValue('success', success);
            // 触发输出流程
            this.triggerFlow('flow');
            return success;
        }
        catch (error) {
            console.error('应用情感动画失败:', error);
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
    };
    /**
     * 根据情感类型获取动画名称
     * @param emotion 情感类型
     * @param intensity 情感强度
     * @returns 动画名称
     */
    EmotionDrivenAnimationNode.prototype.getAnimationForEmotion = function (emotion, intensity) {
        // 根据情感类型和强度选择合适的动画
        switch (emotion) {
            case EmotionType_1.EmotionType.HAPPY:
                return intensity > 0.7 ? 'happy_high' : 'happy_low';
            case EmotionType_1.EmotionType.SAD:
                return intensity > 0.7 ? 'sad_high' : 'sad_low';
            case EmotionType_1.EmotionType.ANGRY:
                return intensity > 0.7 ? 'angry_high' : 'angry_low';
            case EmotionType_1.EmotionType.SURPRISED:
                return 'surprised';
            case EmotionType_1.EmotionType.FEARFUL:
                return 'fearful';
            case EmotionType_1.EmotionType.DISGUSTED:
                return 'disgusted';
            case EmotionType_1.EmotionType.NEUTRAL:
                return 'idle';
            default:
                return null;
        }
    };
    return EmotionDrivenAnimationNode;
}(FlowNode_1.FlowNode));
exports.EmotionDrivenAnimationNode = EmotionDrivenAnimationNode;
/**
 * 注册AI情感节点
 * @param registry 节点注册表
 */
function registerAIEmotionNodes(registry) {
    // 注册情感分析节点
    registry.registerNodeType({
        type: 'ai/emotion/analyze',
        category: Node_1.NodeCategory.AI,
        constructor: EmotionAnalysisNode,
        label: '情感分析',
        description: '分析文本的情感',
        icon: 'emotion',
        color: '#673AB7',
        tags: ['ai', 'emotion', 'analysis']
    });
    // 注册情感驱动动画节点
    registry.registerNodeType({
        type: 'ai/emotion/driveAnimation',
        category: Node_1.NodeCategory.AI,
        constructor: EmotionDrivenAnimationNode,
        label: '情感驱动动画',
        description: '根据情感分析结果驱动角色动画',
        icon: 'animation',
        color: '#673AB7',
        tags: ['ai', 'emotion', 'animation']
    });
}
exports.registerAIEmotionNodes = registerAIEmotionNodes;
