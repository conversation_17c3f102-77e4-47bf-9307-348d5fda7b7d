"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionPlayback = void 0;
var EventEmitter_1 = require("../../utils/EventEmitter");
var Debug_1 = require("../../utils/Debug");
/**
 * 动作回放器
 */
var ActionPlayback = /** @class */ (function () {
    /**
     * 构造函数
     * @param entity 实体
     * @param actionControlSystem 动作控制系统
     * @param config 配置
     * @param characterController 角色控制器（可选）
     */
    function ActionPlayback(entity, actionControlSystem, config, characterController) {
        if (config === void 0) { config = {}; }
        /** 角色控制器 */
        this.characterController = null;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 是否正在回放 */
        this.isPlaying = false;
        /** 是否暂停 */
        this.isPaused = false;
        /** 当前录制 */
        this.recording = null;
        /** 回放开始时间戳 */
        this.playbackStartTimestamp = 0;
        /** 回放暂停时间戳 */
        this.playbackPauseTimestamp = 0;
        /** 回放暂停总时长 */
        this.totalPauseDuration = 0;
        /** 动作事件索引 */
        this.actionEventIndex = 0;
        /** 输入事件索引 */
        this.inputEventIndex = 0;
        /** 变换事件索引 */
        this.transformEventIndex = 0;
        /** 回放完成回调 */
        this.onCompleteCallback = null;
        /** 动画帧请求ID */
        this.animationFrameId = null;
        this.entity = entity;
        this.actionControlSystem = actionControlSystem;
        this.config = __assign({ debug: false, playbackSpeed: 1.0, playbackInput: true, playbackTransform: true, loop: false, autoPlay: false }, config);
        // 设置角色控制器
        this.characterController = characterController || null;
    }
    /**
     * 设置角色控制器
     * @param characterController 角色控制器
     */
    ActionPlayback.prototype.setCharacterController = function (characterController) {
        this.characterController = characterController;
    };
    /**
     * 获取角色控制器
     * @returns 角色控制器
     */
    ActionPlayback.prototype.getCharacterController = function () {
        return this.characterController;
    };
    /**
     * 加载录制
     * @param recording 录制数据
     * @returns 是否成功加载
     */
    ActionPlayback.prototype.loadRecording = function (recording) {
        if (this.isPlaying) {
            this.stopPlayback();
        }
        this.recording = recording;
        this.resetPlaybackState();
        if (this.config.debug) {
            Debug_1.Debug.log("\u52A0\u8F7D\u52A8\u4F5C\u5F55\u5236: ".concat(recording.name), recording);
        }
        // 触发录制加载事件
        this.eventEmitter.emit('recordingLoaded', recording);
        // 如果配置为自动播放，则开始回放
        if (this.config.autoPlay) {
            this.startPlayback();
        }
        return true;
    };
    /**
     * 开始回放
     * @param onComplete 完成回调
     * @returns 是否成功开始回放
     */
    ActionPlayback.prototype.startPlayback = function (onComplete) {
        if (!this.recording) {
            if (this.config.debug) {
                Debug_1.Debug.warn('没有加载录制数据');
            }
            return false;
        }
        if (this.isPlaying && !this.isPaused) {
            if (this.config.debug) {
                Debug_1.Debug.warn('动作回放已经在进行中');
            }
            return false;
        }
        // 如果是从暂停状态恢复
        if (this.isPaused) {
            this.isPaused = false;
            this.totalPauseDuration += (Date.now() - this.playbackPauseTimestamp);
            if (this.config.debug) {
                Debug_1.Debug.log('恢复动作回放');
            }
            // 触发回放恢复事件
            this.eventEmitter.emit('playbackResumed', this.recording);
            // 继续回放循环
            this.startPlaybackLoop();
            return true;
        }
        // 重置回放状态
        this.resetPlaybackState();
        this.isPlaying = true;
        this.playbackStartTimestamp = Date.now();
        this.onCompleteCallback = onComplete || null;
        if (this.config.debug) {
            Debug_1.Debug.log("\u5F00\u59CB\u56DE\u653E\u52A8\u4F5C: ".concat(this.recording.name));
        }
        // 触发回放开始事件
        this.eventEmitter.emit('playbackStart', this.recording);
        // 开始回放循环
        this.startPlaybackLoop();
        return true;
    };
    /**
     * 暂停回放
     * @returns 是否成功暂停
     */
    ActionPlayback.prototype.pausePlayback = function () {
        if (!this.isPlaying || this.isPaused) {
            return false;
        }
        this.isPaused = true;
        this.playbackPauseTimestamp = Date.now();
        // 停止回放循环
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        if (this.config.debug) {
            Debug_1.Debug.log('暂停动作回放');
        }
        // 触发回放暂停事件
        this.eventEmitter.emit('playbackPaused', this.recording);
        return true;
    };
    /**
     * 停止回放
     * @returns 是否成功停止
     */
    ActionPlayback.prototype.stopPlayback = function () {
        if (!this.isPlaying) {
            return false;
        }
        this.isPlaying = false;
        this.isPaused = false;
        // 停止回放循环
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        if (this.config.debug) {
            Debug_1.Debug.log('停止动作回放');
        }
        // 触发回放停止事件
        this.eventEmitter.emit('playbackStop', this.recording);
        return true;
    };
    /**
     * 设置回放速度
     * @param speed 速度 (1.0 = 正常速度)
     */
    ActionPlayback.prototype.setPlaybackSpeed = function (speed) {
        this.config.playbackSpeed = Math.max(0.1, speed);
        if (this.config.debug) {
            Debug_1.Debug.log("\u8BBE\u7F6E\u56DE\u653E\u901F\u5EA6: ".concat(speed));
        }
        // 触发速度变更事件
        this.eventEmitter.emit('playbackSpeedChanged', speed);
    };
    /**
     * 重置回放状态
     */
    ActionPlayback.prototype.resetPlaybackState = function () {
        this.isPlaying = false;
        this.isPaused = false;
        this.playbackStartTimestamp = 0;
        this.playbackPauseTimestamp = 0;
        this.totalPauseDuration = 0;
        this.actionEventIndex = 0;
        this.inputEventIndex = 0;
        this.transformEventIndex = 0;
        this.onCompleteCallback = null;
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    };
    /**
     * 开始回放循环
     */
    ActionPlayback.prototype.startPlaybackLoop = function () {
        var _this = this;
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
        }
        var loop = function () {
            if (!_this.isPlaying || _this.isPaused || !_this.recording) {
                return;
            }
            // 计算当前回放时间
            var currentTime = _this.getCurrentPlaybackTime();
            // 处理动作事件
            _this.processActionEvents(currentTime);
            // 处理输入事件
            if (_this.config.playbackInput) {
                _this.processInputEvents(currentTime);
            }
            // 处理变换事件
            if (_this.config.playbackTransform) {
                _this.processTransformEvents(currentTime);
            }
            // 检查是否完成回放
            if (currentTime >= (_this.recording.endTimestamp - _this.recording.startTimestamp)) {
                if (_this.config.loop) {
                    // 如果循环播放，重置状态并重新开始
                    _this.resetPlaybackIndices();
                    _this.playbackStartTimestamp = Date.now();
                    _this.totalPauseDuration = 0;
                    // 触发循环事件
                    _this.eventEmitter.emit('playbackLoop', _this.recording);
                }
                else {
                    // 完成回放
                    _this.completePlayback();
                    return;
                }
            }
            // 继续循环
            _this.animationFrameId = requestAnimationFrame(loop);
        };
        // 开始循环
        this.animationFrameId = requestAnimationFrame(loop);
    };
    /**
     * 获取当前回放时间
     * @returns 当前回放时间（毫秒）
     */
    ActionPlayback.prototype.getCurrentPlaybackTime = function () {
        if (!this.isPlaying || !this.recording)
            return 0;
        // 计算经过的时间，考虑暂停时间和播放速度
        var elapsedTime = Date.now() - this.playbackStartTimestamp - this.totalPauseDuration;
        return elapsedTime * (this.config.playbackSpeed || 1.0);
    };
    /**
     * 处理动作事件
     * @param currentTime 当前回放时间
     */
    ActionPlayback.prototype.processActionEvents = function (currentTime) {
        if (!this.recording || this.actionEventIndex >= this.recording.actionEvents.length) {
            return;
        }
        // 处理所有应该在当前时间触发的动作事件
        while (this.actionEventIndex < this.recording.actionEvents.length) {
            var event_1 = this.recording.actionEvents[this.actionEventIndex];
            var eventTime = event_1.timestamp - this.recording.startTimestamp;
            if (eventTime <= currentTime) {
                this.playActionEvent(event_1);
                this.actionEventIndex++;
            }
            else {
                break;
            }
        }
    };
    /**
     * 播放动作事件
     * @param event 动作事件
     */
    ActionPlayback.prototype.playActionEvent = function (event) {
        if (event.eventType === 'start') {
            // 播放动作
            this.actionControlSystem.playAction(this.entity, event.actionId, event.params);
            if (this.config.debug) {
                Debug_1.Debug.log("\u56DE\u653E\u52A8\u4F5C\u5F00\u59CB: ".concat(event.actionId));
            }
        }
        else if (event.eventType === 'stop') {
            // 停止动作
            this.actionControlSystem.stopAction(this.entity, event.actionId);
            if (this.config.debug) {
                Debug_1.Debug.log("\u56DE\u653E\u52A8\u4F5C\u7ED3\u675F: ".concat(event.actionId));
            }
        }
    };
    /**
     * 处理输入事件
     * @param currentTime 当前回放时间
     */
    ActionPlayback.prototype.processInputEvents = function (currentTime) {
        if (!this.recording || !this.characterController ||
            this.inputEventIndex >= this.recording.inputEvents.length) {
            return;
        }
        // 处理所有应该在当前时间触发的输入事件
        while (this.inputEventIndex < this.recording.inputEvents.length) {
            var event_2 = this.recording.inputEvents[this.inputEventIndex];
            var eventTime = event_2.timestamp - this.recording.startTimestamp;
            if (eventTime <= currentTime) {
                this.playInputEvent(event_2);
                this.inputEventIndex++;
            }
            else {
                break;
            }
        }
    };
    /**
     * 播放输入事件
     * @param event 输入事件
     */
    ActionPlayback.prototype.playInputEvent = function (event) {
        // 实现输入事件回放
        if (this.config.debug) {
            Debug_1.Debug.log("\u56DE\u653E\u8F93\u5165\u4E8B\u4EF6: ".concat(event.inputType));
        }
    };
    /**
     * 处理变换事件
     * @param currentTime 当前回放时间
     */
    ActionPlayback.prototype.processTransformEvents = function (currentTime) {
        if (!this.recording || this.transformEventIndex >= this.recording.transformEvents.length) {
            return;
        }
        // 找到当前时间应该显示的变换
        var targetIndex = this.transformEventIndex;
        // 查找最接近当前时间的两个变换事件进行插值
        while (targetIndex + 1 < this.recording.transformEvents.length) {
            var nextEvent = this.recording.transformEvents[targetIndex + 1];
            var nextEventTime = nextEvent.timestamp - this.recording.startTimestamp;
            if (nextEventTime <= currentTime) {
                targetIndex++;
            }
            else {
                break;
            }
        }
        // 更新索引
        this.transformEventIndex = targetIndex;
        // 应用变换
        if (targetIndex < this.recording.transformEvents.length) {
            var currentEvent = this.recording.transformEvents[targetIndex];
            // 如果有下一个事件，进行插值
            if (targetIndex + 1 < this.recording.transformEvents.length) {
                var nextEvent = this.recording.transformEvents[targetIndex + 1];
                var currentEventTime = currentEvent.timestamp - this.recording.startTimestamp;
                var nextEventTime = nextEvent.timestamp - this.recording.startTimestamp;
                // 计算插值因子
                var t = (currentTime - currentEventTime) / (nextEventTime - currentEventTime);
                // 应用插值变换
                this.applyInterpolatedTransform(currentEvent, nextEvent, t);
            }
            else {
                // 直接应用当前变换
                this.applyTransform(currentEvent);
            }
        }
    };
    /**
     * 应用变换
     * @param event 变换事件
     */
    ActionPlayback.prototype.applyTransform = function (event) {
        var transform = this.entity.getTransform();
        if (!transform)
            return;
        // 设置位置
        transform.setPosition(event.position.x, event.position.y, event.position.z);
        // 设置旋转
        transform.setRotationQuaternion(event.rotation.x, event.rotation.y, event.rotation.z, event.rotation.w);
    };
    /**
     * 应用插值变换
     * @param event1 变换事件1
     * @param event2 变换事件2
     * @param t 插值因子 (0-1)
     */
    ActionPlayback.prototype.applyInterpolatedTransform = function (event1, event2, t) {
        var transform = this.entity.getTransform();
        if (!transform)
            return;
        // 线性插值位置
        var interpolatedX = event1.position.x + (event2.position.x - event1.position.x) * t;
        var interpolatedY = event1.position.y + (event2.position.y - event1.position.y) * t;
        var interpolatedZ = event1.position.z + (event2.position.z - event1.position.z) * t;
        transform.setPosition(interpolatedX, interpolatedY, interpolatedZ);
        // 线性插值旋转四元数
        // 注意：实际实现应该使用四元数的slerp方法，这里简化为线性插值
        var interpolatedQx = event1.rotation.x + (event2.rotation.x - event1.rotation.x) * t;
        var interpolatedQy = event1.rotation.y + (event2.rotation.y - event1.rotation.y) * t;
        var interpolatedQz = event1.rotation.z + (event2.rotation.z - event1.rotation.z) * t;
        var interpolatedQw = event1.rotation.w + (event2.rotation.w - event1.rotation.w) * t;
        // 归一化四元数
        var length = Math.sqrt(interpolatedQx * interpolatedQx +
            interpolatedQy * interpolatedQy +
            interpolatedQz * interpolatedQz +
            interpolatedQw * interpolatedQw);
        if (length > 0) {
            transform.setRotationQuaternion(interpolatedQx / length, interpolatedQy / length, interpolatedQz / length, interpolatedQw / length);
        }
    };
    /**
     * 重置回放索引
     */
    ActionPlayback.prototype.resetPlaybackIndices = function () {
        this.actionEventIndex = 0;
        this.inputEventIndex = 0;
        this.transformEventIndex = 0;
    };
    /**
     * 完成回放
     */
    ActionPlayback.prototype.completePlayback = function () {
        this.isPlaying = false;
        if (this.config.debug) {
            Debug_1.Debug.log('动作回放完成');
        }
        // 触发回放完成事件
        this.eventEmitter.emit('playbackComplete', this.recording);
        // 调用完成回调
        if (this.onCompleteCallback) {
            this.onCompleteCallback();
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionPlayback.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    ActionPlayback.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    return ActionPlayback;
}());
exports.ActionPlayback = ActionPlayback;
