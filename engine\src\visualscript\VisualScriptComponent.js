"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VisualScriptComponent = void 0;
/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
var Component_1 = require("../core/Component");
/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
var VisualScriptComponent = exports.VisualScriptComponent = /** @class */ (function (_super) {
    __extends(VisualScriptComponent, _super);
    /**
     * 创建视觉脚本组件
     * @param entity 所属实体
     * @param options 组件选项
     */
    function VisualScriptComponent(entity, options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, entity, VisualScriptComponent.TYPE) || this;
        /** 视觉脚本JSON数据 */
        _this._script = null;
        /** 是否正在运行 */
        _this._running = false;
        /** 是否禁用 */
        _this._disabled = false;
        /** 脚本域 */
        _this._domain = 'default';
        if (options.script) {
            _this._script = options.script;
        }
        _this._running = options.autoRun || false;
        _this._disabled = options.disabled || false;
        _this._domain = options.domain || 'default';
        return _this;
    }
    Object.defineProperty(VisualScriptComponent.prototype, "script", {
        /**
         * 获取视觉脚本JSON数据
         */
        get: function () {
            return this._script;
        },
        /**
         * 设置视觉脚本JSON数据
         */
        set: function (value) {
            this._script = value;
            this.emit('scriptChanged', value);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(VisualScriptComponent.prototype, "running", {
        /**
         * 获取是否正在运行
         */
        get: function () {
            return this._running && !this._disabled;
        },
        /**
         * 设置是否运行
         */
        set: function (value) {
            if (this._running !== value) {
                this._running = value;
                this.emit('runningChanged', value);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(VisualScriptComponent.prototype, "disabled", {
        /**
         * 获取是否禁用
         */
        get: function () {
            return this._disabled;
        },
        /**
         * 设置是否禁用
         */
        set: function (value) {
            if (this._disabled !== value) {
                this._disabled = value;
                // 如果禁用，则停止运行
                if (value && this._running) {
                    this._running = false;
                    this.emit('runningChanged', false);
                }
                this.emit('disabledChanged', value);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(VisualScriptComponent.prototype, "domain", {
        /**
         * 获取脚本域
         */
        get: function () {
            return this._domain;
        },
        /**
         * 设置脚本域
         */
        set: function (value) {
            if (this._domain !== value) {
                this._domain = value;
                this.emit('domainChanged', value);
            }
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 开始运行视觉脚本
     */
    VisualScriptComponent.prototype.play = function () {
        if (!this._disabled && !this._running) {
            this.running = true;
        }
    };
    /**
     * 暂停运行视觉脚本
     */
    VisualScriptComponent.prototype.pause = function () {
        if (this._running) {
            this.running = false;
        }
    };
    /**
     * 切换运行状态
     */
    VisualScriptComponent.prototype.toggle = function () {
        if (!this._disabled) {
            this.running = !this._running;
        }
    };
    /**
     * 序列化组件
     */
    VisualScriptComponent.prototype.serialize = function () {
        return {
            type: VisualScriptComponent.TYPE,
            script: this._script,
            running: this._running,
            disabled: this._disabled,
            domain: this._domain
        };
    };
    /**
     * 反序列化组件
     * @param data 序列化数据
     */
    VisualScriptComponent.prototype.deserialize = function (data) {
        if (data.script) {
            this._script = data.script;
        }
        if (data.running !== undefined) {
            this._running = data.running;
        }
        if (data.disabled !== undefined) {
            this._disabled = data.disabled;
        }
        if (data.domain !== undefined) {
            this._domain = data.domain;
        }
    };
    /** 组件类型 */
    VisualScriptComponent.TYPE = 'VisualScript';
    return VisualScriptComponent;
}(Component_1.Component));
