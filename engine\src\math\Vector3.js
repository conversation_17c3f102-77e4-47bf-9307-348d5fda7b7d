"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Vector3 = void 0;
/**
 * 三维向量类
 * 表示三维空间中的点或向量
 */
var Vector3 = /** @class */ (function () {
    /**
     * 创建三维向量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     */
    function Vector3(x, y, z) {
        if (x === void 0) { x = 0; }
        if (y === void 0) { y = 0; }
        if (z === void 0) { z = 0; }
        this.x = x;
        this.y = y;
        this.z = z;
    }
    /**
     * 设置向量分量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @returns 当前向量
     */
    Vector3.prototype.set = function (x, y, z) {
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    };
    /**
     * 复制另一个向量的值
     * @param v 要复制的向量
     * @returns 当前向量
     */
    Vector3.prototype.copy = function (v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    };
    /**
     * 克隆向量
     * @returns 新的向量实例
     */
    Vector3.prototype.clone = function () {
        return new Vector3(this.x, this.y, this.z);
    };
    /**
     * 向量加法
     * @param v 要加的向量
     * @returns 当前向量
     */
    Vector3.prototype.add = function (v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    };
    /**
     * 向量减法
     * @param v 要减的向量
     * @returns 当前向量
     */
    Vector3.prototype.subtract = function (v) {
        this.x -= v.x;
        this.y -= v.y;
        this.z -= v.z;
        return this;
    };
    /**
     * 向量乘以标量
     * @param s 标量
     * @returns 当前向量
     */
    Vector3.prototype.multiplyScalar = function (s) {
        this.x *= s;
        this.y *= s;
        this.z *= s;
        return this;
    };
    /**
     * 向量除以标量
     * @param s 标量
     * @returns 当前向量
     */
    Vector3.prototype.divideScalar = function (s) {
        if (s !== 0) {
            var invScalar = 1 / s;
            this.x *= invScalar;
            this.y *= invScalar;
            this.z *= invScalar;
        }
        else {
            this.x = 0;
            this.y = 0;
            this.z = 0;
        }
        return this;
    };
    /**
     * 向量点积
     * @param v 另一个向量
     * @returns 点积结果
     */
    Vector3.prototype.dot = function (v) {
        return this.x * v.x + this.y * v.y + this.z * v.z;
    };
    /**
     * 向量叉积
     * @param v 另一个向量
     * @returns 当前向量
     */
    Vector3.prototype.cross = function (v) {
        var x = this.y * v.z - this.z * v.y;
        var y = this.z * v.x - this.x * v.z;
        var z = this.x * v.y - this.y * v.x;
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    };
    /**
     * 计算向量长度
     * @returns 向量长度
     */
    Vector3.prototype.length = function () {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    };
    /**
     * 计算向量长度的平方
     * @returns 向量长度的平方
     */
    Vector3.prototype.lengthSquared = function () {
        return this.x * this.x + this.y * this.y + this.z * this.z;
    };
    /**
     * 归一化向量
     * @returns 当前向量
     */
    Vector3.prototype.normalize = function () {
        return this.divideScalar(this.length() || 1);
    };
    /**
     * 计算与另一个向量的距离
     * @param v 另一个向量
     * @returns 距离
     */
    Vector3.prototype.distanceTo = function (v) {
        return Math.sqrt(this.distanceToSquared(v));
    };
    /**
     * 计算与另一个向量的距离的平方
     * @param v 另一个向量
     * @returns 距离的平方
     */
    Vector3.prototype.distanceToSquared = function (v) {
        var dx = this.x - v.x;
        var dy = this.y - v.y;
        var dz = this.z - v.z;
        return dx * dx + dy * dy + dz * dz;
    };
    /**
     * 设置向量为零向量
     * @returns 当前向量
     */
    Vector3.prototype.setZero = function () {
        this.x = 0;
        this.y = 0;
        this.z = 0;
        return this;
    };
    /**
     * 判断向量是否为零向量
     * @returns 是否为零向量
     */
    Vector3.prototype.isZero = function () {
        return this.x === 0 && this.y === 0 && this.z === 0;
    };
    /**
     * 判断向量是否约等于另一个向量
     * @param v 另一个向量
     * @param epsilon 误差范围
     * @returns 是否约等于
     */
    Vector3.prototype.equals = function (v, epsilon) {
        if (epsilon === void 0) { epsilon = 0.0001; }
        return (Math.abs(this.x - v.x) < epsilon &&
            Math.abs(this.y - v.y) < epsilon &&
            Math.abs(this.z - v.z) < epsilon);
    };
    return Vector3;
}());
exports.Vector3 = Vector3;
