"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BERTModel = void 0;
/**
 * BERT模型
 * 用于文本分类、命名实体识别、文本摘要等任务
 */
var AIModelType_1 = require("../AIModelType");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * BERT模型
 */
var BERTModel = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    function BERTModel(config, globalConfig) {
        if (config === void 0) { config = {}; }
        if (globalConfig === void 0) { globalConfig = {}; }
        /** 模型类型 */
        this.type = AIModelType_1.AIModelType.BERT;
        /** 是否已初始化 */
        this.initialized = false;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 模型实例 */
        this.model = null;
        /** 分词器 */
        this.tokenizer = null;
        /** 模型加载进度 */
        this.loadProgress = 0;
        this.config = __assign({}, config);
        this.globalConfig = __assign({}, globalConfig);
    }
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    BERTModel.prototype.getType = function () {
        return this.type;
    };
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    BERTModel.prototype.getConfig = function () {
        return __assign({}, this.config);
    };
    /**
     * 初始化模型
     * @returns 是否成功
     */
    BERTModel.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var debug, useLocalModel, modelPath, apiKey, baseUrl, i, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.initialized) {
                            return [2 /*return*/, true];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 6, , 7]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log('初始化BERT模型...');
                        }
                        useLocalModel = this.config.useLocalModel !== undefined
                            ? this.config.useLocalModel
                            : this.globalConfig.useLocalModel;
                        modelPath = this.config.modelPath || this.globalConfig.modelPath || '';
                        apiKey = this.config.apiKey ||
                            (this.globalConfig.apiKeys && this.globalConfig.apiKeys[AIModelType_1.AIModelType.BERT]) ||
                            '';
                        baseUrl = this.config.baseUrl ||
                            (this.globalConfig.baseUrls && this.globalConfig.baseUrls[AIModelType_1.AIModelType.BERT]) ||
                            '';
                        i = 0;
                        _a.label = 2;
                    case 2:
                        if (!(i <= 10)) return [3 /*break*/, 5];
                        this.loadProgress = i / 10;
                        this.eventEmitter.emit('loadProgress', { progress: this.loadProgress });
                        if (!(i < 10)) return [3 /*break*/, 4];
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        i++;
                        return [3 /*break*/, 2];
                    case 5:
                        // 如果使用本地模型，加载本地模型
                        if (useLocalModel) {
                            if (debug) {
                                console.log("\u52A0\u8F7D\u672C\u5730BERT\u6A21\u578B: ".concat(modelPath));
                            }
                            // 这里应该实现本地模型加载逻辑
                            // 实际应用中，可能需要使用ONNX Runtime或其他库
                            this.model = {
                                classify: function (text, categories) { return _this.mockClassify(text, categories); },
                                recognizeEntities: function (text) { return _this.mockRecognizeEntities(text); },
                                summarize: function (text, maxLength) { return _this.mockSummarize(text, maxLength); },
                                analyzeEmotion: function (text) { return _this.mockAnalyzeEmotion(text); }
                            };
                            // 初始化分词器
                            this.tokenizer = {
                                tokenize: function (text) { return text.split(/\s+/); }
                            };
                        }
                        else {
                            if (debug) {
                                console.log("\u52A0\u8F7D\u8FDC\u7A0BBERT\u6A21\u578B: ".concat(baseUrl));
                            }
                            // 这里应该实现远程API调用逻辑
                            this.model = {
                                classify: function (text, categories) { return _this.mockClassify(text, categories); },
                                recognizeEntities: function (text) { return _this.mockRecognizeEntities(text); },
                                summarize: function (text, maxLength) { return _this.mockSummarize(text, maxLength); },
                                analyzeEmotion: function (text) { return _this.mockAnalyzeEmotion(text); }
                            };
                            // 初始化分词器
                            this.tokenizer = {
                                tokenize: function (text) { return text.split(/\s+/); }
                            };
                        }
                        this.initialized = true;
                        this.eventEmitter.emit('initialized', { success: true });
                        if (debug) {
                            console.log('BERT模型初始化完成');
                        }
                        return [2 /*return*/, true];
                    case 6:
                        error_1 = _a.sent();
                        console.error('初始化BERT模型失败:', error_1);
                        this.eventEmitter.emit('initialized', { success: false, error: error_1 });
                        return [2 /*return*/, false];
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    BERTModel.prototype.generateText = function (prompt, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                throw new Error('BERT模型不支持文本生成');
            });
        });
    };
    /**
     * 分类文本
     * @param text 要分类的文本
     * @param categories 可选的分类类别
     * @returns 分类结果
     */
    BERTModel.prototype.classifyText = function (text, categories) {
        return __awaiter(this, void 0, void 0, function () {
            var debug, result, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log("\u5206\u7C7B\u6587\u672C: \"".concat(text, "\""));
                            if (categories) {
                                console.log('类别:', categories);
                            }
                        }
                        return [4 /*yield*/, this.model.classify(text, categories || [])];
                    case 3:
                        result = _a.sent();
                        if (debug) {
                            console.log('分类结果:', result);
                        }
                        return [2 /*return*/, result];
                    case 4:
                        error_2 = _a.sent();
                        console.error('分类文本失败:', error_2);
                        throw error_2;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 命名实体识别
     * @param text 要识别的文本
     * @returns 识别结果
     */
    BERTModel.prototype.recognizeEntities = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var debug, result, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log("\u8BC6\u522B\u5B9E\u4F53: \"".concat(text, "\""));
                        }
                        return [4 /*yield*/, this.model.recognizeEntities(text)];
                    case 3:
                        result = _a.sent();
                        if (debug) {
                            console.log('识别结果:', result);
                        }
                        return [2 /*return*/, result];
                    case 4:
                        error_3 = _a.sent();
                        console.error('识别实体失败:', error_3);
                        throw error_3;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大长度
     * @returns 摘要结果
     */
    BERTModel.prototype.summarizeText = function (text, maxLength) {
        if (maxLength === void 0) { maxLength = 100; }
        return __awaiter(this, void 0, void 0, function () {
            var debug, result, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log("\u6458\u8981\u6587\u672C: \"".concat(text.substring(0, 50), "...\""));
                            console.log('最大长度:', maxLength);
                        }
                        return [4 /*yield*/, this.model.summarize(text, maxLength)];
                    case 3:
                        result = _a.sent();
                        if (debug) {
                            console.log('摘要结果:', result);
                        }
                        return [2 /*return*/, result];
                    case 4:
                        error_4 = _a.sent();
                        console.error('摘要文本失败:', error_4);
                        throw error_4;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 分析情感
     * @param text 要分析的文本
     * @returns 情感分析结果
     */
    BERTModel.prototype.analyzeEmotion = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var debug, result, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log("\u5206\u6790\u60C5\u611F: \"".concat(text, "\""));
                        }
                        return [4 /*yield*/, this.model.analyzeEmotion(text)];
                    case 3:
                        result = _a.sent();
                        if (debug) {
                            console.log('情感分析结果:', result);
                        }
                        return [2 /*return*/, result];
                    case 4:
                        error_5 = _a.sent();
                        console.error('分析情感失败:', error_5);
                        throw error_5;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 模拟分类文本
     * @param text 要分类的文本
     * @param categories 可选的分类类别
     * @returns 分类结果
     */
    BERTModel.prototype.mockClassify = function (text, categories) {
        if (categories === void 0) { categories = []; }
        return __awaiter(this, void 0, void 0, function () {
            var allLabels, totalScore, _i, categories_1, category, score, _a, categories_2, category, maxScore, maxCategory, _b, categories_3, category;
            return __generator(this, function (_c) {
                // 如果没有提供类别，使用默认类别
                if (categories.length === 0) {
                    categories = ['正面', '负面', '中性'];
                }
                allLabels = {};
                totalScore = 0;
                // 为每个类别生成随机分数
                for (_i = 0, categories_1 = categories; _i < categories_1.length; _i++) {
                    category = categories_1[_i];
                    score = Math.random();
                    allLabels[category] = score;
                    totalScore += score;
                }
                // 归一化分数
                for (_a = 0, categories_2 = categories; _a < categories_2.length; _a++) {
                    category = categories_2[_a];
                    allLabels[category] /= totalScore;
                }
                maxScore = 0;
                maxCategory = categories[0];
                for (_b = 0, categories_3 = categories; _b < categories_3.length; _b++) {
                    category = categories_3[_b];
                    if (allLabels[category] > maxScore) {
                        maxScore = allLabels[category];
                        maxCategory = category;
                    }
                }
                return [2 /*return*/, {
                        label: maxCategory,
                        confidence: maxScore,
                        allLabels: allLabels
                    }];
            });
        });
    };
    /**
     * 模拟命名实体识别
     * @param text 要识别的文本
     * @returns 识别结果
     */
    BERTModel.prototype.mockRecognizeEntities = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var entities, words, entityTypes, currentPosition, _i, words_1, word, entityType, start, end;
            return __generator(this, function (_a) {
                entities = [];
                words = text.split(/\s+/);
                entityTypes = ['人名', '地名', '组织', '时间', '数量'];
                currentPosition = 0;
                for (_i = 0, words_1 = words; _i < words_1.length; _i++) {
                    word = words_1[_i];
                    // 30%的概率将词识别为实体
                    if (Math.random() < 0.3) {
                        entityType = entityTypes[Math.floor(Math.random() * entityTypes.length)];
                        start = text.indexOf(word, currentPosition);
                        end = start + word.length;
                        entities.push({
                            text: word,
                            type: entityType,
                            start: start,
                            end: end,
                            confidence: 0.7 + Math.random() * 0.3
                        });
                    }
                    currentPosition += word.length + 1;
                }
                return [2 /*return*/, {
                        entities: entities
                    }];
            });
        });
    };
    /**
     * 模拟文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大长度
     * @returns 摘要结果
     */
    BERTModel.prototype.mockSummarize = function (text, maxLength) {
        return __awaiter(this, void 0, void 0, function () {
            var sentences, summary, i, compressionRate;
            return __generator(this, function (_a) {
                sentences = text.split(/[.!?]+/).filter(function (s) { return s.trim().length > 0; });
                // 如果文本很短，直接返回
                if (text.length <= maxLength) {
                    return [2 /*return*/, {
                            summary: text,
                            length: text.length,
                            compressionRate: 1.0
                        }];
                }
                summary = '';
                i = 0;
                while (i < sentences.length && summary.length + sentences[i].length + 1 <= maxLength) {
                    summary += sentences[i] + '. ';
                    i++;
                }
                compressionRate = summary.length / text.length;
                return [2 /*return*/, {
                        summary: summary.trim(),
                        length: summary.length,
                        compressionRate: compressionRate
                    }];
            });
        });
    };
    /**
     * 模拟情感分析
     * @param text 要分析的文本
     * @returns 情感分析结果
     */
    BERTModel.prototype.mockAnalyzeEmotion = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var emotions, scores, totalScore, _i, emotions_1, emotion, score, _a, emotions_2, emotion, maxScore, primaryEmotion, _b, emotions_3, emotion, intensity;
            return __generator(this, function (_c) {
                emotions = ['高兴', '悲伤', '愤怒', '恐惧', '惊讶', '厌恶', '中性'];
                scores = {};
                totalScore = 0;
                for (_i = 0, emotions_1 = emotions; _i < emotions_1.length; _i++) {
                    emotion = emotions_1[_i];
                    score = Math.random();
                    scores[emotion] = score;
                    totalScore += score;
                }
                // 归一化分数
                for (_a = 0, emotions_2 = emotions; _a < emotions_2.length; _a++) {
                    emotion = emotions_2[_a];
                    scores[emotion] /= totalScore;
                }
                maxScore = 0;
                primaryEmotion = emotions[0];
                for (_b = 0, emotions_3 = emotions; _b < emotions_3.length; _b++) {
                    emotion = emotions_3[_b];
                    if (scores[emotion] > maxScore) {
                        maxScore = scores[emotion];
                        primaryEmotion = emotion;
                    }
                }
                intensity = 0.5 + Math.random() * 0.5;
                return [2 /*return*/, {
                        primaryEmotion: primaryEmotion,
                        intensity: intensity,
                        scores: scores,
                        confidence: maxScore
                    }];
            });
        });
    };
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    BERTModel.prototype.on = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    BERTModel.prototype.off = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 释放资源
     */
    BERTModel.prototype.dispose = function () {
        // 释放模型资源
        this.model = null;
        this.tokenizer = null;
        // 重置状态
        this.initialized = false;
        // 清空事件监听器
        this.eventEmitter.removeAllListeners();
    };
    return BERTModel;
}());
exports.BERTModel = BERTModel;
