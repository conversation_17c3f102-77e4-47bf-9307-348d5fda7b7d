"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabberComponent = void 0;
/**
 * 抓取者组件
 * 用于标记可以抓取其他对象的实体
 */
var Component_1 = require("../../core/Component");
var EventEmitter_1 = require("../../utils/EventEmitter");
var GrabbableComponent_1 = require("./GrabbableComponent");
/**
 * 抓取者组件
 */
var GrabberComponent = exports.GrabberComponent = /** @class */ (function (_super) {
    __extends(GrabberComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    function GrabberComponent(entity, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, GrabberComponent.TYPE) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化属性
        _this._maxGrabDistance = config.maxGrabDistance || 1.0;
        _this._enabled = config.enabled !== undefined ? config.enabled : true;
        // 注册回调
        if (config.onGrab) {
            _this.on('grab', config.onGrab);
        }
        if (config.onRelease) {
            _this.on('release', config.onRelease);
        }
        return _this;
    }
    Object.defineProperty(GrabberComponent.prototype, "maxGrabDistance", {
        /**
         * 获取最大抓取距离
         */
        get: function () {
            return this._maxGrabDistance;
        },
        /**
         * 设置最大抓取距离
         */
        set: function (value) {
            this._maxGrabDistance = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabberComponent.prototype, "enabled", {
        /**
         * 获取是否启用
         */
        get: function () {
            return this._enabled;
        },
        /**
         * 设置是否启用
         */
        set: function (value) {
            this._enabled = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabberComponent.prototype, "leftHandGrabbed", {
        /**
         * 获取左手抓取的实体
         */
        get: function () {
            return this._leftHandGrabbed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabberComponent.prototype, "rightHandGrabbed", {
        /**
         * 获取右手抓取的实体
         */
        get: function () {
            return this._rightHandGrabbed;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 获取指定手抓取的实体
     * @param hand 手
     * @returns 抓取的实体
     */
    GrabberComponent.prototype.getGrabbedEntity = function (hand) {
        if (hand === GrabbableComponent_1.Hand.LEFT) {
            return this._leftHandGrabbed;
        }
        else if (hand === GrabbableComponent_1.Hand.RIGHT) {
            return this._rightHandGrabbed;
        }
        return undefined;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    GrabberComponent.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    GrabberComponent.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 抓取实体
     * @param entity 要抓取的实体
     * @param hand 使用的手
     * @returns 是否抓取成功
     */
    GrabberComponent.prototype.grab = function (entity, hand) {
        // 如果未启用，则返回失败
        if (!this._enabled) {
            return false;
        }
        // 检查指定的手是否已经抓取了其他实体
        if ((hand === GrabbableComponent_1.Hand.LEFT && this._leftHandGrabbed) ||
            (hand === GrabbableComponent_1.Hand.RIGHT && this._rightHandGrabbed)) {
            return false;
        }
        // 获取可抓取组件
        var grabbableComponent = entity.getComponent('GrabbableComponent');
        if (!grabbableComponent) {
            return false;
        }
        // 尝试抓取
        var success = grabbableComponent.grab(this.entity, hand);
        if (success) {
            // 更新抓取状态
            if (hand === GrabbableComponent_1.Hand.LEFT) {
                this._leftHandGrabbed = entity;
            }
            else if (hand === GrabbableComponent_1.Hand.RIGHT) {
                this._rightHandGrabbed = entity;
            }
            // 触发抓取事件
            this.eventEmitter.emit('grab', this.entity, entity, hand);
        }
        return success;
    };
    /**
     * 释放实体
     * @param hand 使用的手
     * @returns 是否释放成功
     */
    GrabberComponent.prototype.release = function (hand) {
        // 获取指定手抓取的实体
        var entity = this.getGrabbedEntity(hand);
        if (!entity) {
            return false;
        }
        // 获取可抓取组件
        var grabbableComponent = entity.getComponent('GrabbableComponent');
        if (!grabbableComponent) {
            return false;
        }
        // 尝试释放
        var success = grabbableComponent.release();
        if (success) {
            // 更新抓取状态
            if (hand === GrabbableComponent_1.Hand.LEFT) {
                this._leftHandGrabbed = undefined;
            }
            else if (hand === GrabbableComponent_1.Hand.RIGHT) {
                this._rightHandGrabbed = undefined;
            }
            // 触发释放事件
            this.eventEmitter.emit('release', this.entity, entity, hand);
        }
        return success;
    };
    /** 组件类型 */
    GrabberComponent.TYPE = 'GrabberComponent';
    return GrabberComponent;
}(Component_1.Component));
