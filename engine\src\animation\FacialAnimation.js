"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacialAnimationComponent = exports.VisemeType = exports.FacialExpressionType = void 0;
var Component_1 = require("../core/Component");
var EventEmitter_1 = require("../utils/EventEmitter");
// 导入统一的面部表情类型
var FacialExpressionType_1 = require("./FacialExpressionType");
Object.defineProperty(exports, "FacialExpressionType", { enumerable: true, get: function () { return FacialExpressionType_1.FacialExpressionType; } });
/**
 * 口型类型
 */
var VisemeType;
(function (VisemeType) {
    VisemeType["SILENT"] = "silent";
    VisemeType["PP"] = "pp";
    VisemeType["FF"] = "ff";
    VisemeType["TH"] = "th";
    VisemeType["DD"] = "dd";
    VisemeType["KK"] = "kk";
    VisemeType["CH"] = "ch";
    VisemeType["SS"] = "ss";
    VisemeType["NN"] = "nn";
    VisemeType["RR"] = "rr";
    VisemeType["AA"] = "aa";
    VisemeType["EE"] = "ee";
    VisemeType["IH"] = "ih";
    VisemeType["OH"] = "oh";
    VisemeType["OU"] = "ou"; // u
})(VisemeType || (exports.VisemeType = VisemeType = {}));
/**
 * 面部动画组件
 */
var FacialAnimationComponent = exports.FacialAnimationComponent = /** @class */ (function (_super) {
    __extends(FacialAnimationComponent, _super);
    /**
     * 构造函数
     */
    function FacialAnimationComponent() {
        var _this = _super.call(this, FacialAnimationComponent.type) || this;
        /** 当前表情 */
        _this.currentExpression = FacialExpressionType_1.FacialExpressionType.NEUTRAL;
        /** 目标表情 */
        _this.targetExpression = FacialExpressionType_1.FacialExpressionType.NEUTRAL;
        /** 表情权重 */
        _this.expressionWeight = 0;
        /** 表情混合速度 */
        _this.expressionBlendSpeed = 5.0;
        /** 当前口型 */
        _this.currentViseme = VisemeType.SILENT;
        /** 目标口型 */
        _this.targetViseme = VisemeType.SILENT;
        /** 口型权重 */
        _this.visemeWeight = 0;
        /** 口型混合速度 */
        _this.visemeBlendSpeed = 10.0;
        /** 表情混合映射 */
        _this.expressionBlendMap = new Map();
        /** 口型混合映射 */
        _this.visemeBlendMap = new Map();
        /** 是否使用平滑 */
        _this.useSmoothing = true;
        /** 平滑因子 */
        _this.smoothingFactor = 0.5;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        // 初始化表情混合映射
        for (var _i = 0, _a = Object.values(FacialExpressionType_1.FacialExpressionType); _i < _a.length; _i++) {
            var expression = _a[_i];
            _this.expressionBlendMap.set(expression, 0);
        }
        // 初始化口型混合映射
        for (var _b = 0, _c = Object.values(VisemeType); _b < _c.length; _b++) {
            var viseme = _c[_b];
            _this.visemeBlendMap.set(viseme, 0);
        }
        return _this;
    }
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    FacialAnimationComponent.prototype.setExpression = function (expression, weight, blendTime) {
        if (weight === void 0) { weight = 1.0; }
        if (!this.isEnabled())
            return;
        this.targetExpression = expression;
        this.expressionWeight = weight;
        if (blendTime !== undefined) {
            this.expressionBlendSpeed = 1.0 / Math.max(0.001, blendTime);
        }
        this.eventEmitter.emit('expressionChange', { expression: expression, weight: weight });
    };
    /**
     * 获取当前表情
     * @returns 当前表情和权重
     */
    FacialAnimationComponent.prototype.getCurrentExpression = function () {
        return {
            expression: this.currentExpression,
            weight: this.expressionWeight
        };
    };
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    FacialAnimationComponent.prototype.setViseme = function (viseme, weight, blendTime) {
        if (weight === void 0) { weight = 1.0; }
        if (!this.isEnabled())
            return;
        this.targetViseme = viseme;
        this.visemeWeight = weight;
        if (blendTime !== undefined) {
            this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
        }
        this.eventEmitter.emit('visemeChange', { viseme: viseme, weight: weight });
    };
    /**
     * 获取当前口型
     * @returns 当前口型和权重
     */
    FacialAnimationComponent.prototype.getCurrentViseme = function () {
        return {
            viseme: this.currentViseme,
            weight: this.visemeWeight
        };
    };
    /**
     * 获取表情混合映射
     * @returns 表情混合映射的副本
     */
    FacialAnimationComponent.prototype.getExpressionBlendMap = function () {
        return new Map(this.expressionBlendMap);
    };
    /**
     * 获取口型混合映射
     * @returns 口型混合映射的副本
     */
    FacialAnimationComponent.prototype.getVisemeBlendMap = function () {
        return new Map(this.visemeBlendMap);
    };
    /**
     * 重置表情
     */
    FacialAnimationComponent.prototype.resetExpression = function () {
        this.setExpression(FacialExpressionType_1.FacialExpressionType.NEUTRAL, 0);
    };
    /**
     * 重置口型
     */
    FacialAnimationComponent.prototype.resetViseme = function () {
        this.setViseme(VisemeType.SILENT, 0);
    };
    /**
     * 启用组件
     */
    FacialAnimationComponent.prototype.enable = function () {
        this.setEnabled(true);
    };
    /**
     * 禁用组件
     */
    FacialAnimationComponent.prototype.disable = function () {
        this.setEnabled(false);
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationComponent.prototype.update = function (deltaTime) {
        if (!this.isEnabled())
            return;
        // 更新表情混合
        this.updateExpressionBlend(deltaTime);
        // 更新口型混合
        this.updateVisemeBlend(deltaTime);
    };
    /**
     * 更新表情混合
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationComponent.prototype.updateExpressionBlend = function (deltaTime) {
        // 计算混合因子
        var blendFactor = Math.min(1.0, this.expressionBlendSpeed * deltaTime);
        // 更新当前表情
        if (this.currentExpression !== this.targetExpression ||
            Math.abs(this.expressionBlendMap.get(this.targetExpression) || 0 - this.expressionWeight) > 0.01) {
            // 降低所有表情的权重
            for (var _i = 0, _a = Array.from(this.expressionBlendMap.entries()); _i < _a.length; _i++) {
                var _b = _a[_i], expression = _b[0], weight = _b[1];
                if (expression === this.targetExpression.toString()) {
                    // 增加目标表情的权重
                    var targetWeight = this.expressionWeight;
                    var currentWeight = weight;
                    var newWeight = this.useSmoothing
                        ? currentWeight + (targetWeight - currentWeight) * blendFactor * (1.0 - this.smoothingFactor)
                        : targetWeight;
                    this.expressionBlendMap.set(expression, newWeight);
                }
                else {
                    // 降低其他表情的权重
                    var newWeight = this.useSmoothing
                        ? weight * (1.0 - blendFactor)
                        : 0;
                    this.expressionBlendMap.set(expression, newWeight);
                }
            }
            this.currentExpression = this.targetExpression;
        }
    };
    /**
     * 更新口型混合
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationComponent.prototype.updateVisemeBlend = function (deltaTime) {
        // 计算混合因子
        var blendFactor = Math.min(1.0, this.visemeBlendSpeed * deltaTime);
        // 更新当前口型
        if (this.currentViseme !== this.targetViseme ||
            Math.abs(this.visemeBlendMap.get(this.targetViseme) || 0 - this.visemeWeight) > 0.01) {
            // 降低所有口型的权重
            for (var _i = 0, _a = Array.from(this.visemeBlendMap.entries()); _i < _a.length; _i++) {
                var _b = _a[_i], viseme = _b[0], weight = _b[1];
                if (viseme === this.targetViseme.toString()) {
                    // 增加目标口型的权重
                    var targetWeight = this.visemeWeight;
                    var currentWeight = weight;
                    var newWeight = this.useSmoothing
                        ? currentWeight + (targetWeight - currentWeight) * blendFactor * (1.0 - this.smoothingFactor)
                        : targetWeight;
                    this.visemeBlendMap.set(viseme, newWeight);
                }
                else {
                    // 降低其他口型的权重
                    var newWeight = this.useSmoothing
                        ? weight * (1.0 - blendFactor)
                        : 0;
                    this.visemeBlendMap.set(viseme, newWeight);
                }
            }
            this.currentViseme = this.targetViseme;
        }
    };
    /** 组件类型 */
    FacialAnimationComponent.type = 'FacialAnimation';
    return FacialAnimationComponent;
}(Component_1.Component));
