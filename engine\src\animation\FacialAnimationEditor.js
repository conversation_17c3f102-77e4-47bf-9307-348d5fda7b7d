"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacialAnimationEditorComponent = void 0;
var Component_1 = require("../core/Component");
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 面部动画编辑器组件
 */
var FacialAnimationEditorComponent = exports.FacialAnimationEditorComponent = /** @class */ (function (_super) {
    __extends(FacialAnimationEditorComponent, _super);
    /**
     * 构造函数
     * @param entity 实体
     */
    function FacialAnimationEditorComponent(entity) {
        var _this = _super.call(this, FacialAnimationEditorComponent.type) || this;
        /** 动画片段 */
        _this.clips = new Map();
        /** 当前片段 */
        _this.currentClip = null;
        /** 当前时间 */
        _this.currentTime = 0;
        /** 是否播放中 */
        _this.isPlaying = false;
        /** 播放速度 */
        _this.playbackSpeed = 1.0;
        /** 帧率 */
        _this.frameRate = 30;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        _this.setEntity(entity);
        return _this;
    }
    /**
     * 创建动画片段
     * @param name 名称
     * @param duration 持续时间（秒）
     * @param loop 是否循环
     * @returns 动画片段
     */
    FacialAnimationEditorComponent.prototype.createClip = function (name, duration, loop) {
        if (duration === void 0) { duration = 5.0; }
        if (loop === void 0) { loop = false; }
        var clip = {
            name: name,
            duration: duration,
            keyframes: [],
            loop: loop
        };
        this.clips.set(name, clip);
        return clip;
    };
    /**
     * 删除动画片段
     * @param name 名称
     * @returns 是否成功删除
     */
    FacialAnimationEditorComponent.prototype.deleteClip = function (name) {
        if (this.currentClip && this.currentClip.name === name) {
            this.currentClip = null;
        }
        return this.clips.delete(name);
    };
    /**
     * 获取动画片段
     * @param name 名称
     * @returns 动画片段，如果不存在则返回null
     */
    FacialAnimationEditorComponent.prototype.getClip = function (name) {
        return this.clips.get(name) || null;
    };
    /**
     * 获取所有动画片段
     * @returns 动画片段数组
     */
    FacialAnimationEditorComponent.prototype.getClips = function () {
        return Array.from(this.clips.values());
    };
    /**
     * 设置当前片段
     * @param name 名称
     * @returns 是否成功设置
     */
    FacialAnimationEditorComponent.prototype.setCurrentClip = function (name) {
        var clip = this.clips.get(name);
        if (!clip)
            return false;
        this.currentClip = clip;
        this.currentTime = 0;
        this.eventEmitter.emit('clipChanged', { clip: clip });
        return true;
    };
    /**
     * 获取当前片段
     * @returns 当前片段，如果不存在则返回null
     */
    FacialAnimationEditorComponent.prototype.getCurrentClip = function () {
        return this.currentClip;
    };
    /**
     * 添加关键帧
     * @param time 时间（秒）
     * @param keyframe 关键帧数据
     * @returns 是否成功添加
     */
    FacialAnimationEditorComponent.prototype.addKeyframe = function (time, keyframe) {
        if (!this.currentClip)
            return false;
        // 创建关键帧
        var newKeyframe = __assign({ time: time }, keyframe);
        // 查找是否已存在相同时间的关键帧
        var existingIndex = this.currentClip.keyframes.findIndex(function (k) { return k.time === time; });
        if (existingIndex >= 0) {
            // 更新现有关键帧
            this.currentClip.keyframes[existingIndex] = __assign(__assign({}, this.currentClip.keyframes[existingIndex]), newKeyframe);
        }
        else {
            // 添加新关键帧
            this.currentClip.keyframes.push(newKeyframe);
            // 按时间排序
            this.currentClip.keyframes.sort(function (a, b) { return a.time - b.time; });
        }
        this.eventEmitter.emit('keyframeAdded', { keyframe: newKeyframe });
        return true;
    };
    /**
     * 移除关键帧
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    FacialAnimationEditorComponent.prototype.removeKeyframe = function (time) {
        if (!this.currentClip)
            return false;
        var initialLength = this.currentClip.keyframes.length;
        this.currentClip.keyframes = this.currentClip.keyframes.filter(function (k) { return k.time !== time; });
        var removed = this.currentClip.keyframes.length < initialLength;
        if (removed) {
            this.eventEmitter.emit('keyframeRemoved', { time: time });
        }
        return removed;
    };
    /**
     * 获取关键帧
     * @param time 时间（秒）
     * @returns 关键帧，如果不存在则返回null
     */
    FacialAnimationEditorComponent.prototype.getKeyframe = function (time) {
        if (!this.currentClip)
            return null;
        return this.currentClip.keyframes.find(function (k) { return k.time === time; }) || null;
    };
    /**
     * 获取当前时间的插值关键帧
     * @returns 插值关键帧
     */
    FacialAnimationEditorComponent.prototype.getInterpolatedKeyframe = function () {
        if (!this.currentClip || this.currentClip.keyframes.length === 0)
            return null;
        // 如果只有一个关键帧，直接返回
        if (this.currentClip.keyframes.length === 1) {
            return __assign({}, this.currentClip.keyframes[0]);
        }
        // 找到当前时间的前后关键帧
        var prevKeyframe = null;
        var nextKeyframe = null;
        for (var i = 0; i < this.currentClip.keyframes.length; i++) {
            if (this.currentClip.keyframes[i].time <= this.currentTime) {
                prevKeyframe = this.currentClip.keyframes[i];
            }
            else {
                nextKeyframe = this.currentClip.keyframes[i];
                break;
            }
        }
        // 如果没有前一个关键帧，使用最后一个
        if (!prevKeyframe && this.currentClip.loop) {
            prevKeyframe = this.currentClip.keyframes[this.currentClip.keyframes.length - 1];
        }
        // 如果没有后一个关键帧，使用第一个
        if (!nextKeyframe && this.currentClip.loop) {
            nextKeyframe = this.currentClip.keyframes[0];
        }
        // 如果仍然没有前后关键帧，返回null
        if (!prevKeyframe || !nextKeyframe) {
            return prevKeyframe || nextKeyframe;
        }
        // 计算插值因子
        var t = 0;
        if (nextKeyframe.time !== prevKeyframe.time) {
            t = (this.currentTime - prevKeyframe.time) / (nextKeyframe.time - prevKeyframe.time);
            t = Math.max(0, Math.min(1, t)); // 限制在[0,1]范围内
        }
        // 创建插值关键帧
        var interpolated = {
            time: this.currentTime
        };
        // 插值表情
        if (prevKeyframe.expression && nextKeyframe.expression) {
            // 如果表情类型相同，插值权重
            if (prevKeyframe.expression === nextKeyframe.expression) {
                interpolated.expression = prevKeyframe.expression;
                interpolated.expressionWeight = this.lerp(prevKeyframe.expressionWeight || 0, nextKeyframe.expressionWeight || 0, t);
            }
            else {
                // 如果表情类型不同，根据t选择
                interpolated.expression = t < 0.5 ? prevKeyframe.expression : nextKeyframe.expression;
                interpolated.expressionWeight = t < 0.5 ? prevKeyframe.expressionWeight : nextKeyframe.expressionWeight;
            }
        }
        else if (prevKeyframe.expression) {
            interpolated.expression = prevKeyframe.expression;
            interpolated.expressionWeight = prevKeyframe.expressionWeight;
        }
        else if (nextKeyframe.expression) {
            interpolated.expression = nextKeyframe.expression;
            interpolated.expressionWeight = nextKeyframe.expressionWeight;
        }
        // 插值口型
        if (prevKeyframe.viseme && nextKeyframe.viseme) {
            // 如果口型类型相同，插值权重
            if (prevKeyframe.viseme === nextKeyframe.viseme) {
                interpolated.viseme = prevKeyframe.viseme;
                interpolated.visemeWeight = this.lerp(prevKeyframe.visemeWeight || 0, nextKeyframe.visemeWeight || 0, t);
            }
            else {
                // 如果口型类型不同，根据t选择
                interpolated.viseme = t < 0.5 ? prevKeyframe.viseme : nextKeyframe.viseme;
                interpolated.visemeWeight = t < 0.5 ? prevKeyframe.visemeWeight : nextKeyframe.visemeWeight;
            }
        }
        else if (prevKeyframe.viseme) {
            interpolated.viseme = prevKeyframe.viseme;
            interpolated.visemeWeight = prevKeyframe.visemeWeight;
        }
        else if (nextKeyframe.viseme) {
            interpolated.viseme = nextKeyframe.viseme;
            interpolated.visemeWeight = nextKeyframe.visemeWeight;
        }
        return interpolated;
    };
    /**
     * 线性插值
     * @param a 起始值
     * @param b 结束值
     * @param t 插值因子[0,1]
     * @returns 插值结果
     */
    FacialAnimationEditorComponent.prototype.lerp = function (a, b, t) {
        return a + (b - a) * t;
    };
    /**
     * 播放动画
     * @returns 是否成功开始播放
     */
    FacialAnimationEditorComponent.prototype.play = function () {
        if (!this.currentClip)
            return false;
        this.isPlaying = true;
        this.eventEmitter.emit('playStarted', { clip: this.currentClip });
        return true;
    };
    /**
     * 暂停动画
     */
    FacialAnimationEditorComponent.prototype.pause = function () {
        this.isPlaying = false;
        this.eventEmitter.emit('playPaused', { time: this.currentTime });
    };
    /**
     * 停止动画
     */
    FacialAnimationEditorComponent.prototype.stop = function () {
        this.isPlaying = false;
        this.currentTime = 0;
        this.eventEmitter.emit('playStopped');
    };
    /**
     * 设置当前时间
     * @param time 时间（秒）
     */
    FacialAnimationEditorComponent.prototype.setTime = function (time) {
        if (!this.currentClip)
            return;
        this.currentTime = Math.max(0, Math.min(this.currentClip.duration, time));
        this.eventEmitter.emit('timeChanged', { time: this.currentTime });
    };
    /**
     * 获取当前时间
     * @returns 当前时间（秒）
     */
    FacialAnimationEditorComponent.prototype.getTime = function () {
        return this.currentTime;
    };
    /**
     * 设置播放速度
     * @param speed 播放速度
     */
    FacialAnimationEditorComponent.prototype.setPlaybackSpeed = function (speed) {
        this.playbackSpeed = Math.max(0.1, speed);
    };
    /**
     * 获取播放速度
     * @returns 播放速度
     */
    FacialAnimationEditorComponent.prototype.getPlaybackSpeed = function () {
        return this.playbackSpeed;
    };
    /**
     * 设置帧率
     * @param frameRate 帧率
     */
    FacialAnimationEditorComponent.prototype.setFrameRate = function (frameRate) {
        this.frameRate = Math.max(1, frameRate);
    };
    /**
     * 获取帧率
     * @returns 帧率
     */
    FacialAnimationEditorComponent.prototype.getFrameRate = function () {
        return this.frameRate;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationEditorComponent.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    FacialAnimationEditorComponent.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    FacialAnimationEditorComponent.prototype.update = function (deltaTime) {
        if (!this.isPlaying || !this.currentClip)
            return;
        // 更新时间
        this.currentTime += deltaTime * this.playbackSpeed;
        // 处理循环
        if (this.currentTime >= this.currentClip.duration) {
            if (this.currentClip.loop) {
                this.currentTime %= this.currentClip.duration;
                this.eventEmitter.emit('looped', { clip: this.currentClip });
            }
            else {
                this.currentTime = this.currentClip.duration;
                this.isPlaying = false;
                this.eventEmitter.emit('completed', { clip: this.currentClip });
            }
        }
        // 发送时间变化事件
        this.eventEmitter.emit('timeChanged', { time: this.currentTime });
    };
    /** 组件类型 */
    FacialAnimationEditorComponent.type = 'FacialAnimationEditor';
    return FacialAnimationEditorComponent;
}(Component_1.Component));
