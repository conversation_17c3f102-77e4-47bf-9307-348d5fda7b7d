"use strict";
/**
 * UIAnimationComponent.ts
 *
 * UI动画组件，用于为UI元素添加动画效果
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIAnimationComponent = exports.UIAnimation = exports.UIEasing = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
/**
 * 动画缓动函数
 */
var UIEasing = exports.UIEasing = /** @class */ (function () {
    function UIEasing() {
    }
    // 线性
    UIEasing.linear = function (t) { return t; };
    // 二次方
    UIEasing.quadIn = function (t) { return t * t; };
    UIEasing.quadOut = function (t) { return t * (2 - t); };
    UIEasing.quadInOut = function (t) { return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t; };
    // 三次方
    UIEasing.cubicIn = function (t) { return t * t * t; };
    UIEasing.cubicOut = function (t) { return (--t) * t * t + 1; };
    UIEasing.cubicInOut = function (t) { return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1; };
    // 四次方
    UIEasing.quartIn = function (t) { return t * t * t * t; };
    UIEasing.quartOut = function (t) { return 1 - (--t) * t * t * t; };
    UIEasing.quartInOut = function (t) { return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t; };
    // 指数
    UIEasing.expoIn = function (t) { return t === 0 ? 0 : Math.pow(2, 10 * (t - 1)); };
    UIEasing.expoOut = function (t) { return t === 1 ? 1 : 1 - Math.pow(2, -10 * t); };
    UIEasing.expoInOut = function (t) {
        if (t === 0)
            return 0;
        if (t === 1)
            return 1;
        if ((t *= 2) < 1)
            return 0.5 * Math.pow(2, 10 * (t - 1));
        return 0.5 * (2 - Math.pow(2, -10 * (t - 1)));
    };
    // 正弦
    UIEasing.sineIn = function (t) { return 1 - Math.cos(t * Math.PI / 2); };
    UIEasing.sineOut = function (t) { return Math.sin(t * Math.PI / 2); };
    UIEasing.sineInOut = function (t) { return 0.5 * (1 - Math.cos(Math.PI * t)); };
    // 弹性
    UIEasing.elasticIn = function (t) {
        if (t === 0)
            return 0;
        if (t === 1)
            return 1;
        return -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
    };
    UIEasing.elasticOut = function (t) {
        if (t === 0)
            return 0;
        if (t === 1)
            return 1;
        return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1;
    };
    UIEasing.elasticInOut = function (t) {
        if (t === 0)
            return 0;
        if (t === 1)
            return 1;
        t *= 2;
        if (t < 1) {
            return -0.5 * Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
        }
        return 0.5 * Math.pow(2, -10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI) + 1;
    };
    // 反弹
    UIEasing.bounceIn = function (t) { return 1 - UIEasing.bounceOut(1 - t); };
    UIEasing.bounceOut = function (t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        }
        else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        }
        else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        }
        else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    };
    UIEasing.bounceInOut = function (t) {
        if (t < 0.5)
            return UIEasing.bounceIn(t * 2) * 0.5;
        return UIEasing.bounceOut(t * 2 - 1) * 0.5 + 0.5;
    };
    return UIEasing;
}());
/**
 * UI动画类
 * 实现IUIAnimation接口
 */
var UIAnimation = /** @class */ (function () {
    /**
     * 构造函数
     * @param type 动画类型
     * @param target 目标UI元素
     * @param property 目标属性
     * @param from 起始值
     * @param to 结束值
     * @param duration 持续时间（毫秒）
     * @param options 其他选项
     */
    function UIAnimation(type, target, property, from, to, duration, options) {
        if (duration === void 0) { duration = 1000; }
        if (options === void 0) { options = {}; }
        this.progress = 0;
        this.startTime = 0;
        this.isPlaying = false;
        this.isPaused = false;
        this.isCompleted = false;
        this.type = type;
        this.target = target;
        this.property = property;
        this.from = from;
        this.to = to;
        this.duration = duration;
        this.delay = options.delay || 0;
        this.easing = options.easing || UIEasing.linear;
        this.loop = options.loop || false;
        this.onComplete = options.onComplete;
        this.onUpdate = options.onUpdate;
    }
    /**
     * 开始动画
     */
    UIAnimation.prototype.start = function () {
        this.startTime = Date.now() + this.delay;
        this.isPlaying = true;
        this.isPaused = false;
        this.isCompleted = false;
        this.progress = 0;
    };
    /**
     * 暂停动画
     */
    UIAnimation.prototype.pause = function () {
        if (this.isPlaying) {
            this.isPaused = true;
            this.isPlaying = false;
        }
    };
    /**
     * 恢复动画
     */
    UIAnimation.prototype.resume = function () {
        if (this.isPaused) {
            this.isPaused = false;
            this.isPlaying = true;
            // 调整开始时间，以便从当前进度继续
            this.startTime = Date.now() - (this.progress * this.duration);
        }
    };
    /**
     * 停止动画
     */
    UIAnimation.prototype.stop = function () {
        this.isPlaying = false;
        this.isPaused = false;
        this.isCompleted = false;
        this.progress = 0;
    };
    /**
     * 更新动画
     * @param _deltaTime 时间增量 - 未使用，使用 Date.now() 代替
     */
    UIAnimation.prototype.update = function (_deltaTime) {
        if (!this.isPlaying || this.isPaused || this.isCompleted)
            return;
        var currentTime = Date.now();
        // 如果还在延迟中，则不更新
        if (currentTime < this.startTime)
            return;
        // 计算进度
        var elapsedTime = currentTime - this.startTime;
        this.progress = Math.min(elapsedTime / this.duration, 1);
        // 应用缓动函数
        var easedProgress = this.easing(this.progress);
        // 计算当前值
        var currentValue = this.interpolate(this.from, this.to, easedProgress);
        // 应用到目标
        this.applyValue(currentValue);
        // 调用更新回调
        if (this.onUpdate) {
            this.onUpdate(currentValue);
        }
        // 检查是否完成
        if (this.progress >= 1) {
            if (this.loop) {
                // 如果循环，则重置开始时间
                this.startTime = currentTime;
                this.progress = 0;
            }
            else {
                // 否则标记为完成
                this.isPlaying = false;
                this.isCompleted = true;
                // 调用完成回调
                if (this.onComplete) {
                    this.onComplete();
                }
            }
        }
    };
    /**
     * 插值计算
     * @param from 起始值
     * @param to 结束值
     * @param progress 进度（0-1）
     * @returns 插值结果
     */
    UIAnimation.prototype.interpolate = function (from, to, progress) {
        // 数字插值
        if (typeof from === 'number' && typeof to === 'number') {
            return from + (to - from) * progress;
        }
        // Vector2插值
        if (from instanceof three_1.Vector2 && to instanceof three_1.Vector2) {
            return new three_1.Vector2().lerpVectors(from, to, progress);
        }
        // Vector3插值
        if (from instanceof three_1.Vector3 && to instanceof three_1.Vector3) {
            return new three_1.Vector3().lerpVectors(from, to, progress);
        }
        // 颜色插值
        if ((from instanceof three_1.Color || typeof from === 'string') &&
            (to instanceof three_1.Color || typeof to === 'string')) {
            var fromColor = from instanceof three_1.Color ? from : new three_1.Color(from);
            var toColor = to instanceof three_1.Color ? to : new three_1.Color(to);
            return new three_1.Color().lerpColors(fromColor, toColor, progress);
        }
        // 默认返回目标值
        return progress < 1 ? from : to;
    };
    /**
     * 应用值到目标
     * @param value 要应用的值
     */
    UIAnimation.prototype.applyValue = function (value) {
        switch (this.property) {
            case 'position':
                this.target.setPosition(value);
                break;
            case 'size':
                this.target.setSize(value);
                break;
            case 'opacity':
                this.target.setOpacity(value);
                break;
            case 'rotation':
                if (this.target.is3D) {
                    this.target.setRotation(value);
                }
                break;
            case 'scale':
                if (this.target.is3D) {
                    this.target.setScale(value);
                }
                break;
            case 'color':
                if (this.target.is3D) {
                    this.target.setFontColor(value.getStyle());
                }
                break;
            case 'backgroundColor':
                this.target.backgroundColor = value.getStyle();
                break;
            case 'borderColor':
                this.target.borderColor = value.getStyle();
                break;
            case 'fontColor':
                if (!this.target.is3D) {
                    this.target.textColor = value.getStyle();
                }
                else {
                    this.target.fontColor = value.getStyle();
                }
                break;
        }
    };
    return UIAnimation;
}());
exports.UIAnimation = UIAnimation;
/**
 * UI动画组件
 * 用于管理实体的UI动画
 */
var UIAnimationComponent = /** @class */ (function (_super) {
    __extends(UIAnimationComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     */
    function UIAnimationComponent(entity) {
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'UIAnimation') || this;
        // 动画列表
        _this.animations = [];
        // 设置实体引用
        _this.setEntity(entity);
        return _this;
    }
    /**
     * 添加动画
     * @param animation 要添加的动画
     * @returns 添加的动画
     */
    UIAnimationComponent.prototype.addAnimation = function (animation) {
        this.animations.push(animation);
        return animation;
    };
    /**
     * 移除动画
     * @param animation 要移除的动画
     */
    UIAnimationComponent.prototype.removeAnimation = function (animation) {
        var index = this.animations.indexOf(animation);
        if (index !== -1) {
            this.animations.splice(index, 1);
        }
    };
    /**
     * 清除所有动画
     */
    UIAnimationComponent.prototype.clearAnimations = function () {
        this.animations = [];
    };
    /**
     * 更新所有动画
     * @param deltaTime 时间增量（秒）
     */
    UIAnimationComponent.prototype.update = function (deltaTime) {
        // 将秒转换为毫秒
        var deltaMilliseconds = deltaTime * 1000;
        // 更新所有动画
        for (var i = this.animations.length - 1; i >= 0; i--) {
            var animation = this.animations[i];
            animation.update(deltaMilliseconds);
            // 如果动画已完成且不循环，则移除
            if (animation.isCompleted && !animation.loop) {
                this.animations.splice(i, 1);
            }
        }
    };
    /**
     * 创建并添加动画
     * @param type 动画类型
     * @param target 目标UI元素
     * @param property 目标属性
     * @param from 起始值
     * @param to 结束值
     * @param duration 持续时间（毫秒）
     * @param options 其他选项
     * @returns 创建的动画
     */
    UIAnimationComponent.prototype.createAnimation = function (type, target, property, from, to, duration, options) {
        if (duration === void 0) { duration = 1000; }
        if (options === void 0) { options = {}; }
        var animation = new UIAnimation(type, target, property, from, to, duration, options);
        this.addAnimation(animation);
        return animation;
    };
    return UIAnimationComponent;
}(Component_1.Component));
exports.UIAnimationComponent = UIAnimationComponent;
