"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkEntityComponent = void 0;
/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
var Component_1 = require("../../core/Component");
var NetworkEntity_1 = require("../NetworkEntity");
/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
var NetworkEntityComponent = exports.NetworkEntityComponent = /** @class */ (function (_super) {
    __extends(NetworkEntityComponent, _super);
    /**
     * 创建网络实体组件
     * @param props 组件属性
     */
    function NetworkEntityComponent(props) {
        var _this = _super.call(this, NetworkEntityComponent.type) || this;
        /** 上次同步时间 */
        _this.lastSyncTime = 0;
        /** 是否有待同步的更改 */
        _this.hasPendingChanges = false;
        /** 待同步的属性 */
        _this.pendingProperties = new Set();
        _this.entityId = props.entityId;
        _this.type = props.type || NetworkEntity_1.NetworkEntityType.DYNAMIC;
        _this.ownerId = props.ownerId;
        _this.syncMode = props.syncMode || NetworkEntity_1.NetworkEntitySyncMode.TRANSFORM;
        _this.ownershipMode = props.ownershipMode || NetworkEntity_1.NetworkEntityOwnershipMode.FIXED;
        _this.syncInterval = props.syncInterval || 100;
        _this.autoSync = props.autoSync !== undefined ? props.autoSync : true;
        _this.isLocallyOwned = props.isLocallyOwned || false;
        _this.canTransferOwnership = props.canTransferOwnership !== undefined ? props.canTransferOwnership : true;
        _this.syncPriority = props.syncPriority || 0;
        _this.syncDistance = props.syncDistance || 0;
        _this.syncGroup = props.syncGroup || 'default';
        _this.syncTags = props.syncTags || [];
        _this.customData = props.customData || {};
        return _this;
    }
    /**
     * 初始化组件
     */
    NetworkEntityComponent.prototype.initialize = function () {
        // 初始化逻辑
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkEntityComponent.prototype.update = function (deltaTime) {
        // 如果启用了自动同步，则检查是否需要同步
        if (this.autoSync && this.isLocallyOwned && this.hasPendingChanges) {
            var now = Date.now();
            // 检查是否达到同步间隔
            if (now - this.lastSyncTime >= this.syncInterval) {
                this.sync();
            }
        }
    };
    /**
     * 同步实体
     */
    NetworkEntityComponent.prototype.sync = function () {
        if (!this.hasPendingChanges) {
            return;
        }
        // 获取需要同步的数据
        var syncData = this.getSyncData();
        // 触发同步事件
        var entity = this.getEntity();
        if (entity && entity.emit) {
            entity.emit('networkSync', {
                entityId: this.entityId,
                ownerId: this.ownerId,
                data: syncData,
            });
        }
        // 更新同步时间
        this.lastSyncTime = Date.now();
        // 清除待同步标记
        this.hasPendingChanges = false;
        this.pendingProperties.clear();
    };
    /**
     * 获取同步数据
     * @returns 同步数据
     */
    NetworkEntityComponent.prototype.getSyncData = function () {
        var _a;
        var data = {
            entityId: this.entityId,
            type: this.type,
        };
        // 根据同步模式获取不同的数据
        switch (this.syncMode) {
            case NetworkEntity_1.NetworkEntitySyncMode.TRANSFORM:
                // 同步变换数据
                var transform = (_a = this.getEntity()) === null || _a === void 0 ? void 0 : _a.getComponent('Transform');
                if (transform) {
                    var position = transform.getPosition();
                    var rotation = transform.getRotationQuaternion();
                    var scale = transform.getScale();
                    data.transform = {
                        position: {
                            x: position.x,
                            y: position.y,
                            z: position.z,
                        },
                        rotation: {
                            x: rotation.x,
                            y: rotation.y,
                            z: rotation.z,
                            w: rotation.w,
                        },
                        scale: {
                            x: scale.x,
                            y: scale.y,
                            z: scale.z,
                        },
                    };
                }
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.PHYSICS:
                // 同步物理数据
                var physics = this.entity.getComponent('PhysicsBody');
                if (physics) {
                    data.physics = {
                        position: {
                            x: physics.getPosition().x,
                            y: physics.getPosition().y,
                            z: physics.getPosition().z,
                        },
                        rotation: {
                            x: physics.rotation.x,
                            y: physics.rotation.y,
                            z: physics.rotation.z,
                            w: physics.rotation.w,
                        },
                        velocity: {
                            x: physics.velocity.x,
                            y: physics.velocity.y,
                            z: physics.velocity.z,
                        },
                        angularVelocity: {
                            x: physics.angularVelocity.x,
                            y: physics.angularVelocity.y,
                            z: physics.angularVelocity.z,
                        },
                    };
                }
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.FULL:
                // 同步所有数据
                // 变换数据
                var fullTransform = this.entity.getComponent('Transform');
                if (fullTransform) {
                    var position = fullTransform.getPosition();
                    var rotation = fullTransform.getRotationQuaternion();
                    var scale = fullTransform.getScale();
                    data.transform = {
                        position: {
                            x: position.x,
                            y: position.y,
                            z: position.z,
                        },
                        rotation: {
                            x: rotation.x,
                            y: rotation.y,
                            z: rotation.z,
                            w: rotation.w,
                        },
                        scale: {
                            x: scale.x,
                            y: scale.y,
                            z: scale.z,
                        },
                    };
                }
                // 物理数据
                var fullPhysics = this.entity.getComponent('PhysicsBody');
                if (fullPhysics) {
                    data.physics = {
                        position: {
                            x: fullPhysics.getPosition().x,
                            y: fullPhysics.getPosition().y,
                            z: fullPhysics.getPosition().z,
                        },
                        rotation: {
                            x: fullPhysics.rotation.x,
                            y: fullPhysics.rotation.y,
                            z: fullPhysics.rotation.z,
                            w: fullPhysics.rotation.w,
                        },
                        velocity: {
                            x: fullPhysics.velocity.x,
                            y: fullPhysics.velocity.y,
                            z: fullPhysics.velocity.z,
                        },
                        angularVelocity: {
                            x: fullPhysics.angularVelocity.x,
                            y: fullPhysics.angularVelocity.y,
                            z: fullPhysics.angularVelocity.z,
                        },
                    };
                }
                // 其他组件数据
                // ...
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.CUSTOM:
                // 自定义同步数据
                data.customData = this.customData;
                break;
        }
        return data;
    };
    /**
     * 应用同步数据
     * @param data 同步数据
     */
    NetworkEntityComponent.prototype.applySyncData = function (data) {
        // 根据同步模式应用不同的数据
        switch (this.syncMode) {
            case NetworkEntity_1.NetworkEntitySyncMode.TRANSFORM:
                // 应用变换数据
                if (data.transform) {
                    var transform = this.entity.getComponent('Transform');
                    if (transform) {
                        if (data.transform.position) {
                            transform.setPosition(data.transform.getPosition().x, data.transform.getPosition().y, data.transform.getPosition().z);
                        }
                        if (data.transform.rotation) {
                            transform.setRotationQuaternion(data.transform.rotation.x, data.transform.rotation.y, data.transform.rotation.z, data.transform.rotation.w);
                        }
                        if (data.transform.scale) {
                            transform.setScale(data.transform.scale.x, data.transform.scale.y, data.transform.scale.z);
                        }
                    }
                }
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.PHYSICS:
                // 应用物理数据
                if (data.physics) {
                    var physics = this.entity.getComponent('PhysicsBody');
                    if (physics) {
                        if (data.physics.position) {
                            physics.setPosition(data.physics.getPosition().x, data.physics.getPosition().y, data.physics.getPosition().z);
                        }
                        if (data.physics.rotation) {
                            physics.setRotationQuaternion(data.physics.rotation.x, data.physics.rotation.y, data.physics.rotation.z, data.physics.rotation.w);
                        }
                        if (data.physics.velocity) {
                            physics.velocity.set(data.physics.velocity.x, data.physics.velocity.y, data.physics.velocity.z);
                        }
                        if (data.physics.angularVelocity) {
                            physics.angularVelocity.set(data.physics.angularVelocity.x, data.physics.angularVelocity.y, data.physics.angularVelocity.z);
                        }
                    }
                }
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.FULL:
                // 应用所有数据
                // 变换数据
                if (data.transform) {
                    var fullTransform = this.entity.getComponent('Transform');
                    if (fullTransform) {
                        if (data.transform.position) {
                            fullTransform.setPosition(data.transform.getPosition().x, data.transform.getPosition().y, data.transform.getPosition().z);
                        }
                        if (data.transform.rotation) {
                            fullTransform.setRotationQuaternion(data.transform.rotation.x, data.transform.rotation.y, data.transform.rotation.z, data.transform.rotation.w);
                        }
                        if (data.transform.scale) {
                            fullTransform.setScale(data.transform.scale.x, data.transform.scale.y, data.transform.scale.z);
                        }
                    }
                }
                // 物理数据
                if (data.physics) {
                    var fullPhysics = this.entity.getComponent('PhysicsBody');
                    if (fullPhysics) {
                        if (data.physics.position) {
                            fullPhysics.setPosition(data.physics.getPosition().x, data.physics.getPosition().y, data.physics.getPosition().z);
                        }
                        if (data.physics.rotation) {
                            fullPhysics.setRotationQuaternion(data.physics.rotation.x, data.physics.rotation.y, data.physics.rotation.z, data.physics.rotation.w);
                        }
                        if (data.physics.velocity) {
                            fullPhysics.velocity.set(data.physics.velocity.x, data.physics.velocity.y, data.physics.velocity.z);
                        }
                        if (data.physics.angularVelocity) {
                            fullPhysics.angularVelocity.set(data.physics.angularVelocity.x, data.physics.angularVelocity.y, data.physics.angularVelocity.z);
                        }
                    }
                }
                // 其他组件数据
                // ...
                break;
            case NetworkEntity_1.NetworkEntitySyncMode.CUSTOM:
                // 应用自定义同步数据
                if (data.customData) {
                    this.customData = __assign(__assign({}, this.customData), data.customData);
                }
                break;
        }
    };
    /**
     * 标记属性为待同步
     * @param property 属性名
     */
    NetworkEntityComponent.prototype.markPropertyDirty = function (property) {
        this.hasPendingChanges = true;
        this.pendingProperties.add(property);
    };
    /**
     * 标记所有属性为待同步
     */
    NetworkEntityComponent.prototype.markAllPropertiesDirty = function () {
        this.hasPendingChanges = true;
    };
    /**
     * 请求所有权
     * @returns 是否成功
     */
    NetworkEntityComponent.prototype.requestOwnership = function () {
        if (!this.canTransferOwnership) {
            return false;
        }
        // 触发请求所有权事件
        this.entity.emit('networkRequestOwnership', {
            entityId: this.entityId,
            currentOwnerId: this.ownerId,
        });
        return true;
    };
    /**
     * 转移所有权
     * @param newOwnerId 新所有者ID
     * @returns 是否成功
     */
    NetworkEntityComponent.prototype.transferOwnership = function (newOwnerId) {
        if (!this.canTransferOwnership || !this.isLocallyOwned) {
            return false;
        }
        // 触发转移所有权事件
        this.entity.emit('networkTransferOwnership', {
            entityId: this.entityId,
            currentOwnerId: this.ownerId,
            newOwnerId: newOwnerId,
        });
        return true;
    };
    /**
     * 设置所有者
     * @param ownerId 所有者ID
     * @param isLocallyOwned 是否是本地拥有
     */
    NetworkEntityComponent.prototype.setOwner = function (ownerId, isLocallyOwned) {
        this.ownerId = ownerId;
        this.isLocallyOwned = isLocallyOwned;
        // 触发所有权变更事件
        this.entity.emit('networkOwnershipChanged', {
            entityId: this.entityId,
            ownerId: ownerId,
            isLocallyOwned: isLocallyOwned,
        });
    };
    /**
     * 销毁组件
     */
    NetworkEntityComponent.prototype.dispose = function () {
        // 销毁逻辑
    };
    /** 组件类型 */
    NetworkEntityComponent.type = 'NetworkEntity';
    return NetworkEntityComponent;
}(Component_1.Component));
