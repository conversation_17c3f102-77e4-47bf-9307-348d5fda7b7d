"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebRTCConnectionManager = void 0;
/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var WebRTCConnection_1 = require("./WebRTCConnection");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
var BandwidthController_1 = require("./BandwidthController");
/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
var WebRTCConnectionManager = /** @class */ (function (_super) {
    __extends(WebRTCConnectionManager, _super);
    /**
     * 创建WebRTC连接管理器
     * @param config 配置
     */
    function WebRTCConnectionManager(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** WebRTC连接映射表 */
        _this.connections = new Map();
        /** 重连尝试次数映射表 */
        _this.reconnectAttempts = new Map();
        /** 重连定时器ID映射表 */
        _this.reconnectTimers = new Map();
        /** 连接超时定时器ID映射表 */
        _this.connectionTimeoutTimers = new Map();
        /** 网络质量监控器 */
        _this.networkQualityMonitor = null;
        /** 带宽控制器 */
        _this.bandwidthController = null;
        /** 本地用户ID */
        _this.localUserId = null;
        // 默认配置
        _this.config = __assign({ iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
            ], enableDataChannel: true, enableAudio: false, enableVideo: false, enableScreenShare: false, dataChannelConfig: {
                ordered: true,
            }, audioConstraints: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
            }, videoConstraints: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                frameRate: { ideal: 30 },
            }, useCompression: true, maxReconnectAttempts: 5, reconnectInterval: 3000, heartbeatInterval: 10000, connectionTimeout: 30000, autoReconnect: true, useNetworkQualityMonitor: true, useBandwidthController: true }, config);
        // 如果使用网络质量监控，则创建监控器
        if (_this.config.useNetworkQualityMonitor) {
            _this.networkQualityMonitor = new NetworkQualityMonitor_1.NetworkQualityMonitor();
        }
        // 如果使用带宽控制，则创建控制器
        if (_this.config.useBandwidthController) {
            _this.bandwidthController = new BandwidthController_1.BandwidthController();
        }
        return _this;
    }
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     */
    WebRTCConnectionManager.prototype.initialize = function (localUserId) {
        this.localUserId = localUserId;
        // 启动网络质量监控
        if (this.networkQualityMonitor) {
            this.networkQualityMonitor.startSampling();
        }
        // 启动带宽控制
        if (this.bandwidthController) {
            this.bandwidthController.startAutoAdjust();
        }
    };
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @param config 连接配置
     * @returns WebRTC连接
     */
    WebRTCConnectionManager.prototype.createConnection = function (userId, config) {
        // 检查是否已存在连接
        if (this.connections.has(userId)) {
            return this.connections.get(userId);
        }
        // 合并配置
        var connectionConfig = __assign({ enableDataChannel: this.config.enableDataChannel, enableAudio: this.config.enableAudio, enableVideo: this.config.enableVideo, enableScreenShare: this.config.enableScreenShare, dataChannelConfig: this.config.dataChannelConfig, audioConstraints: this.config.audioConstraints, videoConstraints: this.config.videoConstraints, useCompression: this.config.useCompression }, config);
        // 创建WebRTC连接
        var connection = new WebRTCConnection_1.WebRTCConnection(userId, this.config.iceServers, connectionConfig);
        // 设置心跳间隔
        connection.setHeartbeatInterval(this.config.heartbeatInterval);
        // 设置事件监听器
        this.setupConnectionListeners(connection);
        // 添加到连接映射表
        this.connections.set(userId, connection);
        // 初始化重连尝试次数
        this.reconnectAttempts.set(userId, 0);
        // 创建对等连接
        connection.createConnection();
        // 设置连接超时定时器
        this.setConnectionTimeout(userId);
        // 触发连接创建事件
        this.emit('connectionCreated', userId, connection);
        return connection;
    };
    /**
     * 获取WebRTC连接
     * @param userId 远程用户ID
     * @returns WebRTC连接
     */
    WebRTCConnectionManager.prototype.getConnection = function (userId) {
        return this.connections.get(userId);
    };
    /**
     * 关闭WebRTC连接
     * @param userId 远程用户ID
     * @returns 是否成功关闭
     */
    WebRTCConnectionManager.prototype.closeConnection = function (userId) {
        var connection = this.connections.get(userId);
        if (!connection) {
            return false;
        }
        // 清除重连定时器
        this.clearReconnectTimer(userId);
        // 清除连接超时定时器
        this.clearConnectionTimeout(userId);
        // 断开连接
        connection.disconnect();
        // 从连接映射表中移除
        this.connections.delete(userId);
        // 从重连尝试次数映射表中移除
        this.reconnectAttempts.delete(userId);
        // 触发连接关闭事件
        this.emit('connectionClosed', userId);
        return true;
    };
    /**
     * 关闭所有WebRTC连接
     */
    WebRTCConnectionManager.prototype.closeAllConnections = function () {
        for (var _i = 0, _a = this.connections.keys(); _i < _a.length; _i++) {
            var userId = _a[_i];
            this.closeConnection(userId);
        }
    };
    /**
     * 设置连接事件监听器
     * @param connection WebRTC连接
     */
    WebRTCConnectionManager.prototype.setupConnectionListeners = function (connection) {
        var _this = this;
        var userId = connection.getUserId();
        // 连接成功事件
        connection.on('connected', function () {
            // 清除连接超时定时器
            _this.clearConnectionTimeout(userId);
            // 重置重连尝试次数
            _this.reconnectAttempts.set(userId, 0);
            // 触发连接成功事件
            _this.emit('connectionConnected', userId, connection);
        });
        // 连接断开事件
        connection.on('disconnected', function () {
            // 如果启用自动重连，则尝试重连
            if (_this.config.autoReconnect) {
                _this.attemptReconnect(userId);
            }
            // 触发连接断开事件
            _this.emit('connectionDisconnected', userId, connection);
        });
        // 连接错误事件
        connection.on('error', function (error) {
            Debug_1.Debug.error('WebRTCConnectionManager', "Connection error for user ".concat(userId, ":"), error);
            // 如果启用自动重连，则尝试重连
            if (_this.config.autoReconnect) {
                _this.attemptReconnect(userId);
            }
            // 触发连接错误事件
            _this.emit('connectionError', userId, error);
        });
        // 提议事件
        connection.on('offer', function (offer) {
            _this.emit('offer', userId, offer);
        });
        // 应答事件
        connection.on('answer', function (answer) {
            _this.emit('answer', userId, answer);
        });
        // ICE候选事件
        connection.on('iceCandidate', function (candidate) {
            _this.emit('iceCandidate', userId, candidate);
        });
        // 数据通道打开事件
        connection.on('dataChannelOpen', function () {
            _this.emit('dataChannelOpen', userId);
        });
        // 数据通道关闭事件
        connection.on('dataChannelClose', function () {
            _this.emit('dataChannelClose', userId);
        });
        // 数据通道消息事件
        connection.on('dataChannelMessage', function (message) {
            _this.emit('dataChannelMessage', userId, message);
        });
        // 媒体流添加事件
        connection.on('streamAdded', function (stream) {
            _this.emit('streamAdded', userId, stream);
        });
        // 媒体流移除事件
        connection.on('streamRemoved', function (stream) {
            _this.emit('streamRemoved', userId, stream);
        });
    };
    /**
     * 尝试重连
     * @param userId 远程用户ID
     */
    WebRTCConnectionManager.prototype.attemptReconnect = function (userId) {
        var _this = this;
        // 检查是否已达到最大重连次数
        var attempts = this.reconnectAttempts.get(userId) || 0;
        if (attempts >= this.config.maxReconnectAttempts) {
            Debug_1.Debug.warn('WebRTCConnectionManager', "Max reconnect attempts reached for user ".concat(userId));
            // 关闭连接
            this.closeConnection(userId);
            // 触发重连失败事件
            this.emit('reconnectFailed', userId);
            return;
        }
        // 增加重连尝试次数
        this.reconnectAttempts.set(userId, attempts + 1);
        // 清除之前的重连定时器
        this.clearReconnectTimer(userId);
        // 设置重连定时器
        var timerId = window.setTimeout(function () {
            // 获取连接
            var connection = _this.connections.get(userId);
            if (!connection) {
                return;
            }
            // 重新创建连接
            connection.createConnection();
            // 设置连接超时定时器
            _this.setConnectionTimeout(userId);
            // 触发重连尝试事件
            _this.emit('reconnectAttempt', userId, attempts + 1);
        }, this.config.reconnectInterval);
        // 保存定时器ID
        this.reconnectTimers.set(userId, timerId);
    };
    /**
     * 清除重连定时器
     * @param userId 远程用户ID
     */
    WebRTCConnectionManager.prototype.clearReconnectTimer = function (userId) {
        var timerId = this.reconnectTimers.get(userId);
        if (timerId !== undefined) {
            clearTimeout(timerId);
            this.reconnectTimers.delete(userId);
        }
    };
    /**
     * 设置连接超时定时器
     * @param userId 远程用户ID
     */
    WebRTCConnectionManager.prototype.setConnectionTimeout = function (userId) {
        var _this = this;
        // 清除之前的超时定时器
        this.clearConnectionTimeout(userId);
        // 设置超时定时器
        var timerId = window.setTimeout(function () {
            // 获取连接
            var connection = _this.connections.get(userId);
            if (!connection) {
                return;
            }
            // 检查连接状态
            if (connection.getState() !== WebRTCConnection_1.WebRTCConnectionState.CONNECTED) {
                Debug_1.Debug.warn('WebRTCConnectionManager', "Connection timeout for user ".concat(userId));
                // 如果启用自动重连，则尝试重连
                if (_this.config.autoReconnect) {
                    _this.attemptReconnect(userId);
                }
                else {
                    // 否则关闭连接
                    _this.closeConnection(userId);
                }
                // 触发连接超时事件
                _this.emit('connectionTimeout', userId);
            }
        }, this.config.connectionTimeout);
        // 保存定时器ID
        this.connectionTimeoutTimers.set(userId, timerId);
    };
    /**
     * 清除连接超时定时器
     * @param userId 远程用户ID
     */
    WebRTCConnectionManager.prototype.clearConnectionTimeout = function (userId) {
        var timerId = this.connectionTimeoutTimers.get(userId);
        if (timerId !== undefined) {
            clearTimeout(timerId);
            this.connectionTimeoutTimers.delete(userId);
        }
    };
    /**
     * 处理WebRTC提议
     * @param userId 远程用户ID
     * @param offer 提议
     */
    WebRTCConnectionManager.prototype.handleOffer = function (userId, offer) {
        // 获取连接
        var connection = this.connections.get(userId);
        // 如果连接不存在，则创建连接
        if (!connection) {
            connection = this.createConnection(userId);
        }
        // 处理提议
        connection.handleOffer(offer);
    };
    /**
     * 处理WebRTC应答
     * @param userId 远程用户ID
     * @param answer 应答
     */
    WebRTCConnectionManager.prototype.handleAnswer = function (userId, answer) {
        // 获取连接
        var connection = this.connections.get(userId);
        if (!connection) {
            Debug_1.Debug.warn('WebRTCConnectionManager', "No connection found for user ".concat(userId));
            return;
        }
        // 处理应答
        connection.handleAnswer(answer);
    };
    /**
     * 处理WebRTC ICE候选
     * @param userId 远程用户ID
     * @param candidate ICE候选
     */
    WebRTCConnectionManager.prototype.handleIceCandidate = function (userId, candidate) {
        // 获取连接
        var connection = this.connections.get(userId);
        if (!connection) {
            Debug_1.Debug.warn('WebRTCConnectionManager', "No connection found for user ".concat(userId));
            return;
        }
        // 处理ICE候选
        connection.handleIceCandidate(candidate);
    };
    /**
     * 发送消息到用户
     * @param userId 远程用户ID
     * @param message 消息
     * @returns 是否成功发送
     */
    WebRTCConnectionManager.prototype.sendMessage = function (userId, message) {
        // 获取连接
        var connection = this.connections.get(userId);
        if (!connection) {
            Debug_1.Debug.warn('WebRTCConnectionManager', "No connection found for user ".concat(userId));
            return false;
        }
        // 发送消息
        connection.send(message);
        return true;
    };
    /**
     * 广播消息到所有用户
     * @param message 消息
     * @param excludeUserIds 排除的用户ID列表
     */
    WebRTCConnectionManager.prototype.broadcastMessage = function (message, excludeUserIds) {
        if (excludeUserIds === void 0) { excludeUserIds = []; }
        for (var _i = 0, _a = this.connections.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], userId = _b[0], connection = _b[1];
            // 跳过排除的用户
            if (excludeUserIds.includes(userId)) {
                continue;
            }
            // 发送消息
            connection.send(message);
        }
    };
    /**
     * 获取所有连接的用户ID
     * @returns 用户ID列表
     */
    WebRTCConnectionManager.prototype.getConnectedUserIds = function () {
        var userIds = [];
        for (var _i = 0, _a = this.connections.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], userId = _b[0], connection = _b[1];
            if (connection.getState() === WebRTCConnection_1.WebRTCConnectionState.CONNECTED) {
                userIds.push(userId);
            }
        }
        return userIds;
    };
    /**
     * 获取连接数量
     * @returns 连接数量
     */
    WebRTCConnectionManager.prototype.getConnectionCount = function () {
        return this.connections.size;
    };
    /**
     * 获取已连接的连接数量
     * @returns 已连接的连接数量
     */
    WebRTCConnectionManager.prototype.getConnectedCount = function () {
        var count = 0;
        for (var _i = 0, _a = this.connections.values(); _i < _a.length; _i++) {
            var connection = _a[_i];
            if (connection.getState() === WebRTCConnection_1.WebRTCConnectionState.CONNECTED) {
                count++;
            }
        }
        return count;
    };
    /**
     * 销毁管理器
     */
    WebRTCConnectionManager.prototype.dispose = function () {
        // 关闭所有连接
        this.closeAllConnections();
        // 销毁网络质量监控器
        if (this.networkQualityMonitor) {
            this.networkQualityMonitor.dispose();
            this.networkQualityMonitor = null;
        }
        // 销毁带宽控制器
        if (this.bandwidthController) {
            this.bandwidthController.dispose();
            this.bandwidthController = null;
        }
        // 移除所有监听器
        this.removeAllListeners();
    };
    return WebRTCConnectionManager;
}(EventEmitter_1.EventEmitter));
exports.WebRTCConnectionManager = WebRTCConnectionManager;
