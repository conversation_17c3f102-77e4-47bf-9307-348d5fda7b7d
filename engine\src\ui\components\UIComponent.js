"use strict";
/**
 * UIComponent.ts
 *
 * UI组件类，用于管理UI元素的基本属性和行为
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIComponent = exports.UIComponentType = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var IUIElement_1 = require("../interfaces/IUIElement");
/**
 * UI组件类型
 */
var UIComponentType;
(function (UIComponentType) {
    UIComponentType["BASE"] = "base";
    UIComponentType["CONTAINER"] = "container";
    UIComponentType["BUTTON"] = "button";
    UIComponentType["TEXT"] = "text";
    UIComponentType["IMAGE"] = "image";
    UIComponentType["INPUT"] = "input";
    UIComponentType["SLIDER"] = "slider";
    UIComponentType["CHECKBOX"] = "checkbox";
    UIComponentType["DROPDOWN"] = "dropdown";
    UIComponentType["PANEL"] = "panel";
    UIComponentType["WINDOW"] = "window";
    UIComponentType["CUSTOM"] = "custom";
})(UIComponentType || (exports.UIComponentType = UIComponentType = {}));
/**
 * UI组件类
 * 用于管理UI元素的基本属性和行为
 */
var UIComponent = /** @class */ (function (_super) {
    __extends(UIComponent, _super);
    /**
     * 构造函数
     * @param props UI组件属性
     */
    function UIComponent(props) {
        if (props === void 0) { props = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'UIComponent') || this;
        _this.children = [];
        _this.visible = true;
        _this.interactive = true;
        _this.opacity = 1.0;
        // 额外属性
        _this.uiType = UIComponentType.BASE;
        _this.zIndex = 0;
        _this.layoutType = IUIElement_1.UILayoutType.NONE;
        _this.layoutParams = {};
        _this.borderWidth = 0;
        _this.borderRadius = 0;
        _this.padding = { top: 0, right: 0, bottom: 0, left: 0 };
        _this.margin = { top: 0, right: 0, bottom: 0, left: 0 };
        _this.data = {};
        _this.tags = [];
        _this.is3D = false;
        // 事件处理器
        _this.eventHandlers = new Map();
        // 设置实体引用（将在组件添加到实体时设置）
        _this.entity = null; // 临时设置，将在onAttach中正确设置
        _this.id = props.id || "ui-".concat(Date.now());
        _this.uiType = props.type || UIComponentType.BASE;
        _this.visible = props.visible !== undefined ? props.visible : true;
        _this.interactive = props.interactive !== undefined ? props.interactive : true;
        _this.position = props.position || new three_1.Vector3(0, 0, 0);
        _this.size = props.size || new three_1.Vector2(100, 100);
        _this.opacity = props.opacity !== undefined ? props.opacity : 1.0;
        _this.zIndex = props.zIndex || 0;
        _this.layoutType = props.layoutType || IUIElement_1.UILayoutType.NONE;
        _this.layoutParams = props.layoutParams || {};
        _this.backgroundColor = props.backgroundColor;
        _this.borderColor = props.borderColor;
        _this.borderWidth = props.borderWidth || 0;
        _this.borderRadius = props.borderRadius || 0;
        _this.is3D = props.is3D || false;
        // 设置内边距
        if (props.padding !== undefined) {
            if (typeof props.padding === 'number') {
                _this.padding = { top: props.padding, right: props.padding, bottom: props.padding, left: props.padding };
            }
            else {
                _this.padding = {
                    top: props.padding.top || 0,
                    right: props.padding.right || 0,
                    bottom: props.padding.bottom || 0,
                    left: props.padding.left || 0
                };
            }
        }
        // 设置外边距
        if (props.margin !== undefined) {
            if (typeof props.margin === 'number') {
                _this.margin = { top: props.margin, right: props.margin, bottom: props.margin, left: props.margin };
            }
            else {
                _this.margin = {
                    top: props.margin.top || 0,
                    right: props.margin.right || 0,
                    bottom: props.margin.bottom || 0,
                    left: props.margin.left || 0
                };
            }
        }
        // 设置标签
        _this.tags = props.tags || [];
        // 设置数据
        _this.data = props.data || {};
        // 设置事件处理器
        if (props.onClick)
            _this.addEventListener('click', props.onClick);
        if (props.onHover)
            _this.addEventListener('hover', props.onHover);
        if (props.onDragStart)
            _this.addEventListener('dragstart', props.onDragStart);
        if (props.onDrag)
            _this.addEventListener('drag', props.onDrag);
        if (props.onDragEnd)
            _this.addEventListener('dragend', props.onDragEnd);
        return _this;
    }
    /**
     * 当组件附加到实体时调用
     */
    UIComponent.prototype.onAttach = function () {
        var entity = this.getEntity();
        if (entity) {
            this.entity = entity;
            // 如果ID还是默认的，则使用实体ID
            if (this.id.startsWith('ui-') && this.id.includes(Date.now().toString())) {
                this.id = "ui-".concat(entity.id);
            }
        }
    };
    /**
     * 添加子元素
     * @param child 要添加的子元素
     */
    UIComponent.prototype.addChild = function (child) {
        if (!this.children.includes(child)) {
            this.children.push(child);
            if (child instanceof UIComponent) {
                child.parent = this;
            }
        }
    };
    /**
     * 移除子元素
     * @param child 要移除的子元素
     */
    UIComponent.prototype.removeChild = function (child) {
        var index = this.children.indexOf(child);
        if (index !== -1) {
            this.children.splice(index, 1);
            if (child instanceof UIComponent) {
                child.parent = undefined;
            }
        }
    };
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     */
    UIComponent.prototype.update = function (deltaTime) {
        // 更新子元素
        for (var _i = 0, _a = this.children; _i < _a.length; _i++) {
            var child = _a[_i];
            child.update(deltaTime);
        }
    };
    /**
     * 渲染UI元素
     */
    UIComponent.prototype.render = function () {
        // 渲染子元素
        for (var _i = 0, _a = this.children; _i < _a.length; _i++) {
            var child = _a[_i];
            child.render();
        }
    };
    /**
     * 销毁UI元素
     */
    UIComponent.prototype.dispose = function () {
        // 销毁子元素
        for (var _i = 0, _a = __spreadArray([], this.children, true); _i < _a.length; _i++) {
            var child = _a[_i];
            child.dispose();
        }
        // 清空子元素列表
        this.children = [];
        // 清空事件处理器
        this.eventHandlers.clear();
    };
    /**
     * 添加事件监听器
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    UIComponent.prototype.addEventListener = function (eventType, handler) {
        this.eventHandlers.set(eventType, handler);
    };
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     */
    UIComponent.prototype.removeEventListener = function (eventType) {
        this.eventHandlers.delete(eventType);
    };
    /**
     * 触发事件
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    UIComponent.prototype.triggerEvent = function (eventType, eventData) {
        var handler = this.eventHandlers.get(eventType);
        if (handler) {
            handler(eventData);
        }
    };
    /**
     * 设置位置
     * @param position 新位置
     */
    UIComponent.prototype.setPosition = function (position) {
        this.position = position;
    };
    /**
     * 设置尺寸
     * @param size 新尺寸
     */
    UIComponent.prototype.setSize = function (size) {
        this.size = size;
    };
    /**
     * 设置可见性
     * @param visible 是否可见
     */
    UIComponent.prototype.setVisible = function (visible) {
        this.visible = visible;
    };
    /**
     * 设置交互性
     * @param interactive 是否可交互
     */
    UIComponent.prototype.setInteractive = function (interactive) {
        this.interactive = interactive;
    };
    /**
     * 设置透明度
     * @param opacity 透明度值
     */
    UIComponent.prototype.setOpacity = function (opacity) {
        this.opacity = Math.max(0, Math.min(1, opacity));
    };
    /**
     * 添加标签
     * @param tag 要添加的标签
     */
    UIComponent.prototype.addTag = function (tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    };
    /**
     * 移除标签
     * @param tag 要移除的标签
     */
    UIComponent.prototype.removeTag = function (tag) {
        var index = this.tags.indexOf(tag);
        if (index !== -1) {
            this.tags.splice(index, 1);
        }
    };
    /**
     * 检查是否有指定标签
     * @param tag 要检查的标签
     */
    UIComponent.prototype.hasTag = function (tag) {
        return this.tags.includes(tag);
    };
    return UIComponent;
}(Component_1.Component));
exports.UIComponent = UIComponent;
