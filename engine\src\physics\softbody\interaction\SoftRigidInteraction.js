"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftRigidInteraction = void 0;
var CANNON = require("cannon-es");
var SpatialPartitioning_1 = require("../optimization/SpatialPartitioning");
/**
 * 软体与刚体交互系统
 * 处理软体与刚体之间的碰撞和交互
 */
var SoftRigidInteraction = /** @class */ (function () {
    /**
     * 创建软体与刚体交互系统
     * @param options 软体与刚体交互系统选项
     */
    function SoftRigidInteraction(options) {
        if (options === void 0) { options = {}; }
        /** 空间分区系统 */
        this.spatialPartitioning = null;
        /** 软体组件列表 */
        this.softBodies = [];
        /** 刚体组件列表 */
        this.rigidBodies = [];
        /** 碰撞对列表 */
        this.collisionPairs = [];
        this.enabled = options.enabled !== undefined ? options.enabled : true;
        this.collisionRadius = options.collisionRadius || 0.1;
        this.collisionResponse = options.collisionResponse || 1.0;
        this.friction = options.friction || 0.3;
        this.useSpatialPartitioning = options.useSpatialPartitioning !== undefined ? options.useSpatialPartitioning : true;
        // 如果启用空间分区，创建空间分区系统
        if (this.useSpatialPartitioning) {
            this.spatialPartitioning = new SpatialPartitioning_1.SpatialPartitioning();
        }
    }
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    SoftRigidInteraction.prototype.addSoftBody = function (softBody) {
        if (!this.softBodies.includes(softBody)) {
            this.softBodies.push(softBody);
        }
    };
    /**
     * 移除软体组件
     * @param softBody 软体组件
     */
    SoftRigidInteraction.prototype.removeSoftBody = function (softBody) {
        var index = this.softBodies.indexOf(softBody);
        if (index !== -1) {
            this.softBodies.splice(index, 1);
        }
    };
    /**
     * 添加刚体组件
     * @param rigidBody 刚体组件
     */
    SoftRigidInteraction.prototype.addRigidBody = function (rigidBody) {
        if (!this.rigidBodies.includes(rigidBody)) {
            this.rigidBodies.push(rigidBody);
        }
    };
    /**
     * 移除刚体组件
     * @param rigidBody 刚体组件
     */
    SoftRigidInteraction.prototype.removeRigidBody = function (rigidBody) {
        var index = this.rigidBodies.indexOf(rigidBody);
        if (index !== -1) {
            this.rigidBodies.splice(index, 1);
        }
    };
    /**
     * 更新交互
     * @param deltaTime 帧间隔时间（秒）
     */
    SoftRigidInteraction.prototype.update = function (deltaTime) {
        if (!this.enabled)
            return;
        // 清空碰撞对列表
        this.collisionPairs.length = 0;
        // 更新空间分区
        if (this.useSpatialPartitioning && this.spatialPartitioning) {
            this.updateSpatialPartitioning();
        }
        // 检测碰撞
        this.detectCollisions();
        // 解决碰撞
        this.resolveCollisions(deltaTime);
    };
    /**
     * 更新空间分区
     */
    SoftRigidInteraction.prototype.updateSpatialPartitioning = function () {
        if (!this.spatialPartitioning)
            return;
        // 清空空间分区
        this.spatialPartitioning.clear();
        // 添加所有粒子到空间分区
        for (var _i = 0, _a = this.softBodies; _i < _a.length; _i++) {
            var softBody = _a[_i];
            var particles = softBody.getParticles();
            if (particles) {
                for (var _b = 0, particles_1 = particles; _b < particles_1.length; _b++) {
                    var particle = particles_1[_b];
                    this.spatialPartitioning.addParticle(particle);
                }
            }
        }
    };
    /**
     * 检测碰撞
     */
    SoftRigidInteraction.prototype.detectCollisions = function () {
        // 遍历所有刚体
        for (var _i = 0, _a = this.rigidBodies; _i < _a.length; _i++) {
            var rigidBody = _a[_i];
            var cannonBody = rigidBody.getCannonBody();
            if (!cannonBody)
                continue;
            // 获取刚体的碰撞形状
            var shapes = cannonBody.shapes;
            if (!shapes || shapes.length === 0)
                continue;
            // 遍历所有软体
            for (var _b = 0, _c = this.softBodies; _b < _c.length; _b++) {
                var softBody = _c[_b];
                var particles = softBody.getParticles();
                if (!particles)
                    continue;
                // 使用空间分区或暴力检测
                if (this.useSpatialPartitioning && this.spatialPartitioning) {
                    // 获取刚体附近的粒子
                    var nearbyParticles = this.spatialPartitioning.getNearbyParticles(cannonBody.position, cannonBody.boundingRadius + this.collisionRadius);
                    // 检测碰撞
                    for (var _d = 0, nearbyParticles_1 = nearbyParticles; _d < nearbyParticles_1.length; _d++) {
                        var particle = nearbyParticles_1[_d];
                        this.checkParticleRigidBodyCollision(particle, cannonBody);
                    }
                }
                else {
                    // 暴力检测
                    for (var _e = 0, particles_2 = particles; _e < particles_2.length; _e++) {
                        var particle = particles_2[_e];
                        this.checkParticleRigidBodyCollision(particle, cannonBody);
                    }
                }
            }
        }
    };
    /**
     * 检测粒子与刚体的碰撞
     * @param particle 粒子
     * @param rigidBody 刚体
     */
    SoftRigidInteraction.prototype.checkParticleRigidBodyCollision = function (particle, rigidBody) {
        // 简化的碰撞检测：使用球体近似
        var distance = particle.position.distanceTo(rigidBody.position);
        var minDistance = this.collisionRadius + rigidBody.boundingRadius;
        if (distance < minDistance) {
            // 计算碰撞法线
            var normal = new CANNON.Vec3();
            normal.copy(particle.position);
            normal.vsub(rigidBody.position, normal);
            // 归一化法线
            var length_1 = normal.length();
            if (length_1 < 0.0001)
                return;
            normal.scale(1 / length_1, normal);
            // 计算穿透深度
            var penetration = minDistance - distance;
            // 添加到碰撞对列表
            this.collisionPairs.push({
                softBodyParticle: particle,
                rigidBody: rigidBody,
                penetration: penetration,
                normal: normal
            });
        }
    };
    /**
     * 解决碰撞
     * @param deltaTime 帧间隔时间（秒）
     */
    SoftRigidInteraction.prototype.resolveCollisions = function (deltaTime) {
        // 遍历所有碰撞对
        for (var _i = 0, _a = this.collisionPairs; _i < _a.length; _i++) {
            var pair = _a[_i];
            var softBodyParticle = pair.softBodyParticle, rigidBody = pair.rigidBody, penetration = pair.penetration, normal = pair.normal;
            // 计算相对速度
            var relativeVelocity = new CANNON.Vec3();
            relativeVelocity.copy(softBodyParticle.velocity);
            relativeVelocity.vsub(rigidBody.velocity, relativeVelocity);
            // 计算法线方向的相对速度
            var normalVelocity = normal.dot(relativeVelocity);
            // 如果物体正在分离，跳过
            if (normalVelocity > 0)
                continue;
            // 计算反弹冲量
            var restitution = 0.3; // 恢复系数
            var j = -(1 + restitution) * normalVelocity;
            var impulse = new CANNON.Vec3();
            impulse.copy(normal);
            impulse.scale(j * this.collisionResponse, impulse);
            // 应用冲量
            if (softBodyParticle.mass > 0) {
                var softBodyImpulse = new CANNON.Vec3();
                softBodyImpulse.copy(impulse);
                softBodyImpulse.scale(1 / softBodyParticle.mass, softBodyImpulse);
                softBodyParticle.velocity.vadd(softBodyImpulse, softBodyParticle.velocity);
            }
            if (rigidBody.mass > 0) {
                var rigidBodyImpulse = new CANNON.Vec3();
                rigidBodyImpulse.copy(impulse);
                rigidBodyImpulse.scale(-1 / rigidBody.mass, rigidBodyImpulse);
                rigidBody.velocity.vadd(rigidBodyImpulse, rigidBody.velocity);
            }
            // 应用位置校正
            var percent = 0.2; // 位置校正系数
            var correction = new CANNON.Vec3();
            correction.copy(normal);
            correction.scale(percent * penetration, correction);
            if (softBodyParticle.mass > 0) {
                softBodyParticle.position.vadd(correction, softBodyParticle.position);
            }
            if (rigidBody.mass > 0) {
                var rigidBodyCorrection = new CANNON.Vec3();
                rigidBodyCorrection.copy(correction);
                rigidBodyCorrection.scale(-1, rigidBodyCorrection);
                rigidBody.position.vadd(rigidBodyCorrection, rigidBody.position);
            }
            // 应用摩擦力
            if (this.friction > 0) {
                // 计算切线方向
                var tangent = new CANNON.Vec3();
                var relVelTangent = new CANNON.Vec3();
                relVelTangent.copy(relativeVelocity);
                var normalVelocityVec = new CANNON.Vec3();
                normalVelocityVec.copy(normal);
                normalVelocityVec.scale(normalVelocity, normalVelocityVec);
                relVelTangent.vsub(normalVelocityVec, relVelTangent);
                var tangentLength = relVelTangent.length();
                if (tangentLength > 0.0001) {
                    tangent.copy(relVelTangent);
                    tangent.scale(1 / tangentLength, tangent);
                    // 计算摩擦冲量
                    var jt = -tangentLength * this.friction;
                    // 应用摩擦冲量
                    var frictionImpulse = new CANNON.Vec3();
                    frictionImpulse.copy(tangent);
                    frictionImpulse.scale(jt, frictionImpulse);
                    if (softBodyParticle.mass > 0) {
                        var softBodyFrictionImpulse = new CANNON.Vec3();
                        softBodyFrictionImpulse.copy(frictionImpulse);
                        softBodyFrictionImpulse.scale(1 / softBodyParticle.mass, softBodyFrictionImpulse);
                        softBodyParticle.velocity.vadd(softBodyFrictionImpulse, softBodyParticle.velocity);
                    }
                    if (rigidBody.mass > 0) {
                        var rigidBodyFrictionImpulse = new CANNON.Vec3();
                        rigidBodyFrictionImpulse.copy(frictionImpulse);
                        rigidBodyFrictionImpulse.scale(-1 / rigidBody.mass, rigidBodyFrictionImpulse);
                        rigidBody.velocity.vadd(rigidBodyFrictionImpulse, rigidBody.velocity);
                    }
                }
            }
        }
    };
    /**
     * 启用交互
     */
    SoftRigidInteraction.prototype.enable = function () {
        this.enabled = true;
    };
    /**
     * 禁用交互
     */
    SoftRigidInteraction.prototype.disable = function () {
        this.enabled = false;
    };
    /**
     * 设置碰撞检测半径
     * @param radius 碰撞检测半径
     */
    SoftRigidInteraction.prototype.setCollisionRadius = function (radius) {
        this.collisionRadius = radius;
    };
    /**
     * 设置碰撞响应强度
     * @param response 碰撞响应强度
     */
    SoftRigidInteraction.prototype.setCollisionResponse = function (response) {
        this.collisionResponse = response;
    };
    /**
     * 设置摩擦系数
     * @param friction 摩擦系数
     */
    SoftRigidInteraction.prototype.setFriction = function (friction) {
        this.friction = friction;
    };
    return SoftRigidInteraction;
}());
exports.SoftRigidInteraction = SoftRigidInteraction;
