"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkEventDispatcher = void 0;
/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var NetworkEvent_1 = require("./NetworkEvent");
var NetworkEventBuffer_1 = require("./NetworkEventBuffer");
/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
var NetworkEventDispatcher = /** @class */ (function (_super) {
    __extends(NetworkEventDispatcher, _super);
    /**
     * 创建网络事件分发器
     * @param config 配置
     */
    function NetworkEventDispatcher(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 事件缓冲器 */
        _this.eventBuffer = null;
        /** 事件订阅映射表 */
        _this.subscriptions = new Map();
        /** 默认事件处理器映射表 */
        _this.defaultHandlers = new Map();
        /** 下一个订阅ID */
        _this.nextSubscriptionId = 1;
        // 默认配置
        _this.config = __assign({ useEventBuffer: true, eventBufferConfig: {
                maxBufferSize: 1000,
                processInterval: 16,
                autoProcess: true,
                maxEventsPerProcess: 10,
            }, enableEventLogging: false, logLevel: 'info', allowDefaultHandlers: true }, config);
        // 如果使用事件缓冲，则创建事件缓冲器
        if (_this.config.useEventBuffer) {
            _this.eventBuffer = new NetworkEventBuffer_1.NetworkEventBuffer(_this.config.eventBufferConfig);
            _this.eventBuffer.setEventHandler(_this.processEvent.bind(_this));
        }
        // 初始化默认事件处理器
        _this.initDefaultHandlers();
        return _this;
    }
    /**
     * 初始化默认事件处理器
     */
    NetworkEventDispatcher.prototype.initDefaultHandlers = function () {
        if (!this.config.allowDefaultHandlers) {
            return;
        }
        // 系统错误处理器
        this.setDefaultHandler(NetworkEvent_1.NetworkEventType.SYSTEM_ERROR, function (event) {
            Debug_1.Debug.error('NetworkEventDispatcher', 'System error:', event.data);
        });
        // 系统警告处理器
        this.setDefaultHandler(NetworkEvent_1.NetworkEventType.SYSTEM_WARNING, function (event) {
            Debug_1.Debug.warn('NetworkEventDispatcher', 'System warning:', event.data);
        });
        // 系统信息处理器
        this.setDefaultHandler(NetworkEvent_1.NetworkEventType.SYSTEM_INFO, function (event) {
            Debug_1.Debug.log('NetworkEventDispatcher', 'System info:', event.data);
        });
    };
    /**
     * 设置默认事件处理器
     * @param eventType 事件类型
     * @param handler 处理器
     */
    NetworkEventDispatcher.prototype.setDefaultHandler = function (eventType, handler) {
        if (!this.config.allowDefaultHandlers) {
            Debug_1.Debug.warn('NetworkEventDispatcher', 'Default handlers are not allowed');
            return;
        }
        this.defaultHandlers.set(eventType, handler);
    };
    /**
     * 移除默认事件处理器
     * @param eventType 事件类型
     */
    NetworkEventDispatcher.prototype.removeDefaultHandler = function (eventType) {
        this.defaultHandlers.delete(eventType);
    };
    /**
     * 分发事件
     * @param event 网络事件
     */
    NetworkEventDispatcher.prototype.dispatchEvent = function (event) {
        // 记录事件日志
        this.logEvent(event);
        // 如果使用事件缓冲，则添加到缓冲区
        if (this.eventBuffer) {
            this.eventBuffer.addEvent(event);
        }
        else {
            // 否则直接处理事件
            this.processEvent(event);
        }
    };
    /**
     * 处理事件
     * @param event 网络事件
     */
    NetworkEventDispatcher.prototype.processEvent = function (event) {
        // 获取事件类型的订阅
        var subscriptions = this.subscriptions.get(event.type) || [];
        // 按优先级排序
        subscriptions.sort(function (a, b) { return a.priority - b.priority; });
        // 标记是否已处理
        var handled = false;
        // 处理订阅
        var removeSubscriptions = [];
        for (var _i = 0, subscriptions_1 = subscriptions; _i < subscriptions_1.length; _i++) {
            var subscription = subscriptions_1[_i];
            // 检查过滤器
            if (subscription.filter && !subscription.filter(event)) {
                continue;
            }
            try {
                // 调用处理器
                subscription.handler(event);
                // 标记为已处理
                handled = true;
                // 如果是一次性订阅，则标记为移除
                if (subscription.once) {
                    removeSubscriptions.push(subscription.id);
                }
            }
            catch (error) {
                Debug_1.Debug.error('NetworkEventDispatcher', "Error in event handler for ".concat(event.type, ":"), error);
            }
        }
        // 移除一次性订阅
        for (var _a = 0, removeSubscriptions_1 = removeSubscriptions; _a < removeSubscriptions_1.length; _a++) {
            var id = removeSubscriptions_1[_a];
            this.unsubscribeById(id);
        }
        // 如果未处理且存在默认处理器，则调用默认处理器
        if (!handled && this.config.allowDefaultHandlers) {
            var defaultHandler = this.defaultHandlers.get(event.type);
            if (defaultHandler) {
                try {
                    defaultHandler(event);
                }
                catch (error) {
                    Debug_1.Debug.error('NetworkEventDispatcher', "Error in default handler for ".concat(event.type, ":"), error);
                }
            }
        }
        // 标记事件为已处理
        event.handled = true;
    };
    /**
     * 记录事件日志
     * @param event 网络事件
     */
    NetworkEventDispatcher.prototype.logEvent = function (event) {
        if (!this.config.enableEventLogging) {
            return;
        }
        var message = "Event: ".concat(event.type, ", Sender: ").concat(event.senderId || 'unknown');
        switch (this.config.logLevel) {
            case 'debug':
                Debug_1.Debug.log('NetworkEventDispatcher', message, event);
                break;
            case 'info':
                Debug_1.Debug.log('NetworkEventDispatcher', message);
                break;
            case 'warn':
                Debug_1.Debug.warn('NetworkEventDispatcher', message);
                break;
            case 'error':
                Debug_1.Debug.error('NetworkEventDispatcher', message);
                break;
        }
    };
    /**
     * 订阅事件
     * @param eventType 事件类型
     * @param handler 处理器
     * @param options 选项
     * @returns 订阅ID
     */
    NetworkEventDispatcher.prototype.subscribe = function (eventType, handler, options) {
        if (options === void 0) { options = {}; }
        // 生成订阅ID
        var id = "sub_".concat(this.nextSubscriptionId++);
        // 创建订阅
        var subscription = {
            type: eventType,
            handler: handler,
            filter: options.filter,
            priority: options.priority !== undefined ? options.priority : NetworkEventBuffer_1.EventPriority.MEDIUM,
            once: options.once || false,
            id: id,
        };
        // 添加到订阅映射表
        if (!this.subscriptions.has(eventType)) {
            this.subscriptions.set(eventType, []);
        }
        this.subscriptions.get(eventType).push(subscription);
        return id;
    };
    /**
     * 取消订阅
     * @param eventType 事件类型
     * @param handler 处理器
     * @returns 是否成功取消
     */
    NetworkEventDispatcher.prototype.unsubscribe = function (eventType, handler) {
        var subscriptions = this.subscriptions.get(eventType);
        if (!subscriptions) {
            return false;
        }
        // 查找处理器的索引
        var index = subscriptions.findIndex(function (sub) { return sub.handler === handler; });
        if (index === -1) {
            return false;
        }
        // 移除订阅
        subscriptions.splice(index, 1);
        // 如果没有订阅了，则移除事件类型
        if (subscriptions.length === 0) {
            this.subscriptions.delete(eventType);
        }
        return true;
    };
    /**
     * 通过ID取消订阅
     * @param id 订阅ID
     * @returns 是否成功取消
     */
    NetworkEventDispatcher.prototype.unsubscribeById = function (id) {
        for (var _i = 0, _a = this.subscriptions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], eventType = _b[0], subscriptions = _b[1];
            // 查找订阅的索引
            var index = subscriptions.findIndex(function (sub) { return sub.id === id; });
            if (index !== -1) {
                // 移除订阅
                subscriptions.splice(index, 1);
                // 如果没有订阅了，则移除事件类型
                if (subscriptions.length === 0) {
                    this.subscriptions.delete(eventType);
                }
                return true;
            }
        }
        return false;
    };
    /**
     * 取消所有订阅
     * @param eventType 事件类型（可选，如果不指定则取消所有事件类型的订阅）
     */
    NetworkEventDispatcher.prototype.unsubscribeAll = function (eventType) {
        if (eventType) {
            // 移除指定事件类型的所有订阅
            this.subscriptions.delete(eventType);
        }
        else {
            // 移除所有订阅
            this.subscriptions.clear();
        }
    };
    /**
     * 创建事件
     * @param type 事件类型
     * @param data 事件数据
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param priority 优先级
     * @returns 网络事件
     */
    NetworkEventDispatcher.prototype.createEvent = function (type, data, senderId, receiverId, priority) {
        return {
            type: type,
            data: data,
            senderId: senderId,
            receiverId: receiverId,
            timestamp: Date.now(),
            priority: priority,
            handled: false,
        };
    };
    /**
     * 获取事件订阅数量
     * @param eventType 事件类型（可选，如果不指定则返回所有事件类型的订阅数量）
     * @returns 订阅数量
     */
    NetworkEventDispatcher.prototype.getSubscriptionCount = function (eventType) {
        var _a;
        if (eventType) {
            // 返回指定事件类型的订阅数量
            return ((_a = this.subscriptions.get(eventType)) === null || _a === void 0 ? void 0 : _a.length) || 0;
        }
        else {
            // 返回所有事件类型的订阅数量
            var count = 0;
            for (var _i = 0, _b = this.subscriptions.values(); _i < _b.length; _i++) {
                var subscriptions = _b[_i];
                count += subscriptions.length;
            }
            return count;
        }
    };
    /**
     * 获取事件类型列表
     * @returns 事件类型列表
     */
    NetworkEventDispatcher.prototype.getEventTypes = function () {
        return Array.from(this.subscriptions.keys());
    };
    /**
     * 销毁分发器
     */
    NetworkEventDispatcher.prototype.dispose = function () {
        // 销毁事件缓冲器
        if (this.eventBuffer) {
            this.eventBuffer.dispose();
            this.eventBuffer = null;
        }
        // 清空订阅
        this.subscriptions.clear();
        // 清空默认处理器
        this.defaultHandlers.clear();
        // 移除所有监听器
        this.removeAllListeners();
    };
    return NetworkEventDispatcher;
}(EventEmitter_1.EventEmitter));
exports.NetworkEventDispatcher = NetworkEventDispatcher;
