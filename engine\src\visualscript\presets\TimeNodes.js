"use strict";
/**
 * 时间相关的可视化脚本节点
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerTimeNodes = exports.TimerNode = exports.DelayNode = exports.GetTimeNode = void 0;
var VisualScriptNode_1 = require("../VisualScriptNode");
var NodeRegistry_1 = require("../NodeRegistry");
/**
 * 获取当前时间节点
 */
var GetTimeNode = /** @class */ (function (_super) {
    __extends(GetTimeNode, _super);
    function GetTimeNode() {
        var _this = _super.call(this, 'GetTime', '获取时间') || this;
        _this.addOutput('time', 'number', '当前时间');
        _this.addOutput('deltaTime', 'number', '帧时间');
        return _this;
    }
    GetTimeNode.prototype.execute = function () {
        var _a;
        var now = performance.now();
        var deltaTime = ((_a = this.getContext()) === null || _a === void 0 ? void 0 : _a.deltaTime) || 0;
        return {
            time: now,
            deltaTime: deltaTime
        };
    };
    return GetTimeNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.GetTimeNode = GetTimeNode;
/**
 * 延迟节点
 */
var DelayNode = /** @class */ (function (_super) {
    __extends(DelayNode, _super);
    function DelayNode() {
        var _this = _super.call(this, 'Delay', '延迟') || this;
        _this.startTime = 0;
        _this.isWaiting = false;
        _this.addInput('trigger', 'exec', '触发');
        _this.addInput('duration', 'number', '延迟时间');
        _this.addOutput('completed', 'exec', '完成');
        return _this;
    }
    DelayNode.prototype.execute = function (inputs) {
        if (inputs.trigger && !this.isWaiting) {
            this.startTime = performance.now();
            this.isWaiting = true;
        }
        if (this.isWaiting) {
            var elapsed = performance.now() - this.startTime;
            var duration = inputs.duration || 1000;
            if (elapsed >= duration) {
                this.isWaiting = false;
                return { completed: true };
            }
        }
        return {};
    };
    return DelayNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.DelayNode = DelayNode;
/**
 * 计时器节点
 */
var TimerNode = /** @class */ (function (_super) {
    __extends(TimerNode, _super);
    function TimerNode() {
        var _this = _super.call(this, 'Timer', '计时器') || this;
        _this.startTime = 0;
        _this.isRunning = false;
        _this.addInput('start', 'exec', '开始');
        _this.addInput('stop', 'exec', '停止');
        _this.addInput('reset', 'exec', '重置');
        _this.addOutput('elapsed', 'number', '已用时间');
        _this.addOutput('isRunning', 'boolean', '是否运行中');
        return _this;
    }
    TimerNode.prototype.execute = function (inputs) {
        if (inputs.start && !this.isRunning) {
            this.startTime = performance.now();
            this.isRunning = true;
        }
        if (inputs.stop) {
            this.isRunning = false;
        }
        if (inputs.reset) {
            this.startTime = performance.now();
            this.isRunning = false;
        }
        var elapsed = this.isRunning ? performance.now() - this.startTime : 0;
        return {
            elapsed: elapsed,
            isRunning: this.isRunning
        };
    };
    return TimerNode;
}(VisualScriptNode_1.VisualScriptNode));
exports.TimerNode = TimerNode;
/**
 * 注册时间节点
 */
function registerTimeNodes() {
    NodeRegistry_1.NodeRegistry.register('GetTime', GetTimeNode);
    NodeRegistry_1.NodeRegistry.register('Delay', DelayNode);
    NodeRegistry_1.NodeRegistry.register('Timer', TimerNode);
}
exports.registerTimeNodes = registerTimeNodes;
