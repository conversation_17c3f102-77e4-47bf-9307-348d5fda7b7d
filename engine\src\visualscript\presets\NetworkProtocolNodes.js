"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerNetworkProtocolNodes = exports.HTTPRequestNode = exports.UDPSendNode = void 0;
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var NetworkSystem_1 = require("../../network/NetworkSystem");
/**
 * UDP发送节点
 * 使用UDP协议发送数据
 */
var UDPSendNode = /** @class */ (function (_super) {
    __extends(UDPSendNode, _super);
    function UDPSendNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    UDPSendNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'address',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标地址',
            defaultValue: 'localhost'
        });
        this.addInput({
            name: 'port',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '目标端口',
            defaultValue: 8080
        });
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '要发送的数据'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '发送成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '发送失败'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    UDPSendNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var address, port, data, networkSystem, result, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        address = this.getInputValue('address');
                        port = this.getInputValue('port');
                        data = this.getInputValue('data');
                        // 检查输入值是否有效
                        if (!address || !port || data === undefined) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
                        if (!networkSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, networkSystem.sendUDP(address, port, data)];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('UDP发送失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return UDPSendNode;
}(AsyncNode_1.AsyncNode));
exports.UDPSendNode = UDPSendNode;
/**
 * HTTP请求节点
 * 发送HTTP请求
 */
var HTTPRequestNode = /** @class */ (function (_super) {
    __extends(HTTPRequestNode, _super);
    function HTTPRequestNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    HTTPRequestNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'url',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '请求URL',
            defaultValue: 'https://example.com'
        });
        this.addInput({
            name: 'method',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '请求方法',
            defaultValue: 'GET'
        });
        this.addInput({
            name: 'headers',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '请求头',
            defaultValue: {},
            optional: true
        });
        this.addInput({
            name: 'body',
            type: Node_1.SocketType.DATA,
            dataType: 'any',
            direction: Node_1.SocketDirection.INPUT,
            description: '请求体',
            optional: true
        });
        this.addInput({
            name: 'timeout',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '超时时间（毫秒）',
            defaultValue: 5000,
            optional: true
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '请求成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '请求失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'response',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '响应数据'
        });
        this.addOutput({
            name: 'statusCode',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '状态码'
        });
        this.addOutput({
            name: 'headers',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '响应头'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    HTTPRequestNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, method, headers, body, timeout, networkSystem, response, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        url = this.getInputValue('url');
                        method = this.getInputValue('method');
                        headers = this.getInputValue('headers');
                        body = this.getInputValue('body');
                        timeout = this.getInputValue('timeout');
                        // 检查输入值是否有效
                        if (!url) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        networkSystem = this.graph.getWorld().getSystem(NetworkSystem_1.NetworkSystem);
                        if (!networkSystem) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, networkSystem.sendHTTPRequest(url, {
                                method: method,
                                headers: headers,
                                body: body,
                                timeout: timeout
                            })];
                    case 2:
                        response = _a.sent();
                        if (response) {
                            // 设置输出值
                            this.setOutputValue('response', response.data);
                            this.setOutputValue('statusCode', response.status);
                            this.setOutputValue('headers', response.headers);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('HTTP请求失败:', error_2);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return HTTPRequestNode;
}(AsyncNode_1.AsyncNode));
exports.HTTPRequestNode = HTTPRequestNode;
/**
 * 注册网络协议节点
 * @param registry 节点注册表
 */
function registerNetworkProtocolNodes(registry) {
    // 注册UDP发送节点
    registry.registerNodeType({
        type: 'network/protocol/udpSend',
        category: Node_1.NodeCategory.NETWORK,
        constructor: UDPSendNode,
        label: 'UDP发送',
        description: '使用UDP协议发送数据',
        icon: 'udp',
        color: '#00BCD4',
        tags: ['network', 'protocol', 'udp', 'send']
    });
    // 注册HTTP请求节点
    registry.registerNodeType({
        type: 'network/protocol/httpRequest',
        category: Node_1.NodeCategory.NETWORK,
        constructor: HTTPRequestNode,
        label: 'HTTP请求',
        description: '发送HTTP请求',
        icon: 'http',
        color: '#00BCD4',
        tags: ['network', 'protocol', 'http', 'request']
    });
}
exports.registerNetworkProtocolNodes = registerNetworkProtocolNodes;
