"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkConnection = exports.NetworkConnectionState = void 0;
/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 网络连接状态
 */
var NetworkConnectionState;
(function (NetworkConnectionState) {
    /** 已断开连接 */
    NetworkConnectionState["DISCONNECTED"] = "disconnected";
    /** 正在连接 */
    NetworkConnectionState["CONNECTING"] = "connecting";
    /** 已连接 */
    NetworkConnectionState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    NetworkConnectionState["DISCONNECTING"] = "disconnecting";
    /** 连接错误 */
    NetworkConnectionState["ERROR"] = "error";
})(NetworkConnectionState || (exports.NetworkConnectionState = NetworkConnectionState = {}));
/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
var NetworkConnection = /** @class */ (function (_super) {
    __extends(NetworkConnection, _super);
    /**
     * 创建网络连接
     * @param id 连接ID
     * @param remoteAddress 远程地址
     */
    function NetworkConnection(id, remoteAddress) {
        var _this = _super.call(this) || this;
        /** 连接状态 */
        _this.state = NetworkConnectionState.DISCONNECTED;
        /** 连接时间 */
        _this.connectTime = 0;
        /** 最后活动时间 */
        _this.lastActivityTime = 0;
        /** 发送的消息数量 */
        _this.sentMessages = 0;
        /** 接收的消息数量 */
        _this.receivedMessages = 0;
        /** 发送的字节数 */
        _this.sentBytes = 0;
        /** 接收的字节数 */
        _this.receivedBytes = 0;
        _this.id = id;
        _this.remoteAddress = remoteAddress;
        return _this;
    }
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    NetworkConnection.prototype.getState = function () {
        return this.state;
    };
    /**
     * 是否已连接
     * @returns 是否已连接
     */
    NetworkConnection.prototype.isConnected = function () {
        return this.state === NetworkConnectionState.CONNECTED;
    };
    /**
     * 获取连接ID
     * @returns 连接ID
     */
    NetworkConnection.prototype.getId = function () {
        return this.id;
    };
    /**
     * 获取远程地址
     * @returns 远程地址
     */
    NetworkConnection.prototype.getRemoteAddress = function () {
        return this.remoteAddress;
    };
    /**
     * 获取连接时间
     * @returns 连接时间
     */
    NetworkConnection.prototype.getConnectTime = function () {
        return this.connectTime;
    };
    /**
     * 获取最后活动时间
     * @returns 最后活动时间
     */
    NetworkConnection.prototype.getLastActivityTime = function () {
        return this.lastActivityTime;
    };
    /**
     * 获取发送的消息数量
     * @returns 发送的消息数量
     */
    NetworkConnection.prototype.getSentMessages = function () {
        return this.sentMessages;
    };
    /**
     * 获取接收的消息数量
     * @returns 接收的消息数量
     */
    NetworkConnection.prototype.getReceivedMessages = function () {
        return this.receivedMessages;
    };
    /**
     * 获取发送的字节数
     * @returns 发送的字节数
     */
    NetworkConnection.prototype.getSentBytes = function () {
        return this.sentBytes;
    };
    /**
     * 获取接收的字节数
     * @returns 接收的字节数
     */
    NetworkConnection.prototype.getReceivedBytes = function () {
        return this.receivedBytes;
    };
    /**
     * 获取连接统计信息
     * @returns 连接统计信息
     */
    NetworkConnection.prototype.getStats = function () {
        return {
            id: this.id,
            remoteAddress: this.remoteAddress,
            state: this.state,
            connectTime: this.connectTime,
            lastActivityTime: this.lastActivityTime,
            sentMessages: this.sentMessages,
            receivedMessages: this.receivedMessages,
            sentBytes: this.sentBytes,
            receivedBytes: this.receivedBytes,
            uptime: this.connectTime > 0 ? Date.now() - this.connectTime : 0,
        };
    };
    /**
     * 重置统计信息
     */
    NetworkConnection.prototype.resetStats = function () {
        this.sentMessages = 0;
        this.receivedMessages = 0;
        this.sentBytes = 0;
        this.receivedBytes = 0;
    };
    /**
     * 更新最后活动时间
     */
    NetworkConnection.prototype.updateLastActivityTime = function () {
        this.lastActivityTime = Date.now();
    };
    /**
     * 更新发送统计信息
     * @param messageSize 消息大小（字节）
     */
    NetworkConnection.prototype.updateSentStats = function (messageSize) {
        this.sentMessages++;
        this.sentBytes += messageSize;
        this.updateLastActivityTime();
    };
    /**
     * 更新接收统计信息
     * @param messageSize 消息大小（字节）
     */
    NetworkConnection.prototype.updateReceivedStats = function (messageSize) {
        this.receivedMessages++;
        this.receivedBytes += messageSize;
        this.updateLastActivityTime();
    };
    return NetworkConnection;
}(EventEmitter_1.EventEmitter));
exports.NetworkConnection = NetworkConnection;
