"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsBodyComponent = exports.BodyType = void 0;
/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
var CANNON = require("cannon-es");
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * 物理体类型
 */
var BodyType;
(function (BodyType) {
    /** 静态物体 */
    BodyType["STATIC"] = "static";
    /** 动态物体 */
    BodyType["DYNAMIC"] = "dynamic";
    /** 运动学物体 */
    BodyType["KINEMATIC"] = "kinematic";
})(BodyType || (exports.BodyType = BodyType = {}));
/**
 * 物理体组件
 */
var PhysicsBodyComponent = exports.PhysicsBodyComponent = /** @class */ (function (_super) {
    __extends(PhysicsBodyComponent, _super);
    /**
     * 创建物理体组件
     * @param options 物理体选项
     */
    function PhysicsBodyComponent(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsBodyComponent.type) || this;
        /** CANNON.js物理体 */
        _this.body = null;
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        _this.bodyType = options.type || BodyType.DYNAMIC;
        _this.mass = _this.bodyType === BodyType.STATIC ? 0 : (options.mass || 1);
        _this.position = options.position ? options.position.clone() : new THREE.Vector3();
        _this.quaternion = options.quaternion ? options.quaternion.clone() : new THREE.Quaternion();
        _this.linearDamping = options.linearDamping !== undefined ? options.linearDamping : 0.01;
        _this.angularDamping = options.angularDamping !== undefined ? options.angularDamping : 0.01;
        _this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
        _this.sleepSpeedLimit = options.sleepSpeedLimit || 0.1;
        _this.sleepTimeLimit = options.sleepTimeLimit || 1;
        _this.collisionFilterGroup = options.collisionFilterGroup || 1;
        _this.collisionFilterMask = options.collisionFilterMask || -1;
        _this.material = options.material || null;
        _this.fixedRotation = options.fixedRotation || false;
        _this.autoUpdateTransform = options.autoUpdateTransform !== undefined ? options.autoUpdateTransform : true;
        return _this;
    }
    /**
     * 初始化物理体
     * @param world 物理世界
     */
    PhysicsBodyComponent.prototype.initialize = function (world) {
        if (this.initialized || !this.getEntity() || this.destroyed)
            return;
        this.world = world;
        // 从实体的变换组件获取初始位置和旋转
        var entity = this.getEntity();
        var transform = entity === null || entity === void 0 ? void 0 : entity.getTransform();
        if (transform) {
            this.position.copy(transform.getPosition());
            this.quaternion.copy(transform.getRotationQuaternion());
        }
        // 创建物理体
        this.body = new CANNON.Body({
            mass: this.mass,
            position: new CANNON.Vec3(this.getPosition().x, this.getPosition().y, this.getPosition().z),
            quaternion: new CANNON.Quaternion(this.quaternion.x, this.quaternion.y, this.quaternion.z, this.quaternion.w),
            linearDamping: this.linearDamping,
            angularDamping: this.angularDamping,
            allowSleep: this.allowSleep,
            sleepSpeedLimit: this.sleepSpeedLimit,
            sleepTimeLimit: this.sleepTimeLimit,
            collisionFilterGroup: this.collisionFilterGroup,
            collisionFilterMask: this.collisionFilterMask,
            material: this.material || undefined,
            type: this.getBodyType(),
            fixedRotation: this.fixedRotation
        });
        // 设置用户数据（使用自定义属性）
        this.body.userData = {
            entity: entity
        };
        // 添加到物理世界
        world.addBody(this.body);
        this.initialized = true;
    };
    /**
     * 获取CANNON.js物理体类型
     * @returns CANNON.js物理体类型
     */
    PhysicsBodyComponent.prototype.getBodyType = function () {
        switch (this.bodyType) {
            case BodyType.STATIC:
                return CANNON.BODY_TYPES.STATIC;
            case BodyType.DYNAMIC:
                return CANNON.BODY_TYPES.DYNAMIC;
            case BodyType.KINEMATIC:
                return CANNON.BODY_TYPES.KINEMATIC;
            default:
                return CANNON.BODY_TYPES.DYNAMIC;
        }
    };
    /**
     * 更新实体变换
     */
    PhysicsBodyComponent.prototype.updateTransform = function () {
        if (!this.initialized || !this.body || !this.getEntity() || !this.autoUpdateTransform)
            return;
        var entity = this.getEntity();
        var transform = entity === null || entity === void 0 ? void 0 : entity.getTransform();
        if (!transform)
            return;
        // 如果是静态或运动学物体，则从变换组件更新物理体
        if (this.bodyType === BodyType.STATIC || this.bodyType === BodyType.KINEMATIC) {
            var position = transform.getPosition();
            var quaternion = transform.getRotationQuaternion();
            this.body.setPosition(position.x, position.y, position.z);
            this.body.setRotationQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
            // 如果是运动学物体，则需要更新速度
            if (this.bodyType === BodyType.KINEMATIC) {
                this.body.velocity.setZero();
                this.body.angularVelocity.setZero();
            }
        }
        // 如果是动态物体，则从物理体更新变换组件
        else if (this.bodyType === BodyType.DYNAMIC) {
            transform.setPosition(this.body.getPosition().x, this.body.getPosition().y, this.body.getPosition().z);
            transform.setRotationQuaternion(this.body.quaternion.x, this.body.quaternion.y, this.body.quaternion.z, this.body.quaternion.w);
        }
    };
    /**
     * 应用力
     * @param force 力向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    PhysicsBodyComponent.prototype.applyForce = function (force, worldPoint) {
        if (!this.initialized || !this.body)
            return;
        var forceVec = new CANNON.Vec3(force.x, force.y, force.z);
        if (worldPoint) {
            var pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
            this.body.applyForce(forceVec, pointVec);
        }
        else {
            this.body.applyForce(forceVec, this.body.position);
        }
    };
    /**
     * 应用冲量
     * @param impulse 冲量向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    PhysicsBodyComponent.prototype.applyImpulse = function (impulse, worldPoint) {
        if (!this.initialized || !this.body)
            return;
        var impulseVec = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);
        if (worldPoint) {
            var pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
            this.body.applyImpulse(impulseVec, pointVec);
        }
        else {
            this.body.applyImpulse(impulseVec, this.body.position);
        }
    };
    /**
     * 应用扭矩
     * @param torque 扭矩向量
     */
    PhysicsBodyComponent.prototype.applyTorque = function (torque) {
        if (!this.initialized || !this.body)
            return;
        var torqueVec = new CANNON.Vec3(torque.x, torque.y, torque.z);
        this.body.applyTorque(torqueVec);
    };
    /**
     * 设置线性速度
     * @param velocity 线性速度向量
     */
    PhysicsBodyComponent.prototype.setLinearVelocity = function (velocity) {
        if (!this.initialized || !this.body)
            return;
        this.body.velocity.set(velocity.x, velocity.y, velocity.z);
    };
    /**
     * 获取线性速度
     * @returns 线性速度向量
     */
    PhysicsBodyComponent.prototype.getLinearVelocity = function () {
        if (!this.initialized || !this.body)
            return new THREE.Vector3();
        return new THREE.Vector3(this.body.velocity.x, this.body.velocity.y, this.body.velocity.z);
    };
    /**
     * 设置角速度
     * @param angularVelocity 角速度向量
     */
    PhysicsBodyComponent.prototype.setAngularVelocity = function (angularVelocity) {
        if (!this.initialized || !this.body)
            return;
        this.body.angularVelocity.set(angularVelocity.x, angularVelocity.y, angularVelocity.z);
    };
    /**
     * 获取角速度
     * @returns 角速度向量
     */
    PhysicsBodyComponent.prototype.getAngularVelocity = function () {
        if (!this.initialized || !this.body)
            return new THREE.Vector3();
        return new THREE.Vector3(this.body.angularVelocity.x, this.body.angularVelocity.y, this.body.angularVelocity.z);
    };
    /**
     * 设置物理体类型
     * @param type 物理体类型
     */
    PhysicsBodyComponent.prototype.setBodyType = function (type) {
        if (this.bodyType === type)
            return;
        this.bodyType = type;
        if (this.body) {
            // 更新质量
            if (type === BodyType.STATIC) {
                this.mass = 0;
            }
            else if (this.mass === 0) {
                this.mass = 1;
            }
            // 更新物理体类型
            var bodyType = this.getBodyType();
            this.body.type = bodyType;
            this.body.mass = this.mass;
            this.body.updateMassProperties();
        }
    };
    /**
     * 获取CANNON.js物理体
     * @returns CANNON.js物理体
     */
    PhysicsBodyComponent.prototype.getCannonBody = function () {
        return this.body;
    };
    /**
     * 碰撞开始回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞接触点
     */
    PhysicsBodyComponent.prototype.onCollisionStart = function (_otherEntity, _contact) {
        // 可以在子类中重写此方法
    };
    /**
     * 碰撞结束回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞接触点
     */
    PhysicsBodyComponent.prototype.onCollisionEnd = function (_otherEntity, _contact) {
        // 可以在子类中重写此方法
    };
    /**
     * 销毁物理体
     */
    PhysicsBodyComponent.prototype.dispose = function () {
        if (this.destroyed)
            return;
        if (this.initialized && this.body && this.world) {
            this.world.removeBody(this.body);
            this.body = null;
            this.world = null;
            this.initialized = false;
        }
        this.destroyed = true;
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsBodyComponent.type = 'PhysicsBodyComponent';
    return PhysicsBodyComponent;
}(Component_1.Component));
