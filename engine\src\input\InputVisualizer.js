"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputVisualizer = void 0;
/**
 * 输入可视化工具
 * 用于可视化输入状态和事件
 */
var InputManager_1 = require("./InputManager");
/**
 * 输入可视化器
 */
var InputVisualizer = /** @class */ (function () {
    /**
     * 创建输入可视化器
     * @param options 选项
     */
    function InputVisualizer(options) {
        if (options === void 0) { options = {}; }
        /** 更新定时器 */
        this.updateTimer = null;
        /** 事件日志 */
        this.eventLog = [];
        /** 设备事件处理器 */
        this.deviceEventHandlers = new Map();
        /** 输入组件列表 */
        this.inputComponents = [];
        this.inputManager = InputManager_1.InputManager.getInstance();
        this.container = options.container || document.body;
        this.showDevices = options.showDevices !== undefined ? options.showDevices : true;
        this.showActions = options.showActions !== undefined ? options.showActions : true;
        this.showEventLog = options.showEventLog !== undefined ? options.showEventLog : true;
        this.maxEventLogEntries = options.maxEventLogEntries || 100;
        this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
        this.updateInterval = options.updateInterval || 100;
        // 创建UI元素
        this.createUI();
        // 添加设备事件监听器
        this.addDeviceEventListeners();
        // 开始自动更新
        if (this.autoUpdate) {
            this.startAutoUpdate();
        }
    }
    /**
     * 创建UI元素
     */
    InputVisualizer.prototype.createUI = function () {
        var _this = this;
        // 创建主容器
        var visualizerContainer = document.createElement('div');
        visualizerContainer.className = 'input-visualizer';
        visualizerContainer.style.position = 'absolute';
        visualizerContainer.style.top = '10px';
        visualizerContainer.style.right = '10px';
        visualizerContainer.style.width = '300px';
        visualizerContainer.style.maxHeight = '80vh';
        visualizerContainer.style.overflowY = 'auto';
        visualizerContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        visualizerContainer.style.color = 'white';
        visualizerContainer.style.fontFamily = 'monospace';
        visualizerContainer.style.fontSize = '12px';
        visualizerContainer.style.padding = '10px';
        visualizerContainer.style.borderRadius = '5px';
        visualizerContainer.style.zIndex = '1000';
        // 创建标题
        var title = document.createElement('h2');
        title.textContent = '输入可视化器';
        title.style.margin = '0 0 10px 0';
        title.style.fontSize = '16px';
        title.style.fontWeight = 'bold';
        visualizerContainer.appendChild(title);
        // 创建设备状态容器
        if (this.showDevices) {
            var devicesTitle = document.createElement('h3');
            devicesTitle.textContent = '设备状态';
            devicesTitle.style.margin = '10px 0 5px 0';
            devicesTitle.style.fontSize = '14px';
            devicesTitle.style.fontWeight = 'bold';
            visualizerContainer.appendChild(devicesTitle);
            this.devicesElement = document.createElement('div');
            this.devicesElement.className = 'input-visualizer-devices';
            visualizerContainer.appendChild(this.devicesElement);
        }
        // 创建动作状态容器
        if (this.showActions) {
            var actionsTitle = document.createElement('h3');
            actionsTitle.textContent = '动作状态';
            actionsTitle.style.margin = '10px 0 5px 0';
            actionsTitle.style.fontSize = '14px';
            actionsTitle.style.fontWeight = 'bold';
            visualizerContainer.appendChild(actionsTitle);
            this.actionsElement = document.createElement('div');
            this.actionsElement.className = 'input-visualizer-actions';
            visualizerContainer.appendChild(this.actionsElement);
        }
        // 创建事件日志容器
        if (this.showEventLog) {
            var eventLogTitle = document.createElement('h3');
            eventLogTitle.textContent = '事件日志';
            eventLogTitle.style.margin = '10px 0 5px 0';
            eventLogTitle.style.fontSize = '14px';
            eventLogTitle.style.fontWeight = 'bold';
            visualizerContainer.appendChild(eventLogTitle);
            var eventLogControls = document.createElement('div');
            eventLogControls.style.marginBottom = '5px';
            var clearButton = document.createElement('button');
            clearButton.textContent = '清除';
            clearButton.onclick = function () { return _this.clearEventLog(); };
            eventLogControls.appendChild(clearButton);
            visualizerContainer.appendChild(eventLogControls);
            this.eventLogElement = document.createElement('div');
            this.eventLogElement.className = 'input-visualizer-event-log';
            this.eventLogElement.style.maxHeight = '200px';
            this.eventLogElement.style.overflowY = 'auto';
            this.eventLogElement.style.border = '1px solid #444';
            this.eventLogElement.style.padding = '5px';
            this.eventLogElement.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            visualizerContainer.appendChild(this.eventLogElement);
        }
        // 添加到容器
        this.container.appendChild(visualizerContainer);
    };
    /**
     * 添加设备事件监听器
     */
    InputVisualizer.prototype.addDeviceEventListeners = function () {
        var _this = this;
        // 获取所有设备
        var devices = this.getAllDevices();
        var _loop_1 = function (device) {
            var deviceName = device.getName();
            var handler = function (event) {
                // 添加事件日志
                _this.addEventLogEntry(deviceName, event.type, event);
            };
            // 添加事件监听器
            device.on('*', handler);
            // 保存处理器
            this_1.deviceEventHandlers.set(deviceName, handler);
        };
        var this_1 = this;
        // 添加事件监听器
        for (var _i = 0, devices_1 = devices; _i < devices_1.length; _i++) {
            var device = devices_1[_i];
            _loop_1(device);
        }
    };
    /**
     * 移除设备事件监听器
     */
    InputVisualizer.prototype.removeDeviceEventListeners = function () {
        // 获取所有设备
        var devices = this.getAllDevices();
        // 移除事件监听器
        for (var _i = 0, devices_2 = devices; _i < devices_2.length; _i++) {
            var device = devices_2[_i];
            var deviceName = device.getName();
            var handler = this.deviceEventHandlers.get(deviceName);
            if (handler) {
                device.off('*', handler);
                this.deviceEventHandlers.delete(deviceName);
            }
        }
    };
    /**
     * 获取所有设备
     * @returns 设备列表
     */
    InputVisualizer.prototype.getAllDevices = function () {
        var devices = [];
        // 获取键盘设备
        var keyboard = this.inputManager.getDevice('keyboard');
        if (keyboard)
            devices.push(keyboard);
        // 获取鼠标设备
        var mouse = this.inputManager.getDevice('mouse');
        if (mouse)
            devices.push(mouse);
        // 获取触摸设备
        var touch = this.inputManager.getDevice('touch');
        if (touch)
            devices.push(touch);
        // 获取游戏手柄设备
        var gamepad = this.inputManager.getDevice('gamepad');
        if (gamepad)
            devices.push(gamepad);
        // 获取XR设备
        var xr = this.inputManager.getDevice('xr');
        if (xr)
            devices.push(xr);
        return devices;
    };
    /**
     * 添加输入组件
     * @param component 输入组件
     */
    InputVisualizer.prototype.addInputComponent = function (component) {
        if (!this.inputComponents.includes(component)) {
            this.inputComponents.push(component);
        }
    };
    /**
     * 移除输入组件
     * @param component 输入组件
     */
    InputVisualizer.prototype.removeInputComponent = function (component) {
        var index = this.inputComponents.indexOf(component);
        if (index !== -1) {
            this.inputComponents.splice(index, 1);
        }
    };
    /**
     * 添加事件日志条目
     * @param deviceName 设备名称
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    InputVisualizer.prototype.addEventLogEntry = function (deviceName, eventType, eventData) {
        // 添加事件日志条目
        this.eventLog.push({
            timestamp: Date.now(),
            deviceName: deviceName,
            eventType: eventType,
            eventData: eventData
        });
        // 限制事件日志条数
        if (this.eventLog.length > this.maxEventLogEntries) {
            this.eventLog.shift();
        }
        // 更新事件日志UI
        if (this.showEventLog) {
            this.updateEventLogUI();
        }
    };
    /**
     * 清除事件日志
     */
    InputVisualizer.prototype.clearEventLog = function () {
        this.eventLog = [];
        // 更新事件日志UI
        if (this.showEventLog) {
            this.updateEventLogUI();
        }
    };
    /**
     * 开始自动更新
     */
    InputVisualizer.prototype.startAutoUpdate = function () {
        var _this = this;
        if (this.updateTimer !== null)
            return;
        this.updateTimer = window.setInterval(function () {
            _this.update();
        }, this.updateInterval);
    };
    /**
     * 停止自动更新
     */
    InputVisualizer.prototype.stopAutoUpdate = function () {
        if (this.updateTimer === null)
            return;
        window.clearInterval(this.updateTimer);
        this.updateTimer = null;
    };
    /**
     * 更新
     */
    InputVisualizer.prototype.update = function () {
        // 更新设备状态UI
        if (this.showDevices) {
            this.updateDevicesUI();
        }
        // 更新动作状态UI
        if (this.showActions) {
            this.updateActionsUI();
        }
    };
    /**
     * 更新设备状态UI
     */
    InputVisualizer.prototype.updateDevicesUI = function () {
        // 获取所有设备
        var devices = this.getAllDevices();
        // 更新设备状态UI
        var html = '';
        for (var _i = 0, devices_3 = devices; _i < devices_3.length; _i++) {
            var device = devices_3[_i];
            var deviceName = device.getName();
            var keys = device.getKeys();
            html += "<div style=\"margin-bottom: 5px;\"><strong>".concat(deviceName, "</strong></div>");
            html += '<div style="margin-left: 10px;">';
            for (var _a = 0, keys_1 = keys; _a < keys_1.length; _a++) {
                var key = keys_1[_a];
                var value = device.getValue(key);
                html += "<div>".concat(key, ": ").concat(this.formatValue(value), "</div>");
            }
            html += '</div>';
        }
        this.devicesElement.innerHTML = html;
    };
    /**
     * 更新动作状态UI
     */
    InputVisualizer.prototype.updateActionsUI = function () {
        // 获取所有动作
        var actions = [];
        for (var _i = 0, _a = this.inputComponents; _i < _a.length; _i++) {
            var component = _a[_i];
            actions.push.apply(actions, component.getActions());
        }
        // 更新动作状态UI
        var html = '';
        for (var _b = 0, actions_1 = actions; _b < actions_1.length; _b++) {
            var action = actions_1[_b];
            var actionName = action.getName();
            var actionType = action.getType();
            var actionValue = action.getValue();
            html += "<div style=\"margin-bottom: 5px;\"><strong>".concat(actionName, "</strong> (").concat(actionType, ")</div>");
            html += '<div style="margin-left: 10px;">';
            html += "<div>\u503C: ".concat(this.formatValue(actionValue), "</div>");
            html += '</div>';
        }
        this.actionsElement.innerHTML = html;
    };
    /**
     * 更新事件日志UI
     */
    InputVisualizer.prototype.updateEventLogUI = function () {
        // 更新事件日志UI
        var html = '';
        for (var i = this.eventLog.length - 1; i >= 0; i--) {
            var entry = this.eventLog[i];
            var time = new Date(entry.timestamp).toISOString().substr(11, 12);
            html += "<div style=\"margin-bottom: 2px; border-bottom: 1px solid #333; padding-bottom: 2px;\">";
            html += "<span style=\"color: #999;\">[".concat(time, "]</span> ");
            html += "<span style=\"color: #6cf;\">".concat(entry.deviceName, "</span> ");
            html += "<span style=\"color: #fc6;\">".concat(entry.eventType, "</span>");
            if (entry.eventData) {
                var data = JSON.stringify(entry.eventData).substr(0, 100);
                html += " <span style=\"color: #ccc;\">".concat(data, "</span>");
            }
            html += "</div>";
        }
        this.eventLogElement.innerHTML = html;
    };
    /**
     * 格式化值
     * @param value 值
     * @returns 格式化后的值
     */
    InputVisualizer.prototype.formatValue = function (value) {
        var _this = this;
        if (value === undefined)
            return 'undefined';
        if (value === null)
            return 'null';
        if (typeof value === 'boolean') {
            return value ? '<span style="color: #6f6;">true</span>' : '<span style="color: #f66;">false</span>';
        }
        if (typeof value === 'number') {
            return "<span style=\"color: #6cf;\">".concat(value.toFixed(4), "</span>");
        }
        if (typeof value === 'string') {
            return "<span style=\"color: #fc6;\">\"".concat(value, "\"</span>");
        }
        if (Array.isArray(value)) {
            return "[".concat(value.map(function (v) { return _this.formatValue(v); }).join(', '), "]");
        }
        if (typeof value === 'object') {
            return JSON.stringify(value);
        }
        return String(value);
    };
    /**
     * 销毁
     */
    InputVisualizer.prototype.destroy = function () {
        // 停止自动更新
        this.stopAutoUpdate();
        // 移除设备事件监听器
        this.removeDeviceEventListeners();
        // 移除UI元素
        var visualizerElement = this.container.querySelector('.input-visualizer');
        if (visualizerElement) {
            this.container.removeChild(visualizerElement);
        }
    };
    return InputVisualizer;
}());
exports.InputVisualizer = InputVisualizer;
