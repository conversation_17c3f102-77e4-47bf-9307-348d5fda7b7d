"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageType = void 0;
/**
 * 消息类型
 * 定义网络消息的类型常量
 */
var MessageType = exports.MessageType = /** @class */ (function () {
    function MessageType() {
    }
    // 系统消息
    /** 系统消息 */
    MessageType.SYSTEM = 'system';
    /** 心跳消息 */
    MessageType.HEARTBEAT = 'heartbeat';
    /** 确认消息 */
    MessageType.ACK = 'ack';
    /** 错误消息 */
    MessageType.ERROR = 'error';
    // 连接消息
    /** 连接请求 */
    MessageType.CONNECT = 'connect';
    /** 连接成功 */
    MessageType.CONNECT_SUCCESS = 'connect_success';
    /** 连接失败 */
    MessageType.CONNECT_FAILED = 'connect_failed';
    /** 断开连接 */
    MessageType.DISCONNECT = 'disconnect';
    // 房间消息
    /** 加入房间 */
    MessageType.JOIN_ROOM = 'join_room';
    /** 加入房间成功 */
    MessageType.JOIN_ROOM_SUCCESS = 'join_room_success';
    /** 加入房间失败 */
    MessageType.JOIN_ROOM_FAILED = 'join_room_failed';
    /** 离开房间 */
    MessageType.LEAVE_ROOM = 'leave_room';
    /** 用户加入 */
    MessageType.USER_JOINED = 'user_joined';
    /** 用户离开 */
    MessageType.USER_LEFT = 'user_left';
    /** 房间列表 */
    MessageType.ROOM_LIST = 'room_list';
    /** 房间信息 */
    MessageType.ROOM_INFO = 'room_info';
    // 用户消息
    /** 用户列表 */
    MessageType.USER_LIST = 'user_list';
    /** 用户信息 */
    MessageType.USER_INFO = 'user_info';
    /** 用户状态 */
    MessageType.USER_STATUS = 'user_status';
    // 聊天消息
    /** 聊天消息 */
    MessageType.CHAT = 'chat';
    /** 私聊消息 */
    MessageType.PRIVATE_CHAT = 'private_chat';
    /** 广播消息 */
    MessageType.BROADCAST = 'broadcast';
    /** 直接消息 */
    MessageType.DIRECT = 'direct';
    // 实体消息
    /** 创建实体 */
    MessageType.ENTITY_CREATE = 'entity_create';
    /** 更新实体 */
    MessageType.ENTITY_UPDATE = 'entity_update';
    /** 删除实体 */
    MessageType.ENTITY_DELETE = 'entity_delete';
    /** 实体列表 */
    MessageType.ENTITY_LIST = 'entity_list';
    /** 实体事件 */
    MessageType.ENTITY_EVENT = 'entity_event';
    /** 实体同步 */
    MessageType.ENTITY_SYNC = 'entity_sync';
    /** 实体所有权 */
    MessageType.ENTITY_OWNERSHIP = 'entity_ownership';
    /** 实体所有权请求 */
    MessageType.ENTITY_OWNERSHIP_REQUEST = 'entity_ownership_request';
    /** 实体所有权转移 */
    MessageType.ENTITY_OWNERSHIP_TRANSFER = 'entity_ownership_transfer';
    // WebRTC消息
    /** WebRTC提议 */
    MessageType.WEBRTC_OFFER = 'webrtc_offer';
    /** WebRTC应答 */
    MessageType.WEBRTC_ANSWER = 'webrtc_answer';
    /** WebRTC ICE候选 */
    MessageType.WEBRTC_ICE_CANDIDATE = 'webrtc_ice_candidate';
    /** WebRTC连接状态 */
    MessageType.WEBRTC_CONNECTION_STATE = 'webrtc_connection_state';
    // 媒体消息
    /** 媒体流 */
    MessageType.MEDIA_STREAM = 'media_stream';
    /** 音频流 */
    MessageType.AUDIO_STREAM = 'audio_stream';
    /** 视频流 */
    MessageType.VIDEO_STREAM = 'video_stream';
    /** 屏幕共享流 */
    MessageType.SCREEN_SHARE_STREAM = 'screen_share_stream';
    /** 媒体控制 */
    MessageType.MEDIA_CONTROL = 'media_control';
    // 游戏消息
    /** 游戏状态 */
    MessageType.GAME_STATE = 'game_state';
    /** 游戏事件 */
    MessageType.GAME_EVENT = 'game_event';
    /** 游戏命令 */
    MessageType.GAME_COMMAND = 'game_command';
    /** 游戏同步 */
    MessageType.GAME_SYNC = 'game_sync';
    // 自定义消息
    /** 自定义消息 */
    MessageType.CUSTOM = 'custom';
    return MessageType;
}());
