"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouseDevice = void 0;
/**
 * 鼠标输入设备
 */
var InputDevice_1 = require("../InputDevice");
var InputSystem_1 = require("../InputSystem");
/**
 * 鼠标输入设备
 */
var MouseDevice = /** @class */ (function (_super) {
    __extends(MouseDevice, _super);
    /**
     * 创建鼠标输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    function MouseDevice(element, preventDefault, stopPropagation) {
        if (element === void 0) { element = document.body; }
        if (preventDefault === void 0) { preventDefault = true; }
        if (stopPropagation === void 0) { stopPropagation = false; }
        var _this = _super.call(this, 'mouse') || this;
        /** 鼠标位置 */
        _this.position = { x: 0, y: 0 };
        /** 鼠标相对位置（相对于上一帧） */
        _this.movement = { x: 0, y: 0 };
        /** 鼠标滚轮增量 */
        _this.wheelDelta = 0;
        /** 鼠标事件处理器 */
        _this.mouseEventHandlers = {};
        _this.element = element;
        _this.preventDefault = preventDefault;
        _this.stopPropagation = stopPropagation;
        // 初始化事件处理器
        _this.initEventHandlers();
        // 初始化按钮状态
        _this.setValue("button:".concat(InputSystem_1.MouseButton.LEFT), InputSystem_1.MouseButtonState.NONE);
        _this.setValue("button:".concat(InputSystem_1.MouseButton.MIDDLE), InputSystem_1.MouseButtonState.NONE);
        _this.setValue("button:".concat(InputSystem_1.MouseButton.RIGHT), InputSystem_1.MouseButtonState.NONE);
        // 初始化位置和移动
        _this.setValue('position:x', 0);
        _this.setValue('position:y', 0);
        _this.setValue('movement:x', 0);
        _this.setValue('movement:y', 0);
        _this.setValue('wheel:delta', 0);
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    MouseDevice.prototype.initEventHandlers = function () {
        // 鼠标按下事件
        this.mouseEventHandlers.mousedown = this.handleMouseDown.bind(this);
        // 鼠标释放事件
        this.mouseEventHandlers.mouseup = this.handleMouseUp.bind(this);
        // 鼠标移动事件
        this.mouseEventHandlers.mousemove = this.handleMouseMove.bind(this);
        // 鼠标滚轮事件
        this.mouseEventHandlers.wheel = this.handleMouseWheel.bind(this);
    };
    /**
     * 初始化设备
     */
    MouseDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    MouseDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        _super.prototype.destroy.call(this);
    };
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    MouseDevice.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
        // 更新按钮状态
        for (var i = 0; i < 3; i++) {
            var key = "button:".concat(i);
            var state = this.getValue(key);
            if (state === InputSystem_1.MouseButtonState.DOWN) {
                // 将按下状态更新为按住状态
                this.setValue(key, InputSystem_1.MouseButtonState.PRESSED);
            }
            else if (state === InputSystem_1.MouseButtonState.UP) {
                // 将释放状态更新为未按下状态
                this.setValue(key, InputSystem_1.MouseButtonState.NONE);
            }
        }
        // 重置鼠标移动和滚轮增量
        this.movement.x = 0;
        this.movement.y = 0;
        this.wheelDelta = 0;
        this.setValue('movement:x', 0);
        this.setValue('movement:y', 0);
        this.setValue('wheel:delta', 0);
        _super.prototype.update.call(this, deltaTime);
    };
    /**
     * 添加事件监听器
     */
    MouseDevice.prototype.addEventListeners = function () {
        // 添加鼠标事件监听器
        for (var _i = 0, _a = Object.entries(this.mouseEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            this.element.addEventListener(event_1, handler, { passive: !this.preventDefault });
        }
    };
    /**
     * 移除事件监听器
     */
    MouseDevice.prototype.removeEventListeners = function () {
        // 移除鼠标事件监听器
        for (var _i = 0, _a = Object.entries(this.mouseEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_2 = _b[0], handler = _b[1];
            this.element.removeEventListener(event_2, handler);
        }
    };
    /**
     * 更新鼠标位置
     * @param event 鼠标事件
     */
    MouseDevice.prototype.updateMousePosition = function (event) {
        // 获取元素位置和大小
        var rect = this.element.getBoundingClientRect();
        // 计算鼠标在元素内的位置
        var x = event.clientX - rect.left;
        var y = event.clientY - rect.top;
        // 计算鼠标移动
        this.movement.x = x - this.getPosition().x;
        this.movement.y = y - this.getPosition().y;
        // 更新鼠标位置
        this.getPosition().x = x;
        this.getPosition().y = y;
        // 更新设备值
        this.setValue('position:x', this.getPosition().x);
        this.setValue('position:y', this.getPosition().y);
        this.setValue('movement:x', this.movement.x);
        this.setValue('movement:y', this.movement.y);
    };
    /**
     * 处理鼠标按钮按下事件
     * @param event 鼠标事件
     */
    MouseDevice.prototype.handleMouseDown = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 设置鼠标按钮为按下状态
        var key = "button:".concat(event.button);
        this.setValue(key, InputSystem_1.MouseButtonState.DOWN);
        // 触发鼠标按钮按下事件
        this.eventEmitter.emit("".concat(key, ":down"), {
            button: event.button,
            x: this.getPosition().x,
            y: this.getPosition().y,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标按钮释放事件
     * @param event 鼠标事件
     */
    MouseDevice.prototype.handleMouseUp = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 设置鼠标按钮为释放状态
        var key = "button:".concat(event.button);
        this.setValue(key, InputSystem_1.MouseButtonState.UP);
        // 触发鼠标按钮释放事件
        this.eventEmitter.emit("".concat(key, ":up"), {
            button: event.button,
            x: this.getPosition().x,
            y: this.getPosition().y,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    MouseDevice.prototype.handleMouseMove = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 触发鼠标移动事件
        this.eventEmitter.emit('move', {
            x: this.getPosition().x,
            y: this.getPosition().y,
            movementX: this.movement.x,
            movementY: this.movement.y,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标滚轮事件
     * @param event 滚轮事件
     */
    MouseDevice.prototype.handleMouseWheel = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新滚轮增量
        this.wheelDelta = event.deltaY;
        this.setValue('wheel:delta', this.wheelDelta);
        // 触发鼠标滚轮事件
        this.eventEmitter.emit('wheel', {
            delta: this.wheelDelta,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 获取鼠标位置
     * @returns 鼠标位置
     */
    MouseDevice.prototype.getPosition = function () {
        return { x: this.getPosition().x, y: this.getPosition().y };
    };
    /**
     * 获取鼠标移动
     * @returns 鼠标移动
     */
    MouseDevice.prototype.getMovement = function () {
        return { x: this.movement.x, y: this.movement.y };
    };
    /**
     * 获取滚轮增量
     * @returns 滚轮增量
     */
    MouseDevice.prototype.getWheelDelta = function () {
        return this.wheelDelta;
    };
    /**
     * 检查鼠标按钮是否按下
     * @param button 鼠标按钮
     * @returns 是否按下
     */
    MouseDevice.prototype.isButtonDown = function (button) {
        var state = this.getValue("button:".concat(button));
        return state === InputSystem_1.MouseButtonState.DOWN || state === InputSystem_1.MouseButtonState.PRESSED;
    };
    /**
     * 检查鼠标按钮是否刚按下
     * @param button 鼠标按钮
     * @returns 是否刚按下
     */
    MouseDevice.prototype.isButtonJustDown = function (button) {
        return this.getValue("button:".concat(button)) === InputSystem_1.MouseButtonState.DOWN;
    };
    /**
     * 检查鼠标按钮是否刚释放
     * @param button 鼠标按钮
     * @returns 是否刚释放
     */
    MouseDevice.prototype.isButtonJustUp = function (button) {
        return this.getValue("button:".concat(button)) === InputSystem_1.MouseButtonState.UP;
    };
    return MouseDevice;
}(InputDevice_1.BaseInputDevice));
exports.MouseDevice = MouseDevice;
