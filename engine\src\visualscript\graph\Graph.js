"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Graph = void 0;
/**
 * 视觉脚本图形
 * 表示一个完整的视觉脚本图形，包含节点、变量和自定义事件
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 图形类
 */
var Graph = /** @class */ (function (_super) {
    __extends(Graph, _super);
    /**
     * 创建图形
     * @param options 图形选项
     */
    function Graph(options) {
        var _this = _super.call(this) || this;
        /** 节点映射 */
        _this.nodes = new Map();
        /** 变量映射 */
        _this.variables = new Map();
        /** 自定义事件映射 */
        _this.customEvents = new Map();
        _this.id = options.id;
        _this.name = options.name || '未命名图形';
        _this.description = options.description || '';
        _this.createdAt = new Date();
        _this.updatedAt = new Date();
        return _this;
    }
    /**
     * 获取图形名称
     * @returns 图形名称
     */
    Graph.prototype.getName = function () {
        return this.name;
    };
    /**
     * 设置图形名称
     * @param name 图形名称
     */
    Graph.prototype.setName = function (name) {
        this.name = name;
        this.updatedAt = new Date();
        this.emit('nameChanged', name);
    };
    /**
     * 获取图形描述
     * @returns 图形描述
     */
    Graph.prototype.getDescription = function () {
        return this.description;
    };
    /**
     * 设置图形描述
     * @param description 图形描述
     */
    Graph.prototype.setDescription = function (description) {
        this.description = description;
        this.updatedAt = new Date();
        this.emit('descriptionChanged', description);
    };
    /**
     * 添加节点
     * @param node 节点
     * @returns 是否添加成功
     */
    Graph.prototype.addNode = function (node) {
        // 检查是否已存在
        if (this.nodes.has(node.id)) {
            return false;
        }
        // 添加节点
        this.nodes.set(node.id, node);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发添加事件
        this.emit('nodeAdded', node);
        return true;
    };
    /**
     * 移除节点
     * @param id 节点ID
     * @returns 是否移除成功
     */
    Graph.prototype.removeNode = function (id) {
        // 检查是否存在
        var node = this.nodes.get(id);
        if (!node) {
            return false;
        }
        // 断开节点连接
        node.disconnectAll();
        // 移除节点
        this.nodes.delete(id);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发移除事件
        this.emit('nodeRemoved', node);
        return true;
    };
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点
     */
    Graph.prototype.getNode = function (id) {
        return this.nodes.get(id);
    };
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    Graph.prototype.getNodes = function () {
        return Array.from(this.nodes.values());
    };
    /**
     * 添加变量
     * @param variable 变量
     * @returns 是否添加成功
     */
    Graph.prototype.addVariable = function (variable) {
        // 检查是否已存在
        if (this.variables.has(variable.id)) {
            return false;
        }
        // 添加变量
        this.variables.set(variable.id, variable);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发添加事件
        this.emit('variableAdded', variable);
        return true;
    };
    /**
     * 移除变量
     * @param id 变量ID
     * @returns 是否移除成功
     */
    Graph.prototype.removeVariable = function (id) {
        // 检查是否存在
        var variable = this.variables.get(id);
        if (!variable) {
            return false;
        }
        // 移除变量
        this.variables.delete(id);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发移除事件
        this.emit('variableRemoved', variable);
        return true;
    };
    /**
     * 获取变量
     * @param id 变量ID
     * @returns 变量
     */
    Graph.prototype.getVariable = function (id) {
        return this.variables.get(id);
    };
    /**
     * 获取所有变量
     * @returns 变量列表
     */
    Graph.prototype.getVariables = function () {
        return Array.from(this.variables.values());
    };
    /**
     * 添加自定义事件
     * @param event 自定义事件
     * @returns 是否添加成功
     */
    Graph.prototype.addCustomEvent = function (event) {
        // 检查是否已存在
        if (this.customEvents.has(event.id)) {
            return false;
        }
        // 添加自定义事件
        this.customEvents.set(event.id, event);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发添加事件
        this.emit('customEventAdded', event);
        return true;
    };
    /**
     * 移除自定义事件
     * @param id 自定义事件ID
     * @returns 是否移除成功
     */
    Graph.prototype.removeCustomEvent = function (id) {
        // 检查是否存在
        var event = this.customEvents.get(id);
        if (!event) {
            return false;
        }
        // 移除自定义事件
        this.customEvents.delete(id);
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发移除事件
        this.emit('customEventRemoved', event);
        return true;
    };
    /**
     * 获取自定义事件
     * @param id 自定义事件ID
     * @returns 自定义事件
     */
    Graph.prototype.getCustomEvent = function (id) {
        return this.customEvents.get(id);
    };
    /**
     * 获取所有自定义事件
     * @returns 自定义事件列表
     */
    Graph.prototype.getCustomEvents = function () {
        return Array.from(this.customEvents.values());
    };
    /**
     * 清空图形
     */
    Graph.prototype.clear = function () {
        // 断开所有节点连接
        for (var _i = 0, _a = this.nodes.values(); _i < _a.length; _i++) {
            var node = _a[_i];
            node.disconnectAll();
        }
        // 清空节点映射
        this.nodes.clear();
        // 清空变量映射
        this.variables.clear();
        // 清空自定义事件映射
        this.customEvents.clear();
        // 更新修改时间
        this.updatedAt = new Date();
        // 触发清空事件
        this.emit('cleared');
    };
    /**
     * 序列化为JSON
     * @returns 图形JSON数据
     */
    Graph.prototype.toJSON = function () {
        // 序列化节点
        var nodes = this.getNodes().map(function (node) {
            // TODO: 实现节点序列化
            return {
                id: node.id,
                type: node.type,
                metadata: node.metadata,
                parameters: {},
                flows: {}
            };
        });
        // 序列化变量
        var variables = this.getVariables().map(function (variable) {
            return {
                id: variable.id,
                name: variable.name,
                type: variable.type,
                value: variable.value,
                description: variable.description,
                constant: variable.constant,
                global: variable.global
            };
        });
        // 序列化自定义事件
        var customEvents = this.getCustomEvents().map(function (event) {
            return {
                id: event.id,
                name: event.name,
                parameterTypes: event.parameterTypes,
                description: event.description
            };
        });
        // 创建图形JSON数据
        var json = {
            version: '1.0',
            name: this.name,
            description: this.description,
            nodes: nodes,
            variables: variables,
            customEvents: customEvents,
            metadata: {
                createdAt: this.createdAt.toISOString(),
                updatedAt: this.updatedAt.toISOString()
            }
        };
        return json;
    };
    /**
     * 从JSON创建图形
     * @param json 图形JSON数据
     * @returns 图形实例
     */
    Graph.fromJSON = function (json) {
        // TODO: 实现从JSON创建图形
        var graph = new Graph({
            id: json.name || 'graph',
            name: json.name,
            description: json.description
        });
        return graph;
    };
    return Graph;
}(EventEmitter_1.EventEmitter));
exports.Graph = Graph;
