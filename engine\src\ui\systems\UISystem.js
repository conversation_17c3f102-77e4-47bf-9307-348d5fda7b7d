"use strict";
/**
 * UISystem.ts
 *
 * UI系统，管理所有UI元素
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UISystem = void 0;
var System_1 = require("../../core/System");
var three_1 = require("three");
var UI2DComponent_1 = require("../components/UI2DComponent");
var UI3DComponent_1 = require("../components/UI3DComponent");
/**
 * UI系统
 * 管理所有UI元素
 */
var UISystem = /** @class */ (function (_super) {
    __extends(UISystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param config UI系统配置
     */
    function UISystem(config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级
        _super.call(this, 600) || this;
        // 世界引用（已移除，通过引擎获取）
        // UI元素列表
        _this.uiComponents = new Map();
        // 2D UI元素列表
        _this.ui2DComponents = new Map();
        // 3D UI元素列表
        _this.ui3DComponents = new Map();
        // 动画组件列表
        _this.uiAnimationComponents = new Map();
        // 布局组件列表
        _this.uiLayoutComponents = new Map();
        // 事件组件列表
        _this.uiEventComponents = new Map();
        // 鼠标位置
        _this.mousePosition = new three_1.Vector2();
        // 按键状态
        _this.keyStates = new Map();
        // 修饰键状态
        _this.modifiers = {
            altKey: false,
            ctrlKey: false,
            shiftKey: false,
            metaKey: false
        };
        _this.config = {
            debug: config.debug || false,
            autoCreateContainer: config.autoCreateContainer !== undefined ? config.autoCreateContainer : true,
            containerId: config.containerId || 'ui-container',
            enableEvents: config.enableEvents !== undefined ? config.enableEvents : true,
            enableLayouts: config.enableLayouts !== undefined ? config.enableLayouts : true,
            enableAnimations: config.enableAnimations !== undefined ? config.enableAnimations : true
        };
        // 如果自动创建容器
        if (_this.config.autoCreateContainer) {
            _this.createContainer();
        }
        // 如果启用事件系统
        if (_this.config.enableEvents) {
            _this.setupEventListeners();
        }
        return _this;
    }
    /**
     * 创建HTML容器
     */
    UISystem.prototype.createContainer = function () {
        // 检查容器是否已存在
        var container = document.getElementById(this.config.containerId);
        // 如果不存在，则创建
        if (!container) {
            container = document.createElement('div');
            container.id = this.config.containerId;
            container.style.position = 'absolute';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.pointerEvents = 'none';
            container.style.overflow = 'hidden';
            container.style.zIndex = '1000';
            document.body.appendChild(container);
        }
        this.container = container;
    };
    /**
     * 设置事件监听器
     */
    UISystem.prototype.setupEventListeners = function () {
        var _this = this;
        // 鼠标移动事件
        window.addEventListener('mousemove', function (event) {
            _this.mousePosition.set(event.clientX, event.clientY);
            _this.handleMouseMove(event);
        });
        // 鼠标按下事件
        window.addEventListener('mousedown', function (event) {
            _this.handleMouseDown(event);
        });
        // 鼠标释放事件
        window.addEventListener('mouseup', function (event) {
            _this.handleMouseUp(event);
        });
        // 键盘按下事件
        window.addEventListener('keydown', function (event) {
            _this.keyStates.set(event.key, true);
            _this.updateModifiers(event);
            _this.handleKeyDown(event);
        });
        // 键盘释放事件
        window.addEventListener('keyup', function (event) {
            _this.keyStates.set(event.key, false);
            _this.updateModifiers(event);
            _this.handleKeyUp(event);
        });
        // 窗口大小改变事件
        window.addEventListener('resize', function () {
            _this.handleResize();
        });
    };
    /**
     * 更新修饰键状态
     * @param event 键盘事件
     */
    UISystem.prototype.updateModifiers = function (event) {
        this.modifiers.altKey = event.altKey;
        this.modifiers.ctrlKey = event.ctrlKey;
        this.modifiers.shiftKey = event.shiftKey;
        this.modifiers.metaKey = event.metaKey;
    };
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    UISystem.prototype.handleMouseMove = function (event) {
        // 遍历所有事件组件
        for (var _i = 0, _a = this.uiEventComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], eventComponent = _b[1];
            // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
            var camera = undefined;
            // 获取所有UI元素
            var uiElements = Array.from(this.uiComponents.values());
            // 处理鼠标移动
            eventComponent.handleMouseMove(event.clientX, event.clientY, uiElements, camera);
        }
    };
    /**
     * 处理鼠标按下事件
     * @param event 鼠标事件
     */
    UISystem.prototype.handleMouseDown = function (event) {
        // 遍历所有事件组件
        for (var _i = 0, _a = this.uiEventComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], eventComponent = _b[1];
            // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
            var camera = undefined;
            // 获取所有UI元素
            var uiElements = Array.from(this.uiComponents.values());
            // 处理鼠标按下
            eventComponent.handleMouseDown(event.clientX, event.clientY, event.button, uiElements, camera);
        }
    };
    /**
     * 处理鼠标释放事件
     * @param event 鼠标事件
     */
    UISystem.prototype.handleMouseUp = function (event) {
        // 遍历所有事件组件
        for (var _i = 0, _a = this.uiEventComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], eventComponent = _b[1];
            // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
            var camera = undefined;
            // 获取所有UI元素
            var uiElements = Array.from(this.uiComponents.values());
            // 处理鼠标释放
            eventComponent.handleMouseUp(event.clientX, event.clientY, event.button, uiElements, camera);
        }
    };
    /**
     * 处理键盘按下事件
     * @param event 键盘事件
     */
    UISystem.prototype.handleKeyDown = function (event) {
        // 遍历所有事件组件
        for (var _i = 0, _a = this.uiEventComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], eventComponent = _b[1];
            // 处理键盘按下
            eventComponent.handleKeyDown(event.key, event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
            this.modifiers);
        }
    };
    /**
     * 处理键盘释放事件
     * @param event 键盘事件
     */
    UISystem.prototype.handleKeyUp = function (event) {
        // 遍历所有事件组件
        for (var _i = 0, _a = this.uiEventComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], eventComponent = _b[1];
            // 处理键盘释放
            eventComponent.handleKeyUp(event.key, event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
            this.modifiers);
        }
    };
    /**
     * 处理窗口大小改变事件
     */
    UISystem.prototype.handleResize = function () {
        // 更新所有2D UI元素
        for (var _i = 0, _a = this.ui2DComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], component = _b[1];
            // 如果组件是响应式的，则更新
            if (component.responsive) {
                component.update(0);
            }
        }
    };
    /**
     * 注册UI组件
     * @param entity 实体
     * @param component UI组件
     */
    UISystem.prototype.registerUIComponent = function (entity, component) {
        this.uiComponents.set(entity, component);
        // 根据组件类型添加到相应的列表
        if (component instanceof UI2DComponent_1.UI2DComponent) {
            this.ui2DComponents.set(entity, component);
            // 如果有容器，将HTML元素添加到容器
            if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
                this.container.appendChild(component.htmlElement);
            }
        }
        else if (component instanceof UI3DComponent_1.UI3DComponent) {
            this.ui3DComponents.set(entity, component);
        }
    };
    /**
     * 注册UI动画组件
     * @param entity 实体
     * @param component UI动画组件
     */
    UISystem.prototype.registerUIAnimationComponent = function (entity, component) {
        this.uiAnimationComponents.set(entity, component);
    };
    /**
     * 注册UI布局组件
     * @param entity 实体
     * @param component UI布局组件
     */
    UISystem.prototype.registerUILayoutComponent = function (entity, component) {
        this.uiLayoutComponents.set(entity, component);
    };
    /**
     * 注册UI事件组件
     * @param entity 实体
     * @param component UI事件组件
     */
    UISystem.prototype.registerUIEventComponent = function (entity, component) {
        this.uiEventComponents.set(entity, component);
    };
    /**
     * 注销UI组件
     * @param entity 实体
     */
    UISystem.prototype.unregisterUIComponent = function (entity) {
        var component = this.uiComponents.get(entity);
        if (component) {
            // 从列表中移除
            this.uiComponents.delete(entity);
            // 根据组件类型从相应的列表中移除
            if (component instanceof UI2DComponent_1.UI2DComponent) {
                this.ui2DComponents.delete(entity);
                // 如果有HTML元素，从DOM中移除
                if (component.htmlElement && component.htmlElement.parentElement) {
                    component.htmlElement.parentElement.removeChild(component.htmlElement);
                }
            }
            else if (component instanceof UI3DComponent_1.UI3DComponent) {
                this.ui3DComponents.delete(entity);
            }
            // 销毁组件
            component.dispose();
        }
    };
    /**
     * 注销UI动画组件
     * @param entity 实体
     */
    UISystem.prototype.unregisterUIAnimationComponent = function (entity) {
        this.uiAnimationComponents.delete(entity);
    };
    /**
     * 注销UI布局组件
     * @param entity 实体
     */
    UISystem.prototype.unregisterUILayoutComponent = function (entity) {
        this.uiLayoutComponents.delete(entity);
    };
    /**
     * 注销UI事件组件
     * @param entity 实体
     */
    UISystem.prototype.unregisterUIEventComponent = function (entity) {
        this.uiEventComponents.delete(entity);
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    UISystem.prototype.update = function (deltaTime) {
        // 更新所有UI组件
        for (var _i = 0, _a = this.uiComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], component = _b[1];
            component.update(deltaTime);
        }
        // 如果启用动画系统，更新所有动画组件
        if (this.config.enableAnimations) {
            for (var _c = 0, _d = this.uiAnimationComponents; _c < _d.length; _c++) {
                var _e = _d[_c], _entity = _e[0], component = _e[1];
                component.update(deltaTime);
            }
        }
        // 如果启用布局系统，应用所有布局
        if (this.config.enableLayouts) {
            for (var _f = 0, _g = this.uiLayoutComponents; _f < _g.length; _f++) {
                var _h = _g[_f], entity = _h[0], layoutComponent = _h[1];
                var uiComponent = this.uiComponents.get(entity);
                if (uiComponent) {
                    layoutComponent.applyLayout(uiComponent);
                }
            }
        }
        // 更新所有3D UI元素
        // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
        var camera = undefined;
        for (var _j = 0, _k = this.ui3DComponents; _j < _k.length; _j++) {
            var _l = _k[_j], _entity = _l[0], component = _l[1];
            component.update(deltaTime, camera);
        }
    };
    /**
     * 渲染系统
     */
    UISystem.prototype.render = function () {
        // 渲染所有UI组件
        for (var _i = 0, _a = this.uiComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], component = _b[1];
            component.render();
        }
    };
    /**
     * 销毁系统
     */
    UISystem.prototype.dispose = function () {
        // 移除事件监听器
        if (this.config.enableEvents) {
            window.removeEventListener('mousemove', this.handleMouseMove);
            window.removeEventListener('mousedown', this.handleMouseDown);
            window.removeEventListener('mouseup', this.handleMouseUp);
            window.removeEventListener('keydown', this.handleKeyDown);
            window.removeEventListener('keyup', this.handleKeyUp);
            window.removeEventListener('resize', this.handleResize);
        }
        // 销毁所有UI组件
        for (var _i = 0, _a = this.uiComponents; _i < _a.length; _i++) {
            var _b = _a[_i], _entity = _b[0], component = _b[1];
            component.dispose();
        }
        // 清空所有列表
        this.uiComponents.clear();
        this.ui2DComponents.clear();
        this.ui3DComponents.clear();
        this.uiAnimationComponents.clear();
        this.uiLayoutComponents.clear();
        this.uiEventComponents.clear();
        // 移除容器
        if (this.container && this.container.parentElement) {
            this.container.parentElement.removeChild(this.container);
        }
        this.container = undefined;
    };
    return UISystem;
}(System_1.System));
exports.UISystem = UISystem;
