"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceDevice = exports.VoiceRecognitionState = void 0;
/**
 * 语音输入设备
 * 用于处理语音命令和语音识别
 */
var InputDevice_1 = require("../InputDevice");
/**
 * 语音识别状态
 */
var VoiceRecognitionState;
(function (VoiceRecognitionState) {
    /** 未初始化 */
    VoiceRecognitionState["UNINITIALIZED"] = "uninitialized";
    /** 初始化中 */
    VoiceRecognitionState["INITIALIZING"] = "initializing";
    /** 就绪 */
    VoiceRecognitionState["READY"] = "ready";
    /** 录音中 */
    VoiceRecognitionState["RECORDING"] = "recording";
    /** 识别中 */
    VoiceRecognitionState["RECOGNIZING"] = "recognizing";
    /** 错误 */
    VoiceRecognitionState["ERROR"] = "error";
})(VoiceRecognitionState || (exports.VoiceRecognitionState = VoiceRecognitionState = {}));
/**
 * 语音输入设备
 */
var VoiceDevice = /** @class */ (function (_super) {
    __extends(VoiceDevice, _super);
    /**
     * 创建语音输入设备
     * @param options 选项
     */
    function VoiceDevice(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 'voice') || this;
        /** 语音识别器 */
        _this.recognition = null;
        /** 语音合成器 */
        _this.synthesis = null;
        /** 语音识别状态 */
        _this.state = VoiceRecognitionState.UNINITIALIZED;
        /** 语音命令列表 */
        _this.commands = [];
        /** 当前识别结果 */
        _this.currentResult = null;
        /** 是否支持语音识别 */
        _this.isRecognitionSupported = false;
        /** 是否支持语音合成 */
        _this.isSynthesisSupported = false;
        _this.autoStart = options.autoStart !== undefined ? options.autoStart : false;
        _this.language = options.language || 'zh-CN';
        _this.continuous = options.continuous !== undefined ? options.continuous : true;
        _this.interimResults = options.interimResults !== undefined ? options.interimResults : true;
        _this.maxAlternatives = options.maxAlternatives || 1;
        _this.confidenceThreshold = options.confidenceThreshold || 0.5;
        // 添加语音命令
        if (options.commands) {
            _this.addCommands(options.commands);
        }
        // 检查浏览器支持
        _this.checkBrowserSupport();
        return _this;
    }
    /**
     * 检查浏览器支持
     */
    VoiceDevice.prototype.checkBrowserSupport = function () {
        // 检查语音识别支持
        this.isRecognitionSupported = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
        // 检查语音合成支持
        this.isSynthesisSupported = 'speechSynthesis' in window;
        // 更新设备值
        this.setValue('isRecognitionSupported', this.isRecognitionSupported);
        this.setValue('isSynthesisSupported', this.isSynthesisSupported);
    };
    /**
     * 初始化设备
     */
    VoiceDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 初始化语音识别
        this.initSpeechRecognition();
        // 初始化语音合成
        this.initSpeechSynthesis();
        // 如果自动开始，则开始识别
        if (this.autoStart && this.isRecognitionSupported) {
            this.startRecognition();
        }
        _super.prototype.initialize.call(this);
    };
    /**
     * 初始化语音识别
     */
    VoiceDevice.prototype.initSpeechRecognition = function () {
        if (!this.isRecognitionSupported) {
            this.state = VoiceRecognitionState.ERROR;
            this.setValue('state', this.state);
            this.eventEmitter.emit('error', { message: '浏览器不支持语音识别' });
            return;
        }
        try {
            // 创建语音识别器
            var SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            // 设置选项
            this.recognition.lang = this.language;
            this.recognition.continuous = this.continuous;
            this.recognition.interimResults = this.interimResults;
            this.recognition.maxAlternatives = this.maxAlternatives;
            // 添加事件监听器
            this.recognition.onstart = this.handleRecognitionStart.bind(this);
            this.recognition.onresult = this.handleRecognitionResult.bind(this);
            this.recognition.onerror = this.handleRecognitionError.bind(this);
            this.recognition.onend = this.handleRecognitionEnd.bind(this);
            // 更新状态
            this.state = VoiceRecognitionState.READY;
            this.setValue('state', this.state);
            this.eventEmitter.emit('ready');
        }
        catch (error) {
            this.state = VoiceRecognitionState.ERROR;
            this.setValue('state', this.state);
            this.eventEmitter.emit('error', { message: '初始化语音识别失败', error: error });
        }
    };
    /**
     * 初始化语音合成
     */
    VoiceDevice.prototype.initSpeechSynthesis = function () {
        if (!this.isSynthesisSupported) {
            this.eventEmitter.emit('error', { message: '浏览器不支持语音合成' });
            return;
        }
        try {
            // 获取语音合成器
            this.synthesis = window.speechSynthesis;
        }
        catch (error) {
            this.eventEmitter.emit('error', { message: '初始化语音合成失败', error: error });
        }
    };
    /**
     * 处理识别开始事件
     */
    VoiceDevice.prototype.handleRecognitionStart = function () {
        this.state = VoiceRecognitionState.RECORDING;
        this.setValue('state', this.state);
        this.eventEmitter.emit('recognitionStart');
    };
    /**
     * 处理识别结果事件
     * @param event 识别结果事件
     */
    VoiceDevice.prototype.handleRecognitionResult = function (event) {
        this.state = VoiceRecognitionState.RECOGNIZING;
        this.setValue('state', this.state);
        // 获取最新的识别结果
        var result = event.results[event.results.length - 1];
        var isFinal = result.isFinal;
        var text = result[0].transcript.trim();
        var confidence = result[0].confidence;
        // 创建识别结果对象
        var recognitionResult = {
            text: text,
            confidence: confidence,
            isFinal: isFinal,
            alternatives: []
        };
        // 添加替代结果
        for (var i = 1; i < result.length; i++) {
            recognitionResult.alternatives.push({
                text: result[i].transcript.trim(),
                confidence: result[i].confidence
            });
        }
        // 更新当前结果
        this.currentResult = recognitionResult;
        this.setValue('result', recognitionResult);
        // 触发结果事件
        this.eventEmitter.emit('result', recognitionResult);
        // 如果是最终结果，则处理命令
        if (isFinal && confidence >= this.confidenceThreshold) {
            this.processCommands(recognitionResult);
        }
    };
    /**
     * 处理识别错误事件
     * @param event 错误事件
     */
    VoiceDevice.prototype.handleRecognitionError = function (event) {
        this.state = VoiceRecognitionState.ERROR;
        this.setValue('state', this.state);
        this.eventEmitter.emit('error', { message: '语音识别错误', error: event.error });
    };
    /**
     * 处理识别结束事件
     */
    VoiceDevice.prototype.handleRecognitionEnd = function () {
        // 如果是连续模式，则自动重新开始
        if (this.continuous && this.state !== VoiceRecognitionState.ERROR) {
            this.startRecognition();
        }
        else {
            this.state = VoiceRecognitionState.READY;
            this.setValue('state', this.state);
            this.eventEmitter.emit('recognitionEnd');
        }
    };
    /**
     * 处理语音命令
     * @param result 识别结果
     */
    VoiceDevice.prototype.processCommands = function (result) {
        var text = result.text.toLowerCase();
        // 检查每个命令
        for (var _i = 0, _a = this.commands; _i < _a.length; _i++) {
            var command = _a[_i];
            // 检查命令关键词
            for (var _b = 0, _c = command.keywords; _b < _c.length; _b++) {
                var keyword = _c[_b];
                if (text.includes(keyword.toLowerCase())) {
                    // 检查置信度
                    var threshold = command.confidenceThreshold || this.confidenceThreshold;
                    if (result.confidence >= threshold) {
                        // 执行命令回调
                        command.callback(result);
                        // 触发命令事件
                        this.eventEmitter.emit('command', { command: command.name, result: result });
                        return;
                    }
                }
            }
        }
    };
    /**
     * 开始语音识别
     */
    VoiceDevice.prototype.startRecognition = function () {
        if (!this.isRecognitionSupported || !this.recognition) {
            this.eventEmitter.emit('error', { message: '语音识别不可用' });
            return;
        }
        if (this.state === VoiceRecognitionState.RECORDING || this.state === VoiceRecognitionState.RECOGNIZING) {
            return;
        }
        try {
            this.recognition.start();
        }
        catch (error) {
            this.eventEmitter.emit('error', { message: '启动语音识别失败', error: error });
        }
    };
    /**
     * 停止语音识别
     */
    VoiceDevice.prototype.stopRecognition = function () {
        if (!this.isRecognitionSupported || !this.recognition) {
            return;
        }
        if (this.state !== VoiceRecognitionState.RECORDING && this.state !== VoiceRecognitionState.RECOGNIZING) {
            return;
        }
        try {
            this.recognition.stop();
        }
        catch (error) {
            this.eventEmitter.emit('error', { message: '停止语音识别失败', error: error });
        }
    };
    /**
     * 添加语音命令
     * @param commands 语音命令列表
     */
    VoiceDevice.prototype.addCommands = function (commands) {
        var _a;
        (_a = this.commands).push.apply(_a, commands);
    };
    /**
     * 移除语音命令
     * @param name 命令名称
     */
    VoiceDevice.prototype.removeCommand = function (name) {
        this.commands = this.commands.filter(function (command) { return command.name !== name; });
    };
    /**
     * 清除所有语音命令
     */
    VoiceDevice.prototype.clearCommands = function () {
        this.commands = [];
    };
    /**
     * 获取当前识别结果
     * @returns 识别结果
     */
    VoiceDevice.prototype.getResult = function () {
        return this.currentResult;
    };
    /**
     * 获取当前识别状态
     * @returns 识别状态
     */
    VoiceDevice.prototype.getState = function () {
        return this.state;
    };
    /**
     * 语音合成
     * @param text 文本
     * @param options 选项
     */
    VoiceDevice.prototype.speak = function (text, options) {
        var _this = this;
        if (options === void 0) { options = {}; }
        if (!this.isSynthesisSupported || !this.synthesis) {
            this.eventEmitter.emit('error', { message: '语音合成不可用' });
            return;
        }
        // 创建语音合成话语
        var utterance = new SpeechSynthesisUtterance(text);
        // 设置选项
        if (options.voice)
            utterance.voice = options.voice;
        if (options.volume !== undefined)
            utterance.volume = options.volume;
        if (options.rate !== undefined)
            utterance.rate = options.rate;
        if (options.pitch !== undefined)
            utterance.pitch = options.pitch;
        if (options.lang)
            utterance.lang = options.lang;
        // 添加事件监听器
        utterance.onstart = function () { return _this.eventEmitter.emit('speakStart', { text: text }); };
        utterance.onend = function () { return _this.eventEmitter.emit('speakEnd', { text: text }); };
        utterance.onerror = function (event) { return _this.eventEmitter.emit('error', { message: '语音合成错误', error: event }); };
        // 开始合成
        this.synthesis.speak(utterance);
    };
    /**
     * 销毁设备
     */
    VoiceDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 停止语音识别
        this.stopRecognition();
        // 停止语音合成
        if (this.isSynthesisSupported && this.synthesis) {
            this.synthesis.cancel();
        }
        _super.prototype.destroy.call(this);
    };
    return VoiceDevice;
}(InputDevice_1.BaseInputDevice));
exports.VoiceDevice = VoiceDevice;
