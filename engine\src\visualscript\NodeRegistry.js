"use strict";
/**
 * 节点注册表
 * 用于管理和创建可视化脚本节点
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeRegistry = void 0;
var NodeRegistry = exports.NodeRegistry = /** @class */ (function () {
    function NodeRegistry() {
    }
    /**
     * 注册节点类型
     */
    NodeRegistry.register = function (nodeType, constructor, category, description, icon, color) {
        if (category === void 0) { category = 'General'; }
        if (description === void 0) { description = ''; }
        this.nodes.set(nodeType, {
            constructor: constructor,
            category: category,
            description: description,
            icon: icon,
            color: color
        });
        this.categories.add(category);
    };
    /**
     * 创建节点实例
     */
    NodeRegistry.createNode = function (nodeType) {
        var _a;
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        var definition = this.nodes.get(nodeType);
        if (!definition) {
            console.warn("\u672A\u627E\u5230\u8282\u70B9\u7C7B\u578B: ".concat(nodeType));
            return null;
        }
        try {
            return new ((_a = definition.constructor).bind.apply(_a, __spreadArray([void 0], args, false)))();
        }
        catch (error) {
            console.error("\u521B\u5EFA\u8282\u70B9\u5931\u8D25: ".concat(nodeType), error);
            return null;
        }
    };
    /**
     * 获取节点定义
     */
    NodeRegistry.getNodeDefinition = function (nodeType) {
        return this.nodes.get(nodeType) || null;
    };
    /**
     * 获取所有注册的节点类型
     */
    NodeRegistry.getRegisteredNodeTypes = function () {
        return Array.from(this.nodes.keys());
    };
    /**
     * 获取所有类别
     */
    NodeRegistry.getCategories = function () {
        return Array.from(this.categories);
    };
    /**
     * 根据类别获取节点类型
     */
    NodeRegistry.getNodeTypesByCategory = function (category) {
        var result = [];
        for (var _i = 0, _a = this.nodes.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], nodeType = _b[0], definition = _b[1];
            if (definition.category === category) {
                result.push(nodeType);
            }
        }
        return result;
    };
    /**
     * 检查节点类型是否已注册
     */
    NodeRegistry.isRegistered = function (nodeType) {
        return this.nodes.has(nodeType);
    };
    /**
     * 注销节点类型
     */
    NodeRegistry.unregister = function (nodeType) {
        return this.nodes.delete(nodeType);
    };
    /**
     * 清空所有注册的节点
     */
    NodeRegistry.clear = function () {
        this.nodes.clear();
        this.categories.clear();
    };
    /**
     * 获取节点信息列表
     */
    NodeRegistry.getNodeInfoList = function () {
        var result = [];
        for (var _i = 0, _a = this.nodes.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], nodeType = _b[0], definition = _b[1];
            result.push({
                nodeType: nodeType,
                category: definition.category,
                description: definition.description,
                icon: definition.icon,
                color: definition.color
            });
        }
        return result;
    };
    /**
     * 搜索节点
     */
    NodeRegistry.searchNodes = function (query) {
        var lowerQuery = query.toLowerCase();
        var result = [];
        for (var _i = 0, _a = this.nodes.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], nodeType = _b[0], definition = _b[1];
            if (nodeType.toLowerCase().includes(lowerQuery) ||
                definition.description.toLowerCase().includes(lowerQuery) ||
                definition.category.toLowerCase().includes(lowerQuery)) {
                result.push(nodeType);
            }
        }
        return result;
    };
    NodeRegistry.nodes = new Map();
    NodeRegistry.categories = new Set();
    return NodeRegistry;
}());
