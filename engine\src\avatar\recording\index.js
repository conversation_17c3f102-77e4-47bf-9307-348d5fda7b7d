"use strict";
/**
 * 动作录制模块
 *
 * 提供角色动作录制和回放功能
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionPlayback = exports.ActionRecorder = void 0;
// 导出动作录制器
var ActionRecorder_1 = require("./ActionRecorder");
Object.defineProperty(exports, "ActionRecorder", { enumerable: true, get: function () { return ActionRecorder_1.ActionRecorder; } });
// 导出动作回放器
var ActionPlayback_1 = require("./ActionPlayback");
Object.defineProperty(exports, "ActionPlayback", { enumerable: true, get: function () { return ActionPlayback_1.ActionPlayback; } });
