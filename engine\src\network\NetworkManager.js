"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkManager = void 0;
/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var WebRTCConnection_1 = require("./WebRTCConnection");
var WebSocketConnection_1 = require("./WebSocketConnection");
var Debug_1 = require("../utils/Debug");
var MessageType_1 = require("./MessageType");
var MessageSerializer_1 = require("./MessageSerializer");
/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
var NetworkManager = /** @class */ (function (_super) {
    __extends(NetworkManager, _super);
    /**
     * 创建网络管理器
     * @param options 配置选项
     */
    function NetworkManager(options) {
        var _this = _super.call(this) || this;
        /** WebSocket连接 */
        _this.wsConnection = null;
        /** WebRTC连接映射表 */
        _this.rtcConnections = new Map();
        /** 本地用户ID */
        _this.localUserId = null;
        /** 本地用户名 */
        _this.localUsername = null;
        /** 远程用户映射表 */
        _this.remoteUsers = new Map();
        /** 网络实体映射表 */
        _this.networkEntities = new Map();
        /** 是否已连接 */
        _this.connected = false;
        /** 房间ID */
        _this.roomId = null;
        _this.options = options;
        _this.messageSerializer = new MessageSerializer_1.MessageSerializer(options.enableCompression);
        // 设置本地用户信息
        if (options.userId) {
            _this.localUserId = options.userId;
        }
        if (options.username) {
            _this.localUsername = options.username;
        }
        return _this;
    }
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     * @param roomId 房间ID
     * @returns Promise
     */
    NetworkManager.prototype.connect = function (serverUrl, roomId) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.connected) {
                            Debug_1.Debug.warn('NetworkManager', 'Already connected to server');
                            return [2 /*return*/];
                        }
                        this.roomId = roomId || null;
                        // 创建WebSocket连接
                        this.wsConnection = new WebSocketConnection_1.WebSocketConnection(serverUrl);
                        // 设置WebSocket事件监听器
                        this.setupWebSocketListeners();
                        // 连接到WebSocket服务器
                        return [4 /*yield*/, this.wsConnection.connect()];
                    case 1:
                        // 连接到WebSocket服务器
                        _a.sent();
                        // 发送加入房间消息
                        if (this.roomId) {
                            this.sendJoinRoom(this.roomId);
                        }
                        this.connected = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 断开连接
     * @returns Promise
     */
    NetworkManager.prototype.disconnect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _i, _a, connection;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.connected) {
                            return [2 /*return*/];
                        }
                        // 发送离开消息
                        this.sendLeaveRoom();
                        _i = 0, _a = this.rtcConnections.values();
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        connection = _a[_i];
                        return [4 /*yield*/, connection.disconnect()];
                    case 2:
                        _b.sent();
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4:
                        this.rtcConnections.clear();
                        if (!this.wsConnection) return [3 /*break*/, 6];
                        return [4 /*yield*/, this.wsConnection.disconnect()];
                    case 5:
                        _b.sent();
                        this.wsConnection = null;
                        _b.label = 6;
                    case 6:
                        // 清空用户和实体
                        this.remoteUsers.clear();
                        this.networkEntities.clear();
                        this.connected = false;
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 更新网络管理器
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkManager.prototype.update = function (deltaTime) {
        // 更新WebRTC连接
        for (var _i = 0, _a = this.rtcConnections.values(); _i < _a.length; _i++) {
            var connection = _a[_i];
            connection.update(deltaTime);
        }
    };
    /**
     * 发送消息到所有用户
     * @param type 消息类型
     * @param data 消息数据
     */
    NetworkManager.prototype.sendToAll = function (type, data) {
        var message = {
            type: type,
            data: data,
            senderId: this.localUserId,
            timestamp: Date.now(),
        };
        // 通过WebSocket发送到服务器
        if (this.wsConnection) {
            this.wsConnection.send(MessageType_1.MessageType.BROADCAST, message);
        }
        // 通过WebRTC发送到所有对等连接
        for (var _i = 0, _a = this.rtcConnections.values(); _i < _a.length; _i++) {
            var connection = _a[_i];
            connection.send(message);
        }
    };
    /**
     * 发送消息到特定用户
     * @param userId 用户ID
     * @param type 消息类型
     * @param data 消息数据
     */
    NetworkManager.prototype.sendToUser = function (userId, type, data) {
        var message = {
            type: type,
            data: data,
            senderId: this.localUserId,
            receiverId: userId,
            timestamp: Date.now(),
        };
        // 通过WebSocket发送到服务器
        if (this.wsConnection) {
            this.wsConnection.send(MessageType_1.MessageType.DIRECT, message);
        }
        // 如果存在WebRTC连接，则直接发送
        var connection = this.rtcConnections.get(userId);
        if (connection) {
            connection.send(message);
        }
    };
    /**
     * 发送实体更新
     * @param entityId 实体ID
     * @param data 实体数据
     */
    NetworkManager.prototype.sendEntityUpdate = function (entityId, data) {
        var message = {
            type: MessageType_1.MessageType.ENTITY_UPDATE,
            data: __assign({ entityId: entityId }, data),
            senderId: this.localUserId,
            timestamp: Date.now(),
        };
        // 通过WebSocket发送到服务器
        if (this.wsConnection) {
            this.wsConnection.send(MessageType_1.MessageType.BROADCAST, message);
        }
        // 通过WebRTC发送到所有对等连接
        for (var _i = 0, _a = this.rtcConnections.values(); _i < _a.length; _i++) {
            var connection = _a[_i];
            connection.send(message);
        }
    };
    /**
     * 发送加入房间消息
     * @param roomId 房间ID
     */
    NetworkManager.prototype.sendJoinRoom = function (roomId) {
        if (!this.wsConnection) {
            return;
        }
        var message = {
            type: MessageType_1.MessageType.JOIN_ROOM,
            data: {
                roomId: roomId,
                userId: this.localUserId,
                username: this.localUsername,
            },
            senderId: this.localUserId,
            timestamp: Date.now(),
        };
        this.wsConnection.send(MessageType_1.MessageType.SYSTEM, message);
    };
    /**
     * 发送离开房间消息
     */
    NetworkManager.prototype.sendLeaveRoom = function () {
        if (!this.wsConnection || !this.roomId) {
            return;
        }
        var message = {
            type: MessageType_1.MessageType.LEAVE_ROOM,
            data: {
                roomId: this.roomId,
                userId: this.localUserId,
            },
            senderId: this.localUserId,
            timestamp: Date.now(),
        };
        this.wsConnection.send(MessageType_1.MessageType.SYSTEM, message);
    };
    /**
     * 设置WebSocket事件监听器
     */
    NetworkManager.prototype.setupWebSocketListeners = function () {
        var _this = this;
        if (!this.wsConnection) {
            return;
        }
        // 监听连接事件
        this.wsConnection.on('connected', function () {
            Debug_1.Debug.log('NetworkManager', 'WebSocket connected');
            _this.emit('connected');
        });
        // 监听断开连接事件
        this.wsConnection.on('disconnected', function () {
            Debug_1.Debug.log('NetworkManager', 'WebSocket disconnected');
            _this.emit('disconnected');
        });
        // 监听错误事件
        this.wsConnection.on('error', function (error) {
            Debug_1.Debug.error('NetworkManager', 'WebSocket error:', error);
            _this.emit('error', error);
        });
        // 监听消息事件
        this.wsConnection.on('message', function (message) {
            _this.handleMessage(message);
        });
    };
    /**
     * 处理接收到的消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleMessage = function (message) {
        switch (message.type) {
            case MessageType_1.MessageType.JOIN_ROOM_SUCCESS:
                this.handleJoinRoomSuccess(message);
                break;
            case MessageType_1.MessageType.USER_JOINED:
                this.handleUserJoined(message);
                break;
            case MessageType_1.MessageType.USER_LEFT:
                this.handleUserLeft(message);
                break;
            case MessageType_1.MessageType.ENTITY_CREATE:
                this.handleEntityCreate(message);
                break;
            case MessageType_1.MessageType.ENTITY_UPDATE:
                this.handleEntityUpdate(message);
                break;
            case MessageType_1.MessageType.ENTITY_DELETE:
                this.handleEntityDelete(message);
                break;
            case MessageType_1.MessageType.WEBRTC_OFFER:
                this.handleWebRTCOffer(message);
                break;
            case MessageType_1.MessageType.WEBRTC_ANSWER:
                this.handleWebRTCAnswer(message);
                break;
            case MessageType_1.MessageType.WEBRTC_ICE_CANDIDATE:
                this.handleWebRTCIceCandidate(message);
                break;
            default:
                // 触发自定义消息事件
                this.emit('message', message);
                break;
        }
    };
    /**
     * 处理加入房间成功消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleJoinRoomSuccess = function (message) {
        var _a = message.data, userId = _a.userId, users = _a.users, entities = _a.entities;
        // 设置本地用户ID
        this.localUserId = userId;
        Debug_1.Debug.log('NetworkManager', "Joined room as user ".concat(userId));
        // 处理房间中的用户
        if (users && Array.isArray(users)) {
            for (var _i = 0, users_1 = users; _i < users_1.length; _i++) {
                var user = users_1[_i];
                if (user.userId !== this.localUserId) {
                    this.addRemoteUser(user.userId, user.username);
                    // 如果启用了WebRTC，则创建对等连接
                    if (this.options.enableWebRTC) {
                        this.createWebRTCConnection(user.userId);
                    }
                }
            }
        }
        // 处理房间中的实体
        if (entities && Array.isArray(entities)) {
            for (var _b = 0, entities_1 = entities; _b < entities_1.length; _b++) {
                var entity = entities_1[_b];
                this.addNetworkEntity(entity.entityId, entity);
            }
        }
        // 触发加入房间成功事件
        this.emit('joinedRoom', this.roomId, userId);
    };
    /**
     * 处理用户加入消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleUserJoined = function (message) {
        var _a = message.data, userId = _a.userId, username = _a.username;
        // 添加远程用户
        this.addRemoteUser(userId, username);
        // 如果启用了WebRTC，则创建对等连接
        if (this.options.enableWebRTC) {
            this.createWebRTCConnection(userId);
        }
        // 触发用户加入事件
        this.emit('userJoined', userId, username);
    };
    /**
     * 处理用户离开消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleUserLeft = function (message) {
        var userId = message.data.userId;
        // 移除WebRTC连接
        this.removeWebRTCConnection(userId);
        // 移除远程用户
        this.removeRemoteUser(userId);
        // 触发用户离开事件
        this.emit('userLeft', userId);
    };
    /**
     * 处理实体创建消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleEntityCreate = function (message) {
        var _a = message.data, entityId = _a.entityId, data = __rest(_a, ["entityId"]);
        // 添加网络实体
        this.addNetworkEntity(entityId, data);
        // 触发实体创建事件
        this.emit('entityCreated', entityId, data);
    };
    /**
     * 处理实体更新消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleEntityUpdate = function (message) {
        var _a = message.data, entityId = _a.entityId, data = __rest(_a, ["entityId"]);
        // 更新网络实体
        this.updateNetworkEntity(entityId, data);
        // 触发实体更新事件
        this.emit('entityUpdated', entityId, data);
    };
    /**
     * 处理实体删除消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleEntityDelete = function (message) {
        var entityId = message.data.entityId;
        // 移除网络实体
        this.removeNetworkEntity(entityId);
        // 触发实体删除事件
        this.emit('entityDeleted', entityId);
    };
    /**
     * 处理WebRTC提议消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleWebRTCOffer = function (message) {
        var _a = message.data, userId = _a.userId, offer = _a.offer;
        // 获取WebRTC连接
        var connection = this.rtcConnections.get(userId);
        if (connection) {
            // 处理提议
            connection.handleOffer(offer);
        }
    };
    /**
     * 处理WebRTC应答消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleWebRTCAnswer = function (message) {
        var _a = message.data, userId = _a.userId, answer = _a.answer;
        // 获取WebRTC连接
        var connection = this.rtcConnections.get(userId);
        if (connection) {
            // 处理应答
            connection.handleAnswer(answer);
        }
    };
    /**
     * 处理WebRTC ICE候选消息
     * @param message 网络消息
     */
    NetworkManager.prototype.handleWebRTCIceCandidate = function (message) {
        var _a = message.data, userId = _a.userId, candidate = _a.candidate;
        // 获取WebRTC连接
        var connection = this.rtcConnections.get(userId);
        if (connection) {
            // 处理ICE候选
            connection.handleIceCandidate(candidate);
        }
    };
    /**
     * 添加远程用户
     * @param userId 用户ID
     * @param username 用户名
     */
    NetworkManager.prototype.addRemoteUser = function (userId, username) {
        if (this.remoteUsers.has(userId)) {
            return;
        }
        var user = {
            userId: userId,
            username: username,
            joinTime: Date.now(),
        };
        this.remoteUsers.set(userId, user);
        Debug_1.Debug.log('NetworkManager', "Added remote user: ".concat(username, " (").concat(userId, ")"));
    };
    /**
     * 移除远程用户
     * @param userId 用户ID
     */
    NetworkManager.prototype.removeRemoteUser = function (userId) {
        if (!this.remoteUsers.has(userId)) {
            return;
        }
        var user = this.remoteUsers.get(userId);
        this.remoteUsers.delete(userId);
        Debug_1.Debug.log('NetworkManager', "Removed remote user: ".concat(user.username, " (").concat(userId, ")"));
    };
    /**
     * 添加网络实体
     * @param entityId 实体ID
     * @param data 实体数据
     */
    NetworkManager.prototype.addNetworkEntity = function (entityId, data) {
        if (this.networkEntities.has(entityId)) {
            return;
        }
        var entity = {
            entityId: entityId,
            ownerId: data.ownerId,
            data: data,
            createTime: Date.now(),
            updateTime: Date.now(),
        };
        this.networkEntities.set(entityId, entity);
        Debug_1.Debug.log('NetworkManager', "Added network entity: ".concat(entityId));
    };
    /**
     * 更新网络实体
     * @param entityId 实体ID
     * @param data 实体数据
     */
    NetworkManager.prototype.updateNetworkEntity = function (entityId, data) {
        var entity = this.networkEntities.get(entityId);
        if (!entity) {
            return;
        }
        // 更新实体数据
        entity.data = __assign(__assign({}, entity.data), data);
        entity.updateTime = Date.now();
    };
    /**
     * 移除网络实体
     * @param entityId 实体ID
     */
    NetworkManager.prototype.removeNetworkEntity = function (entityId) {
        if (!this.networkEntities.has(entityId)) {
            return;
        }
        this.networkEntities.delete(entityId);
        Debug_1.Debug.log('NetworkManager', "Removed network entity: ".concat(entityId));
    };
    /**
     * 创建WebRTC连接
     * @param userId 用户ID
     */
    NetworkManager.prototype.createWebRTCConnection = function (userId) {
        if (this.rtcConnections.has(userId)) {
            return;
        }
        var connection = new WebRTCConnection_1.WebRTCConnection(userId, this.options.iceServers || []);
        // 设置WebRTC事件监听器
        this.setupWebRTCListeners(connection);
        // 添加到连接映射表
        this.rtcConnections.set(userId, connection);
        // 创建对等连接
        connection.createConnection();
        Debug_1.Debug.log('NetworkManager', "Created WebRTC connection to user: ".concat(userId));
    };
    /**
     * 移除WebRTC连接
     * @param userId 用户ID
     */
    NetworkManager.prototype.removeWebRTCConnection = function (userId) {
        var connection = this.rtcConnections.get(userId);
        if (!connection) {
            return;
        }
        // 断开连接
        connection.disconnect();
        // 从连接映射表中移除
        this.rtcConnections.delete(userId);
        Debug_1.Debug.log('NetworkManager', "Removed WebRTC connection to user: ".concat(userId));
    };
    /**
     * 设置WebRTC事件监听器
     * @param connection WebRTC连接
     */
    NetworkManager.prototype.setupWebRTCListeners = function (connection) {
        var _this = this;
        // 监听连接事件
        connection.on('connected', function () {
            Debug_1.Debug.log('NetworkManager', "WebRTC connected to user: ".concat(connection.getUserId()));
            _this.emit('webrtcConnected', connection.getUserId());
        });
        // 监听断开连接事件
        connection.on('disconnected', function () {
            Debug_1.Debug.log('NetworkManager', "WebRTC disconnected from user: ".concat(connection.getUserId()));
            _this.emit('webrtcDisconnected', connection.getUserId());
        });
        // 监听错误事件
        connection.on('error', function (error) {
            Debug_1.Debug.error('NetworkManager', "WebRTC error with user ".concat(connection.getUserId(), ":"), error);
            _this.emit('webrtcError', connection.getUserId(), error);
        });
        // 监听消息事件
        connection.on('message', function (message) {
            _this.handleMessage(message);
        });
        // 监听ICE候选事件
        connection.on('iceCandidate', function (candidate) {
            _this.sendWebRTCIceCandidate(connection.getUserId(), candidate);
        });
        // 监听提议事件
        connection.on('offer', function (offer) {
            _this.sendWebRTCOffer(connection.getUserId(), offer);
        });
        // 监听应答事件
        connection.on('answer', function (answer) {
            _this.sendWebRTCAnswer(connection.getUserId(), answer);
        });
    };
    /**
     * 发送WebRTC提议
     * @param userId 用户ID
     * @param offer 提议
     */
    NetworkManager.prototype.sendWebRTCOffer = function (userId, offer) {
        if (!this.wsConnection) {
            return;
        }
        var message = {
            type: MessageType_1.MessageType.WEBRTC_OFFER,
            data: {
                userId: this.localUserId,
                targetUserId: userId,
                offer: offer,
            },
            senderId: this.localUserId,
            receiverId: userId,
            timestamp: Date.now(),
        };
        this.wsConnection.send(MessageType_1.MessageType.SYSTEM, message);
    };
    /**
     * 发送WebRTC应答
     * @param userId 用户ID
     * @param answer 应答
     */
    NetworkManager.prototype.sendWebRTCAnswer = function (userId, answer) {
        if (!this.wsConnection) {
            return;
        }
        var message = {
            type: MessageType_1.MessageType.WEBRTC_ANSWER,
            data: {
                userId: this.localUserId,
                targetUserId: userId,
                answer: answer,
            },
            senderId: this.localUserId,
            receiverId: userId,
            timestamp: Date.now(),
        };
        this.wsConnection.send(MessageType_1.MessageType.SYSTEM, message);
    };
    /**
     * 发送WebRTC ICE候选
     * @param userId 用户ID
     * @param candidate ICE候选
     */
    NetworkManager.prototype.sendWebRTCIceCandidate = function (userId, candidate) {
        if (!this.wsConnection) {
            return;
        }
        var message = {
            type: MessageType_1.MessageType.WEBRTC_ICE_CANDIDATE,
            data: {
                userId: this.localUserId,
                targetUserId: userId,
                candidate: candidate,
            },
            senderId: this.localUserId,
            receiverId: userId,
            timestamp: Date.now(),
        };
        this.wsConnection.send(MessageType_1.MessageType.SYSTEM, message);
    };
    /**
     * 销毁网络管理器
     */
    NetworkManager.prototype.dispose = function () {
        // 断开连接
        this.disconnect();
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return NetworkManager;
}(EventEmitter_1.EventEmitter));
exports.NetworkManager = NetworkManager;
