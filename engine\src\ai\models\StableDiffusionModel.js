"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StableDiffusionModel = void 0;
/**
 * Stable Diffusion模型
 * 用于图像生成
 */
var AIModelType_1 = require("../AIModelType");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * Stable Diffusion模型
 */
var StableDiffusionModel = exports.StableDiffusionModel = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    function StableDiffusionModel(config, globalConfig) {
        if (config === void 0) { config = {}; }
        if (globalConfig === void 0) { globalConfig = {}; }
        /** 模型类型 */
        this.type = AIModelType_1.AIModelType.STABLE_DIFFUSION;
        /** 是否已初始化 */
        this.initialized = false;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 模型实例 */
        this.model = null;
        /** 模型加载进度 */
        this.loadProgress = 0;
        this.config = __assign({}, config);
        this.globalConfig = __assign({}, globalConfig);
    }
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    StableDiffusionModel.prototype.getType = function () {
        return this.type;
    };
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    StableDiffusionModel.prototype.getConfig = function () {
        return __assign({}, this.config);
    };
    /**
     * 初始化模型
     * @returns 是否成功
     */
    StableDiffusionModel.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var debug, useLocalModel, modelPath, apiKey, baseUrl, i, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.initialized) {
                            return [2 /*return*/, true];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 6, , 7]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log('初始化Stable Diffusion模型...');
                        }
                        useLocalModel = this.config.useLocalModel !== undefined
                            ? this.config.useLocalModel
                            : this.globalConfig.useLocalModel;
                        modelPath = this.config.modelPath || this.globalConfig.modelPath || '';
                        apiKey = this.config.apiKey ||
                            (this.globalConfig.apiKeys && this.globalConfig.apiKeys[AIModelType_1.AIModelType.STABLE_DIFFUSION]) ||
                            '';
                        baseUrl = this.config.baseUrl ||
                            (this.globalConfig.baseUrls && this.globalConfig.baseUrls[AIModelType_1.AIModelType.STABLE_DIFFUSION]) ||
                            '';
                        i = 0;
                        _a.label = 2;
                    case 2:
                        if (!(i <= 10)) return [3 /*break*/, 5];
                        this.loadProgress = i / 10;
                        this.eventEmitter.emit('loadProgress', { progress: this.loadProgress });
                        if (!(i < 10)) return [3 /*break*/, 4];
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        i++;
                        return [3 /*break*/, 2];
                    case 5:
                        // 如果使用本地模型，加载本地模型
                        if (useLocalModel) {
                            if (debug) {
                                console.log("\u52A0\u8F7D\u672C\u5730Stable Diffusion\u6A21\u578B: ".concat(modelPath));
                            }
                            // 这里应该实现本地模型加载逻辑
                            // 实际应用中，可能需要使用ONNX Runtime或其他库
                            this.model = {
                                generate: function (prompt, options) { return _this.mockGenerate(prompt, options); }
                            };
                        }
                        else {
                            if (debug) {
                                console.log("\u52A0\u8F7D\u8FDC\u7A0BStable Diffusion\u6A21\u578B: ".concat(baseUrl));
                            }
                            // 这里应该实现远程API调用逻辑
                            this.model = {
                                generate: function (prompt, options) { return _this.mockGenerate(prompt, options); }
                            };
                        }
                        this.initialized = true;
                        this.eventEmitter.emit('initialized', { success: true });
                        if (debug) {
                            console.log('Stable Diffusion模型初始化完成');
                        }
                        return [2 /*return*/, true];
                    case 6:
                        error_1 = _a.sent();
                        console.error('初始化Stable Diffusion模型失败:', error_1);
                        this.eventEmitter.emit('initialized', { success: false, error: error_1 });
                        return [2 /*return*/, false];
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成图像
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 图像Blob
     */
    StableDiffusionModel.prototype.generateImage = function (prompt, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var mergedOptions, debug, result, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initialized) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.initialize()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        mergedOptions = __assign(__assign({}, StableDiffusionModel.DEFAULT_IMAGE_OPTIONS), options);
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        debug = this.config.debug || this.globalConfig.debug;
                        if (debug) {
                            console.log("\u751F\u6210\u56FE\u50CF: \"".concat(prompt, "\""));
                            console.log('选项:', mergedOptions);
                        }
                        return [4 /*yield*/, this.model.generate(prompt, mergedOptions)];
                    case 4:
                        result = _a.sent();
                        if (debug) {
                            console.log('图像生成完成');
                        }
                        return [2 /*return*/, result];
                    case 5:
                        error_2 = _a.sent();
                        console.error('生成图像失败:', error_2);
                        throw error_2;
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    StableDiffusionModel.prototype.generateText = function (prompt, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                throw new Error('Stable Diffusion模型不支持文本生成');
            });
        });
    };
    /**
     * 模拟生成图像
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 图像Blob
     */
    StableDiffusionModel.prototype.mockGenerate = function (prompt, options) {
        return __awaiter(this, void 0, void 0, function () {
            var onProgress, i, canvas, ctx, gradient;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        onProgress = options.onProgress;
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i <= 10)) return [3 /*break*/, 4];
                        if (onProgress) {
                            onProgress(i / 10);
                        }
                        if (!(i < 10)) return [3 /*break*/, 3];
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4:
                        canvas = document.createElement('canvas');
                        canvas.width = options.width || 512;
                        canvas.height = options.height || 512;
                        ctx = canvas.getContext('2d');
                        if (ctx) {
                            gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                            gradient.addColorStop(0, '#ff9a9e');
                            gradient.addColorStop(1, '#fad0c4');
                            ctx.fillStyle = gradient;
                            ctx.fillRect(0, 0, canvas.width, canvas.height);
                            // 添加文本
                            ctx.font = '20px Arial';
                            ctx.fillStyle = 'black';
                            ctx.textAlign = 'center';
                            ctx.fillText("\u6A21\u62DF\u56FE\u50CF: ".concat(prompt), canvas.width / 2, canvas.height / 2);
                        }
                        // 将Canvas转换为Blob
                        return [2 /*return*/, new Promise(function (resolve) {
                                canvas.toBlob(function (blob) {
                                    resolve(blob || new Blob());
                                }, 'image/png');
                            })];
                }
            });
        });
    };
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    StableDiffusionModel.prototype.on = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    StableDiffusionModel.prototype.off = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 释放资源
     */
    StableDiffusionModel.prototype.dispose = function () {
        // 释放模型资源
        this.model = null;
        // 重置状态
        this.initialized = false;
        // 清空事件监听器
        this.eventEmitter.removeAllListeners();
    };
    /** 默认图像生成选项 */
    StableDiffusionModel.DEFAULT_IMAGE_OPTIONS = {
        width: 512,
        height: 512,
        steps: 30,
        guidanceScale: 7.5,
        sampler: 'euler_a',
        safetyChecker: true
    };
    return StableDiffusionModel;
}());
