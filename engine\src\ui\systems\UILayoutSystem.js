"use strict";
/**
 * UILayoutSystem.ts
 *
 * UI布局系统，管理UI元素的布局
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UILayoutSystem = void 0;
var System_1 = require("../../core/System");
var three_1 = require("three");
var UILayoutComponent_1 = require("../components/UILayoutComponent");
/**
 * UI布局系统
 * 管理UI元素的布局
 */
var UILayoutSystem = /** @class */ (function (_super) {
    __extends(UILayoutSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config UI布局系统配置
     */
    function UILayoutSystem(world, uiSystem, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级（默认为0）
        _super.call(this, 0) || this;
        // 布局组件列表
        _this.layoutComponents = new Map();
        // 上次应用布局的时间
        _this.lastApplyTime = 0;
        // 保存世界引用
        _this.world = world;
        _this.uiSystem = uiSystem;
        _this.config = {
            debug: config.debug || false,
            autoApplyLayout: config.autoApplyLayout !== undefined ? config.autoApplyLayout : true,
            autoApplyInterval: config.autoApplyInterval || 500
        };
        return _this;
    }
    /**
     * 注册布局组件
     * @param entity 实体
     * @param component 布局组件
     */
    UILayoutSystem.prototype.registerLayoutComponent = function (entity, component) {
        this.layoutComponents.set(entity, component);
        this.uiSystem.registerUILayoutComponent(entity, component);
    };
    /**
     * 注销布局组件
     * @param entity 实体
     */
    UILayoutSystem.prototype.unregisterLayoutComponent = function (entity) {
        this.layoutComponents.delete(entity);
        this.uiSystem.unregisterUILayoutComponent(entity);
    };
    /**
     * 获取或创建布局组件
     * @param entity 实体
     * @param layout 布局
     * @param layoutItemParams 布局项参数
     * @returns 布局组件
     */
    UILayoutSystem.prototype.getOrCreateLayoutComponent = function (entity, layout, layoutItemParams) {
        var component = this.layoutComponents.get(entity);
        if (!component) {
            component = new UILayoutComponent_1.UILayoutComponent(entity, layout, layoutItemParams);
            this.registerLayoutComponent(entity, component);
        }
        else {
            component.setLayout(layout);
            if (layoutItemParams) {
                component.setLayoutItemParams(layoutItemParams);
            }
        }
        return component;
    };
    /**
     * 创建网格布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 网格布局参数
     * @returns 布局组件
     */
    UILayoutSystem.prototype.createGridLayout = function (entity, uiComponent, params) {
        var layout = new UILayoutComponent_1.GridLayout(params);
        var component = this.getOrCreateLayoutComponent(entity, layout);
        // 应用布局
        component.applyLayout(uiComponent);
        return component;
    };
    /**
     * 创建弹性布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 弹性布局参数
     * @returns 布局组件
     */
    UILayoutSystem.prototype.createFlexLayout = function (entity, uiComponent, params) {
        var layout = new UILayoutComponent_1.FlexLayout(params);
        var component = this.getOrCreateLayoutComponent(entity, layout);
        // 应用布局
        component.applyLayout(uiComponent);
        return component;
    };
    /**
     * 创建绝对布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 绝对布局参数
     * @returns 布局组件
     */
    UILayoutSystem.prototype.createAbsoluteLayout = function (entity, uiComponent, params) {
        if (params === void 0) { params = {}; }
        var layout = new UILayoutComponent_1.AbsoluteLayout(params);
        var component = this.getOrCreateLayoutComponent(entity, layout);
        // 应用布局
        component.applyLayout(uiComponent);
        return component;
    };
    /**
     * 创建相对布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 相对布局参数
     * @returns 布局组件
     */
    UILayoutSystem.prototype.createRelativeLayout = function (entity, uiComponent, params) {
        if (params === void 0) { params = {}; }
        var layout = new UILayoutComponent_1.RelativeLayout(params);
        var component = this.getOrCreateLayoutComponent(entity, layout);
        // 应用布局
        component.applyLayout(uiComponent);
        return component;
    };
    /**
     * 设置布局项参数
     * @param entity 实体
     * @param params 布局项参数
     */
    UILayoutSystem.prototype.setLayoutItemParams = function (entity, params) {
        var component = this.layoutComponents.get(entity);
        if (component) {
            component.setLayoutItemParams(params);
        }
        else {
            // 如果没有布局组件，创建一个空布局
            component = new UILayoutComponent_1.UILayoutComponent(entity, new UILayoutComponent_1.AbsoluteLayout(), params);
            this.registerLayoutComponent(entity, component);
        }
    };
    /**
     * 应用所有布局
     */
    UILayoutSystem.prototype.applyAllLayouts = function () {
        for (var _i = 0, _a = this.layoutComponents; _i < _a.length; _i++) {
            var _b = _a[_i], entity = _b[0], layoutComponent = _b[1];
            // 尝试获取实体上的UI组件
            // 注意：这里假设实体上已经添加了UIComponent组件
            // 实际实现可能需要根据具体的组件获取方式进行调整
            var uiComponent = entity.getComponent('UIComponent');
            if (uiComponent) {
                layoutComponent.applyLayout(uiComponent);
            }
        }
    };
    /**
     * 创建居中布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @returns 布局组件
     */
    UILayoutSystem.prototype.createCenterLayout = function (entity, uiComponent) {
        // 居中布局实际上是一个特殊的绝对布局
        var layout = new UILayoutComponent_1.AbsoluteLayout();
        var component = this.getOrCreateLayoutComponent(entity, layout);
        // 设置居中位置
        uiComponent.setPosition(new three_1.Vector2(0, 0));
        return component;
    };
    /**
     * 创建网格布局项
     * @param entity 实体
     * @param column 列位置
     * @param row 行位置
     * @param columnSpan 列跨度
     * @param rowSpan 行跨度
     */
    UILayoutSystem.prototype.createGridLayoutItem = function (entity, column, row, columnSpan, rowSpan) {
        if (columnSpan === void 0) { columnSpan = 1; }
        if (rowSpan === void 0) { rowSpan = 1; }
        var params = {
            gridColumn: "".concat(column, " / span ").concat(columnSpan),
            gridRow: "".concat(row, " / span ").concat(rowSpan)
        };
        this.setLayoutItemParams(entity, params);
    };
    /**
     * 创建弹性布局项
     * @param entity 实体
     * @param flexGrow 增长系数
     * @param flexShrink 收缩系数
     * @param flexBasis 基础尺寸
     * @param alignSelf 自对齐方式
     * @param order 顺序
     */
    UILayoutSystem.prototype.createFlexLayoutItem = function (entity, flexGrow, flexShrink, flexBasis, alignSelf, order) {
        if (flexGrow === void 0) { flexGrow = 0; }
        if (flexShrink === void 0) { flexShrink = 1; }
        if (flexBasis === void 0) { flexBasis = 'auto'; }
        if (alignSelf === void 0) { alignSelf = 'auto'; }
        if (order === void 0) { order = 0; }
        var params = {
            flexGrow: flexGrow,
            flexShrink: flexShrink,
            flexBasis: flexBasis,
            alignSelf: alignSelf,
            order: order
        };
        this.setLayoutItemParams(entity, params);
    };
    /**
     * 创建绝对布局项
     * @param entity 实体
     * @param left 左边距
     * @param top 上边距
     * @param right 右边距
     * @param bottom 下边距
     * @param zIndex 层级
     */
    UILayoutSystem.prototype.createAbsoluteLayoutItem = function (entity, left, top, right, bottom, zIndex) {
        var params = {
            left: left,
            top: top,
            right: right,
            bottom: bottom,
            zIndex: zIndex
        };
        this.setLayoutItemParams(entity, params);
    };
    /**
     * 创建相对布局项
     * @param entity 实体
     * @param margin 外边距
     */
    UILayoutSystem.prototype.createRelativeLayoutItem = function (entity, margin) {
        var params = {
            margin: margin
        };
        this.setLayoutItemParams(entity, params);
    };
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为使用Date.now()来计算时间间隔
     */
    UILayoutSystem.prototype.update = function (_deltaTime) {
        // 如果启用自动应用布局
        if (this.config.autoApplyLayout) {
            var currentTime = Date.now();
            // 检查是否达到应用间隔
            if (currentTime - this.lastApplyTime >= this.config.autoApplyInterval) {
                this.applyAllLayouts();
                this.lastApplyTime = currentTime;
            }
        }
    };
    /**
     * 销毁系统
     */
    UILayoutSystem.prototype.dispose = function () {
        // 清空布局组件列表
        this.layoutComponents.clear();
    };
    return UILayoutSystem;
}(System_1.System));
exports.UILayoutSystem = UILayoutSystem;
