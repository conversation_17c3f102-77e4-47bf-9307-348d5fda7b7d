"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyboardDevice = void 0;
/**
 * 键盘输入设备
 */
var InputDevice_1 = require("../InputDevice");
var InputSystem_1 = require("../InputSystem");
/**
 * 键盘输入设备
 */
var KeyboardDevice = /** @class */ (function (_super) {
    __extends(KeyboardDevice, _super);
    /**
     * 创建键盘输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    function KeyboardDevice(element, preventDefault, stopPropagation) {
        if (element === void 0) { element = document.body; }
        if (preventDefault === void 0) { preventDefault = true; }
        if (stopPropagation === void 0) { stopPropagation = false; }
        var _this = _super.call(this, 'keyboard') || this;
        /** 键盘事件处理器 */
        _this.keyboardEventHandlers = {};
        _this.element = element;
        _this.preventDefault = preventDefault;
        _this.stopPropagation = stopPropagation;
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    KeyboardDevice.prototype.initEventHandlers = function () {
        // 键盘按下事件
        this.keyboardEventHandlers.keydown = this.handleKeyDown.bind(this);
        // 键盘释放事件
        this.keyboardEventHandlers.keyup = this.handleKeyUp.bind(this);
    };
    /**
     * 初始化设备
     */
    KeyboardDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    KeyboardDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        _super.prototype.destroy.call(this);
    };
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    KeyboardDevice.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
        // 更新按键状态
        for (var _i = 0, _a = this.values.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], value = _b[1];
            if (value === InputSystem_1.KeyState.DOWN) {
                // 将按下状态更新为按住状态
                this.setValue(key, InputSystem_1.KeyState.PRESSED);
            }
            else if (value === InputSystem_1.KeyState.UP) {
                // 将释放状态更新为未按下状态
                this.setValue(key, InputSystem_1.KeyState.NONE);
            }
        }
        _super.prototype.update.call(this, deltaTime);
    };
    /**
     * 添加事件监听器
     */
    KeyboardDevice.prototype.addEventListeners = function () {
        // 添加键盘事件监听器
        for (var _i = 0, _a = Object.entries(this.keyboardEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            document.addEventListener(event_1, handler, { passive: !this.preventDefault });
        }
    };
    /**
     * 移除事件监听器
     */
    KeyboardDevice.prototype.removeEventListeners = function () {
        // 移除键盘事件监听器
        for (var _i = 0, _a = Object.entries(this.keyboardEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_2 = _b[0], handler = _b[1];
            document.removeEventListener(event_2, handler);
        }
    };
    /**
     * 处理键盘按键按下事件
     * @param event 键盘事件
     */
    KeyboardDevice.prototype.handleKeyDown = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        var key = event.code || event.key;
        // 如果按键未按下或已释放，则设置为按下状态
        if (this.getValue(key) !== InputSystem_1.KeyState.PRESSED) {
            this.setValue(key, InputSystem_1.KeyState.DOWN);
            // 触发按键按下事件
            this.eventEmitter.emit("".concat(key, ":down"), {
                key: key,
                code: event.code,
                altKey: event.altKey,
                ctrlKey: event.ctrlKey,
                shiftKey: event.shiftKey,
                metaKey: event.metaKey,
                repeat: event.repeat
            });
        }
    };
    /**
     * 处理键盘按键释放事件
     * @param event 键盘事件
     */
    KeyboardDevice.prototype.handleKeyUp = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        var key = event.code || event.key;
        // 设置按键为释放状态
        this.setValue(key, InputSystem_1.KeyState.UP);
        // 触发按键释放事件
        this.eventEmitter.emit("".concat(key, ":up"), {
            key: key,
            code: event.code,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 检查按键是否按下
     * @param key 按键
     * @returns 是否按下
     */
    KeyboardDevice.prototype.isKeyDown = function (key) {
        var state = this.getValue(key);
        return state === InputSystem_1.KeyState.DOWN || state === InputSystem_1.KeyState.PRESSED;
    };
    /**
     * 检查按键是否刚按下
     * @param key 按键
     * @returns 是否刚按下
     */
    KeyboardDevice.prototype.isKeyJustDown = function (key) {
        return this.getValue(key) === InputSystem_1.KeyState.DOWN;
    };
    /**
     * 检查按键是否刚释放
     * @param key 按键
     * @returns 是否刚释放
     */
    KeyboardDevice.prototype.isKeyJustUp = function (key) {
        return this.getValue(key) === InputSystem_1.KeyState.UP;
    };
    return KeyboardDevice;
}(InputDevice_1.BaseInputDevice));
exports.KeyboardDevice = KeyboardDevice;
