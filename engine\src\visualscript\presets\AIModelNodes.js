"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAIModelNodes = exports.EmotionAnalysisNode = exports.ImageGenerationNode = exports.TextGenerationNode = exports.LoadAIModelNode = void 0;
var AsyncNode_1 = require("../nodes/AsyncNode");
var Node_1 = require("../nodes/Node");
var AIModelManager_1 = require("../../ai/AIModelManager");
var AIModelType_1 = require("../../ai/AIModelType");
/**
 * 加载AI模型节点
 * 加载指定类型的AI模型
 */
var LoadAIModelNode = /** @class */ (function (_super) {
    __extends(LoadAIModelNode, _super);
    function LoadAIModelNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 请求ID */
        _this.requestId = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    LoadAIModelNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'modelType',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '模型类型',
            defaultValue: AIModelType_1.AIModelType.GPT
        });
        this.addInput({
            name: 'config',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '模型配置',
            defaultValue: {}
        });
        this.addInput({
            name: 'options',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: '加载选项',
            defaultValue: {}
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '加载成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '加载失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'model',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '加载的模型'
        });
        this.addOutput({
            name: 'progress',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '加载进度 (0-1)'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    LoadAIModelNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var modelType, config, options, aiModelManager, loadOptions, model, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        modelType = this.getInputValue('modelType');
                        config = this.getInputValue('config');
                        options = this.getInputValue('options');
                        aiModelManager = this.graph.getWorld().getSystem(AIModelManager_1.AIModelManager);
                        if (!aiModelManager) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        loadOptions = __assign(__assign({}, options), { onProgress: function (progress) {
                                _this.setOutputValue('progress', progress);
                            } });
                        return [4 /*yield*/, aiModelManager.loadModel(modelType, config, loadOptions)];
                    case 2:
                        model = _a.sent();
                        if (model) {
                            // 设置输出值
                            this.setOutputValue('model', model);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('加载AI模型失败:', error_1);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return LoadAIModelNode;
}(AsyncNode_1.AsyncNode));
exports.LoadAIModelNode = LoadAIModelNode;
/**
 * 文本生成节点
 * 使用AI模型生成文本
 */
var TextGenerationNode = /** @class */ (function (_super) {
    __extends(TextGenerationNode, _super);
    function TextGenerationNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    TextGenerationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'model',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: 'AI模型'
        });
        this.addInput({
            name: 'prompt',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '提示文本',
            defaultValue: '你好，请生成一段文本。'
        });
        this.addInput({
            name: 'maxTokens',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '最大令牌数',
            defaultValue: 100
        });
        this.addInput({
            name: 'temperature',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '温度 (0-1)',
            defaultValue: 0.7
        });
        this.addInput({
            name: 'stream',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否使用流式响应',
            defaultValue: false
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成失败'
        });
        this.addOutput({
            name: 'stream',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '流式响应'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成的文本'
        });
        this.addOutput({
            name: 'streamText',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '流式响应文本'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    TextGenerationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var model, prompt, maxTokens, temperature, stream, options, result, error_2;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        model = this.getInputValue('model');
                        prompt = this.getInputValue('prompt');
                        maxTokens = this.getInputValue('maxTokens');
                        temperature = this.getInputValue('temperature');
                        stream = this.getInputValue('stream');
                        // 检查输入值是否有效
                        if (!model || !prompt) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        options = {
                            maxTokens: maxTokens,
                            temperature: temperature,
                            stream: stream
                        };
                        // 如果使用流式响应，添加回调
                        if (stream) {
                            options.onStream = function (text) {
                                _this.setOutputValue('streamText', text);
                                _this.triggerFlow('stream');
                            };
                        }
                        return [4 /*yield*/, model.generateText(prompt, options)];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('text', result);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('生成文本失败:', error_2);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return TextGenerationNode;
}(AsyncNode_1.AsyncNode));
exports.TextGenerationNode = TextGenerationNode;
/**
 * 图像生成节点
 * 使用AI模型生成图像
 */
var ImageGenerationNode = /** @class */ (function (_super) {
    __extends(ImageGenerationNode, _super);
    function ImageGenerationNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    ImageGenerationNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'model',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: 'AI模型'
        });
        this.addInput({
            name: 'prompt',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '提示文本'
        });
        this.addInput({
            name: 'width',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '图像宽度',
            defaultValue: 512
        });
        this.addInput({
            name: 'height',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '图像高度',
            defaultValue: 512
        });
        this.addInput({
            name: 'steps',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '生成步数',
            defaultValue: 30
        });
        this.addInput({
            name: 'guidanceScale',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '引导比例',
            defaultValue: 7.5
        });
        this.addInput({
            name: 'negativePrompt',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '负面提示',
            defaultValue: ''
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成失败'
        });
        this.addOutput({
            name: 'progress',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成进度'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'image',
            type: Node_1.SocketType.DATA,
            dataType: 'blob',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成的图像'
        });
        this.addOutput({
            name: 'progressValue',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '生成进度 (0-1)'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    ImageGenerationNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var model, prompt, width, height, steps, guidanceScale, negativePrompt, options, result, error_3;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        model = this.getInputValue('model');
                        prompt = this.getInputValue('prompt');
                        width = this.getInputValue('width');
                        height = this.getInputValue('height');
                        steps = this.getInputValue('steps');
                        guidanceScale = this.getInputValue('guidanceScale');
                        negativePrompt = this.getInputValue('negativePrompt');
                        // 检查输入值是否有效
                        if (!model || !prompt) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        // 检查模型是否支持图像生成
                        if (!model.generateImage) {
                            console.error('模型不支持图像生成');
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        options = {
                            width: width,
                            height: height,
                            steps: steps,
                            guidanceScale: guidanceScale,
                            negativePrompt: negativePrompt,
                            onProgress: function (progress) {
                                _this.setOutputValue('progressValue', progress);
                                _this.triggerFlow('progress');
                            }
                        };
                        return [4 /*yield*/, model.generateImage(prompt, options)];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('image', result);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_3 = _a.sent();
                        console.error('生成图像失败:', error_3);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return ImageGenerationNode;
}(AsyncNode_1.AsyncNode));
exports.ImageGenerationNode = ImageGenerationNode;
/**
 * 情感分析节点
 * 分析文本情感
 */
var EmotionAnalysisNode = /** @class */ (function (_super) {
    __extends(EmotionAnalysisNode, _super);
    function EmotionAnalysisNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    EmotionAnalysisNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'model',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.INPUT,
            description: 'AI模型'
        });
        this.addInput({
            name: 'text',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.INPUT,
            description: '要分析的文本'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分析成功'
        });
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '分析失败'
        });
        // 添加输出数据插槽
        this.addOutput({
            name: 'primaryEmotion',
            type: Node_1.SocketType.DATA,
            dataType: 'string',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '主要情感'
        });
        this.addOutput({
            name: 'intensity',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '情感强度 (0-1)'
        });
        this.addOutput({
            name: 'scores',
            type: Node_1.SocketType.DATA,
            dataType: 'object',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '情感分数映射'
        });
        this.addOutput({
            name: 'confidence',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '置信度 (0-1)'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    EmotionAnalysisNode.prototype.execute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var model, text, result, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        model = this.getInputValue('model');
                        text = this.getInputValue('text');
                        // 检查输入值是否有效
                        if (!model || !text) {
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        // 检查模型是否支持情感分析
                        if (!model.analyzeEmotion) {
                            console.error('模型不支持情感分析');
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, model.analyzeEmotion(text)];
                    case 2:
                        result = _a.sent();
                        if (result) {
                            // 设置输出值
                            this.setOutputValue('primaryEmotion', result.primaryEmotion);
                            this.setOutputValue('intensity', result.intensity);
                            this.setOutputValue('scores', result.scores);
                            this.setOutputValue('confidence', result.confidence);
                            // 触发成功流程
                            this.triggerFlow('success');
                            return [2 /*return*/, true];
                        }
                        else {
                            // 触发失败流程
                            this.triggerFlow('fail');
                            return [2 /*return*/, false];
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        console.error('分析情感失败:', error_4);
                        // 触发失败流程
                        this.triggerFlow('fail');
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return EmotionAnalysisNode;
}(AsyncNode_1.AsyncNode));
exports.EmotionAnalysisNode = EmotionAnalysisNode;
/**
 * 注册AI模型节点
 * @param registry 节点注册表
 */
function registerAIModelNodes(registry) {
    // 注册加载AI模型节点
    registry.registerNodeType({
        type: 'ai/model/load',
        category: Node_1.NodeCategory.AI,
        constructor: LoadAIModelNode,
        label: '加载AI模型',
        description: '加载指定类型的AI模型',
        icon: 'model',
        color: '#673AB7',
        tags: ['ai', 'model', 'load']
    });
    // 注册文本生成节点
    registry.registerNodeType({
        type: 'ai/model/generateText',
        category: Node_1.NodeCategory.AI,
        constructor: TextGenerationNode,
        label: '生成文本',
        description: '使用AI模型生成文本',
        icon: 'text',
        color: '#673AB7',
        tags: ['ai', 'model', 'text', 'generate']
    });
    // 注册图像生成节点
    registry.registerNodeType({
        type: 'ai/model/generateImage',
        category: Node_1.NodeCategory.AI,
        constructor: ImageGenerationNode,
        label: '生成图像',
        description: '使用AI模型生成图像',
        icon: 'image',
        color: '#673AB7',
        tags: ['ai', 'model', 'image', 'generate']
    });
    // 注册情感分析节点
    registry.registerNodeType({
        type: 'ai/emotion/analyze',
        category: Node_1.NodeCategory.AI,
        constructor: EmotionAnalysisNode,
        label: '情感分析',
        description: '分析文本情感',
        icon: 'emotion',
        color: '#673AB7',
        tags: ['ai', 'emotion', 'analyze']
    });
}
exports.registerAIModelNodes = registerAIModelNodes;
