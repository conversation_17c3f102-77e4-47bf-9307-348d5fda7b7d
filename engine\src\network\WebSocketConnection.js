"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketConnection = exports.WebSocketConnectionState = void 0;
/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var MessageSerializer_1 = require("./MessageSerializer");
/**
 * WebSocket连接状态
 */
var WebSocketConnectionState;
(function (WebSocketConnectionState) {
    /** 已断开连接 */
    WebSocketConnectionState["DISCONNECTED"] = "disconnected";
    /** 正在连接 */
    WebSocketConnectionState["CONNECTING"] = "connecting";
    /** 已连接 */
    WebSocketConnectionState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    WebSocketConnectionState["DISCONNECTING"] = "disconnecting";
    /** 连接错误 */
    WebSocketConnectionState["ERROR"] = "error";
})(WebSocketConnectionState || (exports.WebSocketConnectionState = WebSocketConnectionState = {}));
/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
var WebSocketConnection = /** @class */ (function (_super) {
    __extends(WebSocketConnection, _super);
    /**
     * 创建WebSocket连接
     * @param url 服务器URL
     * @param useCompression 是否使用压缩
     */
    function WebSocketConnection(url, useCompression) {
        if (useCompression === void 0) { useCompression = true; }
        var _this = _super.call(this) || this;
        /** WebSocket实例 */
        _this.socket = null;
        /** 连接状态 */
        _this.state = WebSocketConnectionState.DISCONNECTED;
        /** 重连尝试次数 */
        _this.reconnectAttempts = 0;
        /** 最大重连尝试次数 */
        _this.maxReconnectAttempts = 5;
        /** 重连间隔（毫秒） */
        _this.reconnectInterval = 3000;
        /** 重连定时器ID */
        _this.reconnectTimerId = null;
        /** 心跳间隔（毫秒） */
        _this.heartbeatInterval = 30000;
        /** 心跳定时器ID */
        _this.heartbeatTimerId = null;
        /** 最后一次接收消息的时间戳 */
        _this.lastReceivedTime = 0;
        /** 消息队列 */
        _this.messageQueue = [];
        /** 是否启用消息队列 */
        _this.useMessageQueue = true;
        /** 是否正在处理消息队列 */
        _this.processingQueue = false;
        _this.url = url;
        _this.messageSerializer = new MessageSerializer_1.MessageSerializer(useCompression);
        return _this;
    }
    /**
     * 连接到服务器
     * @returns Promise
     */
    WebSocketConnection.prototype.connect = function () {
        var _this = this;
        return new Promise(function (resolve, reject) {
            if (_this.state === WebSocketConnectionState.CONNECTED) {
                resolve();
                return;
            }
            if (_this.state === WebSocketConnectionState.CONNECTING) {
                reject(new Error('Already connecting to server'));
                return;
            }
            _this.state = WebSocketConnectionState.CONNECTING;
            try {
                _this.socket = new WebSocket(_this.url);
                // 设置二进制类型为ArrayBuffer
                _this.socket.binaryType = 'arraybuffer';
                // 设置事件监听器
                _this.socket.onopen = function () {
                    _this.state = WebSocketConnectionState.CONNECTED;
                    _this.reconnectAttempts = 0;
                    _this.lastReceivedTime = Date.now();
                    // 启动心跳
                    _this.startHeartbeat();
                    // 处理消息队列
                    _this.processMessageQueue();
                    _this.emit('connected');
                    resolve();
                };
                _this.socket.onclose = function (event) {
                    var wasConnected = _this.state === WebSocketConnectionState.CONNECTED;
                    _this.state = WebSocketConnectionState.DISCONNECTED;
                    // 停止心跳
                    _this.stopHeartbeat();
                    if (wasConnected) {
                        _this.emit('disconnected', event.code, event.reason);
                        // 尝试重连
                        _this.attemptReconnect();
                    }
                    else if (_this.state === WebSocketConnectionState.CONNECTING) {
                        reject(new Error("Failed to connect to server: ".concat(event.code, " ").concat(event.reason)));
                        // 尝试重连
                        _this.attemptReconnect();
                    }
                };
                _this.socket.onerror = function (event) {
                    _this.state = WebSocketConnectionState.ERROR;
                    var error = new Error('WebSocket error');
                    _this.emit('error', error);
                    if (_this.state === WebSocketConnectionState.CONNECTING) {
                        reject(error);
                    }
                };
                _this.socket.onmessage = function (event) {
                    _this.lastReceivedTime = Date.now();
                    try {
                        // 反序列化消息
                        var message = _this.messageSerializer.deserialize(event.data);
                        // 如果是心跳消息，则不触发消息事件
                        if (message.type === 'heartbeat') {
                            return;
                        }
                        _this.emit('message', message);
                    }
                    catch (error) {
                        Debug_1.Debug.error('WebSocketConnection', 'Failed to deserialize message:', error);
                        _this.emit('error', error);
                    }
                };
            }
            catch (error) {
                _this.state = WebSocketConnectionState.ERROR;
                _this.emit('error', error);
                reject(error);
            }
        });
    };
    /**
     * 断开连接
     * @returns Promise
     */
    WebSocketConnection.prototype.disconnect = function () {
        var _this = this;
        return new Promise(function (resolve) {
            if (_this.state === WebSocketConnectionState.DISCONNECTED) {
                resolve();
                return;
            }
            _this.state = WebSocketConnectionState.DISCONNECTING;
            // 停止心跳
            _this.stopHeartbeat();
            // 停止重连
            _this.stopReconnect();
            if (_this.socket) {
                // 设置关闭事件监听器
                var onClose_1 = function () {
                    _this.socket.removeEventListener('close', onClose_1);
                    _this.state = WebSocketConnectionState.DISCONNECTED;
                    _this.emit('disconnected', 1000, 'Normal closure');
                    resolve();
                };
                _this.socket.addEventListener('close', onClose_1);
                // 关闭WebSocket连接
                _this.socket.close(1000, 'Normal closure');
            }
            else {
                _this.state = WebSocketConnectionState.DISCONNECTED;
                resolve();
            }
        });
    };
    /**
     * 发送消息
     * @param type 消息类型
     * @param message 消息对象
     */
    WebSocketConnection.prototype.send = function (type, message) {
        if (this.state !== WebSocketConnectionState.CONNECTED) {
            if (this.useMessageQueue) {
                // 添加到消息队列
                this.messageQueue.push(message);
            }
            return;
        }
        try {
            // 序列化消息
            var data = this.messageSerializer.serialize(message);
            // 发送消息
            this.socket.send(data);
        }
        catch (error) {
            Debug_1.Debug.error('WebSocketConnection', 'Failed to send message:', error);
            this.emit('error', error);
            if (this.useMessageQueue) {
                // 添加到消息队列
                this.messageQueue.push(message);
            }
        }
    };
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    WebSocketConnection.prototype.getState = function () {
        return this.state;
    };
    /**
     * 是否已连接
     * @returns 是否已连接
     */
    WebSocketConnection.prototype.isConnected = function () {
        return this.state === WebSocketConnectionState.CONNECTED;
    };
    /**
     * 设置重连参数
     * @param maxAttempts 最大重连尝试次数
     * @param interval 重连间隔（毫秒）
     */
    WebSocketConnection.prototype.setReconnectParams = function (maxAttempts, interval) {
        this.maxReconnectAttempts = maxAttempts;
        this.reconnectInterval = interval;
    };
    /**
     * 设置心跳间隔
     * @param interval 心跳间隔（毫秒）
     */
    WebSocketConnection.prototype.setHeartbeatInterval = function (interval) {
        this.heartbeatInterval = interval;
        // 如果已连接，则重新启动心跳
        if (this.state === WebSocketConnectionState.CONNECTED) {
            this.stopHeartbeat();
            this.startHeartbeat();
        }
    };
    /**
     * 设置是否使用消息队列
     * @param useQueue 是否使用消息队列
     */
    WebSocketConnection.prototype.setUseMessageQueue = function (useQueue) {
        this.useMessageQueue = useQueue;
    };
    /**
     * 清空消息队列
     */
    WebSocketConnection.prototype.clearMessageQueue = function () {
        this.messageQueue = [];
    };
    /**
     * 尝试重连
     */
    WebSocketConnection.prototype.attemptReconnect = function () {
        var _this = this;
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            Debug_1.Debug.error('WebSocketConnection', 'Max reconnect attempts reached');
            return;
        }
        this.reconnectAttempts++;
        Debug_1.Debug.log('WebSocketConnection', "Attempting to reconnect (".concat(this.reconnectAttempts, "/").concat(this.maxReconnectAttempts, ")"));
        // 设置重连定时器
        this.reconnectTimerId = window.setTimeout(function () {
            _this.connect().catch(function (error) {
                Debug_1.Debug.error('WebSocketConnection', 'Reconnect failed:', error);
            });
        }, this.reconnectInterval);
    };
    /**
     * 停止重连
     */
    WebSocketConnection.prototype.stopReconnect = function () {
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
    };
    /**
     * 启动心跳
     */
    WebSocketConnection.prototype.startHeartbeat = function () {
        var _this = this;
        if (this.heartbeatTimerId !== null) {
            return;
        }
        this.heartbeatTimerId = window.setInterval(function () {
            _this.sendHeartbeat();
        }, this.heartbeatInterval);
    };
    /**
     * 停止心跳
     */
    WebSocketConnection.prototype.stopHeartbeat = function () {
        if (this.heartbeatTimerId !== null) {
            clearInterval(this.heartbeatTimerId);
            this.heartbeatTimerId = null;
        }
    };
    /**
     * 发送心跳消息
     */
    WebSocketConnection.prototype.sendHeartbeat = function () {
        if (this.state !== WebSocketConnectionState.CONNECTED) {
            return;
        }
        var message = {
            type: 'heartbeat',
            data: {
                timestamp: Date.now(),
            },
            timestamp: Date.now(),
        };
        try {
            // 序列化消息
            var data = this.messageSerializer.serialize(message);
            // 发送消息
            this.socket.send(data);
        }
        catch (error) {
            Debug_1.Debug.error('WebSocketConnection', 'Failed to send heartbeat:', error);
        }
    };
    /**
     * 处理消息队列
     */
    WebSocketConnection.prototype.processMessageQueue = function () {
        if (!this.useMessageQueue || this.processingQueue || this.messageQueue.length === 0) {
            return;
        }
        this.processingQueue = true;
        // 复制消息队列
        var queue = __spreadArray([], this.messageQueue, true);
        this.messageQueue = [];
        // 发送队列中的消息
        for (var _i = 0, queue_1 = queue; _i < queue_1.length; _i++) {
            var message = queue_1[_i];
            this.send(message.type, message);
        }
        this.processingQueue = false;
    };
    return WebSocketConnection;
}(EventEmitter_1.EventEmitter));
exports.WebSocketConnection = WebSocketConnection;
