"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputSystem = exports.InputEventType = exports.MouseButton = exports.MouseButtonState = exports.KeyState = void 0;
/**
 * 输入系统
 * 用于处理键盘、鼠标和触摸输入
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var System_1 = require("../core/System");
/**
 * 键盘按键状态
 */
var KeyState;
(function (KeyState) {
    /** 按下 */
    KeyState["DOWN"] = "down";
    /** 按住 */
    KeyState["PRESSED"] = "pressed";
    /** 释放 */
    KeyState["UP"] = "up";
    /** 未按下 */
    KeyState["NONE"] = "none";
})(KeyState || (exports.KeyState = KeyState = {}));
/**
 * 鼠标按钮状态
 */
var MouseButtonState;
(function (MouseButtonState) {
    /** 按下 */
    MouseButtonState["DOWN"] = "down";
    /** 按住 */
    MouseButtonState["PRESSED"] = "pressed";
    /** 释放 */
    MouseButtonState["UP"] = "up";
    /** 未按下 */
    MouseButtonState["NONE"] = "none";
})(MouseButtonState || (exports.MouseButtonState = MouseButtonState = {}));
/**
 * 鼠标按钮
 */
var MouseButton;
(function (MouseButton) {
    /** 左键 */
    MouseButton[MouseButton["LEFT"] = 0] = "LEFT";
    /** 中键 */
    MouseButton[MouseButton["MIDDLE"] = 1] = "MIDDLE";
    /** 右键 */
    MouseButton[MouseButton["RIGHT"] = 2] = "RIGHT";
})(MouseButton || (exports.MouseButton = MouseButton = {}));
/**
 * 输入事件类型
 */
var InputEventType;
(function (InputEventType) {
    /** 键盘按键按下 */
    InputEventType["KEY_DOWN"] = "keyDown";
    /** 键盘按键释放 */
    InputEventType["KEY_UP"] = "keyUp";
    /** 鼠标按钮按下 */
    InputEventType["MOUSE_DOWN"] = "mouseDown";
    /** 鼠标按钮释放 */
    InputEventType["MOUSE_UP"] = "mouseUp";
    /** 鼠标移动 */
    InputEventType["MOUSE_MOVE"] = "mouseMove";
    /** 鼠标滚轮 */
    InputEventType["MOUSE_WHEEL"] = "mouseWheel";
    /** 触摸开始 */
    InputEventType["TOUCH_START"] = "touchStart";
    /** 触摸移动 */
    InputEventType["TOUCH_MOVE"] = "touchMove";
    /** 触摸结束 */
    InputEventType["TOUCH_END"] = "touchEnd";
    /** 触摸取消 */
    InputEventType["TOUCH_CANCEL"] = "touchCancel";
    /** 游戏手柄连接 */
    InputEventType["GAMEPAD_CONNECTED"] = "gamepadConnected";
    /** 游戏手柄断开 */
    InputEventType["GAMEPAD_DISCONNECTED"] = "gamepadDisconnected";
    /** 游戏手柄按钮按下 */
    InputEventType["GAMEPAD_BUTTON_DOWN"] = "gamepadButtonDown";
    /** 游戏手柄按钮释放 */
    InputEventType["GAMEPAD_BUTTON_UP"] = "gamepadButtonUp";
    /** 游戏手柄摇杆移动 */
    InputEventType["GAMEPAD_AXIS_MOVE"] = "gamepadAxisMove";
})(InputEventType || (exports.InputEventType = InputEventType = {}));
/**
 * 输入系统
 */
var InputSystem = exports.InputSystem = /** @class */ (function (_super) {
    __extends(InputSystem, _super);
    /**
     * 创建输入系统
     * @param options 输入系统选项
     */
    function InputSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = 
        // 将系统名称转换为数字优先级，这里使用0作为默认优先级
        _super.call(this, 0) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 按键状态映射 */
        _this.keyStates = new Map();
        /** 鼠标按钮状态映射 */
        _this.mouseButtonStates = new Map();
        /** 鼠标位置 */
        _this.mousePosition = { x: 0, y: 0 };
        /** 鼠标相对位置（相对于上一帧） */
        _this.mouseMovement = { x: 0, y: 0 };
        /** 鼠标滚轮增量 */
        _this.mouseWheelDelta = 0;
        /** 触摸点映射 */
        _this.touchPoints = new Map();
        /** 游戏手柄映射 */
        _this.gamepads = new Map();
        /** 游戏手柄按钮状态映射 */
        _this.gamepadButtonStates = new Map();
        /** 游戏手柄摇杆值映射 */
        _this.gamepadAxisValues = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        /** 是否支持当前环境 */
        _this.supported = true;
        /** 是否处于指针锁定状态 */
        _this.pointerLocked = false;
        /** 键盘事件处理器 */
        _this.keyboardEventHandlers = {};
        /** 鼠标事件处理器 */
        _this.mouseEventHandlers = {};
        /** 触摸事件处理器 */
        _this.touchEventHandlers = {};
        /** 游戏手柄事件处理器 */
        _this.gamepadEventHandlers = {};
        /** 指针锁定事件处理器 */
        _this.pointerLockEventHandlers = {};
        _this.element = options.element || document.body;
        _this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
        _this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;
        _this.enableKeyboard = options.enableKeyboard !== undefined ? options.enableKeyboard : true;
        _this.enableMouse = options.enableMouse !== undefined ? options.enableMouse : true;
        _this.enableTouch = options.enableTouch !== undefined ? options.enableTouch : true;
        _this.enableGamepad = options.enableGamepad !== undefined ? options.enableGamepad : true;
        _this.enablePointerLock = options.enablePointerLock !== undefined ? options.enablePointerLock : false;
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    InputSystem.prototype.initEventHandlers = function () {
        // 键盘事件处理器
        this.keyboardEventHandlers = {
            keydown: this.handleKeyDown.bind(this),
            keyup: this.handleKeyUp.bind(this)
        };
        // 鼠标事件处理器
        this.mouseEventHandlers = {
            mousedown: this.handleMouseDown.bind(this),
            mouseup: this.handleMouseUp.bind(this),
            mousemove: this.handleMouseMove.bind(this),
            contextmenu: this.handleContextMenu.bind(this)
        };
        // 滚轮事件处理器（单独处理，因为类型不同）
        this.mouseEventHandlers['wheel'] = this.handleMouseWheel.bind(this);
        // 触摸事件处理器
        this.touchEventHandlers = {
            touchstart: this.handleTouchStart.bind(this),
            touchmove: this.handleTouchMove.bind(this),
            touchend: this.handleTouchEnd.bind(this),
            touchcancel: this.handleTouchCancel.bind(this)
        };
        // 游戏手柄事件处理器
        this.gamepadEventHandlers = {
            gamepadconnected: this.handleGamepadConnected.bind(this),
            gamepaddisconnected: this.handleGamepadDisconnected.bind(this)
        };
        // 指针锁定事件处理器
        this.pointerLockEventHandlers = {
            pointerlockchange: this.handlePointerLockChange.bind(this),
            pointerlockerror: this.handlePointerLockError.bind(this)
        };
    };
    /**
     * 初始化系统
     */
    InputSystem.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        this.initialized = true;
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    InputSystem.prototype.update = function (deltaTime) {
        if (!this.initialized || !this.isEnabled() || !this.supported)
            return;
        // deltaTime 参数在此方法中未使用，但保留以符合 System 基类的接口
        // 更新游戏手柄状态
        if (this.enableGamepad) {
            this.updateGamepads();
        }
        // 处理按键状态更新
        for (var _i = 0, _a = this.keyStates.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], state = _b[1];
            if (state === KeyState.DOWN) {
                // 将按下状态更新为按住状态
                this.keyStates.set(key, KeyState.PRESSED);
            }
            else if (state === KeyState.UP) {
                // 将释放状态更新为未按下状态
                this.keyStates.set(key, KeyState.NONE);
            }
        }
        // 处理鼠标按钮状态更新
        for (var _c = 0, _d = this.mouseButtonStates.entries(); _c < _d.length; _c++) {
            var _e = _d[_c], button = _e[0], state = _e[1];
            if (state === MouseButtonState.DOWN) {
                // 将按下状态更新为按住状态
                this.mouseButtonStates.set(button, MouseButtonState.PRESSED);
            }
            else if (state === MouseButtonState.UP) {
                // 将释放状态更新为未按下状态
                this.mouseButtonStates.set(button, MouseButtonState.NONE);
            }
        }
        // 重置鼠标移动增量和滚轮增量
        this.mouseMovement.x = 0;
        this.mouseMovement.y = 0;
        this.mouseWheelDelta = 0;
    };
    /**
     * 添加事件监听器
     */
    InputSystem.prototype.addEventListeners = function () {
        // 添加键盘事件监听器
        if (this.enableKeyboard) {
            for (var _i = 0, _a = Object.entries(this.keyboardEventHandlers); _i < _a.length; _i++) {
                var _b = _a[_i], event_1 = _b[0], handler = _b[1];
                document.addEventListener(event_1, handler, { passive: !this.preventDefault });
            }
        }
        // 添加鼠标事件监听器
        if (this.enableMouse) {
            for (var _c = 0, _d = Object.entries(this.mouseEventHandlers); _c < _d.length; _c++) {
                var _e = _d[_c], event_2 = _e[0], handler = _e[1];
                this.element.addEventListener(event_2, handler, { passive: !this.preventDefault });
            }
        }
        // 添加触摸事件监听器
        if (this.enableTouch) {
            for (var _f = 0, _g = Object.entries(this.touchEventHandlers); _f < _g.length; _f++) {
                var _h = _g[_f], event_3 = _h[0], handler = _h[1];
                this.element.addEventListener(event_3, handler, { passive: !this.preventDefault });
            }
        }
        // 添加游戏手柄事件监听器
        if (this.enableGamepad) {
            for (var _j = 0, _k = Object.entries(this.gamepadEventHandlers); _j < _k.length; _j++) {
                var _l = _k[_j], event_4 = _l[0], handler = _l[1];
                window.addEventListener(event_4, handler);
            }
        }
        // 添加指针锁定事件监听器
        if (this.enablePointerLock) {
            for (var _m = 0, _o = Object.entries(this.pointerLockEventHandlers); _m < _o.length; _m++) {
                var _p = _o[_m], event_5 = _p[0], handler = _p[1];
                document.addEventListener(event_5, handler);
            }
        }
    };
    /**
     * 更新游戏手柄状态
     */
    InputSystem.prototype.updateGamepads = function () {
        // 获取所有游戏手柄
        var gamepads = navigator.getGamepads ? navigator.getGamepads() : [];
        // 更新游戏手柄状态
        for (var _i = 0, gamepads_1 = gamepads; _i < gamepads_1.length; _i++) {
            var gamepad = gamepads_1[_i];
            if (!gamepad)
                continue;
            // 更新游戏手柄映射
            this.gamepads.set(gamepad.index, gamepad);
            // 更新按钮状态
            for (var i = 0; i < gamepad.buttons.length; i++) {
                var button = gamepad.buttons[i];
                var buttonKey = "".concat(gamepad.index, ":").concat(i);
                var pressed = button.pressed || button.value > 0.5;
                var wasPressed = this.gamepadButtonStates.get(buttonKey) || false;
                // 更新按钮状态
                this.gamepadButtonStates.set(buttonKey, pressed);
                // 触发按钮事件
                if (pressed && !wasPressed) {
                    this.eventEmitter.emit(InputEventType.GAMEPAD_BUTTON_DOWN, {
                        gamepad: gamepad.index,
                        button: i,
                        value: button.value
                    });
                }
                else if (!pressed && wasPressed) {
                    this.eventEmitter.emit(InputEventType.GAMEPAD_BUTTON_UP, {
                        gamepad: gamepad.index,
                        button: i,
                        value: button.value
                    });
                }
            }
            // 更新摇杆状态
            for (var i = 0; i < gamepad.axes.length; i++) {
                var axis = gamepad.axes[i];
                var axisKey = "".concat(gamepad.index, ":").concat(i);
                var value = Math.abs(axis) < 0.1 ? 0 : axis; // 添加死区
                var oldValue = this.gamepadAxisValues.get(axisKey) || 0;
                // 如果值发生变化，则触发事件
                if (value !== oldValue) {
                    this.gamepadAxisValues.set(axisKey, value);
                    this.eventEmitter.emit(InputEventType.GAMEPAD_AXIS_MOVE, {
                        gamepad: gamepad.index,
                        axis: i,
                        value: value
                    });
                }
            }
        }
    };
    /**
     * 处理键盘按键按下事件
     * @param event 键盘事件
     */
    InputSystem.prototype.handleKeyDown = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        var key = event.code || event.key;
        // 如果按键未按下或已释放，则设置为按下状态
        if (this.keyStates.get(key) !== KeyState.PRESSED) {
            this.keyStates.set(key, KeyState.DOWN);
            // 触发按键按下事件
            this.eventEmitter.emit(InputEventType.KEY_DOWN, {
                key: key,
                code: event.code,
                altKey: event.altKey,
                ctrlKey: event.ctrlKey,
                shiftKey: event.shiftKey,
                metaKey: event.metaKey,
                repeat: event.repeat
            });
        }
    };
    /**
     * 处理键盘按键释放事件
     * @param event 键盘事件
     */
    InputSystem.prototype.handleKeyUp = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        var key = event.code || event.key;
        // 设置按键为释放状态
        this.keyStates.set(key, KeyState.UP);
        // 触发按键释放事件
        this.eventEmitter.emit(InputEventType.KEY_UP, {
            key: key,
            code: event.code,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标按钮按下事件
     * @param event 鼠标事件
     */
    InputSystem.prototype.handleMouseDown = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 设置鼠标按钮为按下状态
        this.mouseButtonStates.set(event.button, MouseButtonState.DOWN);
        // 触发鼠标按钮按下事件
        this.eventEmitter.emit(InputEventType.MOUSE_DOWN, {
            button: event.button,
            x: this.mousePosition.x,
            y: this.mousePosition.y,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
        // 如果启用了指针锁定，则请求锁定
        if (this.enablePointerLock && !this.pointerLocked) {
            this.requestPointerLock();
        }
    };
    /**
     * 处理鼠标按钮释放事件
     * @param event 鼠标事件
     */
    InputSystem.prototype.handleMouseUp = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 设置鼠标按钮为释放状态
        this.mouseButtonStates.set(event.button, MouseButtonState.UP);
        // 触发鼠标按钮释放事件
        this.eventEmitter.emit(InputEventType.MOUSE_UP, {
            button: event.button,
            x: this.mousePosition.x,
            y: this.mousePosition.y,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    InputSystem.prototype.handleMouseMove = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 计算鼠标移动增量
        var oldX = this.mousePosition.x;
        var oldY = this.mousePosition.y;
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 计算移动增量
        if (this.pointerLocked) {
            // 如果处于指针锁定状态，则使用movementX和movementY
            this.mouseMovement.x += event.movementX || 0;
            this.mouseMovement.y += event.movementY || 0;
        }
        else {
            // 否则，计算位置差异
            this.mouseMovement.x += this.mousePosition.x - oldX;
            this.mouseMovement.y += this.mousePosition.y - oldY;
        }
        // 触发鼠标移动事件
        this.eventEmitter.emit(InputEventType.MOUSE_MOVE, {
            x: this.mousePosition.x,
            y: this.mousePosition.y,
            movementX: event.movementX || this.mousePosition.x - oldX,
            movementY: event.movementY || this.mousePosition.y - oldY,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理鼠标滚轮事件
     * @param event 滚轮事件
     */
    InputSystem.prototype.handleMouseWheel = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新鼠标位置
        this.updateMousePosition(event);
        // 计算滚轮增量
        var delta = event.deltaY || event.detail || 0;
        this.mouseWheelDelta += delta;
        // 触发鼠标滚轮事件
        this.eventEmitter.emit(InputEventType.MOUSE_WHEEL, {
            x: this.mousePosition.x,
            y: this.mousePosition.y,
            deltaX: event.deltaX || 0,
            deltaY: delta,
            deltaZ: event.deltaZ || 0,
            deltaMode: event.deltaMode,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理上下文菜单事件（右键菜单）
     * @param event 鼠标事件
     */
    InputSystem.prototype.handleContextMenu = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
    };
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    InputSystem.prototype.handleTouchStart = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.set(touch.identifier, touch);
        }
        // 触发触摸开始事件
        this.eventEmitter.emit(InputEventType.TOUCH_START, {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    InputSystem.prototype.handleTouchMove = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 更新触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.set(touch.identifier, touch);
        }
        // 触发触摸移动事件
        this.eventEmitter.emit(InputEventType.TOUCH_MOVE, {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    InputSystem.prototype.handleTouchEnd = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 移除触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.delete(touch.identifier);
        }
        // 触发触摸结束事件
        this.eventEmitter.emit(InputEventType.TOUCH_END, {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    InputSystem.prototype.handleTouchCancel = function (event) {
        // 阻止默认行为和事件传播
        if (this.preventDefault)
            event.preventDefault();
        if (this.stopPropagation)
            event.stopPropagation();
        // 移除触摸点
        for (var i = 0; i < event.changedTouches.length; i++) {
            var touch = event.changedTouches[i];
            this.touchPoints.delete(touch.identifier);
        }
        // 触发触摸取消事件
        this.eventEmitter.emit(InputEventType.TOUCH_CANCEL, {
            touches: Array.from(event.touches),
            changedTouches: Array.from(event.changedTouches),
            targetTouches: Array.from(event.targetTouches),
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    };
    /**
     * 处理游戏手柄连接事件
     * @param event 游戏手柄事件
     */
    InputSystem.prototype.handleGamepadConnected = function (event) {
        var gamepad = event.gamepad;
        // 添加游戏手柄
        this.gamepads.set(gamepad.index, gamepad);
        // 触发游戏手柄连接事件
        this.eventEmitter.emit(InputEventType.GAMEPAD_CONNECTED, {
            gamepad: gamepad.index,
            id: gamepad.id,
            buttons: gamepad.buttons.length,
            axes: gamepad.axes.length
        });
    };
    /**
     * 处理游戏手柄断开事件
     * @param event 游戏手柄事件
     */
    InputSystem.prototype.handleGamepadDisconnected = function (event) {
        var gamepad = event.gamepad;
        // 移除游戏手柄
        this.gamepads.delete(gamepad.index);
        // 清除该游戏手柄的按钮和摇杆状态
        for (var _i = 0, _a = this.gamepadButtonStates.keys(); _i < _a.length; _i++) {
            var key = _a[_i];
            if (key.startsWith("".concat(gamepad.index, ":"))) {
                this.gamepadButtonStates.delete(key);
            }
        }
        for (var _b = 0, _c = this.gamepadAxisValues.keys(); _b < _c.length; _b++) {
            var key = _c[_b];
            if (key.startsWith("".concat(gamepad.index, ":"))) {
                this.gamepadAxisValues.delete(key);
            }
        }
        // 触发游戏手柄断开事件
        this.eventEmitter.emit(InputEventType.GAMEPAD_DISCONNECTED, {
            gamepad: gamepad.index,
            id: gamepad.id
        });
    };
    /**
     * 处理指针锁定变化事件
     */
    InputSystem.prototype.handlePointerLockChange = function () {
        this.pointerLocked = document.pointerLockElement === this.element;
    };
    /**
     * 处理指针锁定错误事件
     */
    InputSystem.prototype.handlePointerLockError = function () {
        this.pointerLocked = false;
        console.error('指针锁定失败');
    };
    /**
     * 更新鼠标位置
     * @param event 鼠标事件
     */
    InputSystem.prototype.updateMousePosition = function (event) {
        // 获取元素的边界矩形
        var rect = this.element.getBoundingClientRect();
        // 计算相对于元素的坐标
        this.mousePosition.x = event.clientX - rect.left;
        this.mousePosition.y = event.clientY - rect.top;
    };
    /**
     * 请求指针锁定
     */
    InputSystem.prototype.requestPointerLock = function () {
        if (this.element.requestPointerLock) {
            this.element.requestPointerLock();
        }
    };
    /**
     * 退出指针锁定
     */
    InputSystem.prototype.exitPointerLock = function () {
        if (document.exitPointerLock) {
            document.exitPointerLock();
        }
    };
    /**
     * 检查按键是否按下
     * @param key 按键
     * @returns 是否按下
     */
    InputSystem.prototype.isKeyDown = function (key) {
        return this.keyStates.get(key) === KeyState.DOWN;
    };
    /**
     * 检查按键是否按住
     * @param key 按键
     * @returns 是否按住
     */
    InputSystem.prototype.isKeyPressed = function (key) {
        var state = this.keyStates.get(key);
        return state === KeyState.DOWN || state === KeyState.PRESSED;
    };
    /**
     * 检查按键是否释放
     * @param key 按键
     * @returns 是否释放
     */
    InputSystem.prototype.isKeyUp = function (key) {
        return this.keyStates.get(key) === KeyState.UP;
    };
    /**
     * 检查鼠标按钮是否按下
     * @param button 鼠标按钮
     * @returns 是否按下
     */
    InputSystem.prototype.isMouseButtonDown = function (button) {
        return this.mouseButtonStates.get(button) === MouseButtonState.DOWN;
    };
    /**
     * 检查鼠标按钮是否按住
     * @param button 鼠标按钮
     * @returns 是否按住
     */
    InputSystem.prototype.isMouseButtonPressed = function (button) {
        var state = this.mouseButtonStates.get(button);
        return state === MouseButtonState.DOWN || state === MouseButtonState.PRESSED;
    };
    /**
     * 检查鼠标按钮是否释放
     * @param button 鼠标按钮
     * @returns 是否释放
     */
    InputSystem.prototype.isMouseButtonUp = function (button) {
        return this.mouseButtonStates.get(button) === MouseButtonState.UP;
    };
    /**
     * 获取鼠标位置
     * @returns 鼠标位置
     */
    InputSystem.prototype.getMousePosition = function () {
        return __assign({}, this.mousePosition);
    };
    /**
     * 获取鼠标移动增量
     * @returns 鼠标移动增量
     */
    InputSystem.prototype.getMouseMovement = function () {
        return __assign({}, this.mouseMovement);
    };
    /**
     * 获取鼠标滚轮增量
     * @returns 鼠标滚轮增量
     */
    InputSystem.prototype.getMouseWheelDelta = function () {
        return this.mouseWheelDelta;
    };
    /**
     * 获取触摸点
     * @param id 触摸点ID
     * @returns 触摸点
     */
    InputSystem.prototype.getTouchPoint = function (id) {
        return this.touchPoints.get(id);
    };
    /**
     * 获取所有触摸点
     * @returns 触摸点数组
     */
    InputSystem.prototype.getTouchPoints = function () {
        return Array.from(this.touchPoints.values());
    };
    /**
     * 获取游戏手柄
     * @param index 游戏手柄索引
     * @returns 游戏手柄
     */
    InputSystem.prototype.getGamepad = function (index) {
        return this.gamepads.get(index);
    };
    /**
     * 获取所有游戏手柄
     * @returns 游戏手柄数组
     */
    InputSystem.prototype.getGamepads = function () {
        return Array.from(this.gamepads.values());
    };
    /**
     * 检查游戏手柄按钮是否按下
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    InputSystem.prototype.isGamepadButtonPressed = function (gamepadIndex, buttonIndex) {
        return this.gamepadButtonStates.get("".concat(gamepadIndex, ":").concat(buttonIndex)) || false;
    };
    /**
     * 获取游戏手柄摇杆值
     * @param gamepadIndex 游戏手柄索引
     * @param axisIndex 摇杆索引
     * @returns 摇杆值（-1到1）
     */
    InputSystem.prototype.getGamepadAxisValue = function (gamepadIndex, axisIndex) {
        return this.gamepadAxisValues.get("".concat(gamepadIndex, ":").concat(axisIndex)) || 0;
    };
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns 当前实例，用于链式调用
     */
    InputSystem.prototype.on = function (event, callback) {
        // 使用私有的事件发射器处理事件
        this.eventEmitter.on(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param callback 监听器函数（可选）
     * @returns 当前实例，用于链式调用
     */
    InputSystem.prototype.off = function (event, callback) {
        // 使用私有的事件发射器处理事件
        this.eventEmitter.off(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     */
    InputSystem.prototype.removeEventListeners = function () {
        // 移除键盘事件监听器
        if (this.enableKeyboard) {
            for (var _i = 0, _a = Object.entries(this.keyboardEventHandlers); _i < _a.length; _i++) {
                var _b = _a[_i], event_6 = _b[0], handler = _b[1];
                document.removeEventListener(event_6, handler);
            }
        }
        // 移除鼠标事件监听器
        if (this.enableMouse) {
            for (var _c = 0, _d = Object.entries(this.mouseEventHandlers); _c < _d.length; _c++) {
                var _e = _d[_c], event_7 = _e[0], handler = _e[1];
                this.element.removeEventListener(event_7, handler);
            }
            // 单独移除滚轮事件监听器
            this.element.removeEventListener('wheel', this.handleMouseWheel.bind(this));
        }
        // 移除触摸事件监听器
        if (this.enableTouch) {
            for (var _f = 0, _g = Object.entries(this.touchEventHandlers); _f < _g.length; _f++) {
                var _h = _g[_f], event_8 = _h[0], handler = _h[1];
                this.element.removeEventListener(event_8, handler);
            }
        }
        // 移除游戏手柄事件监听器
        if (this.enableGamepad) {
            for (var _j = 0, _k = Object.entries(this.gamepadEventHandlers); _j < _k.length; _j++) {
                var _l = _k[_j], event_9 = _l[0], handler = _l[1];
                window.removeEventListener(event_9, handler);
            }
        }
        // 移除指针锁定事件监听器
        if (this.enablePointerLock) {
            for (var _m = 0, _o = Object.entries(this.pointerLockEventHandlers); _m < _o.length; _m++) {
                var _p = _o[_m], event_10 = _p[0], handler = _p[1];
                document.removeEventListener(event_10, handler);
            }
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    InputSystem.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    InputSystem.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 销毁系统
     */
    InputSystem.prototype.dispose = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        // 退出指针锁定
        if (this.pointerLocked) {
            this.exitPointerLock();
        }
        // 清除状态
        this.keyStates.clear();
        this.mouseButtonStates.clear();
        this.touchPoints.clear();
        this.gamepads.clear();
        this.gamepadButtonStates.clear();
        this.gamepadAxisValues.clear();
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        this.initialized = false;
        this.destroyed = true;
        _super.prototype.dispose.call(this);
    };
    /** 系统名称 */
    InputSystem.NAME = 'InputSystem';
    return InputSystem;
}(System_1.System));
