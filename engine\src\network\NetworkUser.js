"use strict";
/**
 * 网络用户
 * 定义网络用户的结构
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkUserRole = exports.NetworkUserState = void 0;
/**
 * 网络用户状态
 */
var NetworkUserState;
(function (NetworkUserState) {
    /** 离线 */
    NetworkUserState["OFFLINE"] = "offline";
    /** 在线 */
    NetworkUserState["ONLINE"] = "online";
    /** 忙碌 */
    NetworkUserState["BUSY"] = "busy";
    /** 离开 */
    NetworkUserState["AWAY"] = "away";
    /** 隐身 */
    NetworkUserState["INVISIBLE"] = "invisible";
})(NetworkUserState || (exports.NetworkUserState = NetworkUserState = {}));
/**
 * 网络用户角色
 */
var NetworkUserRole;
(function (NetworkUserRole) {
    /** 访客 */
    NetworkUserRole["GUEST"] = "guest";
    /** 用户 */
    NetworkUserRole["USER"] = "user";
    /** 管理员 */
    NetworkUserRole["ADMIN"] = "admin";
    /** 超级管理员 */
    NetworkUserRole["SUPER_ADMIN"] = "super_admin";
})(NetworkUserRole || (exports.NetworkUserRole = NetworkUserRole = {}));
