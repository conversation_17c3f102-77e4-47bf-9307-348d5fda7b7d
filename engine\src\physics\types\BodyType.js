"use strict";
/**
 * 物理体类型定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BodyTypeHelper = exports.BodyType = void 0;
var BodyType;
(function (BodyType) {
    /** 静态物体 - 不会移动，无限质量 */
    BodyType["STATIC"] = "static";
    /** 动态物体 - 可以移动，受力影响 */
    BodyType["DYNAMIC"] = "dynamic";
    /** 运动学物体 - 可以移动，但不受力影响 */
    BodyType["KINEMATIC"] = "kinematic";
})(BodyType || (exports.BodyType = BodyType = {}));
var BodyTypeHelper = /** @class */ (function () {
    function BodyTypeHelper() {
    }
    /**
     * 检查物体类型是否为静态
     */
    BodyTypeHelper.isStatic = function (type) {
        return type === BodyType.STATIC;
    };
    /**
     * 检查物体类型是否为动态
     */
    BodyTypeHelper.isDynamic = function (type) {
        return type === BodyType.DYNAMIC;
    };
    /**
     * 检查物体类型是否为运动学
     */
    BodyTypeHelper.isKinematic = function (type) {
        return type === BodyType.KINEMATIC;
    };
    /**
     * 检查物体是否可以移动
     */
    BodyTypeHelper.canMove = function (type) {
        return type === BodyType.DYNAMIC || type === BodyType.KINEMATIC;
    };
    /**
     * 检查物体是否受力影响
     */
    BodyTypeHelper.isAffectedByForces = function (type) {
        return type === BodyType.DYNAMIC;
    };
    /**
     * 获取默认配置
     */
    BodyTypeHelper.getDefaultConfig = function (type) {
        switch (type) {
            case BodyType.STATIC:
                return {
                    type: type,
                    mass: 0,
                    useGravity: false,
                    isTrigger: false,
                    linearDamping: 0,
                    angularDamping: 0
                };
            case BodyType.DYNAMIC:
                return {
                    type: type,
                    mass: 1,
                    useGravity: true,
                    isTrigger: false,
                    linearDamping: 0.1,
                    angularDamping: 0.1
                };
            case BodyType.KINEMATIC:
                return {
                    type: type,
                    mass: 0,
                    useGravity: false,
                    isTrigger: false,
                    linearDamping: 0,
                    angularDamping: 0
                };
            default:
                return {
                    type: BodyType.DYNAMIC,
                    mass: 1,
                    useGravity: true,
                    isTrigger: false,
                    linearDamping: 0.1,
                    angularDamping: 0.1
                };
        }
    };
    return BodyTypeHelper;
}());
exports.BodyTypeHelper = BodyTypeHelper;
