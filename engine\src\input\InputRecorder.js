"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputRecorder = void 0;
/**
 * 输入录制器
 * 用于录制和回放输入
 */
var InputManager_1 = require("./InputManager");
/**
 * 输入录制器
 */
var InputRecorder = /** @class */ (function () {
    /**
     * 创建输入录制器
     * @param options 选项
     */
    function InputRecorder(options) {
        if (options === void 0) { options = {}; }
        /** 是否正在录制 */
        this.recording = false;
        /** 是否正在回放 */
        this.playing = false;
        /** 当前录制 */
        this.currentRecording = null;
        /** 回放开始时间戳 */
        this.playbackStartTimestamp = 0;
        /** 回放事件索引 */
        this.playbackEventIndex = 0;
        /** 回放回调 */
        this.playbackCallback = null;
        /** 设备值变化处理器 */
        this.deviceValueChangeHandlers = new Map();
        this.inputManager = InputManager_1.InputManager.getInstance();
        this.recordAllDevices = options.recordAllDevices !== undefined ? options.recordAllDevices : true;
        this.deviceNames = options.deviceNames || [];
        if (options.autoStart) {
            this.startRecording();
        }
    }
    /**
     * 开始录制
     */
    InputRecorder.prototype.startRecording = function () {
        if (this.recording)
            return;
        this.recording = true;
        this.currentRecording = {
            startTimestamp: Date.now(),
            endTimestamp: 0,
            events: []
        };
        // 添加设备值变化事件监听器
        if (this.recordAllDevices) {
            // 获取所有设备
            var devices = this.getAllDevices();
            for (var _i = 0, devices_1 = devices; _i < devices_1.length; _i++) {
                var device = devices_1[_i];
                this.addDeviceValueChangeHandler(device);
            }
        }
        else {
            // 获取指定设备
            for (var _a = 0, _b = this.deviceNames; _a < _b.length; _a++) {
                var deviceName = _b[_a];
                var device = this.inputManager.getDevice(deviceName);
                if (device) {
                    this.addDeviceValueChangeHandler(device);
                }
            }
        }
    };
    /**
     * 停止录制
     * @returns 录制结果
     */
    InputRecorder.prototype.stopRecording = function () {
        if (!this.recording || !this.currentRecording)
            return null;
        this.recording = false;
        this.currentRecording.endTimestamp = Date.now();
        // 移除设备值变化事件监听器
        this.removeAllDeviceValueChangeHandlers();
        var recording = this.currentRecording;
        this.currentRecording = null;
        return recording;
    };
    /**
     * 开始回放
     * @param recording 录制
     * @param callback 回放完成回调
     */
    InputRecorder.prototype.startPlayback = function (recording, callback) {
        if (this.playing)
            return;
        this.playing = true;
        this.playbackStartTimestamp = Date.now();
        this.playbackEventIndex = 0;
        this.playbackCallback = callback || null;
        // 开始回放
        this.playbackNextEvent(recording);
    };
    /**
     * 停止回放
     */
    InputRecorder.prototype.stopPlayback = function () {
        this.playing = false;
        this.playbackCallback = null;
    };
    /**
     * 回放下一个事件
     * @param recording 录制
     */
    InputRecorder.prototype.playbackNextEvent = function (recording) {
        var _this = this;
        if (!this.playing || this.playbackEventIndex >= recording.events.length) {
            // 回放完成
            this.playing = false;
            if (this.playbackCallback) {
                this.playbackCallback();
                this.playbackCallback = null;
            }
            return;
        }
        var event = recording.events[this.playbackEventIndex];
        var currentTime = Date.now() - this.playbackStartTimestamp;
        var eventTime = event.timestamp - recording.startTimestamp;
        if (currentTime >= eventTime) {
            // 回放事件
            var device = this.inputManager.getDevice(event.deviceName);
            if (device) {
                device.setValue(event.key, event.value);
            }
            // 下一个事件
            this.playbackEventIndex++;
            this.playbackNextEvent(recording);
        }
        else {
            // 等待下一个事件
            setTimeout(function () {
                _this.playbackNextEvent(recording);
            }, eventTime - currentTime);
        }
    };
    /**
     * 添加设备值变化处理器
     * @param device 设备
     */
    InputRecorder.prototype.addDeviceValueChangeHandler = function (device) {
        var _this = this;
        var deviceName = device.getName();
        var handler = function (event) {
            if (!_this.recording || !_this.currentRecording)
                return;
            // 记录事件
            _this.currentRecording.events.push({
                timestamp: Date.now(),
                deviceName: deviceName,
                key: event.key,
                value: event.value
            });
        };
        // 添加事件监听器
        for (var _i = 0, _a = device.getKeys(); _i < _a.length; _i++) {
            var key = _a[_i];
            device.on("".concat(key, "Changed"), handler);
        }
        // 保存处理器
        this.deviceValueChangeHandlers.set(deviceName, handler);
    };
    /**
     * 移除设备值变化处理器
     * @param device 设备
     */
    InputRecorder.prototype.removeDeviceValueChangeHandler = function (device) {
        var deviceName = device.getName();
        var handler = this.deviceValueChangeHandlers.get(deviceName);
        if (!handler)
            return;
        // 移除事件监听器
        for (var _i = 0, _a = device.getKeys(); _i < _a.length; _i++) {
            var key = _a[_i];
            device.off("".concat(key, "Changed"), handler);
        }
        // 移除处理器
        this.deviceValueChangeHandlers.delete(deviceName);
    };
    /**
     * 移除所有设备值变化处理器
     */
    InputRecorder.prototype.removeAllDeviceValueChangeHandlers = function () {
        if (this.recordAllDevices) {
            // 获取所有设备
            var devices = this.getAllDevices();
            for (var _i = 0, devices_2 = devices; _i < devices_2.length; _i++) {
                var device = devices_2[_i];
                this.removeDeviceValueChangeHandler(device);
            }
        }
        else {
            // 获取指定设备
            for (var _a = 0, _b = this.deviceNames; _a < _b.length; _a++) {
                var deviceName = _b[_a];
                var device = this.inputManager.getDevice(deviceName);
                if (device) {
                    this.removeDeviceValueChangeHandler(device);
                }
            }
        }
    };
    /**
     * 获取所有设备
     * @returns 设备列表
     */
    InputRecorder.prototype.getAllDevices = function () {
        var devices = [];
        // 获取键盘设备
        var keyboard = this.inputManager.getDevice('keyboard');
        if (keyboard)
            devices.push(keyboard);
        // 获取鼠标设备
        var mouse = this.inputManager.getDevice('mouse');
        if (mouse)
            devices.push(mouse);
        // 获取触摸设备
        var touch = this.inputManager.getDevice('touch');
        if (touch)
            devices.push(touch);
        // 获取游戏手柄设备
        var gamepad = this.inputManager.getDevice('gamepad');
        if (gamepad)
            devices.push(gamepad);
        // 获取XR设备
        var xr = this.inputManager.getDevice('xr');
        if (xr)
            devices.push(xr);
        return devices;
    };
    /**
     * 保存录制到文件
     * @param recording 录制
     * @param filename 文件名
     */
    InputRecorder.saveToFile = function (recording, filename) {
        if (filename === void 0) { filename = 'input-recording.json'; }
        var json = JSON.stringify(recording);
        var blob = new Blob([json], { type: 'application/json' });
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    };
    /**
     * 从文件加载录制
     * @param file 文件
     * @returns Promise<InputRecording>
     */
    InputRecorder.loadFromFile = function (file) {
        return new Promise(function (resolve, reject) {
            var reader = new FileReader();
            reader.onload = function (event) {
                var _a;
                try {
                    var json = (_a = event.target) === null || _a === void 0 ? void 0 : _a.result;
                    var recording = JSON.parse(json);
                    resolve(recording);
                }
                catch (error) {
                    reject(error);
                }
            };
            reader.onerror = function (error) {
                reject(error);
            };
            reader.readAsText(file);
        });
    };
    return InputRecorder;
}());
exports.InputRecorder = InputRecorder;
