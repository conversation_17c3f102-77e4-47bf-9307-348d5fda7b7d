"use strict";
/**
 * InteractableComponent.ts
 *
 * 可交互组件，用于标记可交互的对象
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractableComponent = exports.InteractionType = void 0;
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 交互类型枚举
 */
var InteractionType;
(function (InteractionType) {
    /** 点击交互 */
    InteractionType["CLICK"] = "click";
    /** 接近交互 */
    InteractionType["PROXIMITY"] = "proximity";
    /** 悬停交互 */
    InteractionType["HOVER"] = "hover";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
/**
 * 可交互组件
 * 用于标记可交互的对象
 */
var InteractableComponent = /** @class */ (function (_super) {
    __extends(InteractableComponent, _super);
    /**
     * 构造函数
     * @param config 组件配置
     */
    function InteractableComponent(config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, 'Interactable') || this;
        /** 是否高亮 */
        _this._highlighted = false;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 世界位置缓存 */
        _this._worldPosition = new three_1.Vector3();
        /** 上次更新时间 */
        _this.lastUpdateTime = 0;
        // 初始化属性
        _this._interactionType = config.interactionType || InteractionType.CLICK;
        _this._visible = config.visible !== undefined ? config.visible : true;
        _this._interactive = config.interactive !== undefined ? config.interactive : true;
        _this._interactionDistance = config.interactionDistance || 2.0;
        _this._label = config.label || '';
        _this._prompt = config.prompt || '按E键交互';
        _this._interactionSound = config.interactionSound;
        _this._highlightColor = config.highlightColor || '#ffff00';
        // 设置交互回调
        if (config.onInteract) {
            _this.onInteractCallback = config.onInteract;
        }
        return _this;
    }
    Object.defineProperty(InteractableComponent.prototype, "interactionType", {
        /**
         * 获取交互类型
         */
        get: function () {
            return this._interactionType;
        },
        /**
         * 设置交互类型
         */
        set: function (value) {
            this._interactionType = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "visible", {
        /**
         * 获取是否可见
         */
        get: function () {
            return this._visible;
        },
        /**
         * 设置是否可见
         */
        set: function (value) {
            this._visible = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "interactive", {
        /**
         * 获取是否可交互
         */
        get: function () {
            return this._interactive;
        },
        /**
         * 设置是否可交互
         */
        set: function (value) {
            this._interactive = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "interactionDistance", {
        /**
         * 获取交互距离
         */
        get: function () {
            return this._interactionDistance;
        },
        /**
         * 设置交互距离
         */
        set: function (value) {
            this._interactionDistance = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "label", {
        /**
         * 获取交互标签
         */
        get: function () {
            return this._label;
        },
        /**
         * 设置交互标签
         */
        set: function (value) {
            this._label = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "prompt", {
        /**
         * 获取交互提示
         */
        get: function () {
            return this._prompt;
        },
        /**
         * 设置交互提示
         */
        set: function (value) {
            this._prompt = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "interactionSound", {
        /**
         * 获取交互声音
         */
        get: function () {
            return this._interactionSound;
        },
        /**
         * 设置交互声音
         */
        set: function (value) {
            this._interactionSound = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "highlightColor", {
        /**
         * 获取高亮颜色
         */
        get: function () {
            return this._highlightColor;
        },
        /**
         * 设置高亮颜色
         */
        set: function (value) {
            this._highlightColor = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(InteractableComponent.prototype, "highlighted", {
        /**
         * 获取是否高亮
         */
        get: function () {
            return this._highlighted;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 设置高亮状态
     * @param highlighted 是否高亮
     */
    InteractableComponent.prototype.setHighlighted = function (highlighted) {
        if (this._highlighted === highlighted)
            return;
        this._highlighted = highlighted;
        // 触发高亮变化事件
        this.eventEmitter.emit('highlightChanged', { entity: this.getEntity(), highlighted: highlighted });
        // 更新对象的高亮效果
        this.updateHighlight();
    };
    /**
     * 更新高亮效果
     */
    InteractableComponent.prototype.updateHighlight = function () {
        var entity = this.getEntity();
        if (!entity)
            return;
        // 查找实体上的InteractionHighlightComponent组件
        var highlightComponent = entity.getComponent('InteractionHighlight');
        // 如果存在高亮组件，则设置其高亮状态
        if (highlightComponent) {
            highlightComponent.highlighted = this._highlighted;
        }
        else {
            // 如果没有高亮组件，但实体有mesh属性（用于示例代码兼容）
            var mesh = entity.mesh;
            if (mesh && mesh.material) {
                if (this._highlighted) {
                    // 保存原始材质
                    if (!('_originalMaterial' in this)) {
                        this._originalMaterial = mesh.material;
                    }
                    // 创建高亮材质
                    var highlightMaterial = new three_1.MeshBasicMaterial({
                        color: this._highlightColor,
                        transparent: true,
                        opacity: 0.7,
                        wireframe: false,
                        depthTest: true
                    });
                    // 应用高亮材质
                    mesh.material = highlightMaterial;
                }
                else if ('_originalMaterial' in this) {
                    // 恢复原始材质
                    mesh.material = this._originalMaterial;
                }
            }
        }
    };
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    InteractableComponent.prototype.getWorldPosition = function () {
        var _a;
        // 从实体的变换组件中获取世界位置
        var transform = (_a = this.getEntity()) === null || _a === void 0 ? void 0 : _a.getComponent('Transform');
        if (transform) {
            // 使用变换组件的getWorldPosition方法获取世界位置
            this._worldPosition.copy(transform.getWorldPosition());
        }
        return this._worldPosition.clone();
    };
    /**
     * 交互
     */
    InteractableComponent.prototype.interact = function () {
        // 如果不可交互，则返回
        if (!this._interactive)
            return;
        // 触发交互事件
        this.eventEmitter.emit('interact', { entity: this.getEntity() });
        // 调用交互回调
        if (this.onInteractCallback) {
            this.onInteractCallback(this.getEntity());
        }
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    InteractableComponent.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    InteractableComponent.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    InteractableComponent.prototype.update = function (deltaTime) {
        // 更新上次更新时间
        this.lastUpdateTime += deltaTime;
        // 每隔一段时间更新世界位置
        if (this.lastUpdateTime >= 0.1) {
            this.lastUpdateTime = 0;
            // 更新世界位置
            this.getWorldPosition();
        }
    };
    /**
     * 销毁组件
     */
    InteractableComponent.prototype.dispose = function () {
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        // 调用基类的销毁方法
        _super.prototype.dispose.call(this);
    };
    return InteractableComponent;
}(Component_1.Component));
exports.InteractableComponent = InteractableComponent;
