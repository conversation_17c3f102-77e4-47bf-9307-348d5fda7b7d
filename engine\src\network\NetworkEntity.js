"use strict";
/**
 * 网络实体
 * 定义网络实体的结构
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkEntityOwnershipMode = exports.NetworkEntitySyncMode = exports.NetworkEntityType = void 0;
/**
 * 网络实体类型
 */
var NetworkEntityType;
(function (NetworkEntityType) {
    /** 静态实体 */
    NetworkEntityType["STATIC"] = "static";
    /** 动态实体 */
    NetworkEntityType["DYNAMIC"] = "dynamic";
    /** 玩家实体 */
    NetworkEntityType["PLAYER"] = "player";
    /** NPC实体 */
    NetworkEntityType["NPC"] = "npc";
    /** 物品实体 */
    NetworkEntityType["ITEM"] = "item";
    /** 触发器实体 */
    NetworkEntityType["TRIGGER"] = "trigger";
    /** 相机实体 */
    NetworkEntityType["CAMERA"] = "camera";
    /** 光源实体 */
    NetworkEntityType["LIGHT"] = "light";
    /** 音源实体 */
    NetworkEntityType["AUDIO"] = "audio";
    /** 粒子实体 */
    NetworkEntityType["PARTICLE"] = "particle";
    /** UI实体 */
    NetworkEntityType["UI"] = "ui";
    /** 自定义实体 */
    NetworkEntityType["CUSTOM"] = "custom";
})(NetworkEntityType || (exports.NetworkEntityType = NetworkEntityType = {}));
/**
 * 网络实体同步模式
 */
var NetworkEntitySyncMode;
(function (NetworkEntitySyncMode) {
    /** 不同步 */
    NetworkEntitySyncMode["NONE"] = "none";
    /** 变换同步 */
    NetworkEntitySyncMode["TRANSFORM"] = "transform";
    /** 物理同步 */
    NetworkEntitySyncMode["PHYSICS"] = "physics";
    /** 完全同步 */
    NetworkEntitySyncMode["FULL"] = "full";
    /** 自定义同步 */
    NetworkEntitySyncMode["CUSTOM"] = "custom";
})(NetworkEntitySyncMode || (exports.NetworkEntitySyncMode = NetworkEntitySyncMode = {}));
/**
 * 网络实体所有权模式
 */
var NetworkEntityOwnershipMode;
(function (NetworkEntityOwnershipMode) {
    /** 固定所有权 */
    NetworkEntityOwnershipMode["FIXED"] = "fixed";
    /** 请求所有权 */
    NetworkEntityOwnershipMode["REQUEST"] = "request";
    /** 抢占所有权 */
    NetworkEntityOwnershipMode["TAKEOVER"] = "takeover";
    /** 共享所有权 */
    NetworkEntityOwnershipMode["SHARED"] = "shared";
})(NetworkEntityOwnershipMode || (exports.NetworkEntityOwnershipMode = NetworkEntityOwnershipMode = {}));
