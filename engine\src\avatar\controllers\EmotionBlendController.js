"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmotionBlendController = void 0;
var FacialAnimationSystem_1 = require("../systems/FacialAnimationSystem");
var FacialAnimationComponent_1 = require("../components/FacialAnimationComponent");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 情感混合控制器
 */
var EmotionBlendController = /** @class */ (function () {
    /**
     * 构造函数
     * @param entity 实体
     * @param world 世界
     * @param config 配置
     */
    function EmotionBlendController(entity, world, config) {
        if (config === void 0) { config = {}; }
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        /** 面部动画系统 */
        this.facialAnimationSystem = null;
        /** 表情数据映射 */
        this.expressions = new Map();
        /** 微表情计时器 */
        this.microExpressionTimer = 0;
        /** 自然变化计时器 */
        this.naturalVariationTimer = 0;
        /** 自然变化噪声种子 */
        this.naturalVariationSeed = Math.random() * 1000;
        this.entity = entity;
        this.world = world;
        // 设置默认配置
        this.config = {
            debug: (_a = config.debug) !== null && _a !== void 0 ? _a : false,
            defaultTransitionTime: (_b = config.defaultTransitionTime) !== null && _b !== void 0 ? _b : 0.3,
            enableMicroExpressions: (_c = config.enableMicroExpressions) !== null && _c !== void 0 ? _c : true,
            microExpressionFrequency: (_d = config.microExpressionFrequency) !== null && _d !== void 0 ? _d : 4.0,
            microExpressionIntensity: (_e = config.microExpressionIntensity) !== null && _e !== void 0 ? _e : 0.3,
            microExpressionDuration: (_f = config.microExpressionDuration) !== null && _f !== void 0 ? _f : 0.2,
            enableNaturalVariation: (_g = config.enableNaturalVariation) !== null && _g !== void 0 ? _g : true,
            naturalVariationAmount: (_h = config.naturalVariationAmount) !== null && _h !== void 0 ? _h : 0.1,
            naturalVariationFrequency: (_j = config.naturalVariationFrequency) !== null && _j !== void 0 ? _j : 0.5,
            blendMode: (_k = config.blendMode) !== null && _k !== void 0 ? _k : 'weighted'
        };
        // 创建事件发射器
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        // 查找面部动画系统
        this.facialAnimationSystem = this.world.getSystem(FacialAnimationSystem_1.FacialAnimationSystem);
        // 初始化表情
        this.initializeExpressions();
    }
    /**
     * 初始化表情
     */
    EmotionBlendController.prototype.initializeExpressions = function () {
        // 添加所有表情类型
        for (var _i = 0, _a = Object.values(FacialAnimationComponent_1.FacialExpressionType); _i < _a.length; _i++) {
            var expressionType = _a[_i];
            this.expressions.set(expressionType, {
                expression: expressionType,
                targetWeight: 0,
                currentWeight: 0,
                transitionSpeed: 1.0 / this.config.defaultTransitionTime,
                startTime: 0,
                duration: 0,
                active: false,
                priority: 0
            });
        }
        // 设置中性表情为活跃
        var neutralExpression = this.expressions.get(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL);
        if (neutralExpression) {
            neutralExpression.active = true;
            neutralExpression.targetWeight = 1.0;
            neutralExpression.currentWeight = 1.0;
        }
    };
    /**
     * 更新控制器
     * @param deltaTime 帧间隔时间（秒）
     */
    EmotionBlendController.prototype.update = function (deltaTime) {
        // 更新表情权重
        this.updateExpressionWeights(deltaTime);
        // 更新微表情
        if (this.config.enableMicroExpressions) {
            this.updateMicroExpressions(deltaTime);
        }
        // 更新自然变化
        if (this.config.enableNaturalVariation) {
            this.updateNaturalVariation(deltaTime);
        }
        // 应用混合表情
        this.applyBlendedExpression();
    };
    /**
     * 更新表情权重
     * @param deltaTime 帧间隔时间（秒）
     */
    EmotionBlendController.prototype.updateExpressionWeights = function (deltaTime) {
        var currentTime = Date.now() / 1000; // 转换为秒
        // 遍历所有表情
        for (var _i = 0, _a = this.expressions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], expressionType = _b[0], data = _b[1];
            // 如果不活跃，则跳过
            if (!data.active)
                continue;
            // 检查是否超过持续时间
            if (data.duration > 0 && currentTime - data.startTime > data.duration) {
                // 设置目标权重为0
                data.targetWeight = 0;
                // 如果是中性表情，则保持活跃
                if (expressionType === FacialAnimationComponent_1.FacialExpressionType.NEUTRAL) {
                    data.targetWeight = 1.0;
                }
                else {
                    // 标记为非活跃
                    data.active = false;
                }
            }
            // 更新当前权重
            if (data.currentWeight !== data.targetWeight) {
                // 计算权重变化
                var weightChange = data.transitionSpeed * deltaTime;
                // 更新权重
                if (data.currentWeight < data.targetWeight) {
                    data.currentWeight = Math.min(data.targetWeight, data.currentWeight + weightChange);
                }
                else {
                    data.currentWeight = Math.max(data.targetWeight, data.currentWeight - weightChange);
                }
            }
        }
    };
    /**
     * 更新微表情
     * @param deltaTime 帧间隔时间（秒）
     */
    EmotionBlendController.prototype.updateMicroExpressions = function (deltaTime) {
        // 更新计时器
        this.microExpressionTimer += deltaTime;
        // 计算微表情间隔（秒）
        var microExpressionInterval = 60.0 / this.config.microExpressionFrequency;
        // 如果达到间隔，则触发微表情
        if (this.microExpressionTimer >= microExpressionInterval) {
            this.microExpressionTimer = 0;
            // 随机选择一个微表情
            this.triggerRandomMicroExpression();
        }
    };
    /**
     * 触发随机微表情
     */
    EmotionBlendController.prototype.triggerRandomMicroExpression = function () {
        // 获取当前主要表情
        var primaryExpression = this.getPrimaryExpression();
        // 如果没有主要表情，则跳过
        if (!primaryExpression)
            return;
        // 随机选择一个微表情类型
        var microExpressionTypes = [
            FacialAnimationComponent_1.FacialExpressionType.SURPRISED,
            FacialAnimationComponent_1.FacialExpressionType.HAPPY,
            FacialAnimationComponent_1.FacialExpressionType.SAD,
            FacialAnimationComponent_1.FacialExpressionType.FEARFUL,
            FacialAnimationComponent_1.FacialExpressionType.DISGUSTED
        ];
        // 排除当前主要表情
        var availableMicroExpressions = microExpressionTypes.filter(function (type) { return type !== primaryExpression.expression; });
        // 随机选择一个
        var randomIndex = Math.floor(Math.random() * availableMicroExpressions.length);
        var microExpressionType = availableMicroExpressions[randomIndex];
        // 添加微表情
        this.addExpression(microExpressionType, this.config.microExpressionIntensity, this.config.microExpressionDuration, 0.05 // 快速过渡
        );
        if (this.config.debug) {
            console.log('触发微表情:', microExpressionType);
        }
    };
    /**
     * 更新自然变化
     * @param deltaTime 帧间隔时间（秒）
     */
    EmotionBlendController.prototype.updateNaturalVariation = function (deltaTime) {
        // 更新计时器
        this.naturalVariationTimer += deltaTime;
        // 计算自然变化间隔（秒）
        var naturalVariationInterval = 1.0 / this.config.naturalVariationFrequency;
        // 如果达到间隔，则应用自然变化
        if (this.naturalVariationTimer >= naturalVariationInterval) {
            this.naturalVariationTimer = 0;
            // 应用自然变化
            this.applyNaturalVariation();
        }
    };
    /**
     * 应用自然变化
     */
    EmotionBlendController.prototype.applyNaturalVariation = function () {
        // 获取当前主要表情
        var primaryExpression = this.getPrimaryExpression();
        // 如果没有主要表情，则跳过
        if (!primaryExpression)
            return;
        // 使用噪声种子生成更自然的变化
        var time = Date.now() / 1000;
        var noiseValue = Math.sin(time * this.config.naturalVariationFrequency + this.naturalVariationSeed);
        var variation = noiseValue * this.config.naturalVariationAmount;
        // 应用变化
        primaryExpression.currentWeight = Math.max(0, Math.min(1, primaryExpression.currentWeight + variation));
    };
    /**
     * 应用混合表情
     */
    EmotionBlendController.prototype.applyBlendedExpression = function () {
        // 如果没有面部动画系统，则跳过
        if (!this.facialAnimationSystem)
            return;
        // 获取面部动画组件
        var facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.entity);
        // 如果没有面部动画组件，则跳过
        if (!facialAnimation)
            return;
        // 根据混合模式应用表情
        switch (this.config.blendMode) {
            case 'override':
                this.applyOverrideBlend(facialAnimation);
                break;
            case 'add':
                this.applyAdditiveBlend(facialAnimation);
                break;
            case 'multiply':
                this.applyMultiplicativeBlend(facialAnimation);
                break;
            case 'weighted':
            default:
                this.applyWeightedBlend(facialAnimation);
                break;
        }
    };
    /**
     * 应用覆盖混合
     * @param facialAnimation 面部动画组件
     */
    EmotionBlendController.prototype.applyOverrideBlend = function (facialAnimation) {
        // 获取优先级最高的活跃表情
        var activeExpressions = Array.from(this.expressions.values())
            .filter(function (data) { return data.active && data.currentWeight > 0.01; })
            .sort(function (a, b) { return b.priority - a.priority; });
        // 如果没有活跃表情，则使用中性表情
        if (activeExpressions.length === 0) {
            facialAnimation.setExpression(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL, 1.0);
            return;
        }
        // 应用优先级最高的表情
        var primaryExpression = activeExpressions[0];
        facialAnimation.setExpression(primaryExpression.expression, primaryExpression.currentWeight);
    };
    /**
     * 应用加法混合
     * @param facialAnimation 面部动画组件
     */
    EmotionBlendController.prototype.applyAdditiveBlend = function (facialAnimation) {
        // 获取所有活跃表情
        var activeExpressions = Array.from(this.expressions.values())
            .filter(function (data) { return data.active && data.currentWeight > 0.01; });
        // 如果没有活跃表情，则使用中性表情
        if (activeExpressions.length === 0) {
            facialAnimation.setExpression(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL, 1.0);
            return;
        }
        // 应用所有活跃表情
        for (var _i = 0, activeExpressions_1 = activeExpressions; _i < activeExpressions_1.length; _i++) {
            var expressionData = activeExpressions_1[_i];
            facialAnimation.addExpression(expressionData.expression, expressionData.currentWeight);
        }
    };
    /**
     * 应用乘法混合
     * @param facialAnimation 面部动画组件
     */
    EmotionBlendController.prototype.applyMultiplicativeBlend = function (facialAnimation) {
        // 获取所有活跃表情
        var activeExpressions = Array.from(this.expressions.values())
            .filter(function (data) { return data.active && data.currentWeight > 0.01; });
        // 如果没有活跃表情，则使用中性表情
        if (activeExpressions.length === 0) {
            facialAnimation.setExpression(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL, 1.0);
            return;
        }
        // 计算总权重
        var totalWeight = 1.0;
        // 应用所有活跃表情
        for (var _i = 0, activeExpressions_2 = activeExpressions; _i < activeExpressions_2.length; _i++) {
            var expressionData = activeExpressions_2[_i];
            totalWeight *= expressionData.currentWeight;
            facialAnimation.addExpression(expressionData.expression, totalWeight);
        }
    };
    /**
     * 应用加权混合
     * @param facialAnimation 面部动画组件
     */
    EmotionBlendController.prototype.applyWeightedBlend = function (facialAnimation) {
        // 获取所有活跃表情
        var activeExpressions = Array.from(this.expressions.values())
            .filter(function (data) { return data.active && data.currentWeight > 0.01; });
        // 如果没有活跃表情，则使用中性表情
        if (activeExpressions.length === 0) {
            facialAnimation.setExpression(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL, 1.0);
            return;
        }
        // 计算总权重
        var totalWeight = activeExpressions.reduce(function (sum, data) { return sum + data.currentWeight; }, 0);
        // 如果总权重为0，则使用中性表情
        if (totalWeight <= 0) {
            facialAnimation.setExpression(FacialAnimationComponent_1.FacialExpressionType.NEUTRAL, 1.0);
            return;
        }
        // 应用所有活跃表情
        for (var _i = 0, activeExpressions_3 = activeExpressions; _i < activeExpressions_3.length; _i++) {
            var expressionData = activeExpressions_3[_i];
            // 计算归一化权重
            var normalizedWeight = expressionData.currentWeight / totalWeight;
            // 应用表情
            facialAnimation.addExpression(expressionData.expression, normalizedWeight);
        }
    };
    /**
     * 添加表情
     * @param expression 表情类型
     * @param weight 权重
     * @param duration 持续时间（秒）
     * @param transitionTime 过渡时间（秒）
     * @param priority 优先级
     * @returns 是否成功添加
     */
    EmotionBlendController.prototype.addExpression = function (expression, weight, duration, transitionTime, priority) {
        if (weight === void 0) { weight = 1.0; }
        if (duration === void 0) { duration = 0; }
        if (transitionTime === void 0) { transitionTime = this.config.defaultTransitionTime; }
        if (priority === void 0) { priority = 0; }
        // 获取表情数据
        var expressionData = this.expressions.get(expression);
        // 如果没有表情数据，则返回失败
        if (!expressionData)
            return false;
        // 更新表情数据
        expressionData.targetWeight = Math.max(0, Math.min(1, weight));
        expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
        expressionData.startTime = Date.now() / 1000; // 转换为秒
        expressionData.duration = duration;
        expressionData.active = true;
        expressionData.priority = priority;
        // 触发表情添加事件
        this.eventEmitter.emit('expressionAdded', {
            expression: expression,
            weight: weight,
            duration: duration,
            transitionTime: transitionTime,
            priority: priority
        });
        if (this.config.debug) {
            console.log('添加表情:', expression, '权重:', weight, '持续时间:', duration);
        }
        return true;
    };
    /**
     * 移除表情
     * @param expression 表情类型
     * @param transitionTime 过渡时间（秒）
     * @returns 是否成功移除
     */
    EmotionBlendController.prototype.removeExpression = function (expression, transitionTime) {
        if (transitionTime === void 0) { transitionTime = this.config.defaultTransitionTime; }
        // 获取表情数据
        var expressionData = this.expressions.get(expression);
        // 如果没有表情数据，则返回失败
        if (!expressionData)
            return false;
        // 如果是中性表情，则不移除
        if (expression === FacialAnimationComponent_1.FacialExpressionType.NEUTRAL) {
            expressionData.targetWeight = 1.0;
            expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
            return true;
        }
        // 更新表情数据
        expressionData.targetWeight = 0;
        expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
        // 触发表情移除事件
        this.eventEmitter.emit('expressionRemoved', {
            expression: expression,
            transitionTime: transitionTime
        });
        if (this.config.debug) {
            console.log('移除表情:', expression);
        }
        return true;
    };
    /**
     * 清除所有表情
     * @param transitionTime 过渡时间（秒）
     */
    EmotionBlendController.prototype.clearExpressions = function (transitionTime) {
        if (transitionTime === void 0) { transitionTime = this.config.defaultTransitionTime; }
        // 遍历所有表情
        for (var _i = 0, _a = this.expressions.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], expressionType = _b[0], data = _b[1];
            // 如果是中性表情，则设置为1.0
            if (expressionType === FacialAnimationComponent_1.FacialExpressionType.NEUTRAL) {
                data.targetWeight = 1.0;
                data.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
                data.active = true;
            }
            else {
                // 否则设置为0
                data.targetWeight = 0;
                data.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
                data.active = false;
            }
        }
        // 触发表情清除事件
        this.eventEmitter.emit('expressionsCleared', {
            transitionTime: transitionTime
        });
        if (this.config.debug) {
            console.log('清除所有表情');
        }
    };
    /**
     * 获取表情数据
     * @param expression 表情类型
     * @returns 表情数据
     */
    EmotionBlendController.prototype.getExpressionData = function (expression) {
        return this.expressions.get(expression) || null;
    };
    /**
     * 获取所有活跃表情
     * @returns 活跃表情数组
     */
    EmotionBlendController.prototype.getActiveExpressions = function () {
        return Array.from(this.expressions.values())
            .filter(function (data) { return data.active && data.currentWeight > 0.01; });
    };
    /**
     * 获取主要表情
     * @returns 主要表情数据
     */
    EmotionBlendController.prototype.getPrimaryExpression = function () {
        // 获取所有活跃表情
        var activeExpressions = this.getActiveExpressions();
        // 如果没有活跃表情，则返回null
        if (activeExpressions.length === 0)
            return null;
        // 按权重排序
        activeExpressions.sort(function (a, b) { return b.currentWeight - a.currentWeight; });
        // 返回权重最高的表情
        return activeExpressions[0];
    };
    /**
     * 是否有活跃表情
     * @returns 是否有活跃表情
     */
    EmotionBlendController.prototype.hasActiveExpressions = function () {
        // 检查是否有活跃表情
        for (var _i = 0, _a = this.expressions.values(); _i < _a.length; _i++) {
            var data = _a[_i];
            if (data.active && data.currentWeight > 0.01 && data.expression !== FacialAnimationComponent_1.FacialExpressionType.NEUTRAL) {
                return true;
            }
        }
        return false;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    EmotionBlendController.prototype.addEventListener = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    EmotionBlendController.prototype.removeEventListener = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    return EmotionBlendController;
}());
exports.EmotionBlendController = EmotionBlendController;
