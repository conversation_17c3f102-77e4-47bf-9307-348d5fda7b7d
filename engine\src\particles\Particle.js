"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Particle = void 0;
/**
 * 粒子类
 * 表示单个粒子的属性和行为
 */
var THREE = require("three");
var Particle = /** @class */ (function () {
    /**
     * 创建粒子实例
     */
    function Particle() {
        /** 是否活跃 */
        this.active = false;
        /** 位置 */
        this.position = new THREE.Vector3();
        /** 上一帧位置 */
        this.previousPosition = new THREE.Vector3();
        /** 速度 */
        this.velocity = new THREE.Vector3();
        /** 加速度 */
        this.acceleration = new THREE.Vector3();
        /** 旋转 */
        this.rotation = 0;
        /** 旋转速度 */
        this.rotationSpeed = 0;
        /** 缩放 */
        this.scale = new THREE.Vector2(1, 1);
        /** 缩放速度 */
        this.scaleSpeed = new THREE.Vector2(0, 0);
        /** 颜色 */
        this.color = new THREE.Color().setRGB(1, 1, 1);
        /** 起始颜色 */
        this.startColor = new THREE.Color().setRGB(1, 1, 1);
        /** 结束颜色 */
        this.endColor = new THREE.Color().setRGB(1, 1, 1);
        /** 透明度 */
        this.opacity = 1;
        /** 起始透明度 */
        this.startOpacity = 1;
        /** 结束透明度 */
        this.endOpacity = 0;
        /** 生命周期（秒） */
        this.lifetime = 1;
        /** 已存活时间（秒） */
        this.age = 0;
        /** 归一化年龄（0-1） */
        this.normalizedAge = 0;
        /** 质量 */
        this.mass = 1;
        /** 阻力 */
        this.drag = 0;
        /** 重力缩放 */
        this.gravityScale = 1;
        /** 弹性 */
        this.restitution = 0.5;
        /** 纹理索引 */
        this.textureIndex = 0;
        /** 自定义数据 - 可以存储任何与粒子相关的额外信息 */
        this.userData = {};
        this.reset();
    }
    /**
     * 重置粒子状态
     */
    Particle.prototype.reset = function () {
        this.active = false;
        this.setPosition(0, 0, 0);
        this.previousPosition.set(0, 0, 0);
        this.velocity.set(0, 0, 0);
        this.acceleration.set(0, 0, 0);
        this.rotation = 0;
        this.rotationSpeed = 0;
        this.setScale(1, 1);
        this.scaleSpeed.set(0, 0);
        this.color.setRGB(1, 1, 1);
        this.startColor.setRGB(1, 1, 1);
        this.endColor.setRGB(1, 1, 1);
        this.opacity = 1;
        this.startOpacity = 1;
        this.endOpacity = 0;
        this.lifetime = 1;
        this.age = 0;
        this.normalizedAge = 0;
        this.mass = 1;
        this.drag = 0;
        this.gravityScale = 1;
        this.restitution = 0.5;
        this.textureIndex = 0;
        this.userData = {};
    };
    /**
     * 更新粒子
     * @param deltaTime 帧间隔时间（秒）
     * @returns 是否仍然活跃
     */
    Particle.prototype.update = function (deltaTime) {
        if (!this.active) {
            return false;
        }
        // 更新年龄
        this.age += deltaTime;
        this.normalizedAge = Math.min(this.age / this.lifetime, 1);
        // 检查生命周期
        if (this.age >= this.lifetime) {
            this.active = false;
            return false;
        }
        // 保存上一帧位置
        this.previousPosition.copy(this.position);
        // 应用阻力
        if (this.drag > 0) {
            var dragForce = this.velocity.clone().multiplyScalar(-this.drag);
            this.acceleration.add(dragForce);
        }
        // 更新速度
        this.velocity.add(this.acceleration.clone().multiplyScalar(deltaTime));
        // 更新位置
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        // 更新旋转
        this.rotation += this.rotationSpeed * deltaTime;
        // 更新缩放
        this.scale.add(this.scaleSpeed.clone().multiplyScalar(deltaTime));
        // 更新颜色
        this.color.copy(this.startColor).lerp(this.endColor, this.normalizedAge);
        // 更新透明度
        this.opacity = this.startOpacity + (this.endOpacity - this.startOpacity) * this.normalizedAge;
        // 重置加速度
        this.acceleration.set(0, 0, 0);
        return true;
    };
    /**
     * 应用力
     * @param force 力向量
     */
    Particle.prototype.applyForce = function (force) {
        // F = ma, a = F/m
        var acceleration = force.clone().divideScalar(this.mass);
        this.acceleration.add(acceleration);
    };
    /**
     * 应用重力
     * @param gravity 重力向量
     */
    Particle.prototype.applyGravity = function (gravity) {
        if (this.gravityScale !== 0) {
            var gravityForce = gravity.clone().multiplyScalar(this.mass * this.gravityScale);
            this.applyForce(gravityForce);
        }
    };
    /**
     * 处理碰撞
     * @param normal 碰撞法线
     * @param penetration 穿透深度
     */
    Particle.prototype.handleCollision = function (normal, penetration) {
        // 调整位置
        this.position.add(normal.clone().multiplyScalar(penetration));
        // 计算反弹速度
        var dot = this.velocity.dot(normal);
        if (dot < 0) {
            var reflection = normal.clone().multiplyScalar(-2 * dot);
            this.velocity.add(reflection).multiplyScalar(this.restitution);
        }
    };
    /**
     * 获取粒子的变换矩阵
     * @returns 变换矩阵
     */
    Particle.prototype.getTransformMatrix = function () {
        // 创建一个新的矩阵
        var matrix = new THREE.Matrix4();
        // 按照缩放 -> 旋转 -> 平移的顺序构建变换矩阵
        // 注意：在Three.js中，矩阵乘法顺序是从右到左
        // 我们使用Three.js的compose方法，它会自动按正确的顺序应用变换
        // 组合变换：先缩放，再旋转，最后平移
        matrix.compose(this.position, new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 0, 1), this.rotation), new THREE.Vector3(this.scale.x, this.scale.y, 1));
        return matrix;
    };
    /**
     * 获取粒子的颜色，包括透明度
     * @returns 颜色，包括透明度
     */
    Particle.prototype.getColorWithOpacity = function () {
        return new THREE.Vector4(this.color.r, this.color.g, this.color.b, this.opacity);
    };
    /**
     * 计算与相机的距离
     * @param cameraPosition 相机位置
     * @returns 距离
     */
    Particle.prototype.distanceToCamera = function (cameraPosition) {
        return this.position.distanceTo(cameraPosition);
    };
    return Particle;
}());
exports.Particle = Particle;
