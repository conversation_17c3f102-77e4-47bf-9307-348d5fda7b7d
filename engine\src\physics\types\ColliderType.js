"use strict";
/**
 * 碰撞体类型定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ColliderTypeHelper = exports.ColliderType = void 0;
var ColliderType;
(function (ColliderType) {
    /** 盒子碰撞体 */
    ColliderType["BOX"] = "box";
    /** 球体碰撞体 */
    ColliderType["SPHERE"] = "sphere";
    /** 胶囊碰撞体 */
    ColliderType["CAPSULE"] = "capsule";
    /** 圆柱体碰撞体 */
    ColliderType["CYLINDER"] = "cylinder";
    /** 圆锥体碰撞体 */
    ColliderType["CONE"] = "cone";
    /** 平面碰撞体 */
    ColliderType["PLANE"] = "plane";
    /** 网格碰撞体 */
    ColliderType["MESH"] = "mesh";
    /** 凸包碰撞体 */
    ColliderType["CONVEX_HULL"] = "convexHull";
    /** 高度场碰撞体 */
    ColliderType["HEIGHTFIELD"] = "heightfield";
    /** 复合碰撞体 */
    ColliderType["COMPOUND"] = "compound";
})(ColliderType || (exports.ColliderType = ColliderType = {}));
var ColliderTypeHelper = /** @class */ (function () {
    function ColliderTypeHelper() {
    }
    /**
     * 检查是否为基础几何体
     */
    ColliderTypeHelper.isPrimitive = function (type) {
        return [
            ColliderType.BOX,
            ColliderType.SPHERE,
            ColliderType.CAPSULE,
            ColliderType.CYLINDER,
            ColliderType.CONE,
            ColliderType.PLANE
        ].includes(type);
    };
    /**
     * 检查是否为复杂几何体
     */
    ColliderTypeHelper.isComplex = function (type) {
        return [
            ColliderType.MESH,
            ColliderType.CONVEX_HULL,
            ColliderType.HEIGHTFIELD,
            ColliderType.COMPOUND
        ].includes(type);
    };
    /**
     * 检查是否需要网格数据
     */
    ColliderTypeHelper.requiresMeshData = function (type) {
        return [
            ColliderType.MESH,
            ColliderType.CONVEX_HULL,
            ColliderType.HEIGHTFIELD
        ].includes(type);
    };
    /**
     * 获取默认尺寸
     */
    ColliderTypeHelper.getDefaultSize = function (type) {
        switch (type) {
            case ColliderType.BOX:
                return { x: 1, y: 1, z: 1 };
            case ColliderType.SPHERE:
                return { radius: 0.5 };
            case ColliderType.CAPSULE:
                return { radius: 0.5, height: 2 };
            case ColliderType.CYLINDER:
                return { radius: 0.5, height: 2 };
            case ColliderType.CONE:
                return { radius: 0.5, height: 2 };
            case ColliderType.PLANE:
                return { x: 10, z: 10 };
            default:
                return { x: 1, y: 1, z: 1 };
        }
    };
    /**
     * 获取默认配置
     */
    ColliderTypeHelper.getDefaultConfig = function (type) {
        return {
            type: type,
            size: this.getDefaultSize(type),
            material: {
                friction: 0.5,
                restitution: 0.3,
                density: 1.0
            },
            isTrigger: false,
            layer: 0,
            mask: 0xFFFFFFFF
        };
    };
    return ColliderTypeHelper;
}());
exports.ColliderTypeHelper = ColliderTypeHelper;
