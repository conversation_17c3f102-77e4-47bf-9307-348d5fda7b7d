"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerCoreNodes = exports.DelayNode = exports.PrintLogNode = exports.SequenceNode = exports.BranchNode = exports.OnUpdateNode = exports.OnStartNode = void 0;
/**
 * 视觉脚本核心节点
 * 提供基本的流程控制和调试节点
 */
var EventNode_1 = require("../nodes/EventNode");
var FlowNode_1 = require("../nodes/FlowNode");
var Node_1 = require("../nodes/Node");
/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
var OnStartNode = /** @class */ (function (_super) {
    __extends(OnStartNode, _super);
    function OnStartNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    OnStartNode.prototype.initializeSockets = function () {
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '当视觉脚本开始执行时触发'
        });
    };
    /**
     * 当视觉脚本开始执行时调用
     */
    OnStartNode.prototype.onStart = function () {
        // 触发流程
        this.triggerFlow('flow');
    };
    return OnStartNode;
}(EventNode_1.EventNode));
exports.OnStartNode = OnStartNode;
/**
 * 更新事件节点
 * 每帧更新时触发
 */
var OnUpdateNode = /** @class */ (function (_super) {
    __extends(OnUpdateNode, _super);
    function OnUpdateNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    OnUpdateNode.prototype.initializeSockets = function () {
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '每帧更新时触发'
        });
        // 添加帧间隔时间输出
        this.addOutput({
            name: 'deltaTime',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'number',
            description: '帧间隔时间（秒）'
        });
    };
    /**
     * 当视觉脚本更新时调用
     * @param deltaTime 帧间隔时间（秒）
     */
    OnUpdateNode.prototype.onUpdate = function (deltaTime) {
        // 设置帧间隔时间输出
        this.setOutputValue('deltaTime', deltaTime);
        // 触发流程
        this.triggerFlow('flow');
    };
    return OnUpdateNode;
}(EventNode_1.EventNode));
exports.OnUpdateNode = OnUpdateNode;
/**
 * 分支节点
 * 根据条件选择执行路径
 */
var BranchNode = /** @class */ (function (_super) {
    __extends(BranchNode, _super);
    function BranchNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    BranchNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加条件输入
        this.addInput({
            name: 'condition',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'boolean',
            description: '条件',
            defaultValue: false
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'true',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '条件为真时执行'
        });
        this.addOutput({
            name: 'false',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '条件为假时执行'
        });
    };
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    BranchNode.prototype.process = function (inputs) {
        // 获取条件值
        var condition = inputs.condition === true;
        // 根据条件选择输出流程
        return condition ? 'true' : 'false';
    };
    return BranchNode;
}(FlowNode_1.FlowNode));
exports.BranchNode = BranchNode;
/**
 * 序列节点
 * 按顺序执行多个流程
 */
var SequenceNode = /** @class */ (function (_super) {
    __extends(SequenceNode, _super);
    function SequenceNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    SequenceNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow1',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '第一个执行输出'
        });
        this.addOutput({
            name: 'flow2',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '第二个执行输出'
        });
        this.addOutput({
            name: 'flow3',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '第三个执行输出'
        });
        this.addOutput({
            name: 'completed',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '所有流程执行完成后触发'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    SequenceNode.prototype.execute = function () {
        // 按顺序触发所有流程
        this.triggerFlow('flow1');
        this.triggerFlow('flow2');
        this.triggerFlow('flow3');
        this.triggerFlow('completed');
        return 'completed';
    };
    return SequenceNode;
}(FlowNode_1.FlowNode));
exports.SequenceNode = SequenceNode;
/**
 * 打印日志节点
 * 在控制台打印日志
 */
var PrintLogNode = /** @class */ (function (_super) {
    __extends(PrintLogNode, _super);
    function PrintLogNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    PrintLogNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加消息输入
        this.addInput({
            name: 'message',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '要打印的消息',
            defaultValue: ''
        });
        // 添加日志级别输入
        this.addInput({
            name: 'level',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '日志级别',
            defaultValue: 'log'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
    };
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    PrintLogNode.prototype.process = function (inputs) {
        // 获取消息和日志级别
        var message = inputs.message || '';
        var level = inputs.level || 'log';
        // 打印日志
        switch (level) {
            case 'warn':
                console.warn("[VisualScript] ".concat(message));
                break;
            case 'error':
                console.error("[VisualScript] ".concat(message));
                break;
            case 'info':
                console.info("[VisualScript] ".concat(message));
                break;
            case 'debug':
                console.debug("[VisualScript] ".concat(message));
                break;
            default:
                console.log("[VisualScript] ".concat(message));
                break;
        }
        return 'flow';
    };
    return PrintLogNode;
}(FlowNode_1.FlowNode));
exports.PrintLogNode = PrintLogNode;
/**
 * 延时节点
 * 延时执行流程
 */
var DelayNode = /** @class */ (function (_super) {
    __extends(DelayNode, _super);
    function DelayNode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 定时器ID */
        _this.timerId = null;
        return _this;
    }
    /**
     * 初始化插槽
     */
    DelayNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加延时输入
        this.addInput({
            name: 'seconds',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '延时时间（秒）',
            defaultValue: 1
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '延时后执行'
        });
    };
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    DelayNode.prototype.process = function (inputs) {
        var _this = this;
        // 获取延时时间
        var seconds = Math.max(0, inputs.seconds || 0);
        // 清除之前的定时器
        if (this.timerId !== null) {
            clearTimeout(this.timerId);
            this.timerId = null;
        }
        // 设置定时器
        this.timerId = setTimeout(function () {
            _this.triggerFlow('flow');
            _this.timerId = null;
        }, seconds * 1000);
        // 不立即触发输出流程
        return null;
    };
    /**
     * 销毁节点
     */
    DelayNode.prototype.dispose = function () {
        // 清除定时器
        if (this.timerId !== null) {
            clearTimeout(this.timerId);
            this.timerId = null;
        }
        _super.prototype.dispose.call(this);
    };
    return DelayNode;
}(FlowNode_1.FlowNode));
exports.DelayNode = DelayNode;
/**
 * 注册核心节点
 * @param registry 节点注册表
 */
function registerCoreNodes(registry) {
    // 注册开始事件节点
    registry.registerNodeType({
        type: 'core/events/onStart',
        category: Node_1.NodeCategory.EVENT,
        constructor: OnStartNode,
        label: '开始',
        description: '当视觉脚本开始执行时触发',
        icon: 'play',
        color: '#4CAF50',
        tags: ['event', 'core', 'lifecycle']
    });
    // 注册更新事件节点
    registry.registerNodeType({
        type: 'core/events/onUpdate',
        category: Node_1.NodeCategory.EVENT,
        constructor: OnUpdateNode,
        label: '更新',
        description: '每帧更新时触发',
        icon: 'update',
        color: '#2196F3',
        tags: ['event', 'core', 'lifecycle']
    });
    // 注册分支节点
    registry.registerNodeType({
        type: 'core/flow/branch',
        category: Node_1.NodeCategory.FLOW,
        constructor: BranchNode,
        label: '分支',
        description: '根据条件选择执行路径',
        icon: 'branch',
        color: '#FF9800',
        tags: ['flow', 'core', 'control']
    });
    // 注册序列节点
    registry.registerNodeType({
        type: 'core/flow/sequence',
        category: Node_1.NodeCategory.FLOW,
        constructor: SequenceNode,
        label: '序列',
        description: '按顺序执行多个流程',
        icon: 'sequence',
        color: '#9C27B0',
        tags: ['flow', 'core', 'control']
    });
    // 注册打印日志节点
    registry.registerNodeType({
        type: 'core/debug/print',
        category: Node_1.NodeCategory.DEBUG,
        constructor: PrintLogNode,
        label: '打印日志',
        description: '在控制台打印日志',
        icon: 'print',
        color: '#F44336',
        tags: ['debug', 'core', 'utility']
    });
    // 注册延时节点
    registry.registerNodeType({
        type: 'core/flow/delay',
        category: Node_1.NodeCategory.FLOW,
        constructor: DelayNode,
        label: '延时',
        description: '延时执行流程',
        icon: 'delay',
        color: '#00BCD4',
        tags: ['flow', 'core', 'time']
    });
}
exports.registerCoreNodes = registerCoreNodes;
