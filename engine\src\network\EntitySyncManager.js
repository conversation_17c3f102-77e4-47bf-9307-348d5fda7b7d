"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitySyncManager = exports.SyncAreaType = void 0;
/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var NetworkEntityComponent_1 = require("./components/NetworkEntityComponent");
var DataCompressor_1 = require("./DataCompressor");
var SyncPriorityManager_1 = require("./SyncPriorityManager");
/**
 * 同步区域类型
 */
var SyncAreaType;
(function (SyncAreaType) {
    /** 全局 */
    SyncAreaType["GLOBAL"] = "global";
    /** 区域 */
    SyncAreaType["AREA"] = "area";
    /** 距离 */
    SyncAreaType["DISTANCE"] = "distance";
    /** 兴趣点 */
    SyncAreaType["INTEREST"] = "interest";
})(SyncAreaType || (exports.SyncAreaType = SyncAreaType = {}));
/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
var EntitySyncManager = /** @class */ (function (_super) {
    __extends(EntitySyncManager, _super);
    /**
     * 创建实体同步管理器
     * @param config 配置
     */
    function EntitySyncManager(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 实体映射表 */
        _this.entities = new Map();
        /** 实体同步状态映射表 */
        _this.entitySyncStates = new Map();
        /** 同步区域映射表 */
        _this.syncAreas = new Map();
        /** 空间分区网格 */
        _this.spatialGrid = new Map();
        /** 上次同步的实体数据缓存 */
        _this.lastSyncDataCache = new Map();
        /** 数据压缩器 */
        _this.dataCompressor = null;
        /** 同步优先级管理器 */
        _this.priorityManager = null;
        /** 本地用户ID */
        _this.localUserId = null;
        /** 带宽控制器 */
        _this.bandwidthController = null;
        /** 同步定时器ID */
        _this.syncTimerId = null;
        /** 是否正在同步 */
        _this.isSyncing = false;
        /** 待同步实体队列 */
        _this.syncQueue = [];
        // 默认配置
        _this.config = __assign({ defaultSyncInterval: 100, minSyncInterval: 50, maxSyncInterval: 1000, syncDistance: 100, useSpatialPartitioning: true, spatialCellSize: 10, useInterpolation: true, useExtrapolation: true, extrapolationTime: 100, useCompression: true, useDeltaSync: true, usePrioritySync: true, useAdaptiveSync: true }, config);
        // 创建数据压缩器
        if (_this.config.useCompression) {
            _this.dataCompressor = new DataCompressor_1.DataCompressor({
                algorithm: DataCompressor_1.CompressionAlgorithm.LZ_STRING,
                level: DataCompressor_1.CompressionLevel.MEDIUM,
                adaptive: true,
                incremental: {
                    enabled: _this.config.useDeltaSync,
                    maxDepth: 10,
                    includePathInfo: true,
                    compressIncrementalData: true
                }
            });
        }
        // 创建同步优先级管理器
        if (_this.config.usePrioritySync) {
            _this.priorityManager = new SyncPriorityManager_1.SyncPriorityManager({
                useDistancePriority: true,
                distancePriorityWeight: 0.5,
                maxDistance: _this.config.syncDistance,
                useVisibilityPriority: true,
                visibilityPriorityWeight: 0.3,
                useActivityPriority: true,
                activityPriorityWeight: 0.4,
                useImportancePriority: true,
                importancePriorityWeight: 0.2,
                priorityUpdateInterval: _this.config.defaultSyncInterval
            });
        }
        return _this;
    }
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     * @param bandwidthController 带宽控制器
     */
    EntitySyncManager.prototype.initialize = function (localUserId, bandwidthController) {
        this.localUserId = localUserId;
        this.bandwidthController = bandwidthController || null;
        // 创建全局同步区域
        this.addSyncArea({
            type: SyncAreaType.GLOBAL,
            id: 'global',
            priority: 0,
            syncInterval: this.config.defaultSyncInterval,
            enabled: true,
        });
        // 如果启用了增量同步，但没有数据压缩器，则创建一个
        if (this.config.useDeltaSync && !this.dataCompressor) {
            this.dataCompressor = new DataCompressor_1.DataCompressor({
                algorithm: DataCompressor_1.CompressionAlgorithm.INCREMENTAL,
                incremental: {
                    enabled: true,
                    maxDepth: 10,
                    includePathInfo: true,
                    compressIncrementalData: false
                }
            });
        }
        // 初始化同步优先级管理器
        if (this.priorityManager) {
            this.priorityManager.initialize(localUserId);
        }
    };
    /**
     * 启动同步
     * @param interval 同步间隔（毫秒）
     */
    EntitySyncManager.prototype.startSync = function (interval) {
        var _this = this;
        if (interval === void 0) { interval = 100; }
        if (this.syncTimerId !== null) {
            return;
        }
        this.syncTimerId = window.setInterval(function () {
            _this.syncEntities();
        }, interval);
    };
    /**
     * 停止同步
     */
    EntitySyncManager.prototype.stopSync = function () {
        if (this.syncTimerId !== null) {
            clearInterval(this.syncTimerId);
            this.syncTimerId = null;
        }
    };
    /**
     * 添加实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    EntitySyncManager.prototype.addEntity = function (entityId, entity) {
        // 检查实体是否已存在
        if (this.entities.has(entityId)) {
            Debug_1.Debug.warn('EntitySyncManager', "Entity with ID ".concat(entityId, " already exists"));
            return;
        }
        // 检查实体是否有网络实体组件
        var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
        if (!networkEntity) {
            Debug_1.Debug.warn('EntitySyncManager', "Entity with ID ".concat(entityId, " does not have a NetworkEntityComponent"));
            return;
        }
        // 添加到实体映射表
        this.entities.set(entityId, entity);
        // 创建同步状态
        var syncState = {
            entityId: entityId,
            ownerId: networkEntity.ownerId,
            lastSyncTime: Date.now(),
            syncInterval: networkEntity.syncInterval,
            syncPriority: networkEntity.syncPriority,
            syncAreaId: 'global',
            needsSync: false,
            syncDataSize: 0,
            syncCount: 0,
            syncFailCount: 0,
        };
        // 添加到同步状态映射表
        this.entitySyncStates.set(entityId, syncState);
        // 如果使用空间分区，则添加到空间网格
        if (this.config.useSpatialPartitioning) {
            this.updateEntitySpatialCell(entityId, entity);
        }
        // 如果使用优先级同步，则注册到优先级管理器
        if (this.config.usePrioritySync && this.priorityManager) {
            this.priorityManager.registerEntity(entityId, entity);
        }
        // 触发实体添加事件
        this.emit('entityAdded', entityId, entity);
    };
    /**
     * 移除实体
     * @param entityId 实体ID
     */
    EntitySyncManager.prototype.removeEntity = function (entityId) {
        // 检查实体是否存在
        if (!this.entities.has(entityId)) {
            return;
        }
        var entity = this.entities.get(entityId);
        // 从实体映射表中移除
        this.entities.delete(entityId);
        // 从同步状态映射表中移除
        this.entitySyncStates.delete(entityId);
        // 从增量同步缓存中移除
        this.lastSyncDataCache.delete(entityId);
        // 如果使用空间分区，则从空间网格中移除
        if (this.config.useSpatialPartitioning) {
            this.removeEntityFromSpatialGrid(entityId);
        }
        // 如果使用优先级同步，则从优先级管理器中注销
        if (this.config.usePrioritySync && this.priorityManager) {
            this.priorityManager.unregisterEntity(entityId);
        }
        // 触发实体移除事件
        this.emit('entityRemoved', entityId, entity);
    };
    /**
     * 更新实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    EntitySyncManager.prototype.updateEntity = function (entityId, entity) {
        // 检查实体是否存在
        if (!this.entities.has(entityId)) {
            this.addEntity(entityId, entity);
            return;
        }
        // 更新实体
        this.entities.set(entityId, entity);
        // 如果使用空间分区，则更新空间网格
        if (this.config.useSpatialPartitioning) {
            this.updateEntitySpatialCell(entityId, entity);
        }
        // 如果使用优先级同步，则更新优先级
        if (this.config.usePrioritySync && this.priorityManager) {
            this.priorityManager.updateEntityPriority(entityId, entity);
        }
        // 标记实体需要同步
        var syncState = this.entitySyncStates.get(entityId);
        if (syncState) {
            syncState.needsSync = true;
        }
    };
    /**
     * 添加同步区域
     * @param area 同步区域配置
     */
    EntitySyncManager.prototype.addSyncArea = function (area) {
        // 检查区域是否已存在
        if (this.syncAreas.has(area.id)) {
            Debug_1.Debug.warn('EntitySyncManager', "Sync area with ID ".concat(area.id, " already exists"));
            return;
        }
        // 添加到同步区域映射表
        this.syncAreas.set(area.id, __assign(__assign({}, area), { enabled: area.enabled !== undefined ? area.enabled : true }));
        // 触发同步区域添加事件
        this.emit('syncAreaAdded', area.id, area);
    };
    /**
     * 移除同步区域
     * @param areaId 区域ID
     */
    EntitySyncManager.prototype.removeSyncArea = function (areaId) {
        // 检查区域是否存在
        if (!this.syncAreas.has(areaId) || areaId === 'global') {
            return;
        }
        var area = this.syncAreas.get(areaId);
        // 从同步区域映射表中移除
        this.syncAreas.delete(areaId);
        // 将该区域中的实体移动到全局区域
        for (var _i = 0, _a = this.entitySyncStates.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], syncState = _b[1];
            if (syncState.syncAreaId === areaId) {
                syncState.syncAreaId = 'global';
            }
        }
        // 触发同步区域移除事件
        this.emit('syncAreaRemoved', areaId, area);
    };
    /**
     * 更新
     * @param deltaTime 时间增量（毫秒）
     */
    EntitySyncManager.prototype.update = function (deltaTime) {
        // 如果使用优先级同步，则更新优先级管理器
        if (this.config.usePrioritySync && this.priorityManager) {
            this.priorityManager.update(deltaTime, this.entities);
        }
    };
    /**
     * 同步实体
     */
    EntitySyncManager.prototype.syncEntities = function () {
        if (this.isSyncing || !this.localUserId) {
            return;
        }
        this.isSyncing = true;
        try {
            var now = Date.now();
            // 更新同步队列
            this.updateSyncQueue(now);
            // 处理同步队列
            this.processSyncQueue(now);
        }
        finally {
            this.isSyncing = false;
        }
    };
    /**
     * 更新同步队列
     * @param now 当前时间
     */
    EntitySyncManager.prototype.updateSyncQueue = function (now) {
        var _this = this;
        this.syncQueue = [];
        // 遍历所有实体
        for (var _i = 0, _a = this.entitySyncStates.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], syncState = _b[1];
            var entity = this.entities.get(entityId);
            if (!entity)
                continue;
            var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
            if (!networkEntity)
                continue;
            // 只同步本地拥有的实体
            if (networkEntity.ownerId !== this.localUserId)
                continue;
            // 检查是否需要同步
            var timeSinceLastSync = now - syncState.lastSyncTime;
            var shouldSync = syncState.needsSync && timeSinceLastSync >= syncState.syncInterval;
            if (shouldSync) {
                this.syncQueue.push(entityId);
            }
        }
        // 如果使用优先级同步，则按优先级排序
        if (this.config.usePrioritySync) {
            if (this.priorityManager) {
                // 使用优先级管理器获取优先级
                this.syncQueue.sort(function (a, b) {
                    var priorityA = _this.priorityManager.getEntityPriority(a);
                    var priorityB = _this.priorityManager.getEntityPriority(b);
                    // 优先级越高越优先（1最高，0最低）
                    return priorityB - priorityA;
                });
            }
            else {
                // 使用实体自身的优先级
                this.syncQueue.sort(function (a, b) {
                    var stateA = _this.entitySyncStates.get(a);
                    var stateB = _this.entitySyncStates.get(b);
                    if (!stateA || !stateB)
                        return 0;
                    // 优先级越小越优先
                    return stateA.syncPriority - stateB.syncPriority;
                });
            }
        }
    };
    /**
     * 处理同步队列
     * @param now 当前时间
     */
    EntitySyncManager.prototype.processSyncQueue = function (now) {
        // 获取可用带宽
        var availableBandwidth = Number.MAX_SAFE_INTEGER;
        if (this.bandwidthController) {
            var usage = this.bandwidthController.getBandwidthUsage();
            availableBandwidth = usage.uploadLimit - usage.upload;
        }
        // 处理队列中的实体
        var usedBandwidth = 0;
        for (var _i = 0, _a = this.syncQueue; _i < _a.length; _i++) {
            var entityId = _a[_i];
            var entity = this.entities.get(entityId);
            var syncState = this.entitySyncStates.get(entityId);
            if (!entity || !syncState)
                continue;
            // 检查是否超出带宽限制
            if (usedBandwidth >= availableBandwidth) {
                break;
            }
            // 同步实体
            var syncResult = this.syncEntity(entityId, entity, syncState);
            if (syncResult.success) {
                // 更新同步状态
                syncState.lastSyncTime = now;
                syncState.needsSync = false;
                syncState.syncCount++;
                syncState.syncDataSize = syncResult.dataSize;
                // 记录带宽使用
                usedBandwidth += syncResult.dataSize;
                if (this.bandwidthController) {
                    this.bandwidthController.recordUpload(syncResult.dataSize);
                }
            }
            else {
                syncState.syncFailCount++;
            }
        }
    };
    /**
     * 同步单个实体
     * @param entityId 实体ID
     * @param entity 实体
     * @param syncState 同步状态
     * @returns 同步结果
     */
    EntitySyncManager.prototype.syncEntity = function (entityId, entity, syncState) {
        try {
            var networkEntity = entity.getComponent(NetworkEntityComponent_1.NetworkEntityComponent);
            if (!networkEntity) {
                return { success: false, dataSize: 0 };
            }
            // 获取同步数据
            var syncData = networkEntity.getSyncData();
            // 如果使用增量同步，则计算差异
            var finalData = syncData;
            var originalSize = 0;
            var compressedSize = 0;
            if (this.config.useDeltaSync && this.dataCompressor) {
                // 获取上次同步的数据
                var lastSyncData = this.lastSyncDataCache.get(entityId);
                // 创建增量数据
                if (this.dataCompressor.algorithm === DataCompressor_1.CompressionAlgorithm.INCREMENTAL) {
                    // 直接使用增量压缩算法
                    finalData = this.dataCompressor.createIncrementalData(syncData, lastSyncData);
                }
                else {
                    // 手动计算增量
                    finalData = this.createIncrementalData(syncData, lastSyncData);
                }
                // 更新缓存
                this.lastSyncDataCache.set(entityId, syncData);
                // 估算原始大小和压缩后大小
                originalSize = JSON.stringify(syncData).length;
                compressedSize = JSON.stringify(finalData).length;
            }
            else if (this.config.useCompression && this.dataCompressor) {
                // 使用压缩算法压缩数据
                var compressionResult = this.dataCompressor.compress(syncData);
                finalData = compressionResult.data;
                originalSize = compressionResult.originalSize;
                compressedSize = compressionResult.compressedSize;
                // 更新缓存
                this.lastSyncDataCache.set(entityId, syncData);
            }
            else {
                // 不使用压缩或增量同步
                originalSize = JSON.stringify(syncData).length;
                compressedSize = originalSize;
            }
            // 使用压缩后的大小
            var dataSize = compressedSize;
            // 触发实体同步事件
            this.emit('entitySync', entityId, finalData, dataSize);
            // 如果启用了自适应同步，则根据数据大小调整同步间隔
            if (this.config.useAdaptiveSync) {
                this.adjustSyncInterval(entityId, dataSize);
            }
            return { success: true, dataSize: dataSize };
        }
        catch (error) {
            Debug_1.Debug.error('EntitySyncManager', "Failed to sync entity ".concat(entityId, ":"), error);
            return { success: false, dataSize: 0 };
        }
    };
    /**
     * 手动创建增量数据
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 增量数据
     */
    EntitySyncManager.prototype.createIncrementalData = function (newData, oldData) {
        // 如果没有旧数据，则返回完整数据
        if (!oldData) {
            return {
                __incremental: true,
                __complete: true,
                __data: newData
            };
        }
        // 创建增量对象
        var incremental = {
            __incremental: true,
            __version: 1,
            __timestamp: Date.now()
        };
        // 计算差异
        var changes = this.calculateDifferences(newData, oldData);
        // 如果没有变化，则返回空增量
        if (Object.keys(changes).length === 0) {
            incremental.__empty = true;
            return incremental;
        }
        // 合并差异到增量对象
        Object.assign(incremental, changes);
        // 计算增量大小和完整数据大小
        var incrementalSize = JSON.stringify(incremental).length;
        var fullSize = JSON.stringify(newData).length;
        // 如果增量数据比完整数据大，则使用完整数据
        if (incrementalSize >= fullSize) {
            return {
                __incremental: true,
                __complete: true,
                __data: newData
            };
        }
        return incremental;
    };
    /**
     * 计算两个对象之间的差异
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 差异对象
     */
    EntitySyncManager.prototype.calculateDifferences = function (newData, oldData) {
        var differences = {};
        // 如果类型不同，则返回新数据
        if (typeof newData !== typeof oldData) {
            return newData;
        }
        // 如果不是对象，则直接比较
        if (typeof newData !== 'object' || newData === null) {
            return newData !== oldData ? newData : {};
        }
        // 如果是数组，则比较数组
        if (Array.isArray(newData)) {
            // 如果长度不同，则返回新数组
            if (!Array.isArray(oldData) || newData.length !== oldData.length) {
                return newData;
            }
            // 比较数组元素
            var hasChanges = false;
            var arrayChanges = [];
            for (var i = 0; i < newData.length; i++) {
                var elementChanges = this.calculateDifferences(newData[i], oldData[i]);
                if (Object.keys(elementChanges).length > 0) {
                    // 如果有变化，则记录索引和变化
                    arrayChanges.push({
                        index: i,
                        changes: elementChanges
                    });
                    hasChanges = true;
                }
            }
            // 如果有变化，则返回数组变化
            if (hasChanges) {
                return { __array: arrayChanges };
            }
            return {};
        }
        // 比较对象属性
        for (var key in newData) {
            // 如果属性不在旧数据中，则添加
            if (!(key in oldData)) {
                differences[key] = newData[key];
                continue;
            }
            // 递归比较属性值
            var propertyChanges = this.calculateDifferences(newData[key], oldData[key]);
            // 如果有变化，则添加
            if (Object.keys(propertyChanges).length > 0) {
                differences[key] = propertyChanges;
            }
        }
        // 检查删除的属性
        for (var key in oldData) {
            if (!(key in newData)) {
                differences[key] = { __deleted: true };
            }
        }
        return differences;
    };
    /**
     * 应用增量数据
     * @param incrementalData 增量数据
     * @param currentData 当前数据
     * @returns 更新后的数据
     */
    EntitySyncManager.prototype.applyIncrementalData = function (incrementalData, currentData) {
        // 如果不是增量数据，则直接返回
        if (!incrementalData || !incrementalData.__incremental) {
            return incrementalData;
        }
        // 如果是完整数据，则直接使用
        if (incrementalData.__complete) {
            return incrementalData.__data || incrementalData;
        }
        // 如果是空增量，则保持当前状态
        if (incrementalData.__empty) {
            return currentData;
        }
        // 检查增量版本
        var version = incrementalData.__version || 1;
        // 根据版本应用增量
        if (version === 1) {
            // 版本1：简单属性覆盖
            var newData = __assign({}, currentData);
            for (var key in incrementalData) {
                if (!key.startsWith('__')) {
                    // 如果是删除标记，则删除属性
                    if (incrementalData[key] && typeof incrementalData[key] === 'object' && incrementalData[key].__deleted) {
                        delete newData[key];
                    }
                    else if (incrementalData[key] && typeof incrementalData[key] === 'object' && incrementalData[key].__array) {
                        // 如果是数组变化，则应用数组变化
                        if (!Array.isArray(newData[key])) {
                            newData[key] = [];
                        }
                        // 应用数组变化
                        for (var _i = 0, _a = incrementalData[key].__array; _i < _a.length; _i++) {
                            var change = _a[_i];
                            var index = change.index, changes = change.changes;
                            // 确保数组长度足够
                            while (newData[key].length <= index) {
                                newData[key].push(undefined);
                            }
                            // 应用变化
                            if (typeof changes === 'object' && Object.keys(changes).length > 0) {
                                newData[key][index] = this.applyIncrementalData(changes, newData[key][index]);
                            }
                            else {
                                newData[key][index] = changes;
                            }
                        }
                    }
                    else if (incrementalData[key] && typeof incrementalData[key] === 'object' && Object.keys(incrementalData[key]).length > 0) {
                        // 如果是对象变化，则递归应用
                        newData[key] = this.applyIncrementalData(incrementalData[key], newData[key] || {});
                    }
                    else {
                        // 否则直接替换
                        newData[key] = incrementalData[key];
                    }
                }
            }
            return newData;
        }
        else {
            // 如果是未知版本，则使用数据压缩器
            if (this.dataCompressor) {
                return this.dataCompressor.applyIncrementalData(incrementalData, currentData);
            }
            // 如果没有数据压缩器，则返回原始数据
            return currentData;
        }
    };
    /**
     * 调整实体同步间隔
     * @param entityId 实体ID
     * @param dataSize 数据大小
     */
    EntitySyncManager.prototype.adjustSyncInterval = function (entityId, dataSize) {
        var syncState = this.entitySyncStates.get(entityId);
        if (!syncState) {
            return;
        }
        // 如果使用优先级同步，则使用优先级管理器的推荐同步间隔
        if (this.config.usePrioritySync && this.priorityManager) {
            var recommendedInterval = this.priorityManager.getEntityRecommendedSyncInterval(entityId);
            // 根据数据大小进行微调
            var sizeRatio = Math.min(1, dataSize / 10000); // 10KB作为基准
            var sizeAdjustment = Math.round(sizeRatio * 100); // 最多增加100ms
            // 计算最终间隔
            var finalInterval = recommendedInterval + sizeAdjustment;
            // 限制在配置范围内
            syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(finalInterval, this.config.maxSyncInterval));
        }
        else {
            // 使用基于数据大小的简单自适应策略
            var sizeRatio = Math.min(1, dataSize / 10000); // 10KB作为基准
            var intervalRange = this.config.maxSyncInterval - this.config.minSyncInterval;
            var newInterval = Math.round(this.config.minSyncInterval + sizeRatio * intervalRange);
            // 限制在配置范围内
            syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(newInterval, this.config.maxSyncInterval));
        }
    };
    /**
     * 更新实体空间单元格
     * @param entityId 实体ID
     * @param entity 实体
     */
    EntitySyncManager.prototype.updateEntitySpatialCell = function (entityId, entity) {
        if (!this.config.useSpatialPartitioning) {
            return;
        }
        var transform = entity.getComponent('Transform');
        if (!transform) {
            return;
        }
        // 移除实体从旧的单元格
        this.removeEntityFromSpatialGrid(entityId);
        // 计算新的单元格坐标
        var cellX = Math.floor(transform.getPosition().x / this.config.spatialCellSize);
        var cellY = Math.floor(transform.getPosition().y / this.config.spatialCellSize);
        var cellZ = Math.floor(transform.getPosition().z / this.config.spatialCellSize);
        // 生成单元格ID
        var cellId = "".concat(cellX, ",").concat(cellY, ",").concat(cellZ);
        // 添加实体到新的单元格
        if (!this.spatialGrid.has(cellId)) {
            this.spatialGrid.set(cellId, new Set());
        }
        this.spatialGrid.get(cellId).add(entityId);
    };
    /**
     * 从空间网格中移除实体
     * @param entityId 实体ID
     */
    EntitySyncManager.prototype.removeEntityFromSpatialGrid = function (entityId) {
        for (var _i = 0, _a = this.spatialGrid.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], cellId = _b[0], entities = _b[1];
            if (entities.has(entityId)) {
                entities.delete(entityId);
                // 如果单元格为空，则移除
                if (entities.size === 0) {
                    this.spatialGrid.delete(cellId);
                }
            }
        }
    };
    /**
     * 获取实体
     * @param entityId 实体ID
     * @returns 实体
     */
    EntitySyncManager.prototype.getEntity = function (entityId) {
        return this.entities.get(entityId);
    };
    /**
     * 获取实体同步状态
     * @param entityId 实体ID
     * @returns 同步状态
     */
    EntitySyncManager.prototype.getEntitySyncState = function (entityId) {
        return this.entitySyncStates.get(entityId);
    };
    /**
     * 获取同步区域
     * @param areaId 区域ID
     * @returns 同步区域
     */
    EntitySyncManager.prototype.getSyncArea = function (areaId) {
        return this.syncAreas.get(areaId);
    };
    /**
     * 设置实体同步优先级
     * @param entityId 实体ID
     * @param priority 优先级
     */
    EntitySyncManager.prototype.setEntitySyncPriority = function (entityId, priority) {
        var syncState = this.entitySyncStates.get(entityId);
        if (syncState) {
            syncState.syncPriority = priority;
        }
    };
    /**
     * 设置实体同步间隔
     * @param entityId 实体ID
     * @param interval 同步间隔（毫秒）
     */
    EntitySyncManager.prototype.setEntitySyncInterval = function (entityId, interval) {
        var syncState = this.entitySyncStates.get(entityId);
        if (syncState) {
            syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(interval, this.config.maxSyncInterval));
        }
    };
    /**
     * 标记实体需要同步
     * @param entityId 实体ID
     */
    EntitySyncManager.prototype.markEntityForSync = function (entityId) {
        var syncState = this.entitySyncStates.get(entityId);
        if (syncState) {
            syncState.needsSync = true;
        }
    };
    /**
     * 销毁管理器
     */
    EntitySyncManager.prototype.dispose = function () {
        this.stopSync();
        this.entities.clear();
        this.entitySyncStates.clear();
        this.syncAreas.clear();
        this.spatialGrid.clear();
        this.lastSyncDataCache.clear();
        this.syncQueue = [];
        this.removeAllListeners();
        // 清理数据压缩器
        if (this.dataCompressor) {
            this.dataCompressor = null;
        }
        // 清理优先级管理器
        if (this.priorityManager) {
            this.priorityManager.dispose();
            this.priorityManager = null;
        }
    };
    return EntitySyncManager;
}(EventEmitter_1.EventEmitter));
exports.EntitySyncManager = EntitySyncManager;
