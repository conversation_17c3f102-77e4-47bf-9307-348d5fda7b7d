"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerSoftBodyNodes = exports.CutSoftBodyNode = exports.CreateJellyNode = exports.CreateBalloonNode = exports.CreateRopeNode = exports.CreateClothNode = void 0;
var FlowNode_1 = require("../nodes/FlowNode");
var Node_1 = require("../nodes/Node");
var Vector3_1 = require("../../math/Vector3");
var SoftBodySystem_1 = require("../../physics/softbody/SoftBodySystem");
/**
 * 创建布料节点
 * 创建布料软体
 */
var CreateClothNode = /** @class */ (function (_super) {
    __extends(CreateClothNode, _super);
    function CreateClothNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateClothNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'width',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '宽度',
            defaultValue: 1
        });
        this.addInput({
            name: 'height',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '高度',
            defaultValue: 1
        });
        this.addInput({
            name: 'segments',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '分段数',
            defaultValue: 10
        });
        this.addInput({
            name: 'position',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '位置',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        this.addInput({
            name: 'fixedCorners',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否固定角落',
            defaultValue: true
        });
        this.addInput({
            name: 'mass',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '质量',
            defaultValue: 1
        });
        this.addInput({
            name: 'stiffness',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '刚度',
            defaultValue: 0.9
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建的实体'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateClothNode.prototype.execute = function () {
        // 获取输入值
        var width = this.getInputValue('width');
        var height = this.getInputValue('height');
        var segments = this.getInputValue('segments');
        var position = this.getInputValue('position');
        var fixedCorners = this.getInputValue('fixedCorners');
        var mass = this.getInputValue('mass');
        var stiffness = this.getInputValue('stiffness');
        // 获取软体物理系统
        var softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem_1.SoftBodySystem);
        if (!softBodySystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 创建布料实体
        var entity = softBodySystem.createCloth({
            width: width,
            height: height,
            segments: segments,
            position: position,
            fixedCorners: fixedCorners,
            mass: mass,
            stiffness: stiffness
        });
        // 设置输出值
        this.setOutputValue('entity', entity);
        this.setOutputValue('success', !!entity);
        // 触发输出流程
        this.triggerFlow('flow');
        return !!entity;
    };
    return CreateClothNode;
}(FlowNode_1.FlowNode));
exports.CreateClothNode = CreateClothNode;
/**
 * 创建绳索节点
 * 创建绳索软体
 */
var CreateRopeNode = /** @class */ (function (_super) {
    __extends(CreateRopeNode, _super);
    function CreateRopeNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateRopeNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'start',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '起点',
            defaultValue: new Vector3_1.Vector3(0, 1, 0)
        });
        this.addInput({
            name: 'end',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '终点',
            defaultValue: new Vector3_1.Vector3(0, -1, 0)
        });
        this.addInput({
            name: 'segments',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '分段数',
            defaultValue: 10
        });
        this.addInput({
            name: 'fixedEnds',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.INPUT,
            description: '是否固定两端',
            defaultValue: true
        });
        this.addInput({
            name: 'mass',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '质量',
            defaultValue: 1
        });
        this.addInput({
            name: 'stiffness',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '刚度',
            defaultValue: 0.9
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建的实体'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateRopeNode.prototype.execute = function () {
        // 获取输入值
        var start = this.getInputValue('start');
        var end = this.getInputValue('end');
        var segments = this.getInputValue('segments');
        var fixedEnds = this.getInputValue('fixedEnds');
        var mass = this.getInputValue('mass');
        var stiffness = this.getInputValue('stiffness');
        // 获取软体物理系统
        var softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem_1.SoftBodySystem);
        if (!softBodySystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 创建绳索实体
        var entity = softBodySystem.createRope({
            start: start,
            end: end,
            segments: segments,
            fixedEnds: fixedEnds,
            mass: mass,
            stiffness: stiffness
        });
        // 设置输出值
        this.setOutputValue('entity', entity);
        this.setOutputValue('success', !!entity);
        // 触发输出流程
        this.triggerFlow('flow');
        return !!entity;
    };
    return CreateRopeNode;
}(FlowNode_1.FlowNode));
exports.CreateRopeNode = CreateRopeNode;
/**
 * 创建气球节点
 * 创建气球软体
 */
var CreateBalloonNode = /** @class */ (function (_super) {
    __extends(CreateBalloonNode, _super);
    function CreateBalloonNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateBalloonNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'radius',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '半径',
            defaultValue: 0.5
        });
        this.addInput({
            name: 'position',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '位置',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        this.addInput({
            name: 'segments',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '分段数',
            defaultValue: 16
        });
        this.addInput({
            name: 'pressure',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '内部压力',
            defaultValue: 100
        });
        this.addInput({
            name: 'mass',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '质量',
            defaultValue: 1
        });
        this.addInput({
            name: 'stiffness',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '刚度',
            defaultValue: 0.9
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建的实体'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateBalloonNode.prototype.execute = function () {
        // 获取输入值
        var radius = this.getInputValue('radius');
        var position = this.getInputValue('position');
        var segments = this.getInputValue('segments');
        var pressure = this.getInputValue('pressure');
        var mass = this.getInputValue('mass');
        var stiffness = this.getInputValue('stiffness');
        // 获取软体物理系统
        var softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem_1.SoftBodySystem);
        if (!softBodySystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 创建气球实体
        var entity = softBodySystem.createBalloon({
            radius: radius,
            position: position,
            segments: segments,
            pressure: pressure,
            mass: mass,
            stiffness: stiffness
        });
        // 设置输出值
        this.setOutputValue('entity', entity);
        this.setOutputValue('success', !!entity);
        // 触发输出流程
        this.triggerFlow('flow');
        return !!entity;
    };
    return CreateBalloonNode;
}(FlowNode_1.FlowNode));
exports.CreateBalloonNode = CreateBalloonNode;
/**
 * 创建果冻节点
 * 创建果冻软体
 */
var CreateJellyNode = /** @class */ (function (_super) {
    __extends(CreateJellyNode, _super);
    function CreateJellyNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CreateJellyNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'size',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '尺寸',
            defaultValue: new Vector3_1.Vector3(1, 1, 1)
        });
        this.addInput({
            name: 'position',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '位置',
            defaultValue: new Vector3_1.Vector3(0, 0, 0)
        });
        this.addInput({
            name: 'resolution',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '分辨率',
            defaultValue: 8
        });
        this.addInput({
            name: 'mass',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '质量',
            defaultValue: 1
        });
        this.addInput({
            name: 'stiffness',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '刚度',
            defaultValue: 0.8
        });
        this.addInput({
            name: 'damping',
            type: Node_1.SocketType.DATA,
            dataType: 'number',
            direction: Node_1.SocketDirection.INPUT,
            description: '阻尼',
            defaultValue: 0.3
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '创建的实体'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CreateJellyNode.prototype.execute = function () {
        // 获取输入值
        var size = this.getInputValue('size');
        var position = this.getInputValue('position');
        var resolution = this.getInputValue('resolution');
        var mass = this.getInputValue('mass');
        var stiffness = this.getInputValue('stiffness');
        var damping = this.getInputValue('damping');
        // 获取软体物理系统
        var softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem_1.SoftBodySystem);
        if (!softBodySystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 创建果冻实体
        var entity = softBodySystem.createJelly({
            size: size,
            position: position,
            resolution: resolution,
            mass: mass,
            stiffness: stiffness,
            damping: damping
        });
        // 设置输出值
        this.setOutputValue('entity', entity);
        this.setOutputValue('success', !!entity);
        // 触发输出流程
        this.triggerFlow('flow');
        return !!entity;
    };
    return CreateJellyNode;
}(FlowNode_1.FlowNode));
exports.CreateJellyNode = CreateJellyNode;
/**
 * 软体切割节点
 * 切割软体
 */
var CutSoftBodyNode = /** @class */ (function (_super) {
    __extends(CutSoftBodyNode, _super);
    function CutSoftBodyNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    CutSoftBodyNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '输入流程'
        });
        // 添加输入数据插槽
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.INPUT,
            description: '软体实体'
        });
        this.addInput({
            name: 'cutPoint',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '切割点'
        });
        this.addInput({
            name: 'cutNormal',
            type: Node_1.SocketType.DATA,
            dataType: 'Vector3',
            direction: Node_1.SocketDirection.INPUT,
            description: '切割面法线'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '输出流程'
        });
        this.addOutput({
            name: 'success',
            type: Node_1.SocketType.DATA,
            dataType: 'boolean',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '是否成功'
        });
        this.addOutput({
            name: 'newEntity',
            type: Node_1.SocketType.DATA,
            dataType: 'Entity',
            direction: Node_1.SocketDirection.OUTPUT,
            description: '切割后新创建的实体'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    CutSoftBodyNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var cutPoint = this.getInputValue('cutPoint');
        var cutNormal = this.getInputValue('cutNormal');
        // 检查输入值是否有效
        if (!entity || !cutPoint || !cutNormal) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 获取软体物理系统
        var softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem_1.SoftBodySystem);
        if (!softBodySystem) {
            this.setOutputValue('success', false);
            this.triggerFlow('flow');
            return false;
        }
        // 切割软体
        var result = softBodySystem.cutSoftBody(entity, cutPoint, cutNormal);
        // 设置输出值
        this.setOutputValue('success', result.success);
        this.setOutputValue('newEntity', result.newEntity);
        // 触发输出流程
        this.triggerFlow('flow');
        return result.success;
    };
    return CutSoftBodyNode;
}(FlowNode_1.FlowNode));
exports.CutSoftBodyNode = CutSoftBodyNode;
/**
 * 注册软体物理节点
 * @param registry 节点注册表
 */
function registerSoftBodyNodes(registry) {
    // 注册创建布料节点
    registry.registerNodeType({
        type: 'physics/softbody/createCloth',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreateClothNode,
        label: '创建布料',
        description: '创建布料软体',
        icon: 'cloth',
        color: '#9C27B0',
        tags: ['physics', 'softbody', 'cloth']
    });
    // 注册创建绳索节点
    registry.registerNodeType({
        type: 'physics/softbody/createRope',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreateRopeNode,
        label: '创建绳索',
        description: '创建绳索软体',
        icon: 'rope',
        color: '#9C27B0',
        tags: ['physics', 'softbody', 'rope']
    });
    // 注册创建气球节点
    registry.registerNodeType({
        type: 'physics/softbody/createBalloon',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreateBalloonNode,
        label: '创建气球',
        description: '创建气球软体',
        icon: 'balloon',
        color: '#9C27B0',
        tags: ['physics', 'softbody', 'balloon']
    });
    // 注册创建果冻节点
    registry.registerNodeType({
        type: 'physics/softbody/createJelly',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CreateJellyNode,
        label: '创建果冻',
        description: '创建果冻软体',
        icon: 'jelly',
        color: '#9C27B0',
        tags: ['physics', 'softbody', 'jelly']
    });
    // 注册软体切割节点
    registry.registerNodeType({
        type: 'physics/softbody/cut',
        category: Node_1.NodeCategory.PHYSICS,
        constructor: CutSoftBodyNode,
        label: '切割软体',
        description: '切割软体',
        icon: 'cut',
        color: '#9C27B0',
        tags: ['physics', 'softbody', 'cut']
    });
}
exports.registerSoftBodyNodes = registerSoftBodyNodes;
