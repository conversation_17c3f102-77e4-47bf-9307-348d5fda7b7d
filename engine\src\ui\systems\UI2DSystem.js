"use strict";
/**
 * UI2DSystem.ts
 *
 * 2D UI系统，管理2D界面元素
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI2DSystem = void 0;
var System_1 = require("../../core/System");
var UI2DComponent_1 = require("../components/UI2DComponent");
var UIComponent_1 = require("../components/UIComponent");
/**
 * 2D UI系统
 * 管理2D界面元素
 */
var UI2DSystem = /** @class */ (function (_super) {
    __extends(UI2DSystem, _super);
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config 2D UI系统配置
     */
    function UI2DSystem(world, uiSystem, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入优先级（默认为0）
        _super.call(this, 0) || this;
        // 保存世界引用（可以在子类中添加）
        _this.world = world;
        _this.uiSystem = uiSystem;
        _this.config = {
            debug: config.debug || false,
            autoCreateContainer: config.autoCreateContainer !== undefined ? config.autoCreateContainer : true,
            containerId: config.containerId || 'ui-2d-container',
            defaultFont: config.defaultFont || 'Arial, sans-serif',
            defaultFontSize: config.defaultFontSize || 16,
            defaultTextColor: config.defaultTextColor || '#000000',
            defaultBackgroundColor: config.defaultBackgroundColor || 'transparent',
            defaultBorderColor: config.defaultBorderColor || 'transparent',
            defaultBorderWidth: config.defaultBorderWidth || 0,
            defaultBorderRadius: config.defaultBorderRadius || 0,
            defaultPadding: config.defaultPadding || 0,
            defaultMargin: config.defaultMargin || 0
        };
        // 如果自动创建容器
        if (_this.config.autoCreateContainer) {
            _this.createContainer();
        }
        return _this;
    }
    /**
     * 创建HTML容器
     */
    UI2DSystem.prototype.createContainer = function () {
        // 检查容器是否已存在
        var container = document.getElementById(this.config.containerId);
        // 如果不存在，则创建
        if (!container) {
            container = document.createElement('div');
            container.id = this.config.containerId;
            container.style.position = 'absolute';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.pointerEvents = 'none';
            container.style.overflow = 'hidden';
            container.style.zIndex = '1000';
            document.body.appendChild(container);
        }
        this.container = container;
    };
    /**
     * 创建2D UI元素
     * @param entity 实体
     * @param type UI元素类型
     * @param props UI元素属性
     * @returns 创建的2D UI组件
     */
    UI2DSystem.prototype.createUIElement = function (entity, type, props) {
        if (props === void 0) { props = {}; }
        // 合并默认属性和提供的属性
        var mergedProps = __assign({ type: type, fontFamily: this.config.defaultFont, fontSize: this.config.defaultFontSize, textColor: this.config.defaultTextColor, backgroundColor: this.config.defaultBackgroundColor, borderColor: this.config.defaultBorderColor, borderWidth: this.config.defaultBorderWidth, borderRadius: this.config.defaultBorderRadius, padding: this.config.defaultPadding, margin: this.config.defaultMargin }, props);
        // 创建2D UI组件
        var component = new UI2DComponent_1.UI2DComponent(entity, mergedProps);
        // 注册到UI系统
        this.uiSystem.registerUIComponent(entity, component);
        // 如果有容器，将HTML元素添加到容器
        if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
            this.container.appendChild(component.htmlElement);
        }
        return component;
    };
    /**
     * 创建按钮
     * @param entity 实体
     * @param text 按钮文本
     * @param props 按钮属性
     * @returns 创建的按钮组件
     */
    UI2DSystem.prototype.createButton = function (entity, text, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.BUTTON, __assign({ textContent: text, backgroundColor: props.backgroundColor || '#f0f0f0', borderColor: props.borderColor || '#cccccc', borderWidth: props.borderWidth || 1, borderRadius: props.borderRadius || 4, padding: props.padding || 8 }, props));
    };
    /**
     * 创建文本
     * @param entity 实体
     * @param text 文本内容
     * @param props 文本属性
     * @returns 创建的文本组件
     */
    UI2DSystem.prototype.createText = function (entity, text, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.TEXT, __assign({ textContent: text }, props));
    };
    /**
     * 创建图像
     * @param entity 实体
     * @param src 图像源
     * @param props 图像属性
     * @returns 创建的图像组件
     */
    UI2DSystem.prototype.createImage = function (entity, src, props) {
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.IMAGE, props);
        // 设置图像源
        if (component.htmlElement instanceof HTMLImageElement) {
            component.htmlElement.src = src;
        }
        return component;
    };
    /**
     * 创建输入框
     * @param entity 实体
     * @param placeholder 占位文本
     * @param props 输入框属性
     * @returns 创建的输入框组件
     */
    UI2DSystem.prototype.createInput = function (entity, placeholder, props) {
        if (placeholder === void 0) { placeholder = ''; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.INPUT, __assign({ backgroundColor: props.backgroundColor || '#ffffff', borderColor: props.borderColor || '#cccccc', borderWidth: props.borderWidth || 1, borderRadius: props.borderRadius || 4, padding: props.padding || 8 }, props));
        // 设置占位文本
        if (component.htmlElement instanceof HTMLInputElement) {
            component.htmlElement.placeholder = placeholder;
        }
        return component;
    };
    /**
     * 创建复选框
     * @param entity 实体
     * @param label 标签文本
     * @param checked 是否选中
     * @param props 复选框属性
     * @returns 创建的复选框组件
     */
    UI2DSystem.prototype.createCheckbox = function (entity, label, checked, props) {
        if (label === void 0) { label = ''; }
        if (checked === void 0) { checked = false; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.CHECKBOX, props);
        // 设置选中状态
        if (component.htmlElement instanceof HTMLInputElement) {
            component.htmlElement.checked = checked;
            // 创建标签
            if (label) {
                var labelElement = document.createElement('label');
                labelElement.textContent = label;
                labelElement.style.marginLeft = '5px';
                // 创建容器
                var container = document.createElement('div');
                container.style.display = 'flex';
                container.style.alignItems = 'center';
                // 将复选框和标签添加到容器
                container.appendChild(component.htmlElement);
                container.appendChild(labelElement);
                // 替换原始元素
                if (component.htmlElement.parentElement) {
                    component.htmlElement.parentElement.replaceChild(container, component.htmlElement);
                }
                // 更新组件的HTML元素
                component.htmlElement = container;
            }
        }
        return component;
    };
    /**
     * 创建滑块
     * @param entity 实体
     * @param min 最小值
     * @param max 最大值
     * @param value 当前值
     * @param props 滑块属性
     * @returns 创建的滑块组件
     */
    UI2DSystem.prototype.createSlider = function (entity, min, max, value, props) {
        if (min === void 0) { min = 0; }
        if (max === void 0) { max = 100; }
        if (value === void 0) { value = 50; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.SLIDER, props);
        // 设置滑块属性
        if (component.htmlElement instanceof HTMLInputElement) {
            component.htmlElement.min = min.toString();
            component.htmlElement.max = max.toString();
            component.htmlElement.value = value.toString();
        }
        return component;
    };
    /**
     * 创建下拉框
     * @param entity 实体
     * @param options 选项列表
     * @param selectedIndex 选中项索引
     * @param props 下拉框属性
     * @returns 创建的下拉框组件
     */
    UI2DSystem.prototype.createDropdown = function (entity, options, selectedIndex, props) {
        if (options === void 0) { options = []; }
        if (selectedIndex === void 0) { selectedIndex = 0; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.DROPDOWN, __assign({ backgroundColor: props.backgroundColor || '#ffffff', borderColor: props.borderColor || '#cccccc', borderWidth: props.borderWidth || 1, borderRadius: props.borderRadius || 4, padding: props.padding || 8 }, props));
        // 添加选项
        if (component.htmlElement instanceof HTMLSelectElement) {
            options.forEach(function (optionText, index) {
                var _a;
                var option = document.createElement('option');
                option.value = index.toString();
                option.text = optionText;
                option.selected = index === selectedIndex;
                (_a = component.htmlElement) === null || _a === void 0 ? void 0 : _a.appendChild(option);
            });
        }
        return component;
    };
    /**
     * 创建面板
     * @param entity 实体
     * @param props 面板属性
     * @returns 创建的面板组件
     */
    UI2DSystem.prototype.createPanel = function (entity, props) {
        if (props === void 0) { props = {}; }
        return this.createUIElement(entity, UIComponent_1.UIComponentType.PANEL, __assign({ backgroundColor: props.backgroundColor || '#ffffff', borderColor: props.borderColor || '#cccccc', borderWidth: props.borderWidth || 1, borderRadius: props.borderRadius || 4, padding: props.padding || 16 }, props));
    };
    /**
     * 创建窗口
     * @param entity 实体
     * @param title 窗口标题
     * @param props 窗口属性
     * @returns 创建的窗口组件
     */
    UI2DSystem.prototype.createWindow = function (entity, title, props) {
        if (title === void 0) { title = ''; }
        if (props === void 0) { props = {}; }
        var component = this.createUIElement(entity, UIComponent_1.UIComponentType.WINDOW, __assign({ backgroundColor: props.backgroundColor || '#ffffff', borderColor: props.borderColor || '#cccccc', borderWidth: props.borderWidth || 1, borderRadius: props.borderRadius || 4 }, props));
        // 如果有标题，创建标题栏
        if (title && component.htmlElement) {
            // 创建标题栏
            var titleBar = document.createElement('div');
            titleBar.style.padding = '8px';
            titleBar.style.backgroundColor = '#f0f0f0';
            titleBar.style.borderBottom = '1px solid #cccccc';
            titleBar.style.borderTopLeftRadius = "".concat(component.borderRadius, "px");
            titleBar.style.borderTopRightRadius = "".concat(component.borderRadius, "px");
            titleBar.style.fontWeight = 'bold';
            titleBar.textContent = title;
            // 创建内容区域
            var content = document.createElement('div');
            content.style.padding = '16px';
            // 将原始内容移动到内容区域
            while (component.htmlElement.firstChild) {
                content.appendChild(component.htmlElement.firstChild);
            }
            // 添加标题栏和内容区域
            component.htmlElement.appendChild(titleBar);
            component.htmlElement.appendChild(content);
        }
        return component;
    };
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
     */
    UI2DSystem.prototype.update = function (_deltaTime) {
        // 2D UI系统不需要额外的更新逻辑，因为UI系统会更新所有UI组件
    };
    /**
     * 渲染系统
     */
    UI2DSystem.prototype.render = function () {
        // 2D UI系统不需要额外的渲染逻辑，因为UI系统会渲染所有UI组件
    };
    /**
     * 销毁系统
     */
    UI2DSystem.prototype.dispose = function () {
        // 移除容器
        if (this.container && this.container.parentElement) {
            this.container.parentElement.removeChild(this.container);
        }
        this.container = undefined;
    };
    return UI2DSystem;
}(System_1.System));
exports.UI2DSystem = UI2DSystem;
