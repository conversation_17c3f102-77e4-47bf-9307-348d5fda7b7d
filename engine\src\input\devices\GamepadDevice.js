"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GamepadDevice = void 0;
/**
 * 游戏手柄输入设备
 */
var InputDevice_1 = require("../InputDevice");
/**
 * 游戏手柄输入设备
 */
var GamepadDevice = /** @class */ (function (_super) {
    __extends(GamepadDevice, _super);
    /**
     * 创建游戏手柄输入设备
     */
    function GamepadDevice() {
        var _this = _super.call(this, 'gamepad') || this;
        /** 游戏手柄映射 */
        _this.gamepads = new Map();
        /** 游戏手柄事件处理器 */
        _this.gamepadEventHandlers = {};
        // 初始化事件处理器
        _this.initEventHandlers();
        return _this;
    }
    /**
     * 初始化事件处理器
     */
    GamepadDevice.prototype.initEventHandlers = function () {
        // 游戏手柄连接事件
        this.gamepadEventHandlers.gamepadconnected = this.handleGamepadConnected.bind(this);
        // 游戏手柄断开事件
        this.gamepadEventHandlers.gamepaddisconnected = this.handleGamepadDisconnected.bind(this);
    };
    /**
     * 初始化设备
     */
    GamepadDevice.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 添加事件监听器
        this.addEventListeners();
        // 初始化已连接的游戏手柄
        this.initGamepads();
        _super.prototype.initialize.call(this);
    };
    /**
     * 销毁设备
     */
    GamepadDevice.prototype.destroy = function () {
        if (this.destroyed)
            return;
        // 移除事件监听器
        this.removeEventListeners();
        // 清空游戏手柄映射
        this.gamepads.clear();
        _super.prototype.destroy.call(this);
    };
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    GamepadDevice.prototype.update = function (deltaTime) {
        if (!this.initialized || this.destroyed)
            return;
        // 更新游戏手柄状态
        this.updateGamepads();
        _super.prototype.update.call(this, deltaTime);
    };
    /**
     * 添加事件监听器
     */
    GamepadDevice.prototype.addEventListeners = function () {
        // 添加游戏手柄事件监听器
        for (var _i = 0, _a = Object.entries(this.gamepadEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_1 = _b[0], handler = _b[1];
            window.addEventListener(event_1, handler);
        }
    };
    /**
     * 移除事件监听器
     */
    GamepadDevice.prototype.removeEventListeners = function () {
        // 移除游戏手柄事件监听器
        for (var _i = 0, _a = Object.entries(this.gamepadEventHandlers); _i < _a.length; _i++) {
            var _b = _a[_i], event_2 = _b[0], handler = _b[1];
            window.removeEventListener(event_2, handler);
        }
    };
    /**
     * 初始化已连接的游戏手柄
     */
    GamepadDevice.prototype.initGamepads = function () {
        // 获取所有游戏手柄
        var gamepads = navigator.getGamepads ? navigator.getGamepads() : [];
        // 初始化游戏手柄
        for (var _i = 0, gamepads_1 = gamepads; _i < gamepads_1.length; _i++) {
            var gamepad = gamepads_1[_i];
            if (!gamepad)
                continue;
            this.addGamepad(gamepad);
        }
    };
    /**
     * 更新游戏手柄状态
     */
    GamepadDevice.prototype.updateGamepads = function () {
        // 获取所有游戏手柄
        var gamepads = navigator.getGamepads ? navigator.getGamepads() : [];
        // 更新游戏手柄状态
        for (var _i = 0, gamepads_2 = gamepads; _i < gamepads_2.length; _i++) {
            var gamepad = gamepads_2[_i];
            if (!gamepad)
                continue;
            // 更新游戏手柄映射
            this.gamepads.set(gamepad.index, gamepad);
            // 更新按钮状态
            for (var i = 0; i < gamepad.buttons.length; i++) {
                var button = gamepad.buttons[i];
                var buttonKey = "".concat(gamepad.index, ":button:").concat(i);
                var pressed = button.pressed || button.value > 0.5;
                var wasPressed = this.getValue(buttonKey) || false;
                // 更新按钮状态
                this.setValue(buttonKey, pressed);
                this.setValue("".concat(gamepad.index, ":button:").concat(i, ":value"), button.value);
                // 触发按钮事件
                if (pressed && !wasPressed) {
                    this.eventEmitter.emit("".concat(buttonKey, ":down"), {
                        gamepad: gamepad.index,
                        button: i,
                        value: button.value
                    });
                }
                else if (!pressed && wasPressed) {
                    this.eventEmitter.emit("".concat(buttonKey, ":up"), {
                        gamepad: gamepad.index,
                        button: i,
                        value: button.value
                    });
                }
            }
            // 更新轴状态
            for (var i = 0; i < gamepad.axes.length; i++) {
                var axisKey = "".concat(gamepad.index, ":axis:").concat(i);
                var value = gamepad.axes[i];
                var oldValue = this.getValue(axisKey) || 0;
                // 更新轴状态
                this.setValue(axisKey, value);
                // 触发轴事件
                if (value !== oldValue) {
                    this.eventEmitter.emit("".concat(axisKey, ":change"), {
                        gamepad: gamepad.index,
                        axis: i,
                        value: value
                    });
                }
            }
        }
    };
    /**
     * 添加游戏手柄
     * @param gamepad 游戏手柄
     */
    GamepadDevice.prototype.addGamepad = function (gamepad) {
        // 添加游戏手柄到映射
        this.gamepads.set(gamepad.index, gamepad);
        // 初始化按钮状态
        for (var i = 0; i < gamepad.buttons.length; i++) {
            var button = gamepad.buttons[i];
            this.setValue("".concat(gamepad.index, ":button:").concat(i), button.pressed);
            this.setValue("".concat(gamepad.index, ":button:").concat(i, ":value"), button.value);
        }
        // 初始化轴状态
        for (var i = 0; i < gamepad.axes.length; i++) {
            this.setValue("".concat(gamepad.index, ":axis:").concat(i), gamepad.axes[i]);
        }
        // 触发游戏手柄连接事件
        this.eventEmitter.emit('connected', {
            gamepad: gamepad.index,
            id: gamepad.id,
            mapping: gamepad.mapping
        });
    };
    /**
     * 移除游戏手柄
     * @param gamepad 游戏手柄
     */
    GamepadDevice.prototype.removeGamepad = function (gamepad) {
        // 从映射中移除游戏手柄
        this.gamepads.delete(gamepad.index);
        // 触发游戏手柄断开事件
        this.eventEmitter.emit('disconnected', {
            gamepad: gamepad.index,
            id: gamepad.id,
            mapping: gamepad.mapping
        });
    };
    /**
     * 处理游戏手柄连接事件
     * @param event 游戏手柄事件
     */
    GamepadDevice.prototype.handleGamepadConnected = function (event) {
        this.addGamepad(event.gamepad);
    };
    /**
     * 处理游戏手柄断开事件
     * @param event 游戏手柄事件
     */
    GamepadDevice.prototype.handleGamepadDisconnected = function (event) {
        this.removeGamepad(event.gamepad);
    };
    /**
     * 获取游戏手柄
     * @param index 游戏手柄索引
     * @returns 游戏手柄
     */
    GamepadDevice.prototype.getGamepad = function (index) {
        return this.gamepads.get(index);
    };
    /**
     * 获取所有游戏手柄
     * @returns 游戏手柄列表
     */
    GamepadDevice.prototype.getGamepads = function () {
        return Array.from(this.gamepads.values());
    };
    /**
     * 检查游戏手柄按钮是否按下
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    GamepadDevice.prototype.isButtonPressed = function (gamepadIndex, buttonIndex) {
        return !!this.getValue("".concat(gamepadIndex, ":button:").concat(buttonIndex));
    };
    /**
     * 获取游戏手柄按钮值
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 按钮值
     */
    GamepadDevice.prototype.getButtonValue = function (gamepadIndex, buttonIndex) {
        return this.getValue("".concat(gamepadIndex, ":button:").concat(buttonIndex, ":value")) || 0;
    };
    /**
     * 获取游戏手柄轴值
     * @param gamepadIndex 游戏手柄索引
     * @param axisIndex 轴索引
     * @returns 轴值
     */
    GamepadDevice.prototype.getAxisValue = function (gamepadIndex, axisIndex) {
        return this.getValue("".concat(gamepadIndex, ":axis:").concat(axisIndex)) || 0;
    };
    return GamepadDevice;
}(InputDevice_1.BaseInputDevice));
exports.GamepadDevice = GamepadDevice;
