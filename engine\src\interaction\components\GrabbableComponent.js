"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabbableComponent = exports.Hand = exports.GrabType = void 0;
/**
 * 可抓取组件
 * 用于标记可被抓取的对象
 */
var Component_1 = require("../../core/Component");
var three_1 = require("three");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 抓取类型
 */
var GrabType;
(function (GrabType) {
    /** 直接抓取 - 物体直接附加到抓取者手上 */
    GrabType["DIRECT"] = "direct";
    /** 距离抓取 - 物体保持一定距离 */
    GrabType["DISTANCE"] = "distance";
    /** 弹簧抓取 - 物体通过弹簧约束连接 */
    GrabType["SPRING"] = "spring";
})(GrabType || (exports.GrabType = GrabType = {}));
/**
 * 抓取手
 */
var Hand;
(function (Hand) {
    /** 左手 */
    Hand["LEFT"] = "left";
    /** 右手 */
    Hand["RIGHT"] = "right";
    /** 任意手 */
    Hand["ANY"] = "any";
})(Hand || (exports.Hand = Hand = {}));
/**
 * 可抓取组件
 */
var GrabbableComponent = exports.GrabbableComponent = /** @class */ (function (_super) {
    __extends(GrabbableComponent, _super);
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    function GrabbableComponent(entity, config) {
        if (config === void 0) { config = {}; }
        var _this = 
        // 调用基类构造函数，传入组件类型名称
        _super.call(this, GrabbableComponent.TYPE) || this;
        /** 是否被抓取 */
        _this._isGrabbed = false;
        /** 原始位置 */
        _this._originalPosition = new three_1.Vector3();
        /** 原始旋转 */
        _this._originalRotation = new three_1.Quaternion();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        // 设置实体引用
        _this.setEntity(entity);
        // 初始化属性
        _this._grabType = config.grabType || GrabType.DIRECT;
        _this._allowedHands = config.allowedHands || [Hand.ANY];
        _this._grabbable = config.grabbable !== undefined ? config.grabbable : true;
        _this._grabDistance = config.grabDistance || 0.1;
        _this._grabSound = config.grabSound;
        _this._releaseSound = config.releaseSound;
        // 注册回调
        if (config.onGrab) {
            _this.on('grab', config.onGrab);
        }
        if (config.onRelease) {
            _this.on('release', config.onRelease);
        }
        return _this;
    }
    Object.defineProperty(GrabbableComponent.prototype, "grabType", {
        /**
         * 获取抓取类型
         */
        get: function () {
            return this._grabType;
        },
        /**
         * 设置抓取类型
         */
        set: function (value) {
            this._grabType = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "allowedHands", {
        /**
         * 获取允许的抓取手
         */
        get: function () {
            return this._allowedHands;
        },
        /**
         * 设置允许的抓取手
         */
        set: function (value) {
            this._allowedHands = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "grabbable", {
        /**
         * 获取是否可抓取
         */
        get: function () {
            return this._grabbable;
        },
        /**
         * 设置是否可抓取
         */
        set: function (value) {
            this._grabbable = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "grabDistance", {
        /**
         * 获取抓取距离
         */
        get: function () {
            return this._grabDistance;
        },
        /**
         * 设置抓取距离
         */
        set: function (value) {
            this._grabDistance = value;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "isGrabbed", {
        /**
         * 获取是否被抓取
         */
        get: function () {
            return this._isGrabbed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "grabber", {
        /**
         * 获取抓取者
         */
        get: function () {
            return this._grabber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GrabbableComponent.prototype, "grabbedHand", {
        /**
         * 获取抓取手
         */
        get: function () {
            return this._grabbedHand;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    GrabbableComponent.prototype.on = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    GrabbableComponent.prototype.off = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    /**
     * 被抓取
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 是否抓取成功
     */
    GrabbableComponent.prototype.grab = function (grabber, hand) {
        if (hand === void 0) { hand = Hand.RIGHT; }
        // 如果不可抓取或已被抓取，则返回失败
        if (!this._grabbable || this._isGrabbed) {
            return false;
        }
        // 检查是否允许使用指定的手抓取
        if (!this._allowedHands.includes(Hand.ANY) && !this._allowedHands.includes(hand)) {
            return false;
        }
        // 保存原始父实体
        this._originalParent = this.entity.getParent();
        // 保存原始位置和旋转
        var transform = this.entity.getComponent('Transform');
        if (transform) {
            this._originalPosition.copy(transform.position);
            this._originalRotation.copy(transform.rotation);
        }
        // 设置抓取状态
        this._isGrabbed = true;
        this._grabber = grabber;
        this._grabbedHand = hand;
        // 触发抓取事件
        this.eventEmitter.emit('grab', this.entity, grabber);
        return true;
    };
    /**
     * 释放
     * @returns 是否释放成功
     */
    GrabbableComponent.prototype.release = function () {
        // 如果未被抓取，则返回失败
        if (!this._isGrabbed || !this._grabber) {
            return false;
        }
        var grabber = this._grabber;
        // 重置抓取状态
        this._isGrabbed = false;
        this._grabber = undefined;
        this._grabbedHand = undefined;
        // 触发释放事件
        this.eventEmitter.emit('release', this.entity, grabber);
        return true;
    };
    /** 组件类型 */
    GrabbableComponent.TYPE = 'GrabbableComponent';
    return GrabbableComponent;
}(Component_1.Component));
