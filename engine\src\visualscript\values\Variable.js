"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Variable = void 0;
/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
var Variable = /** @class */ (function (_super) {
    __extends(Variable, _super);
    /**
     * 创建变量
     * @param options 变量选项
     */
    function Variable(options) {
        var _this = _super.call(this) || this;
        _this.id = options.id;
        _this._name = options.name;
        _this._type = options.type;
        _this._value = options.value;
        _this._description = options.description || '';
        _this._constant = options.constant || false;
        _this._global = options.global || false;
        return _this;
    }
    Object.defineProperty(Variable.prototype, "name", {
        /**
         * 获取变量名称
         * @returns 变量名称
         */
        get: function () {
            return this._name;
        },
        /**
         * 设置变量名称
         * @param value 变量名称
         */
        set: function (value) {
            if (this._name !== value) {
                var oldValue = this._name;
                this._name = value;
                this.emit('nameChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Variable.prototype, "type", {
        /**
         * 获取变量类型
         * @returns 变量类型
         */
        get: function () {
            return this._type;
        },
        /**
         * 设置变量类型
         * @param value 变量类型
         */
        set: function (value) {
            if (this._type !== value) {
                var oldValue = this._type;
                this._type = value;
                this.emit('typeChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Variable.prototype, "value", {
        /**
         * 获取变量值
         * @returns 变量值
         */
        get: function () {
            return this._value;
        },
        /**
         * 设置变量值
         * @param value 变量值
         */
        set: function (value) {
            // 如果是常量，不允许修改值
            if (this._constant) {
                console.warn("\u65E0\u6CD5\u4FEE\u6539\u5E38\u91CF\u53D8\u91CF\u7684\u503C: ".concat(this._name));
                return;
            }
            if (this._value !== value) {
                var oldValue = this._value;
                this._value = value;
                this.emit('valueChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Variable.prototype, "description", {
        /**
         * 获取变量描述
         * @returns 变量描述
         */
        get: function () {
            return this._description;
        },
        /**
         * 设置变量描述
         * @param value 变量描述
         */
        set: function (value) {
            if (this._description !== value) {
                var oldValue = this._description;
                this._description = value;
                this.emit('descriptionChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Variable.prototype, "constant", {
        /**
         * 获取是否为常量
         * @returns 是否为常量
         */
        get: function () {
            return this._constant;
        },
        /**
         * 设置是否为常量
         * @param value 是否为常量
         */
        set: function (value) {
            if (this._constant !== value) {
                var oldValue = this._constant;
                this._constant = value;
                this.emit('constantChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Variable.prototype, "global", {
        /**
         * 获取是否为全局变量
         * @returns 是否为全局变量
         */
        get: function () {
            return this._global;
        },
        /**
         * 设置是否为全局变量
         * @param value 是否为全局变量
         */
        set: function (value) {
            if (this._global !== value) {
                var oldValue = this._global;
                this._global = value;
                this.emit('globalChanged', value, oldValue);
            }
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 重置变量值
     */
    Variable.prototype.reset = function () {
        // 如果是常量，不允许重置值
        if (this._constant) {
            console.warn("\u65E0\u6CD5\u91CD\u7F6E\u5E38\u91CF\u53D8\u91CF\u7684\u503C: ".concat(this._name));
            return;
        }
        var oldValue = this._value;
        this._value = undefined;
        this.emit('valueChanged', undefined, oldValue);
        this.emit('reset');
    };
    /**
     * 克隆变量
     * @returns 克隆的变量
     */
    Variable.prototype.clone = function () {
        return new Variable({
            id: this.id,
            name: this._name,
            type: this._type,
            value: this._value,
            description: this._description,
            constant: this._constant,
            global: this._global
        });
    };
    /**
     * 序列化变量
     * @returns 序列化数据
     */
    Variable.prototype.serialize = function () {
        return {
            id: this.id,
            name: this._name,
            type: this._type,
            value: this._value,
            description: this._description,
            constant: this._constant,
            global: this._global
        };
    };
    /**
     * 反序列化变量
     * @param data 序列化数据
     */
    Variable.prototype.deserialize = function (data) {
        if (data.name !== undefined) {
            this._name = data.name;
        }
        if (data.type !== undefined) {
            this._type = data.type;
        }
        if (data.value !== undefined) {
            this._value = data.value;
        }
        if (data.description !== undefined) {
            this._description = data.description;
        }
        if (data.constant !== undefined) {
            this._constant = data.constant;
        }
        if (data.global !== undefined) {
            this._global = data.global;
        }
    };
    return Variable;
}(EventEmitter_1.EventEmitter));
exports.Variable = Variable;
